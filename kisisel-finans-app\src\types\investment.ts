// Investment Types - Ya<PERSON>ırım türleri

export interface RiskProfile {
  id: string;
  userId: string;
  riskLevel: 'conservative' | 'moderate' | 'aggressive';
  riskScore: number; // 1-10 arası
  investmentExperience: 'beginner' | 'intermediate' | 'advanced';
  investmentGoal: 'retirement' | 'wealth_growth' | 'income' | 'preservation';
  timeHorizon: 'short' | 'medium' | 'long'; // <2 yıl, 2-10 yıl, >10 yıl
  monthlyInvestmentBudget: number;
  age: number;
  questionnaire: RiskQuestionnaireAnswer[];
  createdAt: string;
  updatedAt: string;
}

export interface RiskQuestionnaireAnswer {
  questionId: string;
  answer: string | number;
  score: number;
}

export interface InvestmentOption {
  id: string;
  name: string;
  type: 'stock' | 'bond' | 'fund' | 'etf' | 'crypto' | 'commodity' | 'real_estate';
  symbol?: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  expectedReturn: number; // Yıllık beklenen getiri %
  minimumInvestment: number;
  fees: {
    managementFee?: number; // Yönetim ücreti %
    transactionFee?: number; // İşlem ücreti
    performanceFee?: number; // Performans ücreti %
  };
  performance: {
    oneMonth: number;
    threeMonths: number;
    sixMonths: number;
    oneYear: number;
    threeYears?: number;
    fiveYears?: number;
  };
  currency: string;
  isActive: boolean;
  tags: string[];
  provider?: string;
  lastUpdated: string;
}

export interface Portfolio {
  id: string;
  userId: string;
  name: string;
  totalValue: number;
  totalInvested: number;
  totalReturn: number;
  totalReturnPercentage: number;
  allocations: PortfolioAllocation[];
  riskLevel: 'low' | 'medium' | 'high';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioAllocation {
  investmentId: string;
  investment: InvestmentOption;
  quantity: number;
  currentValue: number;
  investedAmount: number;
  percentage: number;
  return: number;
  returnPercentage: number;
}

export interface InvestmentRecommendation {
  id: string;
  userId: string;
  riskProfileId: string;
  recommendedAllocations: RecommendedAllocation[];
  expectedReturn: number;
  riskScore: number;
  reasoning: string;
  confidence: number; // 0-1 arası
  createdAt: string;
  isActive: boolean;
}

export interface RecommendedAllocation {
  investmentId: string;
  investment: InvestmentOption;
  recommendedPercentage: number;
  reasoning: string;
  priority: 'high' | 'medium' | 'low';
}

export interface InvestmentTransaction {
  id: string;
  userId: string;
  portfolioId: string;
  investmentId: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  totalAmount: number;
  fees: number;
  date: string;
  status: 'pending' | 'completed' | 'failed';
  notes?: string;
}

export interface MarketData {
  symbol: string;
  name: string;
  currentPrice: number;
  change: number;
  changePercentage: number;
  volume: number;
  marketCap?: number;
  high52Week?: number;
  low52Week?: number;
  lastUpdated: string;
}

// Risk Questionnaire Questions
export interface RiskQuestion {
  id: string;
  question: string;
  type: 'single_choice' | 'multiple_choice' | 'scale' | 'number';
  options?: RiskQuestionOption[];
  weight: number; // Sorunun risk skorundaki ağırlığı
  category: 'experience' | 'goal' | 'timeline' | 'tolerance' | 'financial';
}

export interface RiskQuestionOption {
  id: string;
  text: string;
  score: number;
}

// Investment Goal Types
export type InvestmentGoalType =
  | 'retirement'
  | 'house_purchase'
  | 'education'
  | 'emergency_fund'
  | 'wealth_growth'
  | 'passive_income'
  | 'short_term_savings';

// Asset Classes
export type AssetClass =
  | 'stocks'
  | 'bonds'
  | 'real_estate'
  | 'commodities'
  | 'crypto'
  | 'cash'
  | 'alternatives';

// Investment Strategy Types
export type InvestmentStrategy =
  | 'buy_and_hold'
  | 'dollar_cost_averaging'
  | 'value_investing'
  | 'growth_investing'
  | 'index_investing'
  | 'dividend_investing';

export interface AutoInvestmentPlan {
  id: string;
  userId: string;
  portfolioId: string;
  amount: number;
  frequency: 'weekly' | 'monthly' | 'quarterly';
  nextExecutionDate: string;
  allocations: {
    investmentId: string;
    percentage: number;
  }[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Risk Assessment Constants
export const RISK_QUESTIONS = [
  {
    id: '1',
    question: 'Yatırım deneyiminiz nedir?',
    type: 'single_choice' as const,
    options: [
      { id: '1a', text: 'Hiç deneyimim yok', score: 1 },
      { id: '1b', text: 'Az deneyimim var (1-2 yıl)', score: 2 },
      { id: '1c', text: 'Orta düzeyde deneyimim var (3-5 yıl)', score: 3 },
      { id: '1d', text: 'Çok deneyimliyim (5+ yıl)', score: 4 },
    ],
    weight: 1.5,
    category: 'experience' as const,
  },
  {
    id: '2',
    question: 'Yatırım hedefiniz nedir?',
    type: 'single_choice' as const,
    options: [
      { id: '2a', text: 'Emeklilik için birikim', score: 2 },
      { id: '2b', text: 'Servet artışı', score: 4 },
      { id: '2c', text: 'Düzenli gelir elde etme', score: 1 },
      { id: '2d', text: 'Sermaye korunması', score: 1 },
    ],
    weight: 1.2,
    category: 'goal' as const,
  },
  {
    id: '3',
    question: 'Yatırım süreniz ne kadar?',
    type: 'single_choice' as const,
    options: [
      { id: '3a', text: '1 yıldan az', score: 1 },
      { id: '3b', text: '1-3 yıl', score: 2 },
      { id: '3c', text: '3-10 yıl', score: 3 },
      { id: '3d', text: '10 yıldan fazla', score: 4 },
    ],
    weight: 1.3,
    category: 'timeline' as const,
  },
  {
    id: '4',
    question: 'Portföyünüzün %20 değer kaybetmesi durumunda ne yaparsınız?',
    type: 'single_choice' as const,
    options: [
      { id: '4a', text: 'Hemen satarım', score: 1 },
      { id: '4b', text: 'Endişelenirim ama beklerim', score: 2 },
      { id: '4c', text: 'Normal karşılarım', score: 3 },
      { id: '4d', text: 'Daha fazla alırım', score: 4 },
    ],
    weight: 2.0,
    category: 'tolerance' as const,
  },
  {
    id: '5',
    question: 'Aylık gelirinizin yüzde kaçını yatırıma ayırabilirsiniz?',
    type: 'single_choice' as const,
    options: [
      { id: '5a', text: '%5\'ten az', score: 1 },
      { id: '5b', text: '%5-10', score: 2 },
      { id: '5c', text: '%10-20', score: 3 },
      { id: '5d', text: '%20\'den fazla', score: 4 },
    ],
    weight: 1.0,
    category: 'financial' as const,
  },
  {
    id: '6',
    question: 'Acil durum fonunuz var mı?',
    type: 'single_choice' as const,
    options: [
      { id: '6a', text: 'Hayır, hiç yok', score: 1 },
      { id: '6b', text: '1-3 aylık giderim kadar', score: 2 },
      { id: '6c', text: '3-6 aylık giderim kadar', score: 3 },
      { id: '6d', text: '6+ aylık giderim kadar', score: 4 },
    ],
    weight: 1.1,
    category: 'financial' as const,
  },
];
