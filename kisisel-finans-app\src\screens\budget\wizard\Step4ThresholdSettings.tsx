// RFC-003 Büt<PERSON>e Sihirbazı - Adım 4: Uyarı Eşikleri

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';

const Step4ThresholdSettings: React.FC = () => {
  const { theme } = useTheme();
  const { state, dispatch } = useBudgetWizard();
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Preset configurations
  const presetConfigs = [
    {
      name: 'Muhafazakar',
      description: '<PERSON><PERSON><PERSON> u<PERSON>ı<PERSON>, sıkı kontrol',
      icon: 'shield-checkmark-outline',
      color: theme.colors.success,
      warning: 60,
      critical: 75,
      limit: 90,
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      description: 'Standart uyarı sistemi',
      icon: 'scale-outline',
      color: theme.colors.primary,
      warning: 75,
      critical: 90,
      limit: 100,
    },
    {
      name: 'Esnek',
      description: 'Geç uyarılar, daha fazla özgürlük',
      icon: 'leaf-outline',
      color: theme.colors.warning,
      warning: 85,
      critical: 95,
      limit: 110,
    },
  ];

  const handlePresetSelect = useCallback((preset: typeof presetConfigs[0]) => {
    dispatch({
      type: 'SET_THRESHOLDS',
      payload: {
        defaultWarningThreshold: preset.warning,
        defaultCriticalThreshold: preset.critical,
        defaultLimitThreshold: preset.limit,
      }
    });

    // Update all existing categories with new thresholds
    const updatedCategories = state.selectedCategories.map(category => ({
      ...category,
      warningThreshold: preset.warning,
      criticalThreshold: preset.critical,
      limitThreshold: preset.limit,
    }));

    dispatch({ type: 'SET_CATEGORIES', payload: updatedCategories });

    Alert.alert(
      'Eşikler Güncellendi',
      `${preset.name} profili tüm kategorilere uygulandı.`,
      [{ text: 'Tamam' }]
    );
  }, [state.selectedCategories, dispatch]);

  const handleThresholdChange = useCallback((type: 'warning' | 'critical' | 'limit', value: number) => {
    dispatch({
      type: 'SET_THRESHOLDS',
      payload: { [`default${type.charAt(0).toUpperCase() + type.slice(1)}Threshold`]: Math.round(value) }
    });
  }, [dispatch]);

  const applyToAllCategories = useCallback(() => {
    Alert.alert(
      'Eşikleri Uygula',
      'Mevcut eşik ayarları tüm kategorilere uygulansin mi?',
      [
        { text: 'Hayır', style: 'cancel' },
        {
          text: 'Evet',
          onPress: () => {
            const updatedCategories = state.selectedCategories.map(category => ({
              ...category,
              warningThreshold: state.defaultWarningThreshold,
              criticalThreshold: state.defaultCriticalThreshold,
              limitThreshold: state.defaultLimitThreshold,
            }));

            dispatch({ type: 'SET_CATEGORIES', payload: updatedCategories });
            Alert.alert('Başarılı', 'Eşikler tüm kategorilere uygulandı.');
          }
        }
      ]
    );
  }, [state.selectedCategories, state.defaultWarningThreshold, state.defaultCriticalThreshold, state.defaultLimitThreshold, dispatch]);

  const getThresholdColor = useCallback((value: number, type: 'warning' | 'critical' | 'limit') => {
    switch (type) {
      case 'warning':
        return value <= 70 ? theme.colors.success : value <= 80 ? theme.colors.warning : theme.colors.error;
      case 'critical':
        return value <= 85 ? theme.colors.warning : theme.colors.error;
      case 'limit':
        return value <= 100 ? theme.colors.error : '#8B0000';
      default:
        return theme.colors.primary;
    }
  }, [theme.colors]);

  const formatPercentage = (value: number) => `%${Math.round(value)}`;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Uyarı Eşikleri
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Bütçe limitlerinizdeki uyarı eşiklerini ayarlayın
        </Text>
      </View>

      {/* Hazır Profiller */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Hazır Profiller
        </Text>
        <View style={styles.presetGrid}>
          {presetConfigs.map((preset, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.presetCard,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => handlePresetSelect(preset)}
            >
              <View style={[styles.presetIcon, { backgroundColor: preset.color + '20' }]}>
                <Ionicons name={preset.icon as any} size={24} color={preset.color} />
              </View>
              <View style={styles.presetContent}>
                <Text style={[styles.presetName, { color: theme.colors.text }]}>
                  {preset.name}
                </Text>
                <Text style={[styles.presetDescription, { color: theme.colors.textSecondary }]}>
                  {preset.description}
                </Text>
                <View style={styles.presetValues}>
                  <Text style={[styles.presetValue, { color: theme.colors.textSecondary }]}>
                    Uyarı: %{preset.warning} • Kritik: %{preset.critical} • Limit: %{preset.limit}
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Manuel Ayarlar */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Manuel Ayarlar
        </Text>

        {/* Uyarı Eşiği */}
        <View style={styles.thresholdItem}>
          <View style={styles.thresholdHeader}>
            <View style={styles.thresholdInfo}>
              <Ionicons name="warning-outline" size={20} color={theme.colors.warning} />
              <Text style={[styles.thresholdName, { color: theme.colors.text }]}>
                Uyarı Eşiği
              </Text>
            </View>
            <Text style={[
              styles.thresholdValue,
              { color: getThresholdColor(state.defaultWarningThreshold, 'warning') }
            ]}>
              {formatPercentage(state.defaultWarningThreshold)}
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={50}
            maximumValue={95}
            value={state.defaultWarningThreshold}
            onValueChange={(value) => handleThresholdChange('warning', value)}
            minimumTrackTintColor={getThresholdColor(state.defaultWarningThreshold, 'warning')}
            maximumTrackTintColor={theme.colors.border}
            thumbStyle={{ backgroundColor: getThresholdColor(state.defaultWarningThreshold, 'warning') }}
          />
          <Text style={[styles.thresholdDescription, { color: theme.colors.textSecondary }]}>
            Bu eşiğe ulaşıldığında uyarı bildirimi gönderilir
          </Text>
        </View>

        {/* Kritik Eşik */}
        <View style={styles.thresholdItem}>
          <View style={styles.thresholdHeader}>
            <View style={styles.thresholdInfo}>
              <Ionicons name="alert-circle-outline" size={20} color={theme.colors.error} />
              <Text style={[styles.thresholdName, { color: theme.colors.text }]}>
                Kritik Eşik
              </Text>
            </View>
            <Text style={[
              styles.thresholdValue,
              { color: getThresholdColor(state.defaultCriticalThreshold, 'critical') }
            ]}>
              {formatPercentage(state.defaultCriticalThreshold)}
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={state.defaultWarningThreshold + 5}
            maximumValue={100}
            value={state.defaultCriticalThreshold}
            onValueChange={(value) => handleThresholdChange('critical', value)}
            minimumTrackTintColor={getThresholdColor(state.defaultCriticalThreshold, 'critical')}
            maximumTrackTintColor={theme.colors.border}
            thumbStyle={{ backgroundColor: getThresholdColor(state.defaultCriticalThreshold, 'critical') }}
          />
          <Text style={[styles.thresholdDescription, { color: theme.colors.textSecondary }]}>
            Bu eşiğe ulaşıldığında kritik uyarı gönderilir
          </Text>
        </View>

        {/* Limit Eşiği */}
        <View style={styles.thresholdItem}>
          <View style={styles.thresholdHeader}>
            <View style={styles.thresholdInfo}>
              <Ionicons name="stop-circle-outline" size={20} color="#8B0000" />
              <Text style={[styles.thresholdName, { color: theme.colors.text }]}>
                Limit Eşiği
              </Text>
            </View>
            <Text style={[
              styles.thresholdValue,
              { color: getThresholdColor(state.defaultLimitThreshold, 'limit') }
            ]}>
              {formatPercentage(state.defaultLimitThreshold)}
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={state.defaultCriticalThreshold + 5}
            maximumValue={120}
            value={state.defaultLimitThreshold}
            onValueChange={(value) => handleThresholdChange('limit', value)}
            minimumTrackTintColor={getThresholdColor(state.defaultLimitThreshold, 'limit')}
            maximumTrackTintColor={theme.colors.border}
            thumbStyle={{ backgroundColor: getThresholdColor(state.defaultLimitThreshold, 'limit') }}
          />
          <Text style={[styles.thresholdDescription, { color: theme.colors.textSecondary }]}>
            Bu eşiği aştığınızda limit aşımı uyarısı gönderilir
          </Text>
        </View>
      </View>

      {/* Uygula Butonu */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[
            styles.applyButton,
            { backgroundColor: theme.colors.primary }
          ]}
          onPress={applyToAllCategories}
        >
          <Ionicons name="checkmark-done" size={20} color={theme.colors.surface} />
          <Text style={[styles.applyButtonText, { color: theme.colors.surface }]}>
            Tüm Kategorilere Uygula
          </Text>
        </TouchableOpacity>
      </View>

      {/* Gelişmiş Ayarlar */}
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.advancedToggle}
          onPress={() => setShowAdvanced(!showAdvanced)}
        >
          <Text style={[styles.advancedToggleText, { color: theme.colors.primary }]}>
            Gelişmiş Ayarlar
          </Text>
          <Ionicons
            name={showAdvanced ? "chevron-up" : "chevron-down"}
            size={20}
            color={theme.colors.primary}
          />
        </TouchableOpacity>

        {showAdvanced && (
          <View style={[styles.advancedContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.advancedTitle, { color: theme.colors.text }]}>
              Uyarı Sistemi Hakkında
            </Text>

            <View style={styles.infoItem}>
              <Ionicons name="information-circle-outline" size={16} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                Uyarı eşikleri kategori bazında özelleştirilebilir
              </Text>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="notifications-outline" size={16} color={theme.colors.warning} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                Bildirimler günlük olarak kontrol edilir
              </Text>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="analytics-outline" size={16} color={theme.colors.success} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                Harcama trendleri analiz edilerek akıllı öneriler sunulur
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Validation Error */}
      {state.errors.thresholds && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.thresholds}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  presetGrid: {
    gap: 12,
  },
  presetCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  presetIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  presetContent: {
    flex: 1,
  },
  presetName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  presetDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  presetValues: {
    marginTop: 4,
  },
  presetValue: {
    fontSize: 12,
  },
  thresholdItem: {
    marginBottom: 24,
  },
  thresholdHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  thresholdInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thresholdName: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  thresholdValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  slider: {
    width: '100%',
    height: 40,
    marginBottom: 8,
  },
  thresholdDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  advancedToggleText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 4,
  },
  advancedContent: {
    marginTop: 12,
    padding: 16,
    borderRadius: 8,
  },
  advancedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  errorContainer: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default Step4ThresholdSettings;
