// Share Service - Email ve sosyal medya paylaşım servisi

import * as Sharing from 'expo-sharing';
import * as MailComposer from 'expo-mail-composer';
import * as FileSystem from 'expo-file-system';
import { Alert, Platform } from 'react-native';

export interface ShareOptions {
  title?: string;
  message?: string;
  url?: string;
  type?: 'email' | 'social' | 'whatsapp' | 'telegram' | 'general';
  recipients?: string[];
  subject?: string;
  attachments?: string[];
}

export interface EmailOptions {
  recipients?: string[];
  subject: string;
  body: string;
  attachments?: string[];
  isHtml?: boolean;
}

class ShareService {
  /**
   * Email gönderme
   */
  static async sendEmail(options: EmailOptions): Promise<{ success: boolean; error?: string }> {
    try {
      // Email composer mevcut mu kontrol et
      const isAvailable = await MailComposer.isAvailableAsync();
      if (!isAvailable) {
        return {
          success: false,
          error: 'Email uygulaması bulunamadı. Lütfen bir email uygulaması yükleyin.'
        };
      }

      // Email seçeneklerini hazırla
      const emailOptions: MailComposer.MailComposerOptions = {
        recipients: options.recipients || [],
        subject: options.subject,
        body: options.body,
        isHtml: options.isHtml || false,
      };

      // Attachments varsa ekle
      if (options.attachments && options.attachments.length > 0) {
        emailOptions.attachments = options.attachments;
      }

      // Email composer'ı aç
      const result = await MailComposer.composeAsync(emailOptions);

      if (result.status === MailComposer.MailComposerStatus.SENT) {
        console.log('✅ Email sent successfully');
        return { success: true };
      } else if (result.status === MailComposer.MailComposerStatus.SAVED) {
        console.log('📝 Email saved as draft');
        return { success: true };
      } else if (result.status === MailComposer.MailComposerStatus.CANCELLED) {
        console.log('❌ Email cancelled by user');
        return { success: false, error: 'Email gönderimi iptal edildi' };
      } else {
        console.log('❌ Email failed');
        return { success: false, error: 'Email gönderilemedi' };
      }

    } catch (error) {
      console.error('❌ Email error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email gönderirken bir hata oluştu'
      };
    }
  }

  /**
   * Genel paylaşım (sistem paylaşım menüsü)
   */
  static async shareGeneral(options: ShareOptions): Promise<{ success: boolean; error?: string }> {
    try {
      // Sharing mevcut mu kontrol et
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        return {
          success: false,
          error: 'Paylaşım özelliği bu cihazda desteklenmiyor'
        };
      }

      // Dosya paylaşımı
      if (options.url) {
        await Sharing.shareAsync(options.url, {
          dialogTitle: options.title || 'Paylaş',
          mimeType: this.getMimeType(options.url),
        });
      } else {
        // Metin paylaşımı (Android için)
        if (Platform.OS === 'android' && options.message) {
          // Android'de metin paylaşımı için geçici dosya oluştur
          const tempFile = `${FileSystem.documentDirectory}temp_share.txt`;
          await FileSystem.writeAsStringAsync(tempFile, options.message);
          await Sharing.shareAsync(tempFile, {
            dialogTitle: options.title || 'Paylaş',
          });
          // Geçici dosyayı sil
          await FileSystem.deleteAsync(tempFile, { idempotent: true });
        } else {
          return {
            success: false,
            error: 'Paylaşılacak içerik bulunamadı'
          };
        }
      }

      console.log('✅ Content shared successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Share error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Paylaşım sırasında bir hata oluştu'
      };
    }
  }

  /**
   * WhatsApp paylaşımı
   */
  static async shareToWhatsApp(message: string, fileUrl?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const whatsappUrl = Platform.select({
        ios: 'whatsapp://send',
        android: 'whatsapp://send',
      });

      if (!whatsappUrl) {
        return { success: false, error: 'WhatsApp desteklenmiyor' };
      }

      // Dosya varsa önce genel paylaşım kullan
      if (fileUrl) {
        return await this.shareGeneral({
          title: 'WhatsApp ile Paylaş',
          url: fileUrl,
          message: message,
        });
      } else {
        // Sadece metin paylaşımı
        return await this.shareGeneral({
          title: 'WhatsApp ile Paylaş',
          message: message,
        });
      }

    } catch (error) {
      console.error('❌ WhatsApp share error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'WhatsApp paylaşımı sırasında bir hata oluştu'
      };
    }
  }

  /**
   * Dosya MIME type'ını belirle
   */
  private static getMimeType(fileUrl: string): string {
    const extension = fileUrl.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'xlsx':
      case 'xls':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';
      case 'txt':
        return 'text/plain';
      case 'json':
        return 'application/json';
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Dosya boyutunu kontrol et (MB cinsinden)
   */
  static async checkFileSize(fileUrl: string, maxSizeMB: number = 25): Promise<{ valid: boolean; sizeMB: number }> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(fileUrl);
      if (fileInfo.exists && fileInfo.size) {
        const sizeMB = fileInfo.size / (1024 * 1024);
        return {
          valid: sizeMB <= maxSizeMB,
          sizeMB: Math.round(sizeMB * 100) / 100
        };
      }
      return { valid: false, sizeMB: 0 };
    } catch (error) {
      console.error('❌ File size check error:', error);
      return { valid: false, sizeMB: 0 };
    }
  }

  /**
   * Paylaşım seçenekleri menüsü göster
   */
  static showShareOptions(
    fileUrl: string,
    fileName: string,
    onEmailPress: () => void,
    onWhatsAppPress: () => void,
    onGeneralPress: () => void
  ) {
    Alert.alert(
      'Paylaş',
      `${fileName} dosyasını nasıl paylaşmak istiyorsunuz?`,
      [
        {
          text: 'Email',
          onPress: onEmailPress,
        },
        {
          text: 'WhatsApp',
          onPress: onWhatsAppPress,
        },
        {
          text: 'Diğer',
          onPress: onGeneralPress,
        },
        {
          text: 'İptal',
          style: 'cancel',
        },
      ]
    );
  }
}

export default ShareService;
