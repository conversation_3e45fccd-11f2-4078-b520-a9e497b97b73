// Kategori Senkronizasyon Servisi

import CategorySyncMigration from '../database/migrations/CategorySyncMigration';

export class CategorySyncService {
  
  /**
   * Kategori senkronizasyonunu çalıştır
   */
  async syncCategories(): Promise<void> {
    try {
      console.log('🔄 Starting category synchronization service...');
      
      // Migration'ı çalıştır
      await CategorySyncMigration.execute();
      
      console.log('✅ Category synchronization service completed!');
    } catch (error) {
      console.error('❌ Category synchronization service failed:', error);
      throw error;
    }
  }

  /**
   * Kullanıcı kategorilerini senkronize et
   */
  async syncUserCategories(userId: string): Promise<void> {
    try {
      console.log(`🔄 Syncing categories for user: ${userId}`);
      
      await CategorySyncMigration.syncUserCategories(userId);
      
      console.log(`✅ User categories synced for: ${userId}`);
    } catch (error) {
      console.error(`❌ User category sync failed for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * <PERSON>gori durumunu kontrol et
   */
  async checkCategoryStatus(): Promise<{
    totalCategories: number;
    incomeCategories: number;
    expenseCategories: number;
    categoriesWithCode: number;
    categoriesWithoutCode: number;
  }> {
    try {
      const DatabaseManager = require('../database/DatabaseManager').default;
      await DatabaseManager.initialize(); // Database'i initialize et
      const db = DatabaseManager.getDatabase();
      
      const totalResult = await db.getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM categories'
      );
      
      const incomeResult = await db.getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM categories WHERE type = ?',
        ['income']
      );
      
      const expenseResult = await db.getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM categories WHERE type = ?',
        ['expense']
      );
      
      const withCodeResult = await db.getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM categories WHERE code IS NOT NULL AND code != ""'
      );
      
      const withoutCodeResult = await db.getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM categories WHERE code IS NULL OR code = ""'
      );
      
      return {
        totalCategories: totalResult?.count || 0,
        incomeCategories: incomeResult?.count || 0,
        expenseCategories: expenseResult?.count || 0,
        categoriesWithCode: withCodeResult?.count || 0,
        categoriesWithoutCode: withoutCodeResult?.count || 0,
      };
    } catch (error) {
      console.error('❌ Error checking category status:', error);
      throw error;
    }
  }
}

export default new CategorySyncService();
