// Recurring Template Repository - Tekrarlayan işlem şablonları yönetimi

import { getDatabaseInstance } from '../DatabaseManager';
import { RecurringTemplate, RecurrenceRule, TransactionType, CurrencyCode } from '../../types/transaction';

class RecurringTemplateRepository {
  private getDatabase() {
    return getDatabaseInstance();
  }

  /**
   * <PERSON>ni tekrarlayan şablon oluştur
   */
  async create(template: Omit<RecurringTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const db = this.getDatabase();
      const id = `recurring_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();

      await db.runAsync(
        `INSERT INTO recurring_templates (
          id, user_id, name, type, amount, currency, category, subcategory,
          description, payment_method, frequency, interval_value,
          next_execution_date, last_execution_date, end_date, is_active,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          template.userId,
          template.name,
          template.type,
          template.amount,
          template.currency,
          template.category,
          template.subcategory || null,
          template.description || null,
          template.paymentMethod || null,
          template.recurrence.frequency,
          template.recurrence.interval,
          template.nextExecutionDate,
          template.lastExecutionDate || null,
          template.recurrence.endDate || null,
          template.isActive ? 1 : 0,
          now,
          now,
        ]
      );

      console.log(`✅ Recurring template created: ${id}`);
      return id;
    } catch (error) {
      console.error('❌ Error creating recurring template:', error);
      throw error;
    }
  }

  /**
   * Kullanıcının tüm tekrarlayan şablonlarını al
   */
  async getByUserId(userId: string): Promise<RecurringTemplate[]> {
    try {
      const db = this.getDatabase();

      const rows = await db.getAllAsync<any>(
        `SELECT * FROM recurring_templates
         WHERE user_id = ?
         ORDER BY created_at DESC`,
        [userId]
      );

      const templates: RecurringTemplate[] = rows.map(row => this.mapRowToTemplate(row));

      console.log(`✅ Retrieved ${templates.length} recurring templates for user ${userId}`);
      return templates;
    } catch (error) {
      console.error('❌ Error getting recurring templates:', error);
      throw error;
    }
  }

  /**
   * Aktif tekrarlayan şablonları al
   */
  async getActiveTemplates(userId: string): Promise<RecurringTemplate[]> {
    try {
      const db = this.getDatabase();

      const rows = await db.getAllAsync<any>(
        `SELECT * FROM recurring_templates
         WHERE user_id = ? AND is_active = 1
         ORDER BY next_execution_date ASC`,
        [userId]
      );

      const templates: RecurringTemplate[] = rows.map(row => this.mapRowToTemplate(row));

      console.log(`✅ Retrieved ${templates.length} active recurring templates`);
      return templates;
    } catch (error) {
      console.error('❌ Error getting active recurring templates:', error);
      throw error;
    }
  }

  /**
   * Çalıştırılması gereken şablonları al
   */
  async getTemplatesForExecution(currentDate: string): Promise<RecurringTemplate[]> {
    try {
      const db = this.getDatabase();

      const rows = await db.getAllAsync<any>(
        `SELECT * FROM recurring_templates
         WHERE is_active = 1
         AND next_execution_date <= ?
         AND (end_date IS NULL OR end_date >= ?)
         ORDER BY next_execution_date ASC`,
        [currentDate, currentDate]
      );

      const templates: RecurringTemplate[] = rows.map(row => this.mapRowToTemplate(row));

      console.log(`✅ Found ${templates.length} templates ready for execution`);
      return templates;
    } catch (error) {
      console.error('❌ Error getting templates for execution:', error);
      throw error;
    }
  }

  /**
   * Şablon güncelle
   */
  async update(id: string, updates: Partial<RecurringTemplate>): Promise<void> {
    try {
      const db = this.getDatabase();
      const now = new Date().toISOString();

      const setClause = [];
      const values = [];

      if (updates.name !== undefined) {
        setClause.push('name = ?');
        values.push(updates.name);
      }
      if (updates.amount !== undefined) {
        setClause.push('amount = ?');
        values.push(updates.amount);
      }
      if (updates.currency !== undefined) {
        setClause.push('currency = ?');
        values.push(updates.currency);
      }
      if (updates.category !== undefined) {
        setClause.push('category = ?');
        values.push(updates.category);
      }
      if (updates.subcategory !== undefined) {
        setClause.push('subcategory = ?');
        values.push(updates.subcategory);
      }
      if (updates.description !== undefined) {
        setClause.push('description = ?');
        values.push(updates.description);
      }
      if (updates.paymentMethod !== undefined) {
        setClause.push('payment_method = ?');
        values.push(updates.paymentMethod);
      }
      if (updates.recurrence !== undefined) {
        setClause.push('frequency = ?', 'interval_value = ?');
        values.push(updates.recurrence.frequency, updates.recurrence.interval);
        if (updates.recurrence.endDate !== undefined) {
          setClause.push('end_date = ?');
          values.push(updates.recurrence.endDate);
        }
      }
      if (updates.nextExecutionDate !== undefined) {
        setClause.push('next_execution_date = ?');
        values.push(updates.nextExecutionDate);
      }
      if (updates.lastExecutionDate !== undefined) {
        setClause.push('last_execution_date = ?');
        values.push(updates.lastExecutionDate);
      }
      if (updates.isActive !== undefined) {
        setClause.push('is_active = ?');
        values.push(updates.isActive ? 1 : 0);
      }

      setClause.push('updated_at = ?');
      values.push(now);

      values.push(id);

      await db.runAsync(
        `UPDATE recurring_templates SET ${setClause.join(', ')} WHERE id = ?`,
        values
      );

      console.log(`✅ Recurring template updated: ${id}`);
    } catch (error) {
      console.error('❌ Error updating recurring template:', error);
      throw error;
    }
  }

  /**
   * Şablon sil
   */
  async delete(id: string): Promise<void> {
    try {
      const db = this.getDatabase();

      await db.runAsync(
        'DELETE FROM recurring_templates WHERE id = ?',
        [id]
      );

      console.log(`✅ Recurring template deleted: ${id}`);
    } catch (error) {
      console.error('❌ Error deleting recurring template:', error);
      throw error;
    }
  }

  /**
   * Şablonu aktif/pasif yap
   */
  async toggleActive(id: string, isActive: boolean): Promise<void> {
    try {
      const db = this.getDatabase();
      const now = new Date().toISOString();

      await db.runAsync(
        'UPDATE recurring_templates SET is_active = ?, updated_at = ? WHERE id = ?',
        [isActive ? 1 : 0, now, id]
      );

      console.log(`✅ Recurring template ${isActive ? 'activated' : 'deactivated'}: ${id}`);
    } catch (error) {
      console.error('❌ Error toggling recurring template:', error);
      throw error;
    }
  }

  /**
   * Database row'unu RecurringTemplate objesine çevir
   */
  private mapRowToTemplate(row: any): RecurringTemplate {
    const recurrence: RecurrenceRule = {
      frequency: row.frequency,
      interval: row.interval_value,
      endDate: row.end_date,
    };

    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      type: row.type as TransactionType,
      amount: row.amount,
      currency: row.currency as CurrencyCode,
      category: row.category,
      subcategory: row.subcategory,
      description: row.description,
      paymentMethod: row.payment_method,
      recurrence,
      isActive: row.is_active === 1,
      nextExecutionDate: row.next_execution_date,
      lastExecutionDate: row.last_execution_date,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}

export default new RecurringTemplateRepository();
