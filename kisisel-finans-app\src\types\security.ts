// Security Types - Güvenlik izleme tip tanımlamaları

export type SecurityEventType = 
  // Authentication Events
  | 'login_success'
  | 'login_failed'
  | 'logout'
  | 'password_change'
  | 'password_reset_request'
  | 'password_reset_success'
  | 'account_locked'
  | 'account_unlocked'
  
  // Authorization Events
  | 'permission_granted'
  | 'permission_denied'
  | 'role_assigned'
  | 'role_removed'
  | 'privilege_escalation'
  
  // Session Events
  | 'session_created'
  | 'session_expired'
  | 'session_terminated'
  | 'concurrent_session_detected'
  
  // Data Access Events
  | 'data_access'
  | 'data_export'
  | 'sensitive_data_access'
  | 'unauthorized_access_attempt'
  
  // System Events
  | 'system_config_change'
  | 'admin_action'
  | 'backup_created'
  | 'backup_restored'
  
  // Security Events
  | 'brute_force_detected'
  | 'suspicious_activity'
  | 'malware_detected'
  | 'security_breach'
  | 'encryption_key_rotation'
  
  // Device Events
  | 'new_device_login'
  | 'device_fingerprint_mismatch'
  | 'jailbreak_detected'
  | 'root_detected'
  | 'emulator_detected';

export type SecurityLevel = 'low' | 'medium' | 'high' | 'critical';

export type ThreatCategory = 
  | 'authentication'
  | 'authorization' 
  | 'data_breach'
  | 'malware'
  | 'social_engineering'
  | 'insider_threat'
  | 'system_compromise'
  | 'device_security';

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  level: SecurityLevel;
  category: ThreatCategory;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  deviceId?: string;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    city?: string;
    latitude?: number;
    longitude?: number;
  };
  details: {
    action: string;
    resource?: string;
    result: 'success' | 'failure' | 'blocked';
    reason?: string;
    metadata?: Record<string, any>;
  };
  context?: {
    previousEvents?: string[];
    riskScore?: number;
    anomalyDetected?: boolean;
    mitigationApplied?: boolean;
  };
}

export interface SecurityAlert {
  id: string;
  title: string;
  description: string;
  level: SecurityLevel;
  category: ThreatCategory;
  eventIds: string[];
  createdAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  actions: SecurityAction[];
  metadata?: Record<string, any>;
}

export interface SecurityAction {
  id: string;
  type: 'block_user' | 'lock_account' | 'require_2fa' | 'notify_admin' | 'log_event' | 'quarantine_device';
  timestamp: string;
  performedBy: 'system' | 'admin' | string;
  details: Record<string, any>;
  result: 'success' | 'failure';
}

export interface ThreatDetectionRule {
  id: string;
  name: string;
  description: string;
  category: ThreatCategory;
  enabled: boolean;
  conditions: {
    eventTypes: SecurityEventType[];
    timeWindow: number; // milliseconds
    threshold: number;
    filters?: Record<string, any>;
  };
  actions: SecurityAction['type'][];
  severity: SecurityLevel;
  createdAt: string;
  updatedAt: string;
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsByType: Record<SecurityEventType, number>;
  eventsByLevel: Record<SecurityLevel, number>;
  activeAlerts: number;
  resolvedAlerts: number;
  threatScore: number; // 0-100
  lastUpdate: string;
  timeRange: {
    start: string;
    end: string;
  };
}

export interface AuditTrail {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  changes?: {
    before?: Record<string, any>;
    after?: Record<string, any>;
  };
  metadata?: Record<string, any>;
}

export interface DeviceFingerprint {
  id: string;
  userId: string;
  deviceId: string;
  platform: string;
  osVersion: string;
  appVersion: string;
  screenResolution: string;
  timezone: string;
  language: string;
  isJailbroken: boolean;
  isRooted: boolean;
  isEmulator: boolean;
  firstSeen: string;
  lastSeen: string;
  trustScore: number; // 0-100
  metadata?: Record<string, any>;
}

export interface SecurityConfiguration {
  monitoring: {
    enabled: boolean;
    logLevel: SecurityLevel;
    retentionDays: number;
    realTimeAlerts: boolean;
  };
  threatDetection: {
    enabled: boolean;
    bruteForceThreshold: number;
    suspiciousActivityThreshold: number;
    anomalyDetectionEnabled: boolean;
  };
  deviceSecurity: {
    fingerprintingEnabled: boolean;
    jailbreakDetection: boolean;
    rootDetection: boolean;
    emulatorDetection: boolean;
  };
  notifications: {
    emailAlerts: boolean;
    pushNotifications: boolean;
    smsAlerts: boolean;
    webhookUrl?: string;
  };
  compliance: {
    gdprCompliant: boolean;
    auditTrailEnabled: boolean;
    dataRetentionDays: number;
    anonymizeAfterDays: number;
  };
}

export interface SecurityReport {
  id: string;
  title: string;
  type: 'daily' | 'weekly' | 'monthly' | 'incident' | 'compliance';
  generatedAt: string;
  generatedBy: string;
  timeRange: {
    start: string;
    end: string;
  };
  metrics: SecurityMetrics;
  alerts: SecurityAlert[];
  recommendations: string[];
  riskAssessment: {
    overallRisk: SecurityLevel;
    riskFactors: Array<{
      factor: string;
      impact: SecurityLevel;
      description: string;
    }>;
  };
  compliance: {
    status: 'compliant' | 'non_compliant' | 'partial';
    issues: string[];
    recommendations: string[];
  };
}

export interface LoginAttempt {
  id: string;
  userId?: string;
  email: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  success: boolean;
  failureReason?: string;
  location?: {
    country?: string;
    city?: string;
  };
  deviceFingerprint?: string;
  blocked: boolean;
  riskScore: number;
}

export interface SessionSecurity {
  sessionId: string;
  userId: string;
  deviceId: string;
  ipAddress: string;
  createdAt: string;
  lastActivity: string;
  isActive: boolean;
  riskScore: number;
  anomalies: string[];
  location?: {
    country?: string;
    city?: string;
  };
}

// Default security rules
export const DEFAULT_THREAT_DETECTION_RULES: Omit<ThreatDetectionRule, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Brute Force Detection',
    description: 'Detect multiple failed login attempts',
    category: 'authentication',
    enabled: true,
    conditions: {
      eventTypes: ['login_failed'],
      timeWindow: 15 * 60 * 1000, // 15 minutes
      threshold: 5,
      filters: { sameUser: true },
    },
    actions: ['lock_account', 'notify_admin'],
    severity: 'high',
  },
  {
    name: 'Suspicious Login Pattern',
    description: 'Detect logins from unusual locations',
    category: 'authentication',
    enabled: true,
    conditions: {
      eventTypes: ['login_success'],
      timeWindow: 60 * 60 * 1000, // 1 hour
      threshold: 1,
      filters: { unusualLocation: true },
    },
    actions: ['require_2fa', 'notify_admin'],
    severity: 'medium',
  },
  {
    name: 'Privilege Escalation',
    description: 'Detect unauthorized privilege escalation',
    category: 'authorization',
    enabled: true,
    conditions: {
      eventTypes: ['role_assigned', 'permission_granted'],
      timeWindow: 5 * 60 * 1000, // 5 minutes
      threshold: 1,
      filters: { adminRole: true },
    },
    actions: ['notify_admin', 'log_event'],
    severity: 'critical',
  },
  {
    name: 'Device Security Violation',
    description: 'Detect compromised devices',
    category: 'device_security',
    enabled: true,
    conditions: {
      eventTypes: ['jailbreak_detected', 'root_detected', 'emulator_detected'],
      timeWindow: 1000, // Immediate
      threshold: 1,
    },
    actions: ['block_user', 'notify_admin'],
    severity: 'high',
  },
  {
    name: 'Data Export Monitoring',
    description: 'Monitor sensitive data exports',
    category: 'data_breach',
    enabled: true,
    conditions: {
      eventTypes: ['data_export'],
      timeWindow: 60 * 60 * 1000, // 1 hour
      threshold: 3,
    },
    actions: ['notify_admin', 'log_event'],
    severity: 'medium',
  },
];
