// Kategori sabitleri - RFC-003 Code Bazlı Sistem

export interface CategoryItem {
  id: string;
  code: string; // Yeni: İngilizce sabit kod
  name: string;
  icon: string;
  color: string;
  type: 'income' | 'expense';
  subcategories?: string[];
}

// G<PERSON>r kate<PERSON>i - Database ile senkronize (14 kategori)
export const INCOME_CATEGORIES: CategoryItem[] = [
  {
    id: 'salary',
    code: 'salary',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'briefcase',
    color: '#4CAF50',
    type: 'income',
    subcategories: ['<PERSON>', 'Ek <PERSON>', 'Prim', '<PERSON>kramiye']
  },
  {
    id: 'side_income',
    code: 'side_income',
    name: '<PERSON>',
    icon: 'cash',
    color: '#FF9800',
    type: 'income',
    subcategories: ['Satı<PERSON>', '<PERSON><PERSON> Ödeme', 'Sigor<PERSON>', '<PERSON>ğer']
  },
  {
    id: 'investment_income',
    code: 'investment_income',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: 'trending-up',
    color: '#2196F3',
    type: 'income',
    subcategories: ['<PERSON><PERSON>', 'Tah<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']
  },
  {
    id: 'freelance',
    code: 'freelance',
    name: 'Freelance',
    icon: 'laptop',
    color: '#9C27B0',
    type: 'income',
    subcategories: ['Proje', 'Danışmanlık', 'Tasarım', 'Yazılım']
  },
  {
    id: 'bonus',
    code: 'bonus',
    name: 'Prim / Bonus',
    icon: 'gift',
    color: '#E91E63',
    type: 'income',
    subcategories: ['Performans Primi', 'Yıl Sonu Primi', 'Satış Primi']
  },
  {
    id: 'gift_income',
    code: 'gift_income',
    name: 'Hediye / Bağış',
    icon: 'heart',
    color: '#F44336',
    type: 'income',
    subcategories: ['Hediye', 'Bağış', 'Miras', 'Yardım']
  },
  {
    id: 'rental_income',
    code: 'rental_income',
    name: 'Kira Geliri',
    icon: 'home',
    color: '#795548',
    type: 'income',
    subcategories: ['Ev Kirası', 'Dükkan Kirası', 'Araç Kirası']
  },
  {
    id: 'sales_income',
    code: 'sales_income',
    name: 'Satış Geliri',
    icon: 'storefront',
    color: '#607D8B',
    type: 'income',
    subcategories: ['Ürün Satışı', 'Hizmet Satışı', 'İkinci El']
  },
  {
    id: 'government_support',
    code: 'government_support',
    name: 'Devlet Desteği',
    icon: 'shield-checkmark',
    color: '#3F51B5',
    type: 'income',
    subcategories: ['İşsizlik Maaşı', 'Sosyal Yardım', 'Teşvik']
  },
  {
    id: 'interest_income',
    code: 'interest_income',
    name: 'Faiz Geliri',
    icon: 'card',
    color: '#00BCD4',
    type: 'income',
    subcategories: ['Mevduat Faizi', 'Tahvil Faizi', 'Kredi Faizi']
  },
  {
    id: 'crypto_income',
    code: 'crypto_income',
    name: 'Kripto Geliri',
    icon: 'logo-bitcoin',
    color: '#FF5722',
    type: 'income',
    subcategories: ['Bitcoin', 'Ethereum', 'Altcoin', 'Mining']
  },
  {
    id: 'stock_dividend',
    code: 'stock_dividend',
    name: 'Hisse Senedi / Tahvil',
    icon: 'bar-chart',
    color: '#8BC34A',
    type: 'income',
    subcategories: ['Hisse Temettü', 'Tahvil Faizi', 'Sermaye Kazancı']
  },
  {
    id: 'refund',
    code: 'refund',
    name: 'İade',
    icon: 'return-up-back',
    color: '#CDDC39',
    type: 'income',
    subcategories: ['Vergi İadesi', 'Ürün İadesi', 'Sigorta İadesi']
  },
  {
    id: 'other_income',
    code: 'other_income',
    name: 'Diğer',
    icon: 'ellipsis-horizontal',
    color: '#9E9E9E',
    type: 'income',
    subcategories: ['Beklenmeyen Gelir', 'Diğer Kaynaklar']
  }
];

// Gider kategorileri - Database ile senkronize (16 kategori)
export const EXPENSE_CATEGORIES: CategoryItem[] = [
  {
    id: 'food',
    code: 'food',
    name: 'Yiyecek & İçecek',
    icon: 'restaurant',
    color: '#FF9800',
    type: 'expense',
    subcategories: ['Market', 'Restoran', 'Kahve', 'Fast Food', 'Alkol']
  },
  {
    id: 'transportation',
    code: 'transportation',
    name: 'Ulaşım',
    icon: 'car',
    color: '#2196F3',
    type: 'expense',
    subcategories: ['Yakıt', 'Toplu Taşıma', 'Taksi', 'Otopark', 'Araç Bakım']
  },
  {
    id: 'bills',
    code: 'bills',
    name: 'Faturalar',
    icon: 'receipt',
    color: '#F44336',
    type: 'expense',
    subcategories: ['Elektrik', 'Su', 'Doğalgaz', 'İnternet', 'Telefon']
  },
  {
    id: 'shopping',
    code: 'shopping',
    name: 'Alışveriş',
    icon: 'bag',
    color: '#E91E63',
    type: 'expense',
    subcategories: ['Giyim', 'Elektronik', 'Ev Eşyası', 'Kozmetik', 'Kitap']
  },
  {
    id: 'entertainment',
    code: 'entertainment',
    name: 'Eğlence',
    icon: 'musical-notes',
    color: '#9C27B0',
    type: 'expense',
    subcategories: ['Sinema', 'Konser', 'Oyun', 'Spor', 'Hobi']
  },
  {
    id: 'health',
    code: 'health',
    name: 'Sağlık',
    icon: 'medical',
    color: '#4CAF50',
    type: 'expense',
    subcategories: ['Doktor', 'İlaç', 'Hastane', 'Diş', 'Vitamin']
  },
  {
    id: 'education',
    code: 'education',
    name: 'Eğitim',
    icon: 'school',
    color: '#3F51B5',
    type: 'expense',
    subcategories: ['Kurs', 'Kitap', 'Okul', 'Sertifika', 'Online Eğitim']
  },
  {
    id: 'rent',
    code: 'rent',
    name: 'Kira',
    icon: 'home',
    color: '#795548',
    type: 'expense',
    subcategories: ['Ev Kirası', 'Ofis Kirası', 'Depo Kirası', 'Araç Kirası']
  },
  {
    id: 'insurance',
    code: 'insurance',
    name: 'Sigorta',
    icon: 'shield',
    color: '#607D8B',
    type: 'expense',
    subcategories: ['Sağlık Sigortası', 'Araç Sigortası', 'Ev Sigortası', 'Hayat Sigortası']
  },
  {
    id: 'personal_care',
    code: 'personal_care',
    name: 'Kişisel Bakım',
    icon: 'person',
    color: '#E91E63',
    type: 'expense',
    subcategories: ['Kuaför', 'Kozmetik', 'Kıyafet', 'Spor Salonu']
  },
  {
    id: 'gifts',
    code: 'gifts',
    name: 'Hediyeler',
    icon: 'gift',
    color: '#FF9800',
    type: 'expense',
    subcategories: ['Doğum Günü', 'Düğün', 'Bayram', 'Özel Günler']
  },
  {
    id: 'travel',
    code: 'travel',
    name: 'Seyahat',
    icon: 'airplane',
    color: '#00BCD4',
    type: 'expense',
    subcategories: ['Uçak', 'Otel', 'Tatil', 'Vize', 'Sigorta']
  },
  {
    id: 'donation',
    code: 'donation',
    name: 'Bağış',
    icon: 'heart',
    color: '#4CAF50',
    type: 'expense',
    subcategories: ['Hayır Kurumu', 'Zakat', 'Sadaka', 'Yardım']
  },
  {
    id: 'tax',
    code: 'tax',
    name: 'Vergiler',
    icon: 'document-text',
    color: '#9E9E9E',
    type: 'expense',
    subcategories: ['Gelir Vergisi', 'KDV', 'MTV', 'Emlak Vergisi']
  },
  {
    id: 'housing',
    code: 'housing',
    name: 'Konut',
    icon: 'business',
    color: '#795548',
    type: 'expense',
    subcategories: ['Tadilat', 'Mobilya', 'Temizlik', 'Bahçe']
  },
  {
    id: 'other',
    code: 'other',
    name: 'Diğer',
    icon: 'ellipsis-horizontal',
    color: '#9E9E9E',
    type: 'expense',
    subcategories: ['Ceza', 'Diğer Giderler', 'Beklenmeyen']
  }
];

// Tüm kategoriler
export const ALL_CATEGORIES = [...INCOME_CATEGORIES, ...EXPENSE_CATEGORIES];

// Kategori bulma fonksiyonları - Code bazlı sistem
export const getCategoryById = (id: string): CategoryItem | undefined => {
  return ALL_CATEGORIES.find(category => category.id === id);
};

export const getCategoryByCode = (code: string): CategoryItem | undefined => {
  return ALL_CATEGORIES.find(category => category.code === code);
};

export const getCategoriesByType = (type: 'income' | 'expense'): CategoryItem[] => {
  return ALL_CATEGORIES.filter(category => category.type === type);
};

export const getCategoryByName = (name: string): CategoryItem | undefined => {
  return ALL_CATEGORIES.find(category =>
    category.name.toLowerCase() === name.toLowerCase()
  );
};

// Code'dan Türkçe isim alma
export const getCategoryNameByCode = (code: string): string => {
  const category = getCategoryByCode(code);
  return category?.name || code;
};

// Türkçe isimden code alma
export const getCategoryCodeByName = (name: string): string => {
  const category = getCategoryByName(name);
  return category?.code || name;
};

// Varsayılan kategori
export const DEFAULT_CATEGORY: CategoryItem = {
  id: 'other',
  code: 'other',
  name: 'Diğer',
  icon: 'ellipsis-horizontal',
  color: '#9E9E9E',
  type: 'expense'
};

// Ödeme yöntemleri
export const PAYMENT_METHODS = [
  { id: 'cash', name: 'Nakit', icon: 'cash' },
  { id: 'card', name: 'Kart', icon: 'card' },
  { id: 'bank_transfer', name: 'Havale/EFT', icon: 'swap-horizontal' },
  { id: 'digital_wallet', name: 'Dijital Cüzdan', icon: 'phone-portrait' },
  { id: 'check', name: 'Çek', icon: 'document-text' },
  { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal' }
];

// Tekrarlama türleri
export const RECURRING_TYPES = [
  { id: 'daily', name: 'Günlük', icon: 'calendar' },
  { id: 'weekly', name: 'Haftalık', icon: 'calendar' },
  { id: 'monthly', name: 'Aylık', icon: 'calendar' },
  { id: 'quarterly', name: '3 Aylık', icon: 'calendar' },
  { id: 'yearly', name: 'Yıllık', icon: 'calendar' }
];

// Bütçe dönemleri
export const BUDGET_PERIODS = [
  { id: 'weekly', name: 'Haftalık', icon: 'calendar' },
  { id: 'monthly', name: 'Aylık', icon: 'calendar' },
  { id: 'quarterly', name: '3 Aylık', icon: 'calendar' },
  { id: 'yearly', name: 'Yıllık', icon: 'calendar' }
];

// Hedef kategorileri
export const GOAL_CATEGORIES = [
  { id: 'emergency', name: 'Acil Durum Fonu', icon: 'shield', color: '#F44336' },
  { id: 'vacation', name: 'Tatil', icon: 'airplane', color: '#00BCD4' },
  { id: 'house', name: 'Ev', icon: 'home', color: '#795548' },
  { id: 'car', name: 'Araç', icon: 'car', color: '#2196F3' },
  { id: 'education', name: 'Eğitim', icon: 'school', color: '#3F51B5' },
  { id: 'retirement', name: 'Emeklilik', icon: 'time', color: '#607D8B' },
  { id: 'investment', name: 'Yatırım', icon: 'trending-up', color: '#FF9800' },
  { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal', color: '#9E9E9E' }
];

// Öncelik seviyeleri
export const PRIORITY_LEVELS = [
  { id: 'low', name: 'Düşük', color: '#4CAF50' },
  { id: 'medium', name: 'Orta', color: '#FF9800' },
  { id: 'high', name: 'Yüksek', color: '#F44336' }
];

// Borç türleri
export const DEBT_TYPES = [
  { id: 'credit_card', name: 'Kredi Kartı', icon: 'card' },
  { id: 'personal_loan', name: 'Kişisel Kredi', icon: 'cash' },
  { id: 'mortgage', name: 'Mortgage', icon: 'home' },
  { id: 'student_loan', name: 'Öğrenci Kredisi', icon: 'school' },
  { id: 'car_loan', name: 'Araç Kredisi', icon: 'car' },
  { id: 'business_loan', name: 'İş Kredisi', icon: 'briefcase' },
  { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal' }
];

// Yatırım türleri
export const INVESTMENT_TYPES = [
  { id: 'stock', name: 'Hisse Senedi', icon: 'trending-up' },
  { id: 'bond', name: 'Tahvil', icon: 'document-text' },
  { id: 'mutual_fund', name: 'Yatırım Fonu', icon: 'pie-chart' },
  { id: 'crypto', name: 'Kripto Para', icon: 'logo-bitcoin' },
  { id: 'real_estate', name: 'Gayrimenkul', icon: 'home' },
  { id: 'gold', name: 'Altın', icon: 'diamond' },
  { id: 'deposit', name: 'Mevduat', icon: 'card' },
  { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal' }
];
