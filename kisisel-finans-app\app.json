{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "kisisel-finans-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Bu uygulama makbuz fotoğrafları çekmek için kameraya erişim gerektirir.", "NSPhotoLibraryUsageDescription": "Bu uygulama makbuz fotoğraflarını galeriden seçmek için fotoğraf kütüphanesine erişim gerektirir.", "NSMicrophoneUsageDescription": "Bu uygulama ses kaydetmek için mikrofona eri<PERSON>im gere<PERSON>.", "NSFaceIDUsageDescription": "Bu uygulama güvenli giriş için Face ID kullanır.", "UIBackgroundModes": ["background-fetch", "background-processing"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.USE_FINGERPRINT", "android.permission.USE_BIOMETRIC", "android.permission.RECORD_AUDIO", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK", "android.permission.VIBRATE", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.USE_FINGERPRINT", "android.permission.USE_BIOMETRIC", "android.permission.RECORD_AUDIO", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK", "android.permission.VIBRATE"], "package": "com.zekotek.kisiselfinansapp"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-image-picker", {"photosPermission": "Bu uygulama makbuz fotoğraflarını galeriden seçmek için fotoğraf kütüphanesine erişim gerektirir.", "cameraPermission": "Bu uygulama makbuz fotoğrafları çekmek için kameraya erişim gerektirir."}], ["expo-local-authentication", {"faceIDPermission": "Bu uygulama güvenli giriş için Face ID kullanır."}], "expo-secure-store", "expo-sqlite", "expo-font", "expo-notifications", "expo-background-fetch", "expo-task-manager"]}}