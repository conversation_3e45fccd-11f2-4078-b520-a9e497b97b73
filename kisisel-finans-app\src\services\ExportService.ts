// Export Service - PDF ve Excel rapor oluşturma servisi
import XLSX from 'xlsx';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Transaction, IncomeTransaction, ExpenseTransaction } from '../types/transaction';
import { INCOME_CATEGORY_LABELS, EXPENSE_CATEGORY_LABELS, PAYMENT_METHOD_LABELS } from '../types/transaction';

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  dateRange: {
    start: string;
    end: string;
  };
  includeCategories?: string[];
  excludeCategories?: string[];
  groupBy?: 'date' | 'category' | 'type' | 'none';
  includeSummary?: boolean;
  includeCharts?: boolean;
}

export interface ExportResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  error?: string;
}

class ExportService {
  /**
   * Ana export fonksiyonu
   */
  async exportTransactions(
    transactions: Transaction[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      console.log('📊 Starting export process:', options);

      // Filtreleme uygula
      const filteredTransactions = this.filterTransactions(transactions, options);
      console.log(`📋 Filtered ${filteredTransactions.length} transactions for export`);

      // Format'a göre export et
      switch (options.format) {
        case 'excel':
          return await this.exportToExcel(filteredTransactions, options);
        case 'csv':
          return await this.exportToCSV(filteredTransactions, options);
        case 'pdf':
          return await this.exportToPDF(filteredTransactions, options);
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }
    } catch (error: any) {
      console.error('❌ Export failed:', error);
      return {
        success: false,
        error: error.message || 'Export işlemi başarısız oldu',
      };
    }
  }

  /**
   * İşlemleri filtrele
   */
  private filterTransactions(transactions: Transaction[], options: ExportOptions): Transaction[] {
    console.log('🔍 Filtering transactions...');
    console.log('📅 Date range:', options.dateRange);
    console.log('📊 Total transactions to filter:', transactions.length);

    const filteredTransactions = transactions.filter(transaction => {
      // Tarih aralığı kontrolü
      const transactionDate = new Date(transaction.date);
      const startDate = new Date(options.dateRange.start);
      const endDate = new Date(options.dateRange.end);

      // Saatleri normalize et - başlangıç günün başı, bitiş günün sonu
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);

      console.log(`📅 Checking transaction: ${transaction.description} (${transaction.date})`);
      console.log(`📅 Transaction date: ${transactionDate.toISOString()}`);
      console.log(`📅 Start date (normalized): ${startDate.toISOString()}`);
      console.log(`📅 End date (normalized): ${endDate.toISOString()}`);

      if (transactionDate < startDate || transactionDate > endDate) {
        console.log(`❌ Transaction filtered out: date out of range`);
        return false;
      }

      // Kategori filtreleri
      if (options.includeCategories && options.includeCategories.length > 0) {
        if (!options.includeCategories.includes(transaction.category)) {
          return false;
        }
      }

      if (options.excludeCategories && options.excludeCategories.length > 0) {
        if (options.excludeCategories.includes(transaction.category)) {
          console.log(`❌ Transaction filtered out: excluded category`);
          return false;
        }
      }

      console.log(`✅ Transaction passed filters`);
      return true;
    });

    console.log(`📊 Filtered transactions: ${filteredTransactions.length}/${transactions.length}`);
    return filteredTransactions;
  }

  /**
   * Excel formatında export
   */
  private async exportToExcel(
    transactions: Transaction[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // Workbook oluştur
      const workbook = XLSX.utils.book_new();

      // Ana işlemler sayfası
      const transactionData = this.prepareTransactionData(transactions);
      const transactionSheet = XLSX.utils.json_to_sheet(transactionData);
      XLSX.utils.book_append_sheet(workbook, transactionSheet, 'İşlemler');

      // Özet sayfası (eğer isteniyorsa)
      if (options.includeSummary) {
        const summaryData = this.prepareSummaryData(transactions);
        const summarySheet = XLSX.utils.json_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Özet');
      }

      // Kategori analizi sayfası
      const categoryData = this.prepareCategoryAnalysis(transactions);
      const categorySheet = XLSX.utils.json_to_sheet(categoryData);
      XLSX.utils.book_append_sheet(workbook, categorySheet, 'Kategori Analizi');

      // Dosya adı oluştur
      const fileName = this.generateFileName('xlsx', options);
      const filePath = `${FileSystem.documentDirectory}${fileName}`;

      // Excel dosyasını base64 formatında oluştur
      const excelBuffer = XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });

      // Dosyayı cihaza yaz
      await FileSystem.writeAsStringAsync(filePath, excelBuffer, {
        encoding: FileSystem.EncodingType.Base64,
      });

      console.log('✅ Excel export completed:', filePath);
      console.log('📁 File saved to device storage');

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        console.log('📤 Opening share dialog...');
        await Sharing.shareAsync(filePath, {
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          dialogTitle: 'Finansal Raporu Paylaş',
        });
        console.log('✅ Share dialog opened successfully');
      } else {
        console.log('❌ Sharing not available on this device');
      }

      return {
        success: true,
        filePath,
        fileName,
      };
    } catch (error: any) {
      console.error('❌ Excel export failed:', error);
      throw error;
    }
  }

  /**
   * CSV formatında export
   */
  private async exportToCSV(
    transactions: Transaction[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // CSV verisi hazırla
      const transactionData = this.prepareTransactionData(transactions);

      // CSV formatına çevir
      const worksheet = XLSX.utils.json_to_sheet(transactionData);
      const csvContent = XLSX.utils.sheet_to_csv(worksheet);

      // Dosya adı oluştur
      const fileName = this.generateFileName('csv', options);
      const filePath = `${FileSystem.documentDirectory}${fileName}`;

      // CSV dosyasını yaz
      await FileSystem.writeAsStringAsync(filePath, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      console.log('✅ CSV export completed:', filePath);
      console.log('📁 File saved to device storage');

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        console.log('📤 Opening share dialog...');
        await Sharing.shareAsync(filePath, {
          mimeType: 'text/csv',
          dialogTitle: 'CSV Raporu Paylaş',
        });
        console.log('✅ Share dialog opened successfully');
      } else {
        console.log('❌ Sharing not available on this device');
      }

      return {
        success: true,
        filePath,
        fileName,
      };
    } catch (error: any) {
      console.error('❌ CSV export failed:', error);
      throw error;
    }
  }

  /**
   * PDF formatında export (HTML olarak)
   */
  private async exportToPDF(
    transactions: Transaction[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // PDF için HTML içeriği oluştur
      const htmlContent = this.generatePDFContent(transactions, options);

      // Dosya adı oluştur
      const fileName = this.generateFileName('html', options);
      const filePath = `${FileSystem.documentDirectory}${fileName}`;

      // HTML dosyasını yaz
      await FileSystem.writeAsStringAsync(filePath, htmlContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      console.log('✅ PDF (HTML) export completed:', filePath);
      console.log('📁 File saved to device storage');

      // Dosyayı paylaş
      if (await Sharing.isAvailableAsync()) {
        console.log('📤 Opening share dialog...');
        await Sharing.shareAsync(filePath, {
          mimeType: 'text/html',
          dialogTitle: 'HTML Raporu Paylaş',
        });
        console.log('✅ Share dialog opened successfully');
      } else {
        console.log('❌ Sharing not available on this device');
      }

      return {
        success: true,
        filePath,
        fileName,
      };
    } catch (error: any) {
      console.error('❌ PDF export failed:', error);
      throw error;
    }
  }

  /**
   * İşlem verilerini export formatına hazırla
   */
  private prepareTransactionData(transactions: Transaction[]): any[] {
    return transactions.map(transaction => {
      const isIncome = transaction.type === 'income';
      const incomeTransaction = transaction as IncomeTransaction;
      const expenseTransaction = transaction as ExpenseTransaction;

      return {
        'Tarih': new Date(transaction.date).toLocaleDateString('tr-TR'),
        'Tür': isIncome ? 'Gelir' : 'Gider',
        'Tutar': transaction.amount,
        'Para Birimi': transaction.currency,
        'Kategori': this.getCategoryLabel(transaction.category, transaction.type),
        'Alt Kategori': isIncome ? '-' : (expenseTransaction.subcategory || '-'),
        'Açıklama': transaction.description || '-',
        'Kaynak/Mağaza': isIncome ? (incomeTransaction.source || '-') : (expenseTransaction.merchant || '-'),
        'Ödeme Yöntemi': transaction.paymentMethod ? PAYMENT_METHOD_LABELS[transaction.paymentMethod] : '-',
        'İş Gideri': isIncome ? '-' : (expenseTransaction.isBusinessExpense ? 'Evet' : 'Hayır'),
        'Vergi İndirimi': isIncome ? '-' : (expenseTransaction.isDeductible ? 'Evet' : 'Hayır'),
        'Vergilendirilebilir': isIncome ? (incomeTransaction.taxable ? 'Evet' : 'Hayır') : '-',
        'Vergi Tutarı': isIncome ? (incomeTransaction.taxAmount || 0) : '-',
      };
    });
  }

  /**
   * Özet verilerini hazırla
   */
  private prepareSummaryData(transactions: Transaction[]): any[] {
    const totalIncome = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const netAmount = totalIncome - totalExpense;

    return [
      { 'Metrik': 'Toplam Gelir', 'Değer': totalIncome, 'Para Birimi': 'TRY' },
      { 'Metrik': 'Toplam Gider', 'Değer': totalExpense, 'Para Birimi': 'TRY' },
      { 'Metrik': 'Net Tutar', 'Değer': netAmount, 'Para Birimi': 'TRY' },
      { 'Metrik': 'İşlem Sayısı', 'Değer': transactions.length, 'Para Birimi': '-' },
    ];
  }

  /**
   * Kategori analizi verilerini hazırla
   */
  private prepareCategoryAnalysis(transactions: Transaction[]): any[] {
    const categoryTotals: Record<string, { income: number; expense: number; count: number }> = {};

    transactions.forEach(transaction => {
      const categoryLabel = this.getCategoryLabel(transaction.category, transaction.type);

      if (!categoryTotals[categoryLabel]) {
        categoryTotals[categoryLabel] = { income: 0, expense: 0, count: 0 };
      }

      if (transaction.type === 'income') {
        categoryTotals[categoryLabel].income += transaction.amount;
      } else {
        categoryTotals[categoryLabel].expense += transaction.amount;
      }
      categoryTotals[categoryLabel].count += 1;
    });

    return Object.entries(categoryTotals).map(([category, data]) => ({
      'Kategori': category,
      'Toplam Gelir': data.income,
      'Toplam Gider': data.expense,
      'Net Tutar': data.income - data.expense,
      'İşlem Sayısı': data.count,
    }));
  }

  /**
   * Kategori etiketini al
   */
  private getCategoryLabel(category: string, type: 'income' | 'expense'): string {
    if (type === 'income') {
      return INCOME_CATEGORY_LABELS[category as keyof typeof INCOME_CATEGORY_LABELS] || category;
    } else {
      return EXPENSE_CATEGORY_LABELS[category as keyof typeof EXPENSE_CATEGORY_LABELS] || category;
    }
  }

  /**
   * PDF için HTML içeriği oluştur
   */
  private generatePDFContent(transactions: Transaction[], options: ExportOptions): string {
    const transactionData = this.prepareTransactionData(transactions);
    const summaryData = this.prepareSummaryData(transactions);

    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Finansal Rapor</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1, h2 { color: #333; }
          table { border-collapse: collapse; width: 100%; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
          th { background-color: #f2f2f2; font-weight: bold; }
          .summary { background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }
          .income { color: #28a745; }
          .expense { color: #dc3545; }
          .header { text-align: center; margin-bottom: 30px; }
          .date-range { color: #666; font-style: italic; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Finansal Rapor</h1>
          <p>Rapor Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
          <p class="date-range">Dönem: ${new Date(options.dateRange.start).toLocaleDateString('tr-TR')} - ${new Date(options.dateRange.end).toLocaleDateString('tr-TR')}</p>
        </div>

        <div class="summary">
          <h2>Özet Bilgiler</h2>
          <table>
            <tr><th>Metrik</th><th>Değer</th><th>Para Birimi</th></tr>
            ${summaryData.map(item => `
              <tr>
                <td>${item.Metrik}</td>
                <td class="${item.Metrik.includes('Gelir') ? 'income' : item.Metrik.includes('Gider') ? 'expense' : ''}">${item.Değer}</td>
                <td>${item['Para Birimi']}</td>
              </tr>
            `).join('')}
          </table>
        </div>

        <h2>İşlem Detayları (${transactionData.length} işlem)</h2>
        <table>
          <tr>
            <th>Tarih</th>
            <th>Tür</th>
            <th>Tutar</th>
            <th>Para Birimi</th>
            <th>Kategori</th>
            <th>Açıklama</th>
            <th>Ödeme Yöntemi</th>
          </tr>
          ${transactionData.map(item => `
            <tr>
              <td>${item.Tarih}</td>
              <td class="${item.Tür === 'Gelir' ? 'income' : 'expense'}">${item.Tür}</td>
              <td class="${item.Tür === 'Gelir' ? 'income' : 'expense'}">${item.Tutar}</td>
              <td>${item['Para Birimi']}</td>
              <td>${item.Kategori}</td>
              <td>${item.Açıklama}</td>
              <td>${item['Ödeme Yöntemi']}</td>
            </tr>
          `).join('')}
        </table>

        <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
          <p>Bu rapor Kişisel Finans Uygulaması tarafından oluşturulmuştur.</p>
          <p>Oluşturulma Zamanı: ${new Date().toLocaleString('tr-TR')}</p>
        </div>
      </body>
      </html>
    `;

    return html;
  }

  /**
   * Dosya adı oluştur
   */
  private generateFileName(format: string, options: ExportOptions): string {
    const startDate = new Date(options.dateRange.start).toISOString().split('T')[0];
    const endDate = new Date(options.dateRange.end).toISOString().split('T')[0];
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];

    return `finansal-rapor_${startDate}_${endDate}_${timestamp}.${format}`;
  }

  /**
   * Dosyayı paylaş - ShareService kullanarak
   */
  async shareFile(filePath: string, fileName: string): Promise<boolean> {
    try {
      console.log('📁 File created at:', filePath);
      console.log('📄 File name:', fileName);

      // ShareService import edilmeli - bu fonksiyon artık ExportModal'da kullanılıyor
      // Bu fonksiyon backward compatibility için korunuyor
      return true;
    } catch (error: any) {
      console.error('❌ Share failed:', error);
      return false;
    }
  }

  /**
   * Dosya tipini belirle
   */
  private getFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';
      case 'html':
        return 'text/html';
      case 'pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }
}

export default new ExportService();
