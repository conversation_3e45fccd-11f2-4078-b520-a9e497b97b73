// Enhanced Add Transaction Screen - Gelişmiş işlem ekleme ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import { Ionicons } from '@expo/vector-icons';
import DatePicker from '../../components/DatePicker';
import ReceiptCamera from '../../components/ReceiptCamera';
import OCRModal from '../../components/OCRModal';
import OCRService, { ExtractedReceiptData } from '../../services/OCRService';
import MLCategorizationService, { CategoryPrediction } from '../../services/MLCategorizationService';
import OfflineService, { OfflineTransaction } from '../../services/OfflineService';

// Database imports
import TransactionRepository from '../../database/repositories/TransactionRepository';
import TransactionService from '../../services/TransactionService';
import RecurringTransactionService from '../../services/RecurringTransactionService';
import {
  Transaction,
  IncomeTransaction,
  ExpenseTransaction,
  IncomeCategory,
  ExpenseCategory,
  PaymentMethod,
  CurrencyCode,
  TransactionStatus,
  Receipt,
} from '../../types/transaction';
import {
  INCOME_CATEGORY_LABELS,
  EXPENSE_CATEGORY_LABELS,
  PAYMENT_METHOD_LABELS,
  INCOME_CATEGORY_ICONS,
  EXPENSE_CATEGORY_ICONS,
} from '../../types/transaction';
import {
  INCOME_CATEGORIES,
  EXPENSE_CATEGORIES,
  getCategoryNameByCode,
  getCategoryCodeByName,
} from '../../constants/categories';

type AddTransactionNavigationProp = StackNavigationProp<MainStackParamList, 'AddTransaction'>;
type AddTransactionRouteProp = RouteProp<MainStackParamList, 'AddTransaction'>;

const AddTransactionScreen: React.FC = () => {
  const navigation = useNavigation<AddTransactionNavigationProp>();
  const route = useRoute<AddTransactionRouteProp>();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const { currencies, baseCurrency, convertAmount } = useCurrency();

  const initialType = route.params?.type || 'expense';
  const editTransaction = route.params?.editTransaction;
  const isEditing = !!editTransaction;

  const [formData, setFormData] = useState({
    type: editTransaction?.type || initialType as 'income' | 'expense',
    amount: editTransaction?.amount?.toString() || '',
    category: editTransaction?.category || '' as IncomeCategory | ExpenseCategory | '',
    subcategory: (editTransaction?.type === 'expense' && 'subcategory' in editTransaction) ? editTransaction.subcategory || '' : '',
    description: editTransaction?.description || '',
    merchant: (editTransaction?.type === 'expense' && 'merchant' in editTransaction) ? editTransaction.merchant || '' : '',
    source: (editTransaction?.type === 'income' && 'source' in editTransaction) ? editTransaction.source || '' : '',
    date: editTransaction?.date ? new Date(editTransaction.date) : new Date(),
    paymentMethod: editTransaction?.paymentMethod || 'cash' as PaymentMethod,
    currency: editTransaction?.currency || baseCurrency?.code || 'TRY' as CurrencyCode,
    isRecurring: editTransaction?.recurrence?.frequency !== 'none' && editTransaction?.recurrence?.frequency !== undefined,
    recurringFrequency: (editTransaction?.recurrence?.frequency && editTransaction?.recurrence?.frequency !== 'none')
      ? editTransaction.recurrence.frequency
      : 'monthly' as 'daily' | 'weekly' | 'bi_weekly' | 'monthly' | 'quarterly' | 'semi_annually' | 'annually',
    receiptImage: '',
    tags: [] as string[],
    isBusinessExpense: (editTransaction?.type === 'expense' && 'isBusinessExpense' in editTransaction) ? editTransaction.isBusinessExpense || false : false,
    isDeductible: (editTransaction?.type === 'expense' && 'isDeductible' in editTransaction) ? editTransaction.isDeductible || false : false,
    taxAmount: (editTransaction?.type === 'income' && 'taxAmount' in editTransaction) ? editTransaction.taxAmount?.toString() || '' : '',
    budgetId: editTransaction?.budgetId || '', // Bütçe seçimi için
  });

  const [isLoading, setIsLoading] = useState(false);
  const [currencyModalVisible, setCurrencyModalVisible] = useState(false);
  const [availableCategories, setAvailableCategories] = useState<Array<{
    id: string;
    name: string;
    icon: string;
    color: string;
  }>>([]);

  // OCR states
  const [ocrModalVisible, setOcrModalVisible] = useState(false);
  const [ocrImageUri, setOcrImageUri] = useState<string>('');

  // ML Categorization states
  const [categoryPredictions, setCategoryPredictions] = useState<CategoryPrediction[]>([]);
  const [showCategorySuggestions, setShowCategorySuggestions] = useState(false);
  const [mlCategorizationLoading, setMlCategorizationLoading] = useState(false);

  // Budget selection states
  const [availableBudgets, setAvailableBudgets] = useState<Array<{
    id: string;
    name: string;
    startDate: string;
    endDate: string;
  }>>([]);
  const [showBudgetModal, setShowBudgetModal] = useState(false);

  // Load categories on mount
  useEffect(() => {
    loadCategories();
  }, [formData.type]);

  // Load budgets on mount (only for income)
  useEffect(() => {
    if (formData.type === 'income' && user) {
      loadBudgets();
    }
  }, [formData.type, user]);

  // Load edit transaction data
  useEffect(() => {
    if (editTransaction) {
      console.log('📝 Loading edit transaction data:', editTransaction);

      const incomeTransaction = editTransaction as IncomeTransaction;
      const expenseTransaction = editTransaction as ExpenseTransaction;

      setFormData({
        type: editTransaction.type,
        amount: editTransaction.amount.toString(),
        currency: editTransaction.currency,
        category: editTransaction.category,
        subcategory: editTransaction.type === 'expense' ? expenseTransaction.subcategory || '' : '',
        description: editTransaction.description,
        merchant: editTransaction.type === 'expense' ? expenseTransaction.merchant || '' : '',
        source: editTransaction.type === 'income' ? incomeTransaction.source || '' : '',
        paymentMethod: editTransaction.paymentMethod || 'cash',
        date: new Date(editTransaction.date),
        isBusinessExpense: editTransaction.type === 'expense' ? expenseTransaction.isBusinessExpense || false : false,
        isDeductible: editTransaction.type === 'expense' ? expenseTransaction.isDeductible || false : false,
        taxAmount: editTransaction.type === 'income' ? (incomeTransaction.taxAmount || 0).toString() : '0',
        receiptImage: editTransaction.receipt?.imageUri || '',
        tags: editTransaction.tags?.map(tag => typeof tag === 'string' ? tag : tag.name) || [],
        isRecurring: editTransaction.recurrence?.frequency !== 'none' && editTransaction.recurrence?.frequency !== undefined,
        recurringFrequency: editTransaction.recurrence?.frequency === 'daily' ? 'daily' :
                           editTransaction.recurrence?.frequency === 'weekly' ? 'weekly' :
                           editTransaction.recurrence?.frequency === 'monthly' ? 'monthly' :
                           editTransaction.recurrence?.frequency === 'annually' ? 'annually' : 'monthly',
      });
    }
  }, [editTransaction]);

  const loadCategories = () => {
    if (formData.type === 'income') {
      // RFC-003 Code Bazlı Sistem - constants/categories.ts'den çek
      const incomeCategories = INCOME_CATEGORIES.map(category => ({
        id: category.code, // Code kullan
        name: category.name,
        icon: category.icon,
        color: category.color,
      }));
      setAvailableCategories(incomeCategories);
    } else {
      // RFC-003 Code Bazlı Sistem - constants/categories.ts'den çek
      const expenseCategories = EXPENSE_CATEGORIES.map(category => ({
        id: category.code, // Code kullan
        name: category.name,
        icon: category.icon,
        color: category.color,
      }));
      setAvailableCategories(expenseCategories);
    }
  };

  const loadBudgets = async () => {
    if (!user) return;

    try {
      console.log('💰 Loading budgets for income transaction...');
      const { default: BudgetService } = await import('../../services/BudgetService');
      const budgets = await BudgetService.getUserBudgets(user.id);

      // Sadece aktif bütçeleri al
      const activeBudgets = budgets.filter(budget => budget.isActive);

      const budgetOptions = activeBudgets.map(budget => ({
        id: budget.id,
        name: budget.name,
        startDate: budget.startDate,
        endDate: budget.endDate,
      }));

      setAvailableBudgets(budgetOptions);

      // Eğer bütçe seçilmemişse, en son oluşturulan bütçeyi varsayılan yap
      if (!formData.budgetId && budgetOptions.length > 0) {
        const latestBudget = budgetOptions[0]; // BudgetService zaten created_at DESC ile sıralıyor
        setFormData(prev => ({ ...prev, budgetId: latestBudget.id }));
        console.log(`💰 Auto-selected latest budget: ${latestBudget.name}`);
      }

      console.log(`💰 Loaded ${budgetOptions.length} active budgets`);
    } catch (error) {
      console.error('❌ Error loading budgets:', error);
    }
  };

  // Helper functions
  const formatAmount = (text: string): string => {
    // Remove non-numeric characters except comma and dot
    const cleaned = text.replace(/[^0-9.,]/g, '');
    // Replace comma with dot for parsing
    return cleaned.replace(',', '.');
  };

  const getPaymentMethodIcon = (method: PaymentMethod): string => {
    const iconMap: Record<PaymentMethod, string> = {
      cash: 'cash-outline',
      debit_card: 'card-outline',
      credit_card: 'card-outline',
      bank_transfer: 'swap-horizontal-outline',
      eft: 'send-outline',
      wire_transfer: 'git-branch-outline',
      digital_wallet: 'phone-portrait-outline',
      mobile_payment: 'phone-portrait-outline',
      check: 'document-outline',
      cryptocurrency: 'logo-bitcoin',
      bitcoin: 'logo-bitcoin',
      ethereum: 'logo-bitcoin',
      other_crypto: 'logo-bitcoin',
      installment: 'calendar-outline',
      other: 'ellipsis-horizontal-outline',
    };
    return iconMap[method] || 'cash-outline';
  };

  const handleSave = async () => {
    // Validation
    if (!user) {
      Alert.alert('Hata', 'Kullanıcı girişi gerekli.');
      return;
    }

    if (!formData.amount.trim() || !formData.category || !formData.description.trim()) {
      Alert.alert('Hata', 'Lütfen tüm zorunlu alanları doldurun.');
      return;
    }

    const amount = parseFloat(formData.amount.replace(',', '.'));
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }

    // Tax amount validation for income
    let taxAmount = 0;
    if (formData.type === 'income' && formData.taxAmount.trim()) {
      taxAmount = parseFloat(formData.taxAmount.replace(',', '.'));
      if (isNaN(taxAmount) || taxAmount < 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir vergi tutarı girin.');
        return;
      }
    }

    setIsLoading(true);

    try {
      console.log(isEditing ? '💾 Updating transaction...' : '💾 Creating transaction...');

      // Create receipt object if image exists
      let receiptData: Receipt | undefined;
      if (formData.receiptImage) {
        receiptData = {
          id: `receipt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          imageUri: formData.receiptImage,
          uploadedAt: new Date().toISOString(),
        };
        console.log('📄 Receipt created:', receiptData);
      }

      // Create transaction object
      let transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>;

      if (formData.type === 'income') {
        transactionData = {
          userId: user.id,
          type: 'income',
          amount,
          currency: formData.currency,
          category: formData.category as IncomeCategory,
          source: formData.source || undefined,
          date: formData.date.toISOString(),
          description: formData.description,
          paymentMethod: formData.paymentMethod,
          status: 'completed' as TransactionStatus,
          tags: [], // Will be implemented later
          receipt: receiptData,
          isDeleted: false,
          taxable: taxAmount > 0,
          taxAmount: taxAmount,
          budgetId: formData.budgetId || undefined, // Seçilen bütçe
        } as Omit<IncomeTransaction, 'id' | 'createdAt' | 'updatedAt'>;
      } else {
        transactionData = {
          userId: user.id,
          type: 'expense',
          amount,
          currency: formData.currency,
          category: formData.category as ExpenseCategory,
          subcategory: formData.subcategory || undefined,
          merchant: formData.merchant || undefined,
          date: formData.date.toISOString(),
          description: formData.description,
          paymentMethod: formData.paymentMethod,
          status: 'completed' as TransactionStatus,
          tags: [], // Will be implemented later
          receipt: receiptData,
          isDeleted: false,
          isBusinessExpense: formData.isBusinessExpense,
          isDeductible: formData.isDeductible,
        } as Omit<ExpenseTransaction, 'id' | 'createdAt' | 'updatedAt'>;
      }

      if (isEditing && editTransaction) {
        // Update existing transaction
        await TransactionRepository.update(editTransaction.id, transactionData);
        console.log('✅ Transaction updated:', editTransaction.id);

        Alert.alert(
          'Başarılı',
          `${formData.type === 'income' ? 'Gelir' : 'Gider'} başarıyla güncellendi.`,
          [{ text: 'Tamam', onPress: () => navigation.goBack() }]
        );
      } else {
        // Create new transaction
        let transactionId: string;
        let isOfflineTransaction = false;

        // Network durumunu kontrol et
        const networkStatus = OfflineService.getNetworkStatus();

        if (networkStatus.isConnected) {
          // Online: TransactionService kullan (hibrit sistem için)
          transactionId = await TransactionService.createTransaction(transactionData);
          console.log('✅ Online transaction created:', transactionId);
        } else {
          // Offline: Offline service'e kaydet
          const offlineTransaction = await OfflineService.saveOfflineTransaction(transactionData);
          transactionId = offlineTransaction.id;
          isOfflineTransaction = true;
          console.log('💾 Offline transaction saved:', transactionId);
        }

        // Eğer tekrarlayan işlem ise, template oluştur (sadece online'da)
        if (formData.isRecurring && !isOfflineTransaction) {
          try {
            const nextExecutionDate = calculateNextExecutionDate(formData.recurringFrequency, new Date());

            const recurringTemplate = {
              userId: user.id,
              name: `${formData.description || 'Otomatik İşlem'} (${formData.recurringFrequency})`,
              type: formData.type,
              amount,
              currency: formData.currency,
              category: formData.category,
              subcategory: formData.subcategory,
              description: formData.description,
              paymentMethod: formData.paymentMethod,
              recurrence: {
                frequency: formData.recurringFrequency,
                interval: 1,
              },
              isActive: true,
              nextExecutionDate: nextExecutionDate.toISOString().split('T')[0],
            };

            await RecurringTransactionService.createRecurringTemplate(recurringTemplate);
            console.log('✅ Recurring template created');
          } catch (recurringError) {
            console.error('❌ Error creating recurring template:', recurringError);
            // Recurring template hatası işlemi iptal etmez
          }
        }

        // Başarı mesajı
        const successMessage = isOfflineTransaction
          ? `${formData.type === 'income' ? 'Gelir' : 'Gider'} çevrimdışı olarak kaydedildi. İnternet bağlantısı geldiğinde senkronize edilecek.`
          : `${formData.type === 'income' ? 'Gelir' : 'Gider'} başarıyla eklendi.${formData.isRecurring ? ' Tekrarlayan işlem de oluşturuldu.' : ''}`;

        Alert.alert(
          isOfflineTransaction ? 'Çevrimdışı Kaydedildi 📱' : 'Başarılı ✅',
          successMessage,
          [{ text: 'Tamam', onPress: () => navigation.goBack() }]
        );
      }
    } catch (error: any) {
      console.error('❌ Error creating transaction:', error);
      Alert.alert('Hata', 'İşlem eklenirken bir hata oluştu: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to calculate next execution date
  const calculateNextExecutionDate = (frequency: string, currentDate: Date): Date => {
    const next = new Date(currentDate);

    switch (frequency) {
      case 'daily':
        next.setDate(currentDate.getDate() + 1);
        break;
      case 'weekly':
        next.setDate(currentDate.getDate() + 7);
        break;
      case 'bi_weekly':
        next.setDate(currentDate.getDate() + 14);
        break;
      case 'monthly':
        next.setMonth(currentDate.getMonth() + 1);
        break;
      case 'quarterly':
        next.setMonth(currentDate.getMonth() + 3);
        break;
      case 'semi_annually':
        next.setMonth(currentDate.getMonth() + 6);
        break;
      case 'annually':
        next.setFullYear(currentDate.getFullYear() + 1);
        break;
      default:
        next.setMonth(currentDate.getMonth() + 1);
    }

    return next;
  };

  // OCR Functions
  const handleReceiptCapture = (imageUri: string) => {
    console.log('📄 Receipt captured for OCR:', imageUri);
    setOcrImageUri(imageUri);
    setOcrModalVisible(true);
  };

  const handleOCRDataExtracted = async (data: ExtractedReceiptData) => {
    console.log('📄 OCR data extracted:', data);

    // Form verilerini OCR sonuçları ile doldur
    if (data.amount) {
      setFormData(prev => ({ ...prev, amount: data.amount!.toString() }));
    }

    if (data.merchant) {
      setFormData(prev => ({ ...prev, merchant: data.merchant! }));
    }

    if (data.date) {
      setFormData(prev => ({ ...prev, date: new Date(data.date!) }));
    }

    if (data.currency) {
      setFormData(prev => ({ ...prev, currency: data.currency as CurrencyCode }));
    }

    // Ödeme yöntemi
    if (data.paymentMethod) {
      setFormData(prev => ({ ...prev, paymentMethod: data.paymentMethod as PaymentMethod }));
    }

    // Fiş görüntüsünü kaydet
    if (ocrImageUri) {
      setFormData(prev => ({ ...prev, receiptImage: ocrImageUri }));
    }

    // Açıklama alanını doldur
    if (data.merchant && !formData.description) {
      setFormData(prev => ({
        ...prev,
        description: `${data.merchant} - Fiş No: ${data.receiptNumber || 'N/A'}`
      }));
    }

    // ML Auto-categorization ile akıllı kategori önerisi
    if (data.merchant || data.amount) {
      await performMLCategorization(
        data.merchant || formData.description,
        data.merchant,
        data.amount
      );
    }

    Alert.alert(
      'OCR Başarılı! 🎉',
      `Fiş verileriniz otomatik olarak dolduruldu. ML ile kategori önerileri de hazırlandı.`,
      [{ text: 'Tamam' }]
    );
  };

  // Kategori görüntü adını al - RFC-003 Code Bazlı Sistem
  const getCategoryDisplayName = (category: string): string => {
    // constants/categories.ts'den çek
    return getCategoryNameByCode(category);
  };

  // ML Auto-categorization fonksiyonu
  const performMLCategorization = async (
    description: string,
    merchant?: string,
    amount?: number
  ) => {
    if (!description.trim()) return;

    setMlCategorizationLoading(true);
    try {
      console.log('🤖 ML: Starting categorization for:', { description, merchant, amount });

      const result = await MLCategorizationService.predictCategory(
        description,
        merchant,
        amount,
        user?.id
      );

      console.log('🎯 ML: Categorization result:', result);

      setCategoryPredictions(result.predictions);

      // En iyi tahmini otomatik olarak seç (confidence > 0.7)
      if (result.confidence > 0.7 && !formData.category) {
        setFormData(prev => ({
          ...prev,
          category: result.selectedCategory as ExpenseCategory | IncomeCategory
        }));
        console.log('✅ ML: Auto-selected category:', result.selectedCategory);
      } else if (result.predictions.length > 0) {
        // Düşük confidence'ta önerileri göster
        setShowCategorySuggestions(true);
        console.log('💡 ML: Showing category suggestions');
      }

    } catch (error) {
      console.error('❌ ML: Categorization error:', error);
    } finally {
      setMlCategorizationLoading(false);
    }
  };



  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    typeContainer: {
      flexDirection: 'row',
      marginBottom: 24,
    },
    typeButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 12,
      marginHorizontal: 4,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    typeButtonActive: {
      borderColor: 'transparent',
    },
    typeText: {
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 60,
    },
    currencySelector: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: 12,
    },
    currencySymbol: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginRight: 4,
    },
    currencyCode: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginRight: 4,
    },
    amountInput: {
      flex: 1,
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    categoryScroll: {
      flexDirection: 'row',
    },
    categoryButton: {
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      borderWidth: 1,
      marginRight: 8,
      backgroundColor: theme.colors.surface,
      minWidth: 80,
    },
    categoryButtonActive: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    categoryText: {
      fontSize: 12,
      color: theme.colors.text,
      marginTop: 4,
      textAlign: 'center',
    },
    subcategoryScroll: {
      flexDirection: 'row',
    },
    subcategoryButton: {
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: 8,
    },
    subcategoryButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    subcategoryText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    subcategoryTextActive: {
      color: theme.colors.surface,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
      textAlignVertical: 'top',
    },
    paymentScroll: {
      flexDirection: 'row',
    },
    paymentButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: 8,
    },
    paymentButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    paymentText: {
      fontSize: 12,
      color: theme.colors.text,
      marginLeft: 4,
    },
    paymentTextActive: {
      color: theme.colors.surface,
    },
    dateButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    dateText: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 8,
    },
    recurringContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    recurringLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    recurringText: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 8,
    },
    switch: {
      width: 44,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.border,
      justifyContent: 'center',
      paddingHorizontal: 2,
    },
    switchActive: {
      backgroundColor: theme.colors.primary,
    },
    switchThumb: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: theme.colors.surface,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
    switchThumbActive: {
      transform: [{ translateX: 20 }],
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginTop: 20,
      marginBottom: 40,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
    amountContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 50,
    },
    currencyContainer: {
      marginLeft: 8,
    },
    currencyText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    checkboxContainer: {
      marginBottom: 12,
    },
    checkbox: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
    },
    checkboxText: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 12,
    },
    // Currency Modal Styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '70%',
      paddingBottom: 20,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
    },
    modalCloseButton: {
      padding: 4,
    },
    currencyOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    currencyOptionSelected: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    currencyFlag: {
      fontSize: 24,
      marginRight: 12,
    },
    currencyInfo: {
      flex: 1,
    },
    currencyName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 2,
    },
    currencySymbolText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    // Recurring Frequency Styles
    recurringFrequencyContainer: {
      marginTop: 16,
    },
    frequencyScroll: {
      flexDirection: 'row',
    },
    frequencyButton: {
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: 8,
    },
    frequencyButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    frequencyText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    frequencyTextActive: {
      color: theme.colors.surface,
    },
    // ML Suggestions Styles
    suggestionsContainer: {
      marginTop: 12,
      padding: 12,
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.primary + '20',
    },
    suggestionsTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    suggestionButton: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      paddingVertical: 6,
      paddingHorizontal: 12,
      marginRight: 8,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      alignItems: 'center',
    },
    suggestionText: {
      fontSize: 12,
      fontWeight: '500',
      color: theme.colors.primary,
    },
    suggestionConfidence: {
      fontSize: 10,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    dismissSuggestions: {
      alignSelf: 'flex-end',
      marginTop: 8,
      paddingVertical: 4,
      paddingHorizontal: 8,
    },
    dismissText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    // Budget Selection Styles
    budgetSelector: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 16,
      minHeight: 56,
    },
    budgetSelectorContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    budgetSelectorText: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 12,
      flex: 1,
    },
    clearBudgetButton: {
      marginTop: 8,
      alignSelf: 'flex-start',
    },
    clearBudgetText: {
      fontSize: 14,
      color: theme.colors.primary,
      textDecorationLine: 'underline',
    },
    budgetOption: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    budgetOptionSelected: {
      backgroundColor: theme.colors.primary + '10',
    },
    budgetOptionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    budgetInfo: {
      marginLeft: 12,
      flex: 1,
    },
    budgetName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
    },
    budgetPeriod: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        {/* Type Selector */}
        <View style={styles.typeContainer}>
          <TouchableOpacity
            style={[
              styles.typeButton,
              formData.type === 'income' && styles.typeButtonActive,
              { backgroundColor: formData.type === 'income' ? theme.colors.income : theme.colors.surface }
            ]}
            onPress={() => setFormData({ ...formData, type: 'income', category: '' })}
          >
            <Ionicons
              name="trending-up"
              size={20}
              color={formData.type === 'income' ? theme.colors.surface : theme.colors.income}
            />
            <Text style={[
              styles.typeText,
              { color: formData.type === 'income' ? theme.colors.surface : theme.colors.income }
            ]}>
              Gelir
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.typeButton,
              formData.type === 'expense' && styles.typeButtonActive,
              { backgroundColor: formData.type === 'expense' ? theme.colors.expense : theme.colors.surface }
            ]}
            onPress={() => setFormData({ ...formData, type: 'expense', category: '' })}
          >
            <Ionicons
              name="trending-down"
              size={20}
              color={formData.type === 'expense' ? theme.colors.surface : theme.colors.expense}
            />
            <Text style={[
              styles.typeText,
              { color: formData.type === 'expense' ? theme.colors.surface : theme.colors.expense }
            ]}>
              Gider
            </Text>
          </TouchableOpacity>
        </View>

        {/* Amount Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Tutar *</Text>
          <View style={styles.amountInputWrapper}>
            <TouchableOpacity
              style={styles.currencySelector}
              onPress={() => setCurrencyModalVisible(true)}
            >
              <Text style={styles.currencySymbol}>
                {currencies.find(c => c.code === formData.currency)?.symbol || '₺'}
              </Text>
              <Text style={styles.currencyCode}>{formData.currency}</Text>
              <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.amount}
              onChangeText={(text) => setFormData({ ...formData, amount: formatAmount(text) })}
              keyboardType="numeric"
              autoFocus
            />
          </View>
        </View>

        {/* Category Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Kategori *</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
            {availableCategories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  formData.category === category.id && styles.categoryButtonActive,
                  { borderColor: category.color }
                ]}
                onPress={() => setFormData({ ...formData, category: category.id as any, subcategory: '' })}
              >
                <Ionicons name={category.icon as any} size={20} color={category.color} />
                <Text style={[
                  styles.categoryText,
                  formData.category === category.id && { color: category.color }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Subcategory Selection - Will be implemented later */}
        {formData.type === 'expense' && formData.category && (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Alt Kategori</Text>
            <TextInput
              style={styles.textInput}
              value={formData.subcategory}
              onChangeText={(text) => setFormData({ ...formData, subcategory: text })}
              placeholder="Alt kategori seçin (opsiyonel)"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        )}

        {/* Merchant/Source */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>
            {formData.type === 'income' ? 'Gelir Kaynağı' : 'İşyeri/Mağaza'}
          </Text>
          <TextInput
            style={styles.textInput}
            value={formData.type === 'income' ? formData.source : formData.merchant}
            onChangeText={(text) =>
              formData.type === 'income'
                ? setFormData({ ...formData, source: text })
                : setFormData({ ...formData, merchant: text })
            }
            placeholder={formData.type === 'income' ? 'Şirket adı, müşteri adı vb.' : 'Mağaza, restoran adı vb.'}
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        {/* Description Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>
            Açıklama *
            {mlCategorizationLoading && (
              <ActivityIndicator size="small" color={theme.colors.primary} style={{ marginLeft: 8 }} />
            )}
          </Text>
          <TextInput
            style={styles.textInput}
            placeholder="İşlem açıklaması"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.description}
            onChangeText={(text) => {
              setFormData({ ...formData, description: text });
              // ML Auto-categorization trigger (debounced)
              if (text.length > 3) {
                setTimeout(() => {
                  performMLCategorization(text, formData.merchant, parseFloat(formData.amount) || undefined);
                }, 1000);
              }
            }}
            multiline
            numberOfLines={3}
          />

          {/* ML Category Suggestions */}
          {showCategorySuggestions && categoryPredictions.length > 0 && (
            <View style={styles.suggestionsContainer}>
              <Text style={styles.suggestionsTitle}>🤖 Kategori Önerileri:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {categoryPredictions.slice(0, 3).map((prediction, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.suggestionButton}
                    onPress={() => {
                      console.log('🎯 ML: User selected category:', prediction.category);
                      console.log('🎯 ML: Current formData before update:', formData.category);

                      // Kategoriyi güncelle
                      setFormData(prev => {
                        const updated = { ...prev, category: prediction.category as ExpenseCategory | IncomeCategory };
                        console.log('🎯 ML: Updated formData:', updated.category);
                        return updated;
                      });

                      // Önerileri kapat
                      setShowCategorySuggestions(false);
                      setCategoryPredictions([]);

                      console.log('✅ ML: Category selection completed');
                    }}
                  >
                    <Text style={styles.suggestionText}>
                      {getCategoryDisplayName(prediction.category)}
                    </Text>
                    <Text style={styles.suggestionConfidence}>
                      {Math.round(prediction.confidence * 100)}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity
                style={styles.dismissSuggestions}
                onPress={() => setShowCategorySuggestions(false)}
              >
                <Text style={styles.dismissText}>Kapat</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Tax Amount for Income */}
        {formData.type === 'income' && (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Vergi Tutarı</Text>
            <View style={styles.amountContainer}>
              <TextInput
                style={styles.amountInput}
                value={formData.taxAmount}
                onChangeText={(text) => setFormData({ ...formData, taxAmount: formatAmount(text) })}
                placeholder="0,00"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
              <View style={styles.currencyContainer}>
                <Text style={styles.currencyText}>
                  {currencies.find(c => c.code === formData.currency)?.symbol || '₺'}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Budget Selection for Income */}
        {formData.type === 'income' && (
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Bütçe Seçimi</Text>
            <TouchableOpacity
              style={styles.budgetSelector}
              onPress={() => setShowBudgetModal(true)}
            >
              <View style={styles.budgetSelectorContent}>
                <Ionicons name="wallet-outline" size={20} color={theme.colors.textSecondary} />
                <Text style={styles.budgetSelectorText}>
                  {formData.budgetId
                    ? availableBudgets.find(b => b.id === formData.budgetId)?.name || 'Bütçe seçin'
                    : 'Bütçe seçin (opsiyonel)'
                  }
                </Text>
              </View>
              <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>

            {formData.budgetId && (
              <TouchableOpacity
                style={styles.clearBudgetButton}
                onPress={() => setFormData({ ...formData, budgetId: '' })}
              >
                <Text style={styles.clearBudgetText}>Bütçe seçimini kaldır</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Business Expense Options for Expense */}
        {formData.type === 'expense' && (
          <>
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setFormData({ ...formData, isBusinessExpense: !formData.isBusinessExpense })}
              >
                <Ionicons
                  name={formData.isBusinessExpense ? 'checkbox' : 'square-outline'}
                  size={24}
                  color={formData.isBusinessExpense ? theme.colors.primary : theme.colors.textSecondary}
                />
                <Text style={styles.checkboxText}>İş gideri</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setFormData({ ...formData, isDeductible: !formData.isDeductible })}
              >
                <Ionicons
                  name={formData.isDeductible ? 'checkbox' : 'square-outline'}
                  size={24}
                  color={formData.isDeductible ? theme.colors.primary : theme.colors.textSecondary}
                />
                <Text style={styles.checkboxText}>Vergiden düşülebilir</Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {/* Payment Method */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Ödeme Yöntemi</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.paymentScroll}>
            {Object.entries(PAYMENT_METHOD_LABELS).map(([key, label]) => (
              <TouchableOpacity
                key={key}
                style={[
                  styles.paymentButton,
                  formData.paymentMethod === key && styles.paymentButtonActive
                ]}
                onPress={() => setFormData({ ...formData, paymentMethod: key as PaymentMethod })}
              >
                <Ionicons
                  name={getPaymentMethodIcon(key as PaymentMethod)}
                  size={16}
                  color={formData.paymentMethod === key ? theme.colors.primary : theme.colors.textSecondary}
                />
                <Text style={[
                  styles.paymentText,
                  formData.paymentMethod === key && styles.paymentTextActive
                ]}>
                  {label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Date Input */}
        <DatePicker
          label="Tarih"
          value={formData.date}
          onDateChange={(date) => setFormData({ ...formData, date })}
          maximumDate={new Date()}
        />

        {/* Receipt Camera */}
        <ReceiptCamera
          selectedImage={formData.receiptImage}
          onImageSelected={(uri) => setFormData({ ...formData, receiptImage: uri })}
          onOCRRequested={handleReceiptCapture}
        />

        {/* Recurring Option */}
        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.recurringContainer}
            onPress={() => setFormData({ ...formData, isRecurring: !formData.isRecurring })}
          >
            <View style={styles.recurringLeft}>
              <Ionicons name="repeat-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.recurringText}>Tekrarlanan İşlem</Text>
            </View>
            <View style={[styles.switch, formData.isRecurring && styles.switchActive]}>
              <View style={[styles.switchThumb, formData.isRecurring && styles.switchThumbActive]} />
            </View>
          </TouchableOpacity>

          {/* Recurring Frequency Selection */}
          {formData.isRecurring && (
            <View style={styles.recurringFrequencyContainer}>
              <Text style={styles.inputLabel}>Tekrarlama Sıklığı</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.frequencyScroll}>
                {[
                  { key: 'daily', label: 'Günlük' },
                  { key: 'weekly', label: 'Haftalık' },
                  { key: 'bi_weekly', label: 'İki Haftalık' },
                  { key: 'monthly', label: 'Aylık' },
                  { key: 'quarterly', label: 'Üç Aylık' },
                  { key: 'semi_annually', label: 'Altı Aylık' },
                  { key: 'annually', label: 'Yıllık' },
                ].map((freq) => (
                  <TouchableOpacity
                    key={freq.key}
                    style={[
                      styles.frequencyButton,
                      formData.recurringFrequency === freq.key && styles.frequencyButtonActive
                    ]}
                    onPress={() => setFormData({ ...formData, recurringFrequency: freq.key as any })}
                  >
                    <Text style={[
                      styles.frequencyText,
                      formData.recurringFrequency === freq.key && styles.frequencyTextActive
                    ]}>
                      {freq.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading
              ? (isEditing ? 'Güncelleniyor...' : 'Kaydediliyor...')
              : (isEditing ? 'Güncelle' : 'Kaydet')
            }
          </Text>
        </TouchableOpacity>

        {/* Currency Selection Modal */}
        <Modal
          visible={currencyModalVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setCurrencyModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Para Birimi Seçin</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setCurrencyModalVisible(false)}
                >
                  <Ionicons name="close" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>

              <FlatList
                data={currencies}
                keyExtractor={(item) => item.code}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.currencyOption,
                      formData.currency === item.code && styles.currencyOptionSelected
                    ]}
                    onPress={() => {
                      setFormData({ ...formData, currency: item.code });
                      setCurrencyModalVisible(false);
                    }}
                  >
                    <Text style={styles.currencyFlag}>{item.flag}</Text>
                    <View style={styles.currencyInfo}>
                      <Text style={styles.currencyName}>{item.name}</Text>
                      <Text style={styles.currencySymbolText}>{item.symbol} - {item.code}</Text>
                    </View>
                    {formData.currency === item.code && (
                      <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                    )}
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
              />
            </View>
          </View>
        </Modal>

        {/* Budget Selection Modal */}
        <Modal
          visible={showBudgetModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowBudgetModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Bütçe Seçin</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowBudgetModal(false)}
                >
                  <Ionicons name="close" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>

              <FlatList
                data={[
                  { id: '', name: 'Bütçe seçmek istemiyorum', startDate: '', endDate: '' },
                  ...availableBudgets
                ]}
                keyExtractor={(item) => item.id || 'no-budget'}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.budgetOption,
                      formData.budgetId === item.id && styles.budgetOptionSelected
                    ]}
                    onPress={() => {
                      setFormData({ ...formData, budgetId: item.id });
                      setShowBudgetModal(false);
                    }}
                  >
                    <View style={styles.budgetOptionContent}>
                      <Ionicons
                        name={item.id ? "wallet-outline" : "close-circle-outline"}
                        size={20}
                        color={item.id ? theme.colors.primary : theme.colors.textSecondary}
                      />
                      <View style={styles.budgetInfo}>
                        <Text style={styles.budgetName}>{item.name}</Text>
                        {item.id && (
                          <Text style={styles.budgetPeriod}>
                            {new Date(item.startDate).toLocaleDateString('tr-TR')} - {new Date(item.endDate).toLocaleDateString('tr-TR')}
                          </Text>
                        )}
                      </View>
                    </View>
                    {formData.budgetId === item.id && (
                      <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                    )}
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
              />
            </View>
          </View>
        </Modal>

        {/* OCR Modal */}
        <OCRModal
          visible={ocrModalVisible}
          onClose={() => setOcrModalVisible(false)}
          imageUri={ocrImageUri}
          onDataExtracted={handleOCRDataExtracted}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AddTransactionScreen;
