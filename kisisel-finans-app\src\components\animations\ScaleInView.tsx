// Scale In Animation Component - Ölçekleme animasyonu bileşeni

import React, { useEffect } from 'react';
import { ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
  Easing,
} from 'react-native-reanimated';

interface ScaleInViewProps {
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  initialScale?: number;
  useSpring?: boolean;
  style?: ViewStyle;
}

const ScaleInView: React.FC<ScaleInViewProps> = ({
  children,
  duration = 500,
  delay = 0,
  initialScale = 0.8,
  useSpring = true,
  style,
}) => {
  const scale = useSharedValue(initialScale);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const scaleAnimation = useSpring
      ? withSpring(1, {
          damping: 12,
          stiffness: 100,
        })
      : withTiming(1, {
          duration,
          easing: Easing.out(Easing.back(1.5)),
        });

    scale.value = withDelay(delay, scaleAnimation);
    opacity.value = withDelay(
      delay,
      withTiming(1, {
        duration: duration * 0.6,
        easing: Easing.out(Easing.quad),
      })
    );
  }, [scale, opacity, duration, delay, useSpring]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

export default ScaleInView;
