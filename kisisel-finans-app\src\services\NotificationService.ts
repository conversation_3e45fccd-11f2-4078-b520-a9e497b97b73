// Notification Service - Bildirim servisi

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Bildirim ayarları
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

interface NotificationData {
  title: string;
  body: string;
  data?: any;
}

interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  trigger: Date;
  data?: any;
}

class NotificationService {
  private static instance: NotificationService;
  private readonly NOTIFICATION_TOKEN_KEY = 'notification_token';
  private readonly SCHEDULED_NOTIFICATIONS_KEY = 'scheduled_notifications';

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Bildirim izinlerini ister
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      return finalStatus === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * Push notification token'ını alır
   */
  async getExpoPushToken(): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync()).data;
      await AsyncStorage.setItem(this.NOTIFICATION_TOKEN_KEY, token);
      return token;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Kaydedilmiş token'ı alır
   */
  async getSavedToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.NOTIFICATION_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting saved token:', error);
      return null;
    }
  }

  /**
   * Anında bildirim gönderir
   */
  async sendImmediateNotification(notification: NotificationData): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
        },
        trigger: null, // Anında gönder
      });

      return notificationId;
    } catch (error) {
      console.error('Error sending immediate notification:', error);
      return null;
    }
  }

  /**
   * Zamanlanmış bildirim oluşturur
   */
  async scheduleNotification(
    notification: NotificationData,
    trigger: Date
  ): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
        },
        trigger: {
          date: trigger,
        },
      });

      // Zamanlanmış bildirimleri kaydet
      await this.saveScheduledNotification({
        id: notificationId,
        title: notification.title,
        body: notification.body,
        trigger,
        data: notification.data,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  }

  /**
   * Tekrarlanan bildirim oluşturur
   */
  async scheduleRepeatingNotification(
    notification: NotificationData,
    repeatInterval: 'day' | 'week' | 'month',
    hour: number = 9,
    minute: number = 0
  ): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return null;
      }

      let trigger: any;

      switch (repeatInterval) {
        case 'day':
          trigger = {
            hour,
            minute,
            repeats: true,
          };
          break;
        case 'week':
          trigger = {
            weekday: 2, // Pazartesi
            hour,
            minute,
            repeats: true,
          };
          break;
        case 'month':
          trigger = {
            day: 1, // Ayın ilk günü
            hour,
            minute,
            repeats: true,
          };
          break;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling repeating notification:', error);
      return null;
    }
  }

  /**
   * Bildirimi iptal eder
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      await this.removeScheduledNotification(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  /**
   * Tüm zamanlanmış bildirimleri iptal eder
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await AsyncStorage.removeItem(this.SCHEDULED_NOTIFICATIONS_KEY);
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  /**
   * Zamanlanmış bildirimleri alır
   */
  async getScheduledNotifications(): Promise<ScheduledNotification[]> {
    try {
      const notifications = await AsyncStorage.getItem(this.SCHEDULED_NOTIFICATIONS_KEY);
      return notifications ? JSON.parse(notifications) : [];
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Zamanlanmış bildirimi kaydeder
   */
  private async saveScheduledNotification(notification: ScheduledNotification): Promise<void> {
    try {
      const notifications = await this.getScheduledNotifications();
      notifications.push(notification);
      await AsyncStorage.setItem(
        this.SCHEDULED_NOTIFICATIONS_KEY,
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error('Error saving scheduled notification:', error);
    }
  }

  /**
   * Zamanlanmış bildirimi kaldırır
   */
  private async removeScheduledNotification(notificationId: string): Promise<void> {
    try {
      const notifications = await this.getScheduledNotifications();
      const filteredNotifications = notifications.filter(n => n.id !== notificationId);
      await AsyncStorage.setItem(
        this.SCHEDULED_NOTIFICATIONS_KEY,
        JSON.stringify(filteredNotifications)
      );
    } catch (error) {
      console.error('Error removing scheduled notification:', error);
    }
  }

  /**
   * Bütçe uyarısı gönderir
   */
  async sendBudgetAlert(budgetName: string, spentPercentage: number): Promise<void> {
    let title = '';
    let body = '';

    if (spentPercentage >= 100) {
      title = '🚨 Bütçe Aşıldı!';
      body = `${budgetName} bütçeniz aşıldı. Harcamalarınızı kontrol edin.`;
    } else if (spentPercentage >= 80) {
      title = '⚠️ Bütçe Uyarısı';
      body = `${budgetName} bütçenizin %${spentPercentage.toFixed(0)}'i kullanıldı.`;
    }

    if (title && body) {
      await this.sendImmediateNotification({
        title,
        body,
        data: { type: 'budget_alert', budgetName, spentPercentage },
      });
    }
  }

  /**
   * Hedef hatırlatması gönderir
   */
  async sendGoalReminder(goalTitle: string, daysRemaining: number): Promise<void> {
    let title = '';
    let body = '';

    if (daysRemaining <= 0) {
      title = '🎯 Hedef Tarihi Geldi!';
      body = `${goalTitle} hedefinizin tarihi geldi. İlerlemenizi kontrol edin.`;
    } else if (daysRemaining <= 7) {
      title = '📅 Hedef Hatırlatması';
      body = `${goalTitle} hedefinize ${daysRemaining} gün kaldı.`;
    } else if (daysRemaining <= 30) {
      title = '🎯 Hedef Hatırlatması';
      body = `${goalTitle} hedefinize ${daysRemaining} gün kaldı.`;
    }

    if (title && body) {
      await this.sendImmediateNotification({
        title,
        body,
        data: { type: 'goal_reminder', goalTitle, daysRemaining },
      });
    }
  }

  /**
   * Haftalık rapor bildirimi gönderir
   */
  async sendWeeklyReport(totalIncome: number, totalExpense: number): Promise<void> {
    const netAmount = totalIncome - totalExpense;
    const title = '📊 Haftalık Rapor';
    const body = `Bu hafta ${totalIncome.toLocaleString('tr-TR')} ₺ gelir, ${totalExpense.toLocaleString('tr-TR')} ₺ gider. Net: ${netAmount >= 0 ? '+' : ''}${netAmount.toLocaleString('tr-TR')} ₺`;

    await this.sendImmediateNotification({
      title,
      body,
      data: { type: 'weekly_report', totalIncome, totalExpense, netAmount },
    });
  }

  /**
   * Bildirim dinleyicilerini ayarlar
   */
  setupNotificationListeners(
    onNotificationReceived?: (notification: Notifications.Notification) => void,
    onNotificationResponse?: (response: Notifications.NotificationResponse) => void
  ): () => void {
    const receivedSubscription = Notifications.addNotificationReceivedListener(
      notification => {
        if (onNotificationReceived) {
          onNotificationReceived(notification);
        }
      }
    );

    const responseSubscription = Notifications.addNotificationResponseReceivedListener(
      response => {
        if (onNotificationResponse) {
          onNotificationResponse(response);
        }
      }
    );

    // Cleanup fonksiyonu döndür
    return () => {
      receivedSubscription.remove();
      responseSubscription.remove();
    };
  }
}

// Singleton instance'ı export et
const notificationService = NotificationService.getInstance();

export default notificationService;
