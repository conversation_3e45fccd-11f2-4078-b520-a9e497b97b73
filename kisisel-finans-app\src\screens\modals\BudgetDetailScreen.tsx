// RFC-003 Gelişmiş Bütçe Detay Ekranı

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { useRoute, useNavigation, useFocusEffect, DrawerActions } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { Ionicons } from '@expo/vector-icons';
import BudgetService from '../../services/BudgetService';
import TransactionService from '../../services/TransactionService';
import CategorySyncService from '../../services/CategorySyncService';

type BudgetDetailRouteProp = RouteProp<MainStackParamList, 'BudgetDetail'>;

const { width } = Dimensions.get('window');

const BudgetDetailScreen: React.FC = () => {
  const route = useRoute<BudgetDetailRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const { budgetId } = route.params;

  // State
  const [budget, setBudget] = useState<any>(null);
  const [budgetSummary, setBudgetSummary] = useState<any>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [incomeSummary, setIncomeSummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load budget details
  const loadBudgetDetails = useCallback(async () => {
    try {
      console.log(`📊 Loading budget details for ID: ${budgetId}`);

      // Debug: Kategori senkronizasyonu kontrol
      try {
        console.log('🔄 Checking category synchronization...');

        const categoryStatus = await CategorySyncService.checkCategoryStatus();
        console.log('📊 Category Status:', categoryStatus);

        // ⚠️ CATEGORY SYNC DEVRE DIŞI - BUDGET_CATEGORIES SİLİYOR!
        // Eğer kategoriler eksikse veya code'ları yoksa senkronize et
        if (false && categoryStatus.categoriesWithoutCode > 0 || categoryStatus.totalCategories < 30) {
          console.log('🔄 Running category synchronization...');
          await CategorySyncService.syncCategories();
          await CategorySyncService.syncUserCategories(user.id);
          console.log('✅ Category synchronization completed!');
        }

        // Güncel kategori listesini göster
        const DatabaseManager = require('../../database/DatabaseManager').default;
        const db = DatabaseManager.getDatabase();
        const allCategories = await db.getAllAsync('SELECT * FROM categories WHERE user_id = ? OR user_id = ? ORDER BY type, name', [user.id, 'system']);
        console.log(`📊 UPDATED DATABASE CATEGORIES (${allCategories.length} total):`);

        const incomeCategories = allCategories.filter(cat => cat.type === 'income');
        const expenseCategories = allCategories.filter(cat => cat.type === 'expense');

        console.log(`📊 INCOME CATEGORIES (${incomeCategories.length}):`);
        incomeCategories.forEach(cat => console.log(`  - ${cat.name} (${cat.code || 'no code'}) [${cat.id}]`));

        console.log(`📊 EXPENSE CATEGORIES (${expenseCategories.length}):`);
        expenseCategories.forEach(cat => console.log(`  - ${cat.name} (${cat.code || 'no code'}) [${cat.id}]`));
      } catch (dbError) {
        console.log('📊 Category sync check failed:', dbError);
      }

      // Debug: Veritabanındaki tüm kategorileri listele
      const db = require('../../database/DatabaseManager').default.getDatabase();
      const allCategories = await db.getAllAsync('SELECT * FROM categories ORDER BY type, name');
      console.log(`📊 DATABASE CATEGORIES (${allCategories.length} total):`);

      const incomeCategories = allCategories.filter(cat => cat.type === 'income');
      const expenseCategories = allCategories.filter(cat => cat.type === 'expense');

      console.log(`📊 INCOME CATEGORIES (${incomeCategories.length}):`);
      incomeCategories.forEach(cat => console.log(`  - ${cat.name} (${cat.code || 'no code'}) [${cat.id}]`));

      console.log(`📊 EXPENSE CATEGORIES (${expenseCategories.length}):`);
      expenseCategories.forEach(cat => console.log(`  - ${cat.name} (${cat.code || 'no code'}) [${cat.id}]`));

      // Paralel olarak tüm verileri yükle
      console.log('🔥 CRITICAL DEBUG: Loading budget details for ID:', budgetId);

      const [budgetData, summaryData, categoriesData, incomeData] = await Promise.all([
        BudgetService.getBudgetById(budgetId),
        BudgetService.getBudgetSummary(budgetId),
        BudgetService.getBudgetCategoriesWithDetails(budgetId),
        // Gelir özetini de yükle
        (async () => {
          try {
            const IncomeHybridService = require('../../services/IncomeHybridService').default;
            return await IncomeHybridService.getIncomeBudgetSummary(budgetId);
          } catch (error) {
            console.log('⚠️ Income summary not available:', error.message);
            return null;
          }
        })()
      ]);

      console.log('🔥 CRITICAL DEBUG: Budget data:', budgetData);
      console.log('🔥 CRITICAL DEBUG: Summary data:', summaryData);
      console.log('🔥 CRITICAL DEBUG: Categories data:', categoriesData);
      console.log('🔥 CRITICAL DEBUG: Categories count:', categoriesData?.length || 0);

      // MANUEL VERİTABANI KONTROLÜ
      const DatabaseManager = require('../../database/DatabaseManager').default;
      const debugDb = DatabaseManager.getDatabase();

      console.log('🔥 CRITICAL DEBUG: Manual database check...');
      const manualBudgetCheck = await debugDb.getFirstAsync('SELECT * FROM budgets WHERE id = ?', [budgetId]);
      console.log('🔥 CRITICAL DEBUG: Manual budget check:', manualBudgetCheck);

      const manualCategoriesCheck = await debugDb.getAllAsync('SELECT * FROM budget_categories WHERE budget_id = ?', [budgetId]);
      console.log('🔥 CRITICAL DEBUG: Manual categories check:', manualCategoriesCheck);
      console.log('🔥 CRITICAL DEBUG: Manual categories count:', manualCategoriesCheck?.length || 0);

      // TÜM BÜTÇE KATEGORİLERİNİ KONTROL ET
      const allBudgetCategories = await debugDb.getAllAsync('SELECT * FROM budget_categories');
      console.log('🔥 CRITICAL DEBUG: ALL budget categories in DB:', allBudgetCategories);
      console.log('🔥 CRITICAL DEBUG: Total budget categories in DB:', allBudgetCategories?.length || 0);

      // TEST2 BÜTÇESİNİ ÖZEL OLARAK KONTROL ET
      const test2Categories = await debugDb.getAllAsync('SELECT * FROM budget_categories WHERE budget_id = ?', ['budget_1749077642531_wysfm70s4']);
      console.log('🔥 CRITICAL DEBUG: Test2 budget categories:', test2Categories);
      console.log('🔥 CRITICAL DEBUG: Test2 categories count:', test2Categories?.length || 0);

      setBudget(budgetData);
      setBudgetSummary(summaryData);
      setCategories(categoriesData);
      setIncomeSummary(incomeData);

      // Gelir özetini logla
      if (incomeData) {
        console.log('💰 Income Summary:', {
          target: incomeData.totalIncomeTarget,
          actual: incomeData.totalIncomeActual,
          progress: incomeData.incomeProgressPercentage,
          status: incomeData.incomeStatus,
          categoriesCount: incomeData.categories?.length || 0
        });
      }

      // Son işlemleri ve gerçek harcamaları yükle
      if (categoriesData.length > 0 && budgetData) {
        const categoryIds = categoriesData.map(cat => cat.categoryId);
        console.log(`📊 Budget categories:`, categoriesData.map(cat => ({
          id: cat.categoryId,
          name: cat.categoryName,
          planned: cat.plannedAmount
        })));

        // Son işlemleri al
        const transactions = await TransactionService.getTransactionsByCategories(
          user.id,
          categoryIds,
          budgetData.startDate,
          budgetData.endDate,
          5 // Son 5 işlem
        );
        setRecentTransactions(transactions);
        console.log(`📊 Recent transactions:`, transactions.length);

        // Kategori harcamalarını hesapla
        const categorySpending = await TransactionService.calculateCategorySpending(
          user.id, // Gerçek user ID kullan
          categoryIds,
          budgetData.startDate,
          budgetData.endDate
        );

        console.log(`📊 Category spending result:`, categorySpending);

        // Kategorileri gerçek harcama verileriyle güncelle
        const updatedCategories = categoriesData.map(category => {
          const spentAmount = categorySpending[category.categoryId] || 0;
          const progress = category.plannedAmount > 0 ? (spentAmount / category.plannedAmount) * 100 : 0;

          console.log(`📊 Category ${category.categoryName}:`, {
            categoryId: category.categoryId,
            planned: category.plannedAmount,
            spent: spentAmount,
            progress: progress
          });

          return {
            ...category,
            spentAmount,
            progress
          };
        });

        setCategories(updatedCategories);
        console.log(`📊 Updated categories:`, updatedCategories.length);
      }

      console.log('📊 Budget details loaded:', { budgetData, summaryData, categoriesData });
    } catch (error) {
      console.error('❌ Error loading budget details:', error);
      Alert.alert('Hata', 'Bütçe detayları yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [budgetId]);

  // Load on mount and focus
  useEffect(() => {
    loadBudgetDetails();
  }, [loadBudgetDetails]);

  useFocusEffect(
    useCallback(() => {
      loadBudgetDetails();
    }, [loadBudgetDetails])
  );

  // Refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadBudgetDetails();
    setRefreshing(false);
  }, [loadBudgetDetails]);

  // Helper functions
  const formatCurrency = useCallback((amount: number | string, currency: string = 'TRY') => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount || 0;
    const symbol = currency === 'TRY' ? '₺' : currency === 'USD' ? '$' : '€';
    return `${numAmount.toLocaleString('tr-TR')} ${symbol}`;
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  }, []);

  const getPeriodLabel = useCallback((period: string) => {
    switch (period) {
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'quarterly': return '3 Aylık';
      case 'annually': return 'Yıllık';
      default: return period;
    }
  }, []);

  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'safe': return theme.colors.success;
      case 'warning': return theme.colors.warning;
      case 'critical': return '#FF6B35';
      case 'exceeded': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  }, [theme]);

  const getStatusIcon = useCallback((status: string) => {
    switch (status) {
      case 'safe': return 'checkmark-circle';
      case 'warning': return 'warning';
      case 'critical': return 'alert-circle';
      case 'exceeded': return 'close-circle';
      default: return 'help-circle';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    switch (status) {
      case 'safe': return 'Güvenli';
      case 'warning': return 'Dikkat';
      case 'critical': return 'Kritik';
      case 'exceeded': return 'Aşıldı';
      default: return 'Bilinmiyor';
    }
  }, []);

  // Navigation handlers
  const handleAddTransaction = useCallback(() => {
    // İşlem ekleme sayfasına git
    navigation.navigate('AddTransaction' as never, {
      budgetId,
      preselectedCategory: categories.length > 0 ? categories[0].categoryId : undefined
    } as never);
  }, [navigation, budgetId, categories]);

  const handleViewAllTransactions = useCallback(() => {
    // İşlemler sayfasına git (kategorilere göre filtrelenmiş)
    navigation.navigate('Transactions' as never, {
      filters: {
        categories: categories.map(cat => cat.categoryId),
        dateRange: {
          start: budget?.startDate,
          end: budget?.endDate
        }
      }
    } as never);
  }, [navigation, categories, budget]);

  const handleEditBudget = useCallback(() => {
    // Bütçe düzenleme sayfasına git
    navigation.navigate('BudgetEdit' as never, {
      budgetId
    } as never);
  }, [navigation, budgetId]);

  const handleViewReports = useCallback(() => {
    // Bütçe rapor sayfası oluşturacağız
    navigation.navigate('BudgetReports' as never, { budgetId } as never);
  }, [navigation, budgetId]);

  const handleShareBudget = useCallback(() => {
    // Bütçe paylaşma fonksiyonu
    console.log('📤 Sharing budget:', budgetId);
    // TODO: Share functionality
  }, [budgetId]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    customHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 50,
      paddingBottom: 16,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
    },
    headerCenter: {
      flex: 1,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    menuButton: {
      padding: 8,
      borderRadius: 20,
    },
    scrollContainer: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingVertical: 30,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.surface,
    },
    budgetName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    budgetPeriod: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      backgroundColor: theme.colors.surfaceSecondary,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 12,
    },
    progressContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 1,
    },
    progressInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    progressLabel: {
      fontSize: 16,
      color: theme.colors.text,
      fontWeight: '500',
    },
    progressPercentage: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    progressBar: {
      height: 12,
      backgroundColor: theme.colors.border,
      borderRadius: 6,
    },
    progressFill: {
      height: '100%',
      borderRadius: 6,
    },
    amountsContainer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    amountCard: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
    },
    amountLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    amountValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
    },
    detailsContainer: {
      padding: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
    },
    detailItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailLabel: {
      fontSize: 16,
      color: theme.colors.text,
    },
    detailValue: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    actionsContainer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 12,
      paddingVertical: 16,
    },
    actionButtonText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      marginLeft: 8,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginTop: 16,
    },
    centered: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
    },
    categoryItem: {
      marginBottom: 12,
      padding: 12,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
    },
    categoryHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    categoryIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    categoryName: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
      color: theme.colors.text,
    },
    categoryAmount: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    // New Styles for Enhanced UI
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    headerInfo: {
      flex: 1,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 20,
      gap: 4,
    },
    statusText: {
      color: 'white',
      fontSize: 12,
      fontWeight: '600',
    },
    headerEditButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
    },
    dateRange: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    dateText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    daysRemaining: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    progressTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    progressLabels: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 8,
    },
    summaryContainer: {
      padding: 20,
    },
    summaryGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    summaryCard: {
      flex: 1,
      minWidth: (width - 60) / 2,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      gap: 8,
    },
    summaryLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    statsContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 8,
    },
    statRow: {
      flexDirection: 'row',
      gap: 20,
    },
    statItem: {
      flex: 1,
      alignItems: 'center',
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    statValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      marginTop: 16,
    },
    retryButtonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
    },
    // Categories Styles
    categoriesContainer: {
      padding: 20,
    },
    categoryCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    categoryDetails: {
      flex: 1,
    },
    categoryStatus: {
      fontSize: 12,
      fontWeight: '600',
      marginTop: 2,
    },
    categoryAmounts: {
      alignItems: 'flex-end',
    },
    categoryPlanned: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    categorySpent: {
      fontSize: 12,
      color: theme.colors.text,
      marginTop: 2,
    },
    categoryProgress: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 12,
      gap: 12,
    },
    categoryProgressBar: {
      flex: 1,
      height: 6,
      backgroundColor: theme.colors.border,
      borderRadius: 3,
    },
    categoryProgressFill: {
      height: '100%',
      borderRadius: 3,
    },
    categoryProgressText: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.text,
      minWidth: 40,
      textAlign: 'right',
    },
    // Transactions Styles
    transactionsContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 8,
    },
    transactionsHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    viewAllText: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    transactionItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    transactionInfo: {
      flex: 1,
    },
    transactionDescription: {
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: 2,
    },
    transactionDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    transactionAmount: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    // Actions Styles
    primaryAction: {
      backgroundColor: theme.colors.primary,
      marginBottom: 12,
    },
    primaryActionText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    secondaryActions: {
      flexDirection: 'row',
      gap: 8,
    },
    secondaryActionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      paddingVertical: 12,
      gap: 4,
    },
    secondaryActionText: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    bottomSpacing: {
      height: 20,
    },
    // Alert Badge Styles
    categoryNameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    alertBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
      gap: 2,
    },
    alertBadgeText: {
      fontSize: 10,
      fontWeight: 'bold',
      color: 'white',
    },
  });

  // Loading state
  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          Bütçe detayları yükleniyor...
        </Text>
      </View>
    );
  }

  // Error state
  if (!budget || !budgetSummary) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.errorTitle}>Bütçe Bulunamadı</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={loadBudgetDetails}
          >
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Custom Header */}
      <View style={[styles.customHeader, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Bütçe Detayı
          </Text>
        </View>

        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        >
          <Ionicons name="menu" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Budget Info Header */}
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <View style={styles.headerInfo}>
              <Text style={styles.budgetName}>{budget.name}</Text>
              <Text style={styles.budgetPeriod}>{getPeriodLabel(budget.period)}</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <TouchableOpacity
                style={[styles.headerEditButton, { backgroundColor: theme.colors.success }]}
                onPress={handleViewReports}
              >
                <Ionicons name="bar-chart-outline" size={18} color="white" />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.headerEditButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleEditBudget}
              >
                <Ionicons name="create-outline" size={18} color="white" />
              </TouchableOpacity>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(budgetSummary.status) }]}>
                <Ionicons
                  name={getStatusIcon(budgetSummary.status)}
                  size={16}
                  color="white"
                />
                <Text style={styles.statusText}>
                  {getStatusText(budgetSummary.status)}
                </Text>
              </View>
            </View>
          </View>

        <View style={styles.dateRange}>
          <Text style={styles.dateText}>
            {formatDate(budget.startDate)} - {formatDate(budget.endDate)}
          </Text>
          <Text style={styles.daysRemaining}>
            {budgetSummary.daysRemaining} gün kaldı
          </Text>
        </View>
      </View>

      {/* Progress Overview */}
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>Genel İlerleme</Text>
          <Text style={styles.progressPercentage}>
            {budgetSummary.overallProgress.toFixed(1)}%
          </Text>
        </View>

        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${Math.min(budgetSummary.overallProgress, 100)}%`,
                backgroundColor: budgetSummary.overallProgress >= 100 ? theme.colors.error :
                               budgetSummary.overallProgress >= 80 ? theme.colors.warning :
                               theme.colors.success
              }
            ]}
          />
        </View>

        <View style={styles.progressLabels}>
          <Text style={styles.progressLabel}>
            {formatCurrency(budgetSummary.totalSpent, budget.currency)} harcandı
          </Text>
          <Text style={styles.progressLabel}>
            {formatCurrency(budgetSummary.totalPlanned, budget.currency)} toplam
          </Text>
        </View>
      </View>

      {/* Financial Summary */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Ionicons name="trending-down" size={24} color={theme.colors.error} />
            <Text style={styles.summaryLabel}>Harcanan</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.error }]}>
              {formatCurrency(budgetSummary.totalSpent, budget.currency)}
            </Text>
          </View>

          <View style={styles.summaryCard}>
            <Ionicons name="wallet" size={24} color={theme.colors.success} />
            <Text style={styles.summaryLabel}>Kalan</Text>
            <Text style={[
              styles.summaryValue,
              { color: budgetSummary.totalRemaining >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {formatCurrency(budgetSummary.totalRemaining, budget.currency)}
            </Text>
          </View>

          <View style={styles.summaryCard}>
            <Ionicons name="calculator" size={24} color={theme.colors.primary} />
            <Text style={styles.summaryLabel}>Toplam Bütçe</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
              {formatCurrency(budgetSummary.totalPlanned, budget.currency)}
            </Text>
          </View>

          <View style={styles.summaryCard}>
            <Ionicons name="trending-up" size={24} color={theme.colors.warning} />
            <Text style={styles.summaryLabel}>Günlük Ort.</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.warning }]}>
              {formatCurrency(budgetSummary.averageDailySpending, budget.currency)}
            </Text>
          </View>
        </View>
      </View>

      {/* Income Summary */}
      {incomeSummary && (
        <View style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>💰 Gelir Durumu</Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryCard}>
              <Ionicons name="trending-up" size={24} color={theme.colors.success} />
              <Text style={styles.summaryLabel}>Hedef Gelir</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.success }]}>
                {formatCurrency(incomeSummary.totalIncomeTarget, budget.currency)}
              </Text>
            </View>

            <View style={styles.summaryCard}>
              <Ionicons name="cash" size={24} color={theme.colors.primary} />
              <Text style={styles.summaryLabel}>Gerçekleşen</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
                {formatCurrency(incomeSummary.totalIncomeActual, budget.currency)}
              </Text>
            </View>

            <View style={styles.summaryCard}>
              <Ionicons name="hourglass" size={24} color={theme.colors.warning} />
              <Text style={styles.summaryLabel}>Kalan Hedef</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.warning }]}>
                {formatCurrency(incomeSummary.totalIncomeRemaining, budget.currency)}
              </Text>
            </View>

            <View style={styles.summaryCard}>
              <Ionicons name="speedometer" size={24} color={theme.colors.info} />
              <Text style={styles.summaryLabel}>İlerleme</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.info }]}>
                {incomeSummary.incomeProgressPercentage.toFixed(1)}%
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>İstatistikler</Text>

        <View style={styles.statRow}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Kategori Sayısı</Text>
            <Text style={styles.statValue}>{categories.length}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Tahmini Bitiş</Text>
            <Text style={[
              styles.statValue,
              { color: budgetSummary.projectedEndAmount > budgetSummary.totalPlanned ?
                theme.colors.error : theme.colors.success }
            ]}>
              {formatCurrency(budgetSummary.projectedEndAmount, budget.currency)}
            </Text>
          </View>
        </View>
      </View>

      {/* Category Details */}
      {categories.length > 0 && (
        <View style={styles.categoriesContainer}>
          <Text style={styles.sectionTitle}>Kategori Detayları</Text>
          {categories.map((category: any) => (
            <View key={category.id} style={styles.categoryCard}>
              <View style={styles.categoryHeader}>
                <View style={styles.categoryInfo}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.categoryColor || '#AED6F1' }]}>
                    <Ionicons
                      name={category.categoryIcon || 'ellipsis-horizontal-outline'}
                      size={20}
                      color="white"
                    />
                  </View>
                  <View style={styles.categoryDetails}>
                    <View style={styles.categoryNameRow}>
                      <Text style={styles.categoryName}>
                        {category.categoryName || 'Kategori'}
                      </Text>
                      {/* Uyarı Badge'leri */}
                      {category.progress >= 100 && (
                        <View style={[styles.alertBadge, { backgroundColor: theme.colors.error }]}>
                          <Ionicons name="close-circle" size={12} color="white" />
                          <Text style={styles.alertBadgeText}>AŞILDI</Text>
                        </View>
                      )}
                      {category.progress >= 90 && category.progress < 100 && (
                        <View style={[styles.alertBadge, { backgroundColor: '#FF6B35' }]}>
                          <Ionicons name="alert-circle" size={12} color="white" />
                          <Text style={styles.alertBadgeText}>KRİTİK</Text>
                        </View>
                      )}
                      {category.progress >= 75 && category.progress < 90 && (
                        <View style={[styles.alertBadge, { backgroundColor: theme.colors.warning }]}>
                          <Ionicons name="warning" size={12} color="white" />
                          <Text style={styles.alertBadgeText}>UYARI</Text>
                        </View>
                      )}
                    </View>
                    <Text style={[styles.categoryStatus, { color: getStatusColor(category.status) }]}>
                      {getStatusText(category.status)}
                    </Text>
                  </View>
                </View>
                <View style={styles.categoryAmounts}>
                  <Text style={styles.categoryPlanned}>
                    {formatCurrency(category.plannedAmount, budget.currency)}
                  </Text>
                  <Text style={styles.categorySpent}>
                    {formatCurrency(category.spentAmount, budget.currency)} harcandı
                  </Text>
                </View>
              </View>

              <View style={styles.categoryProgress}>
                <View style={styles.categoryProgressBar}>
                  <View
                    style={[
                      styles.categoryProgressFill,
                      {
                        width: `${Math.min(category.progress, 100)}%`,
                        backgroundColor: getStatusColor(category.status)
                      }
                    ]}
                  />
                </View>
                <Text style={styles.categoryProgressText}>
                  {category.progress.toFixed(1)}%
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}

      {/* Recent Transactions */}
      {recentTransactions.length > 0 && (
        <View style={styles.transactionsContainer}>
          <View style={styles.transactionsHeader}>
            <Text style={styles.sectionTitle}>Son İşlemler</Text>
            <TouchableOpacity onPress={handleViewAllTransactions}>
              <Text style={styles.viewAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>
          {recentTransactions.slice(0, 3).map((transaction: any) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionInfo}>
                <Text style={styles.transactionDescription}>
                  {transaction.description}
                </Text>
                <Text style={styles.transactionDate}>
                  {formatDate(transaction.date)}
                </Text>
              </View>
              <Text style={[
                styles.transactionAmount,
                { color: transaction.type === 'expense' ? theme.colors.error : theme.colors.success }
              ]}>
                {transaction.type === 'expense' ? '-' : '+'}
                {formatCurrency(transaction.amount, budget.currency)}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryAction]}
          onPress={handleAddTransaction}
        >
          <Ionicons name="add-circle" size={24} color="white" />
          <Text style={styles.primaryActionText}>Harcama Ekle</Text>
        </TouchableOpacity>

        <View style={styles.secondaryActions}>
          <TouchableOpacity
            style={styles.secondaryActionButton}
            onPress={handleEditBudget}
          >
            <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.secondaryActionText}>Düzenle</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryActionButton}
            onPress={handleViewReports}
          >
            <Ionicons name="bar-chart-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.secondaryActionText}>Rapor</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryActionButton}
            onPress={handleShareBudget}
          >
            <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.secondaryActionText}>Paylaş</Text>
          </TouchableOpacity>
        </View>
      </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

export default BudgetDetailScreen;
