// JWT Service - JSON Web Token yönetimi

import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';

export interface JWTPayload {
  userId: string;
  email: string;
  name: string;
  iat: number; // issued at
  exp: number; // expires at
  sessionId: string;
  deviceId: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface SessionInfo {
  sessionId: string;
  deviceId: string;
  createdAt: number;
  lastActivity: number;
  isActive: boolean;
}

class JWTService {
  private static instance: JWTService;
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private static readonly SESSION_INFO_KEY = 'session_info';
  private static readonly DEVICE_ID_KEY = 'device_id';

  // Token süreleri (saniye)
  private static readonly ACCESS_TOKEN_EXPIRY = 15 * 60; // 15 dakika
  private static readonly REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60; // 7 gün
  private static readonly SESSION_TIMEOUT = 30 * 60; // 30 dakika inaktivite

  private deviceId: string | null = null;
  private sessionCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeDeviceId();
    this.startSessionMonitoring();
  }

  static getInstance(): JWTService {
    if (!JWTService.instance) {
      JWTService.instance = new JWTService();
    }
    return JWTService.instance;
  }

  /**
   * Cihaz ID'sini başlat
   */
  private async initializeDeviceId(): Promise<void> {
    try {
      let deviceId = await SecureStore.getItemAsync(JWTService.DEVICE_ID_KEY);
      if (!deviceId) {
        deviceId = await Crypto.digestStringAsync(
          Crypto.CryptoDigestAlgorithm.SHA256,
          `${Date.now()}_${Math.random()}`,
          { encoding: Crypto.CryptoEncoding.HEX }
        );
        await SecureStore.setItemAsync(JWTService.DEVICE_ID_KEY, deviceId);
      }
      this.deviceId = deviceId;
    } catch (error) {
      console.error('Device ID initialization error:', error);
      this.deviceId = `fallback_${Date.now()}`;
    }
  }

  /**
   * Mock JWT token oluştur (gerçek uygulamada backend'den gelecek)
   */
  async generateTokenPair(userId: string, email: string, name: string): Promise<TokenPair> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const sessionId = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        `${userId}_${now}_${Math.random()}`,
        { encoding: Crypto.CryptoEncoding.HEX }
      );

      // Mock JWT payload
      const accessPayload: JWTPayload = {
        userId,
        email,
        name,
        iat: now,
        exp: now + JWTService.ACCESS_TOKEN_EXPIRY,
        sessionId,
        deviceId: this.deviceId || 'unknown',
      };

      const refreshPayload = {
        userId,
        sessionId,
        deviceId: this.deviceId || 'unknown',
        iat: now,
        exp: now + JWTService.REFRESH_TOKEN_EXPIRY,
        type: 'refresh',
      };

      // Mock token generation (gerçek uygulamada backend'de yapılacak)
      const accessToken = await this.createMockToken(accessPayload);
      const refreshToken = await this.createMockToken(refreshPayload);

      // Session bilgisini sakla
      const sessionInfo: SessionInfo = {
        sessionId,
        deviceId: this.deviceId || 'unknown',
        createdAt: now,
        lastActivity: now,
        isActive: true,
      };

      await this.storeTokens(accessToken, refreshToken);
      await this.storeSessionInfo(sessionInfo);

      return {
        accessToken,
        refreshToken,
        expiresIn: JWTService.ACCESS_TOKEN_EXPIRY,
        tokenType: 'Bearer',
      };
    } catch (error) {
      console.error('Token generation error:', error);
      throw new Error('Token oluşturulamadı');
    }
  }

  /**
   * Mock token oluştur
   */
  private async createMockToken(payload: any): Promise<string> {
    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEncoded = btoa(JSON.stringify(header));
    const payloadEncoded = btoa(JSON.stringify(payload));

    // Mock signature (gerçek uygulamada secret key ile imzalanacak)
    const signature = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      `${headerEncoded}.${payloadEncoded}.mock_secret`,
      { encoding: Crypto.CryptoEncoding.HEX }
    );

    return `${headerEncoded}.${payloadEncoded}.${signature}`;
  }

  /**
   * Token'ları güvenli olarak sakla
   */
  private async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
    await SecureStore.setItemAsync(JWTService.ACCESS_TOKEN_KEY, accessToken);
    await SecureStore.setItemAsync(JWTService.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Session bilgisini sakla
   */
  private async storeSessionInfo(sessionInfo: SessionInfo): Promise<void> {
    await SecureStore.setItemAsync(JWTService.SESSION_INFO_KEY, JSON.stringify(sessionInfo));
  }

  /**
   * Access token'ı al
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(JWTService.ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Get access token error:', error);
      return null;
    }
  }

  /**
   * Refresh token'ı al
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(JWTService.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Get refresh token error:', error);
      return null;
    }
  }

  /**
   * Token'ı decode et (mock implementation)
   */
  async decodeToken(token: string): Promise<JWTPayload | null> {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = JSON.parse(atob(parts[1]));
      return payload as JWTPayload;
    } catch (error) {
      console.error('Token decode error:', error);
      return null;
    }
  }

  /**
   * Token'ın geçerli olup olmadığını kontrol et
   */
  async isTokenValid(token: string): Promise<boolean> {
    try {
      const payload = await this.decodeToken(token);
      if (!payload) {
        return false;
      }

      const now = Math.floor(Date.now() / 1000);
      return payload.exp > now;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * Token'ın yenilenmesi gerekip gerekmediğini kontrol et
   */
  async shouldRefreshToken(): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      if (!accessToken) {
        return false;
      }

      const payload = await this.decodeToken(accessToken);
      if (!payload) {
        return false;
      }

      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = payload.exp - now;

      // Token 5 dakika içinde sona erecekse yenile
      return timeUntilExpiry < 5 * 60;
    } catch (error) {
      console.error('Should refresh token check error:', error);
      return false;
    }
  }

  /**
   * Token'ı yenile
   */
  async refreshAccessToken(): Promise<TokenPair | null> {
    try {
      const refreshToken = await this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('Refresh token bulunamadı');
      }

      const isValid = await this.isTokenValid(refreshToken);
      if (!isValid) {
        throw new Error('Refresh token geçersiz');
      }

      const payload = await this.decodeToken(refreshToken);
      if (!payload || !payload.userId) {
        throw new Error('Refresh token payload geçersiz');
      }

      // Mock refresh (gerçek uygulamada API çağrısı yapılacak)
      const newTokenPair = await this.generateTokenPair(
        payload.userId,
        payload.email || '',
        payload.name || ''
      );

      return newTokenPair;
    } catch (error) {
      console.error('Token refresh error:', error);
      await this.clearTokens();
      return null;
    }
  }

  /**
   * Session aktivitesini güncelle
   */
  async updateSessionActivity(): Promise<void> {
    try {
      const sessionInfoStr = await SecureStore.getItemAsync(JWTService.SESSION_INFO_KEY);
      if (!sessionInfoStr) {
        return;
      }

      const sessionInfo: SessionInfo = JSON.parse(sessionInfoStr);
      sessionInfo.lastActivity = Math.floor(Date.now() / 1000);

      await this.storeSessionInfo(sessionInfo);
    } catch (error) {
      console.error('Update session activity error:', error);
    }
  }

  /**
   * Session'ın aktif olup olmadığını kontrol et
   */
  async isSessionActive(): Promise<boolean> {
    try {
      const sessionInfoStr = await SecureStore.getItemAsync(JWTService.SESSION_INFO_KEY);
      if (!sessionInfoStr) {
        return false;
      }

      const sessionInfo: SessionInfo = JSON.parse(sessionInfoStr);
      const now = Math.floor(Date.now() / 1000);
      const timeSinceLastActivity = now - sessionInfo.lastActivity;

      return sessionInfo.isActive && timeSinceLastActivity < JWTService.SESSION_TIMEOUT;
    } catch (error) {
      console.error('Session active check error:', error);
      return false;
    }
  }

  /**
   * Session monitoring başlat
   */
  private startSessionMonitoring(): void {
    this.sessionCheckInterval = setInterval(async () => {
      const isActive = await this.isSessionActive();
      if (!isActive) {
        await this.clearTokens();
        // AuthContext'e logout sinyali gönder
        // Bu kısım AuthContext ile entegre edilecek
      }
    }, 60 * 1000); // Her dakika kontrol et
  }

  /**
   * Session monitoring'i durdur
   */
  private stopSessionMonitoring(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  /**
   * Tüm token'ları temizle
   */
  async clearTokens(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(JWTService.ACCESS_TOKEN_KEY);
      await SecureStore.deleteItemAsync(JWTService.REFRESH_TOKEN_KEY);
      await SecureStore.deleteItemAsync(JWTService.SESSION_INFO_KEY);

      this.stopSessionMonitoring();
    } catch (error) {
      console.error('Clear tokens error:', error);
    }
  }

  /**
   * Service'i temizle
   */
  async cleanup(): Promise<void> {
    this.stopSessionMonitoring();
    await this.clearTokens();
  }
}

export default JWTService;
