// RFC-003 <PERSON><PERSON><PERSON>mi - Adım 2B: <PERSON><PERSON><PERSON>

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';
import CategoryService from '../../../services/CategoryService';

interface IncomeCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  code: string;
}

interface SelectedIncomeCategory extends IncomeCategory {
  targetAmount: number;
  isSelected: boolean;
}

const Step2IncomeCategories: React.FC = () => {
  const { theme } = useTheme();
  const { state, dispatch } = useBudgetWizard();
  const [incomeCategories, setIncomeCategories] = useState<IncomeCategory[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<SelectedIncomeCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Gelir kategorilerini yükle
  useEffect(() => {
    loadIncomeCategories();
  }, []);

  const loadIncomeCategories = async () => {
    try {
      setIsLoading(true);
      const categories = await CategoryService.getIncomeCategories();
      
      // Ana gelir kategorilerini filtrele
      const mainIncomeCategories = categories.filter(cat => 
        ['salary', 'freelance', 'side_income', 'investment_income', 'bonus', 'other_income'].includes(cat.code)
      );

      setIncomeCategories(mainIncomeCategories);
      
      // Mevcut seçili kategorileri state'den al
      const existingSelected = state.incomeCategories || [];
      const selected = mainIncomeCategories.map(cat => ({
        ...cat,
        targetAmount: existingSelected.find(s => s.categoryId === cat.id)?.targetAmount || 0,
        isSelected: existingSelected.some(s => s.categoryId === cat.id),
      }));
      
      setSelectedCategories(selected);
    } catch (error) {
      console.error('❌ Error loading income categories:', error);
      Alert.alert('Hata', 'Gelir kategorileri yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = useCallback((amount: number): string => {
    const symbol = state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€';
    return `${amount.toLocaleString('tr-TR')} ${symbol}`;
  }, [state.currency]);

  const handleCategoryToggle = useCallback((categoryId: string) => {
    setSelectedCategories(prev => 
      prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, isSelected: !cat.isSelected, targetAmount: cat.isSelected ? 0 : cat.targetAmount }
          : cat
      )
    );
  }, []);

  const handleTargetAmountChange = useCallback((categoryId: string, amount: string) => {
    const numericAmount = parseFloat(amount.replace(/[^0-9.]/g, '')) || 0;
    
    setSelectedCategories(prev => 
      prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, targetAmount: numericAmount }
          : cat
      )
    );
  }, []);

  const getTotalIncomeTarget = (): number => {
    return selectedCategories
      .filter(cat => cat.isSelected)
      .reduce((sum, cat) => sum + cat.targetAmount, 0);
  };

  const handleSaveIncomeCategories = useCallback(() => {
    const selected = selectedCategories.filter(cat => cat.isSelected && cat.targetAmount > 0);
    
    if (selected.length === 0) {
      Alert.alert('Uyarı', 'En az bir gelir kategorisi seçin ve hedef tutarını belirleyin.');
      return;
    }

    const totalTarget = getTotalIncomeTarget();
    
    // Wizard state'ini güncelle
    dispatch({
      type: 'SET_INCOME_CATEGORIES',
      payload: {
        incomeCategories: selected.map(cat => ({
          categoryId: cat.id,
          targetAmount: cat.targetAmount,
          warningThreshold: 50,
          targetThreshold: 100,
          warningEnabled: true,
          targetEnabled: true,
        })),
        totalIncomeTarget: totalTarget,
      }
    });

    Alert.alert(
      'Başarılı!',
      `${selected.length} gelir kategorisi seçildi.\nToplam gelir hedefi: ${formatCurrency(totalTarget)}`,
      [{ text: 'Tamam' }]
    );
  }, [selectedCategories, dispatch, formatCurrency]);

  const renderIncomeCategory = (category: SelectedIncomeCategory) => (
    <View key={category.id} style={[
      styles.categoryCard,
      { 
        backgroundColor: theme.colors.surface,
        borderColor: category.isSelected ? category.color : theme.colors.border,
        borderWidth: category.isSelected ? 2 : 1,
      }
    ]}>
      <TouchableOpacity
        style={styles.categoryHeader}
        onPress={() => handleCategoryToggle(category.id)}
      >
        <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
          <Ionicons name={category.icon as any} size={24} color="white" />
        </View>
        
        <View style={styles.categoryInfo}>
          <Text style={[styles.categoryName, { color: theme.colors.text }]}>
            {category.name}
          </Text>
          <Text style={[styles.categoryCode, { color: theme.colors.textSecondary }]}>
            {getCategoryDescription(category.code)}
          </Text>
        </View>
        
        <View style={[
          styles.checkbox,
          { 
            backgroundColor: category.isSelected ? category.color : 'transparent',
            borderColor: category.isSelected ? category.color : theme.colors.border,
          }
        ]}>
          {category.isSelected && (
            <Ionicons name="checkmark" size={16} color="white" />
          )}
        </View>
      </TouchableOpacity>

      {category.isSelected && (
        <View style={styles.targetAmountContainer}>
          <Text style={[styles.targetLabel, { color: theme.colors.text }]}>
            Hedef Tutar:
          </Text>
          <View style={[styles.amountInputContainer, { backgroundColor: theme.colors.background }]}>
            <TextInput
              style={[styles.amountInput, { color: theme.colors.text }]}
              value={category.targetAmount > 0 ? category.targetAmount.toString() : ''}
              onChangeText={(text) => handleTargetAmountChange(category.id, text)}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <Text style={[styles.currencySymbol, { color: theme.colors.textSecondary }]}>
              {state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€'}
            </Text>
          </View>
        </View>
      )}
    </View>
  );

  const getCategoryDescription = (code: string): string => {
    const descriptions: Record<string, string> = {
      salary: 'Maaş, ücret, bordro',
      freelance: 'Serbest çalışma, proje',
      side_income: 'Yan gelir, ek iş',
      investment_income: 'Yatırım getirisi',
      bonus: 'Prim, bonus, ikramiye',
      other_income: 'Diğer gelir kaynakları',
    };
    return descriptions[code] || '';
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Gelir kategorileri yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Gelir Kategorileri
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Gelir kaynaklarınızı seçin ve her biri için hedef tutarları belirleyin
        </Text>
      </View>

      {/* Gelir Kategorileri */}
      <View style={styles.section}>
        {selectedCategories.map(renderIncomeCategory)}
      </View>

      {/* Özet */}
      {getTotalIncomeTarget() > 0 && (
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
            Gelir Hedefi Özeti
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Seçili Kategoriler:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
              {selectedCategories.filter(cat => cat.isSelected).length}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.summaryTotal]}>
            <Text style={[styles.summaryTotalLabel, { color: theme.colors.text }]}>
              Toplam Gelir Hedefi:
            </Text>
            <Text style={[styles.summaryTotalValue, { color: theme.colors.success }]}>
              {formatCurrency(getTotalIncomeTarget())}
            </Text>
          </View>
        </View>
      )}

      {/* Kaydet Butonu */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            selectedCategories.filter(cat => cat.isSelected).length === 0 && styles.disabledButton
          ]}
          onPress={handleSaveIncomeCategories}
          disabled={selectedCategories.filter(cat => cat.isSelected).length === 0}
        >
          <Ionicons name="checkmark-circle" size={20} color="white" />
          <Text style={styles.saveButtonText}>
            Gelir Kategorilerini Kaydet
          </Text>
        </TouchableOpacity>
      </View>

      {/* İpucu */}
      <View style={[styles.tipCard, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="bulb-outline" size={20} color={theme.colors.warning} />
        <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
          <Text style={{ fontWeight: '600' }}>İpucu:</Text> Gelir kategorileri bütçenizin gelir tarafını takip etmenizi sağlar. 
          Her kategori için gerçekçi hedefler belirleyin.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  loadingText: {
    fontSize: 16,
  },
  categoryCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryCode: {
    fontSize: 14,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  targetAmountContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  targetLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  summaryTotal: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: 8,
    marginTop: 4,
  },
  summaryTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
    flex: 1,
  },
});

export default Step2IncomeCategories;
