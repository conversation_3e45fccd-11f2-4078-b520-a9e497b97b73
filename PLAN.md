# RFC-003 <PERSON><PERSON>i Senkronizasyon Planı

## 🎯 HEDEF
Categories tablosuna `code` sütunu ekleyerek UI-Database senkronizasyonunu sağlamak

## 📋 ADIMLAR

### 1. Database Schema Güncelleme
- [x] Categories tablosuna `code` sütun<PERSON> ekle (UNIQUE)
- [x] Mevcut 11 kategoriye code değerleri ata
- [x] Eksik kategorileri ekle (17+ kate<PERSON><PERSON> i<PERSON>)

### 2. UI Katmanlarını Güncelle
- [x] constants/categories.ts → code bazlı yapı
- [x] types/transaction.ts → code referansları
- [x] AddTransactionScreen → code kullanımı
- [x] ML Service → code mapping
- [x] TransactionService → code bazlı arama

### 3. Hibrit Sistem Düzeltme
- [x] Budget-Transaction linking → code bazlı
- [x] Category matching → code bazlı
- [ ] UI senkronizasyonu → tek kaynak

### 4. Test ve Doğrulama
- [x] Database migration test (V12 düzeltildi)
- [ ] UI kategori listesi test
- [ ] Transaction ekleme test
- [ ] Budget oluşturma test

## 🗂️ KATEGORI KODLARI
```
food, transportation, bills, shopping, entertainment, 
health, education, rent, insurance, personal_care, 
gifts, travel, donation, tax, housing, other,
salary, investment_income, side_income, freelance
```

## 🚀 BAŞLAYALIM
İlk adım: Database schema migration
