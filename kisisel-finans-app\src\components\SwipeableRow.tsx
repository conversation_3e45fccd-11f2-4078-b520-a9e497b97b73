// Swipeable Row Component - Kaydırılabilir satır bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');
const SWIPE_THRESHOLD = screenWidth * 0.3;
const ACTION_WIDTH = 80;

interface SwipeAction {
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  backgroundColor: string;
  onPress: () => void;
}

interface SwipeableRowProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
  style?: any;
}

const SwipeableRow: React.FC<SwipeableRowProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeStart,
  onSwipeEnd,
  style,
}) => {
  const { theme } = useTheme();
  const translateX = useSharedValue(0);
  const isSwipeActive = useSharedValue(false);

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      isSwipeActive.value = true;
      if (onSwipeStart) {
        runOnJS(onSwipeStart)();
      }
    },
    onActive: (event) => {
      const maxLeftSwipe = leftActions.length * ACTION_WIDTH;
      const maxRightSwipe = -rightActions.length * ACTION_WIDTH;

      translateX.value = Math.max(
        maxRightSwipe,
        Math.min(maxLeftSwipe, event.translationX)
      );
    },
    onEnd: (event) => {
      const velocity = event.velocityX;
      const translation = event.translationX;

      let finalPosition = 0;

      // Determine final position based on velocity and translation
      if (Math.abs(velocity) > 500) {
        // Fast swipe
        if (velocity > 0 && leftActions.length > 0) {
          finalPosition = leftActions.length * ACTION_WIDTH;
        } else if (velocity < 0 && rightActions.length > 0) {
          finalPosition = -rightActions.length * ACTION_WIDTH;
        }
      } else {
        // Slow swipe - check threshold
        if (translation > SWIPE_THRESHOLD && leftActions.length > 0) {
          finalPosition = leftActions.length * ACTION_WIDTH;
        } else if (translation < -SWIPE_THRESHOLD && rightActions.length > 0) {
          finalPosition = -rightActions.length * ACTION_WIDTH;
        }
      }

      translateX.value = withSpring(finalPosition, {
        damping: 15,
        stiffness: 150,
      });

      isSwipeActive.value = false;
      if (onSwipeEnd) {
        runOnJS(onSwipeEnd)();
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  const leftActionsStyle = useAnimatedStyle(() => {
    const opacity = translateX.value > 0 ? 1 : 0;
    const scale = translateX.value > 0 ? 1 : 0.8;

    return {
      opacity: withTiming(opacity, { duration: 200 }),
      transform: [{ scale: withTiming(scale, { duration: 200 }) }],
    };
  });

  const rightActionsStyle = useAnimatedStyle(() => {
    const opacity = translateX.value < 0 ? 1 : 0;
    const scale = translateX.value < 0 ? 1 : 0.8;

    return {
      opacity: withTiming(opacity, { duration: 200 }),
      transform: [{ scale: withTiming(scale, { duration: 200 }) }],
    };
  });

  const closeSwipe = () => {
    translateX.value = withSpring(0, {
      damping: 15,
      stiffness: 150,
    });
  };

  const handleActionPress = (action: SwipeAction) => {
    action.onPress();
    closeSwipe();
  };

  const styles = StyleSheet.create({
    container: {
      position: 'relative',
      overflow: 'hidden',
    },
    content: {
      backgroundColor: theme.colors.surface,
      zIndex: 1,
    },
    leftActions: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      flexDirection: 'row',
      alignItems: 'center',
      zIndex: 0,
    },
    rightActions: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      flexDirection: 'row',
      alignItems: 'center',
      zIndex: 0,
    },
    actionButton: {
      width: ACTION_WIDTH,
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={[styles.container, style]}>
      {/* Left Actions */}
      {leftActions.length > 0 && (
        <Animated.View style={[styles.leftActions, leftActionsStyle]}>
          {leftActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.actionButton,
                { backgroundColor: action.backgroundColor },
              ]}
              onPress={() => handleActionPress(action)}
            >
              <Ionicons name={action.icon} size={24} color={action.color} />
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}

      {/* Right Actions */}
      {rightActions.length > 0 && (
        <Animated.View style={[styles.rightActions, rightActionsStyle]}>
          {rightActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.actionButton,
                { backgroundColor: action.backgroundColor },
              ]}
              onPress={() => handleActionPress(action)}
            >
              <Ionicons name={action.icon} size={24} color={action.color} />
            </TouchableOpacity>
          ))}
        </Animated.View>
      )}

      {/* Main Content */}
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.content, animatedStyle]}>
          {children}
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

export default SwipeableRow;
