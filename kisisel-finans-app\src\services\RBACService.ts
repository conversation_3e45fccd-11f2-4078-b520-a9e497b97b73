// RBAC Service - Rol tabanlı eri<PERSON><PERSON> kontrolü servisi

import AsyncStorage from '@react-native-async-storage/async-storage';
import SecureStorageWrapper from './SecureStorageWrapper';
import {
  Role,
  UserRole,
  Permission,
  RoleType,
  PermissionCheck,
  AccessControlResult,
  RoleAssignment,
  AccessLog,
  DEFAULT_ROLES,
  ROLE_HIERARCHY,
  PERMISSION_GROUPS,
} from '../types/rbac';

export interface RBACConfig {
  enableLogging: boolean;
  cacheTimeout: number;
  strictMode: boolean;
  inheritanceEnabled: boolean;
  contextValidation: boolean;
}

class RBACService {
  private static instance: RBACService;
  private readonly ROLES_STORAGE_KEY = 'rbac_roles';
  private readonly USER_ROLES_STORAGE_KEY = 'rbac_user_roles';
  private readonly ACCESS_LOGS_STORAGE_KEY = 'rbac_access_logs';
  private readonly CONFIG_STORAGE_KEY = 'rbac_config';

  // Cache
  private rolesCache: Map<string, Role> = new Map();
  private userRolesCache: Map<string, UserRole[]> = new Map();
  private permissionsCache: Map<string, Permission[]> = new Map();
  private lastCacheUpdate: number = 0;

  // Configuration
  private config: RBACConfig = {
    enableLogging: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes
    strictMode: false,
    inheritanceEnabled: true,
    contextValidation: true,
  };

  static getInstance(): RBACService {
    if (!RBACService.instance) {
      RBACService.instance = new RBACService();
    }
    return RBACService.instance;
  }

  constructor() {
    this.initializeService();
  }

  /**
   * Service'i başlat
   */
  private async initializeService(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.loadRoles();
      await this.setupDefaultRoles();
    } catch (error) {
      console.error('RBAC Service initialization error:', error);
    }
  }

  /**
   * Konfigürasyonu yükle
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const configStr = await AsyncStorage.getItem(this.CONFIG_STORAGE_KEY);
      if (configStr) {
        this.config = { ...this.config, ...JSON.parse(configStr) };
      }
    } catch (error) {
      console.error('Error loading RBAC config:', error);
    }
  }

  /**
   * Rolleri yükle
   */
  private async loadRoles(): Promise<void> {
    try {
      const rolesStr = await AsyncStorage.getItem(this.ROLES_STORAGE_KEY);
      if (rolesStr) {
        const roles: Role[] = JSON.parse(rolesStr);
        this.rolesCache.clear();
        roles.forEach(role => {
          this.rolesCache.set(role.id, role);
        });
      }
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  }

  /**
   * Varsayılan rolleri ayarla
   */
  private async setupDefaultRoles(): Promise<void> {
    try {
      // Eğer hiç rol yoksa varsayılan rolleri oluştur
      if (this.rolesCache.size === 0) {
        for (const defaultRole of DEFAULT_ROLES) {
          const role: Role = {
            ...defaultRole,
            id: `role_${defaultRole.name}_${Date.now()}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          this.rolesCache.set(role.id, role);
        }
        await this.saveRoles();
      }
    } catch (error) {
      console.error('Error setting up default roles:', error);
    }
  }

  /**
   * Rolleri kaydet
   */
  private async saveRoles(): Promise<void> {
    try {
      const roles = Array.from(this.rolesCache.values());
      await AsyncStorage.setItem(this.ROLES_STORAGE_KEY, JSON.stringify(roles));
    } catch (error) {
      console.error('Error saving roles:', error);
    }
  }

  /**
   * Kullanıcıya rol ata
   */
  async assignRole(
    userId: string,
    roleType: RoleType,
    assignedBy: string,
    context?: { familyId?: string; organizationId?: string },
    expiresAt?: string
  ): Promise<boolean> {
    try {
      // Rol var mı kontrol et
      const role = this.getRoleByType(roleType);
      if (!role) {
        throw new Error(`Role not found: ${roleType}`);
      }

      // Mevcut kullanıcı rollerini al
      const userRoles = await this.getUserRoles(userId);

      // Aynı rol zaten atanmış mı kontrol et
      const existingRole = userRoles.find(ur => ur.roleName === roleType && ur.isActive);
      if (existingRole) {
        console.warn(`User ${userId} already has role ${roleType}`);
        return true;
      }

      // Yeni rol ataması oluştur
      const roleAssignment: UserRole = {
        userId,
        roleId: role.id,
        roleName: roleType,
        assignedBy,
        assignedAt: new Date().toISOString(),
        expiresAt,
        isActive: true,
        context,
      };

      // Kullanıcı rollerini güncelle
      userRoles.push(roleAssignment);
      await this.saveUserRoles(userId, userRoles);

      // Cache'i güncelle
      this.userRolesCache.set(userId, userRoles);
      this.permissionsCache.delete(userId); // Permission cache'i temizle
      this.lastCacheUpdate = Date.now(); // Cache timestamp güncelle

      // Log
      if (this.config.enableLogging) {
        await this.logAccess({
          userId: assignedBy,
          action: 'role_assign',
          resource: `user:${userId}`,
          permission: 'user:manage_roles',
          allowed: true,
          timestamp: new Date().toISOString(),
        });
      }

      return true;
    } catch (error) {
      console.error('Error assigning role:', error);
      return false;
    }
  }

  /**
   * Kullanıcıdan rol kaldır
   */
  async removeRole(userId: string, roleType: RoleType, removedBy: string): Promise<boolean> {
    try {
      const userRoles = await this.getUserRoles(userId);
      const roleIndex = userRoles.findIndex(ur => ur.roleName === roleType && ur.isActive);

      if (roleIndex === -1) {
        console.warn(`User ${userId} does not have role ${roleType}`);
        return true;
      }

      // Rolü array'den kaldır
      userRoles.splice(roleIndex, 1);

      await this.saveUserRoles(userId, userRoles);

      // Cache'i güncelle
      this.userRolesCache.set(userId, userRoles);
      this.permissionsCache.delete(userId);
      this.lastCacheUpdate = Date.now(); // Cache timestamp güncelle

      // Log
      if (this.config.enableLogging) {
        await this.logAccess({
          userId: removedBy,
          action: 'role_remove',
          resource: `user:${userId}`,
          permission: 'user:manage_roles',
          allowed: true,
          timestamp: new Date().toISOString(),
        });
      }

      return true;
    } catch (error) {
      console.error('Error removing role:', error);
      return false;
    }
  }

  /**
   * Kullanıcının rollerini al
   */
  async getUserRoles(userId: string): Promise<UserRole[]> {
    try {
      // Cache kontrolü
      if (this.userRolesCache.has(userId) && this.isCacheValid()) {
        return this.userRolesCache.get(userId)!;
      }

      // Storage'dan al
      const userRoles = await SecureStorageWrapper.getItem(`${this.USER_ROLES_STORAGE_KEY}_${userId}`, {
        encrypt: true,
      });

      const roles = userRoles || [];

      // Süresi dolmuş rolleri filtrele
      const activeRoles = roles.filter((role: UserRole) => {
        if (!role.isActive) return false;
        if (role.expiresAt && new Date(role.expiresAt) < new Date()) {
          role.isActive = false;
          return false;
        }
        return true;
      });

      // Cache'e ekle
      this.userRolesCache.set(userId, activeRoles);

      return activeRoles;
    } catch (error) {
      console.error('Error getting user roles:', error);
      return [];
    }
  }

  /**
   * Kullanıcının izinlerini al
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    try {
      // Cache kontrolü
      if (this.permissionsCache.has(userId) && this.isCacheValid()) {
        return this.permissionsCache.get(userId)!;
      }

      const userRoles = await this.getUserRoles(userId);
      const permissions = new Set<Permission>();

      for (const userRole of userRoles) {
        const role = this.rolesCache.get(userRole.roleId);
        if (role) {
          // Rol izinlerini ekle
          role.permissions.forEach(permission => permissions.add(permission));

          // Inheritance varsa parent rol izinlerini de ekle
          if (this.config.inheritanceEnabled) {
            const inheritedPermissions = this.getInheritedPermissions(role.name);
            inheritedPermissions.forEach(permission => permissions.add(permission));
          }
        }
      }

      const permissionArray = Array.from(permissions);

      // Cache'e ekle
      this.permissionsCache.set(userId, permissionArray);

      return permissionArray;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * İzin kontrolü yap
   */
  async checkPermission(userId: string, permissionCheck: PermissionCheck): Promise<AccessControlResult> {
    try {
      const userRoles = await this.getUserRoles(userId);
      const userPermissions = await this.getUserPermissions(userId);
      const roleTypes = userRoles.map(ur => ur.roleName);

      // İzin var mı kontrol et
      const hasPermission = userPermissions.includes(permissionCheck.permission);

      const result: AccessControlResult = {
        allowed: hasPermission,
        currentRoles: roleTypes,
        currentPermissions: userPermissions,
      };

      if (!hasPermission) {
        result.reason = `Missing permission: ${permissionCheck.permission}`;
        result.requiredPermission = permissionCheck.permission;

        // Hangi rol bu izni verebilir?
        const requiredRole = this.findRoleWithPermission(permissionCheck.permission);
        if (requiredRole) {
          result.requiredRole = requiredRole.name;
        }
      }

      // Context validation
      if (hasPermission && this.config.contextValidation && permissionCheck.context) {
        const contextValid = await this.validateContext(userId, permissionCheck);
        if (!contextValid) {
          result.allowed = false;
          result.reason = 'Context validation failed';
        }
      }

      // Log
      if (this.config.enableLogging) {
        await this.logAccess({
          userId,
          action: 'permission_check',
          resource: permissionCheck.context?.resourceId || 'unknown',
          permission: permissionCheck.permission,
          allowed: result.allowed,
          reason: result.reason,
          timestamp: new Date().toISOString(),
        });
      }

      return result;
    } catch (error) {
      console.error('Error checking permission:', error);
      return {
        allowed: false,
        reason: 'Permission check failed',
        currentRoles: [],
        currentPermissions: [],
      };
    }
  }

  /**
   * Kullanıcı rollerini kaydet
   */
  private async saveUserRoles(userId: string, roles: UserRole[]): Promise<void> {
    try {
      await SecureStorageWrapper.setItem(`${this.USER_ROLES_STORAGE_KEY}_${userId}`, roles, {
        encrypt: true,
      });
    } catch (error) {
      console.error('Error saving user roles:', error);
    }
  }

  /**
   * Rol tipine göre rol bul
   */
  private getRoleByType(roleType: RoleType): Role | undefined {
    return Array.from(this.rolesCache.values()).find(role => role.name === roleType);
  }

  /**
   * İzin ile rol bul
   */
  private findRoleWithPermission(permission: Permission): Role | undefined {
    return Array.from(this.rolesCache.values())
      .filter(role => role.permissions.includes(permission))
      .sort((a, b) => a.priority - b.priority)[0]; // En düşük öncelikli rolü döndür
  }

  /**
   * Inherited permissions al
   */
  private getInheritedPermissions(roleType: RoleType): Permission[] {
    const hierarchy = ROLE_HIERARCHY.find(h => h.role === roleType);
    if (!hierarchy) return [];

    const permissions = new Set<Permission>();

    for (const parentRoleType of hierarchy.inheritsFrom) {
      const parentRole = this.getRoleByType(parentRoleType);
      if (parentRole) {
        parentRole.permissions.forEach(permission => permissions.add(permission));
        // Recursive inheritance
        const parentInherited = this.getInheritedPermissions(parentRoleType);
        parentInherited.forEach(permission => permissions.add(permission));
      }
    }

    return Array.from(permissions);
  }

  /**
   * Context validation
   */
  private async validateContext(userId: string, permissionCheck: PermissionCheck): Promise<boolean> {
    // Context validation logic burada implement edilecek
    // Örnek: familyId kontrolü, resource ownership kontrolü vb.
    return true; // Şimdilik her zaman true döndür
  }

  /**
   * Access log
   */
  private async logAccess(log: Omit<AccessLog, 'id'>): Promise<void> {
    try {
      const accessLog: AccessLog = {
        ...log,
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      // Mevcut logları al
      const logs = await AsyncStorage.getItem(this.ACCESS_LOGS_STORAGE_KEY);
      const accessLogs: AccessLog[] = logs ? JSON.parse(logs) : [];

      // Yeni log ekle
      accessLogs.push(accessLog);

      // Son 1000 log'u sakla
      if (accessLogs.length > 1000) {
        accessLogs.splice(0, accessLogs.length - 1000);
      }

      await AsyncStorage.setItem(this.ACCESS_LOGS_STORAGE_KEY, JSON.stringify(accessLogs));
    } catch (error) {
      console.error('Error logging access:', error);
    }
  }

  /**
   * Cache geçerli mi kontrol et
   */
  private isCacheValid(): boolean {
    return Date.now() - this.lastCacheUpdate < this.config.cacheTimeout;
  }

  /**
   * Cache'i temizle
   */
  clearCache(): void {
    this.userRolesCache.clear();
    this.permissionsCache.clear();
    this.lastCacheUpdate = 0;
  }

  /**
   * Tüm rolleri al
   */
  getAllRoles(): Role[] {
    return Array.from(this.rolesCache.values());
  }

  /**
   * Permission gruplarını al
   */
  getPermissionGroups() {
    return PERMISSION_GROUPS;
  }

  /**
   * Role hierarchy al
   */
  getRoleHierarchy() {
    return ROLE_HIERARCHY;
  }

  /**
   * Access loglarını al
   */
  async getAccessLogs(userId?: string, limit: number = 100): Promise<AccessLog[]> {
    try {
      const logs = await AsyncStorage.getItem(this.ACCESS_LOGS_STORAGE_KEY);
      const accessLogs: AccessLog[] = logs ? JSON.parse(logs) : [];

      let filteredLogs = accessLogs;
      if (userId) {
        filteredLogs = accessLogs.filter(log => log.userId === userId);
      }

      return filteredLogs
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting access logs:', error);
      return [];
    }
  }

  /**
   * Konfigürasyonu güncelle
   */
  async updateConfiguration(newConfig: Partial<RBACConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      await AsyncStorage.setItem(this.CONFIG_STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('Error updating RBAC config:', error);
    }
  }

  /**
   * RBAC durumunu al
   */
  getRBACStatus() {
    return {
      rolesCount: this.rolesCache.size,
      cacheSize: {
        roles: this.rolesCache.size,
        userRoles: this.userRolesCache.size,
        permissions: this.permissionsCache.size,
      },
      config: this.config,
      lastCacheUpdate: this.lastCacheUpdate,
    };
  }
}

export default RBACService.getInstance();
