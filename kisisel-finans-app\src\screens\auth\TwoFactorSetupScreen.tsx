// Two Factor Setup Screen - 2FA kurulum ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { Ionicons } from '@expo/vector-icons';
import TwoFactorService, { AuthenticatorSetup } from '../../services/TwoFactorService';
import QRCode from 'react-native-qrcode-svg';

const TwoFactorSetupScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();

  const [twoFactorSettings, setTwoFactorSettings] = useState(TwoFactorService.getInstance().getTwoFactorSettings());
  const [emailCode, setEmailCode] = useState('');
  const [isEmailVerificationPending, setIsEmailVerificationPending] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // SMS states
  const [smsCode, setSmsCode] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isSmsVerificationPending, setIsSmsVerificationPending] = useState(false);
  const [smsVerificationStatus, setSmsVerificationStatus] = useState<any>(null);
  const [registeredPhone, setRegisteredPhone] = useState<string | null>(null);

  // Authenticator states
  const [authenticatorCode, setAuthenticatorCode] = useState('');
  const [authenticatorSetup, setAuthenticatorSetup] = useState<AuthenticatorSetup | null>(null);
  const [isAuthenticatorSetupPending, setIsAuthenticatorSetupPending] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);

  useEffect(() => {
    loadVerificationStatus();
    loadRegisteredPhone();
  }, []);

  const loadVerificationStatus = async () => {
    const emailStatus = await TwoFactorService.getInstance().getVerificationStatus('email');
    setVerificationStatus(emailStatus);
    setIsEmailVerificationPending(emailStatus.hasPending);

    const smsStatus = await TwoFactorService.getInstance().getVerificationStatus('sms');
    setSmsVerificationStatus(smsStatus);
    setIsSmsVerificationPending(smsStatus.hasPending);
  };

  const loadRegisteredPhone = async () => {
    const phone = await TwoFactorService.getInstance().getRegisteredPhoneNumber();
    setRegisteredPhone(phone);
  };

  const handleEmailTwoFactorToggle = async (enabled: boolean) => {
    if (enabled) {
      // 2FA'yı etkinleştir
      setLoading(true);
      try {
        const success = await TwoFactorService.getInstance().enableEmailTwoFactor(user?.email || '');
        if (success) {
          setIsEmailVerificationPending(true);
          await loadVerificationStatus();
          Alert.alert(
            'Doğrulama Kodu Gönderildi',
            `${user?.email} adresine 6 haneli doğrulama kodu gönderildi. Kodu aşağıya girin.`,
            [{ text: 'Tamam' }]
          );
        } else {
          Alert.alert('Hata', 'Email doğrulama kodu gönderilemedi');
        }
      } catch (error) {
        Alert.alert('Hata', 'Email 2FA etkinleştirilemedi');
      } finally {
        setLoading(false);
      }
    } else {
      // 2FA'yı devre dışı bırak
      Alert.alert(
        'Email 2FA Devre Dışı Bırak',
        'Email ile iki faktörlü kimlik doğrulamayı devre dışı bırakmak istediğinizden emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Devre Dışı Bırak',
            style: 'destructive',
            onPress: async () => {
              const success = await TwoFactorService.getInstance().disableEmailTwoFactor();
              if (success) {
                setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
                Alert.alert('Başarılı', 'Email 2FA devre dışı bırakıldı');
              } else {
                Alert.alert('Hata', 'Email 2FA devre dışı bırakılamadı');
              }
            }
          }
        ]
      );
    }
  };

  const handleVerifyEmailCode = async () => {
    if (!emailCode.trim()) {
      Alert.alert('Hata', 'Lütfen doğrulama kodunu girin');
      return;
    }

    setLoading(true);
    try {
      const success = await TwoFactorService.getInstance().verifyEmailCode(emailCode);
      if (success) {
        // 2FA'yı tamamla
        await TwoFactorService.getInstance().completeEmailTwoFactor();
        setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
        setIsEmailVerificationPending(false);
        setEmailCode('');

        Alert.alert(
          'Başarılı!',
          'Email ile iki faktörlü kimlik doğrulama başarıyla etkinleştirildi.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error: any) {
      Alert.alert('Doğrulama Hatası', error.message || 'Kod doğrulanamadı');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setLoading(true);
    try {
      const success = await TwoFactorService.getInstance().sendEmailVerificationCode(user?.email || '');
      if (success) {
        await loadVerificationStatus();
        Alert.alert('Başarılı', 'Yeni doğrulama kodu gönderildi');
      } else {
        Alert.alert('Hata', 'Kod tekrar gönderilemedi');
      }
    } catch (error) {
      Alert.alert('Hata', 'Kod tekrar gönderilemedi');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelVerification = async () => {
    await TwoFactorService.getInstance().cancelPendingVerification('email');
    setIsEmailVerificationPending(false);
    setEmailCode('');
    await loadVerificationStatus();
  };

  // SMS Handler Functions
  const handleSMSTwoFactorToggle = async (enabled: boolean) => {
    if (enabled) {
      // SMS 2FA'yı etkinleştir
      if (!phoneNumber.trim()) {
        Alert.alert('Hata', 'Lütfen telefon numaranızı girin');
        return;
      }

      setLoading(true);
      try {
        const success = await TwoFactorService.getInstance().enableSMSTwoFactor(phoneNumber);
        if (success) {
          setIsSmsVerificationPending(true);
          await loadVerificationStatus();
          Alert.alert(
            'SMS Kodu Gönderildi',
            `${phoneNumber} numarasına 6 haneli doğrulama kodu gönderildi.`,
            [{ text: 'Tamam' }]
          );
        } else {
          Alert.alert('Hata', 'SMS doğrulama kodu gönderilemedi');
        }
      } catch (error: any) {
        Alert.alert('Hata', error.message || 'SMS 2FA etkinleştirilemedi');
      } finally {
        setLoading(false);
      }
    } else {
      // SMS 2FA'yı devre dışı bırak
      Alert.alert(
        'SMS 2FA Devre Dışı Bırak',
        'SMS ile iki faktörlü kimlik doğrulamayı devre dışı bırakmak istediğinizden emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Devre Dışı Bırak',
            style: 'destructive',
            onPress: async () => {
              const success = await TwoFactorService.getInstance().disableSMSTwoFactor();
              if (success) {
                setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
                Alert.alert('Başarılı', 'SMS 2FA devre dışı bırakıldı');
              } else {
                Alert.alert('Hata', 'SMS 2FA devre dışı bırakılamadı');
              }
            }
          }
        ]
      );
    }
  };

  const handleVerifySMSCode = async () => {
    if (!smsCode.trim()) {
      Alert.alert('Hata', 'Lütfen SMS kodunu girin');
      return;
    }

    setLoading(true);
    try {
      const success = await TwoFactorService.getInstance().verifySMSCode(smsCode);
      if (success) {
        // SMS 2FA'yı tamamla
        await TwoFactorService.getInstance().completeSMSTwoFactor();
        setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
        setIsSmsVerificationPending(false);
        setSmsCode('');
        await loadRegisteredPhone();

        Alert.alert(
          'Başarılı!',
          'SMS ile iki faktörlü kimlik doğrulama başarıyla etkinleştirildi.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error: any) {
      Alert.alert('Doğrulama Hatası', error.message || 'SMS kodu doğrulanamadı');
    } finally {
      setLoading(false);
    }
  };

  const handleResendSMSCode = async () => {
    setLoading(true);
    try {
      const success = await TwoFactorService.getInstance().sendSMSVerificationCode(phoneNumber);
      if (success) {
        await loadVerificationStatus();
        Alert.alert('Başarılı', 'Yeni SMS kodu gönderildi');
      } else {
        Alert.alert('Hata', 'SMS kodu tekrar gönderilemedi');
      }
    } catch (error) {
      Alert.alert('Hata', 'SMS kodu tekrar gönderilemedi');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSMSVerification = async () => {
    await TwoFactorService.getInstance().cancelPendingVerification('sms');
    setIsSmsVerificationPending(false);
    setSmsCode('');
    await loadVerificationStatus();
  };

  // Authenticator Handler Functions
  const handleAuthenticatorTwoFactorToggle = async (enabled: boolean) => {
    if (enabled) {
      // Authenticator 2FA'yı etkinleştir
      setLoading(true);
      try {
        const setup = await TwoFactorService.getInstance().setupAuthenticator(user?.email || '');
        setAuthenticatorSetup(setup);
        setIsAuthenticatorSetupPending(true);

        Alert.alert(
          'Authenticator Kurulumu',
          'QR kodu tarayın veya manuel olarak secret key\'i girin. Ardından uygulamadan aldığınız 6 haneli kodu girin.',
          [{ text: 'Tamam' }]
        );
      } catch (error: any) {
        Alert.alert('Hata', error.message || 'Authenticator kurulumu başarısız oldu');
      } finally {
        setLoading(false);
      }
    } else {
      // Authenticator 2FA'yı devre dışı bırak
      Alert.alert(
        'Authenticator 2FA Devre Dışı Bırak',
        'Authenticator ile iki faktörlü kimlik doğrulamayı devre dışı bırakmak istediğinizden emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Devre Dışı Bırak',
            style: 'destructive',
            onPress: async () => {
              const success = await TwoFactorService.getInstance().disableAuthenticatorTwoFactor();
              if (success) {
                setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
                Alert.alert('Başarılı', 'Authenticator 2FA devre dışı bırakıldı');
              } else {
                Alert.alert('Hata', 'Authenticator 2FA devre dışı bırakılamadı');
              }
            }
          }
        ]
      );
    }
  };

  const handleVerifyAuthenticatorCode = async () => {
    if (!authenticatorCode.trim()) {
      Alert.alert('Hata', 'Lütfen authenticator kodunu girin');
      return;
    }

    setLoading(true);
    try {
      const success = await TwoFactorService.getInstance().verifyAuthenticatorCode(authenticatorCode);
      if (success) {
        // Authenticator 2FA'yı tamamla
        await TwoFactorService.getInstance().enableAuthenticatorTwoFactor();
        setTwoFactorSettings(TwoFactorService.getInstance().getTwoFactorSettings());
        setIsAuthenticatorSetupPending(false);
        setAuthenticatorCode('');
        setShowBackupCodes(true);

        Alert.alert(
          'Başarılı!',
          'Authenticator ile iki faktörlü kimlik doğrulama başarıyla etkinleştirildi. Backup kodlarınızı güvenli bir yerde saklayın.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error: any) {
      Alert.alert('Doğrulama Hatası', error.message || 'Authenticator kodu doğrulanamadı');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAuthenticatorSetup = async () => {
    await TwoFactorService.getInstance().cancelAuthenticatorSetup();
    setIsAuthenticatorSetupPending(false);
    setAuthenticatorSetup(null);
    setAuthenticatorCode('');
  };

  const handleCopyToClipboard = (text: string) => {
    // Clipboard kopyalama işlemi
    Alert.alert('Kopyalandı', 'Metin panoya kopyalandı');
  };

  const formatTimeRemaining = (expiresAt: number): string => {
    const remaining = Math.max(0, expiresAt - Date.now());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>İki Faktörlü Kimlik Doğrulama</Text>
      </View>

      {/* Info */}
      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="shield-checkmark" size={32} color={theme.colors.primary} />
        <Text style={[styles.infoTitle, { color: theme.colors.text }]}>Hesabınızı Güvende Tutun</Text>
        <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
          İki faktörlü kimlik doğrulama, hesabınıza ek bir güvenlik katmanı ekler.
          Giriş yaparken şifrenizin yanı sıra ikinci bir doğrulama adımı gerekir.
        </Text>
      </View>

      {/* Email 2FA */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="mail" size={24} color={theme.colors.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Email Doğrulama</Text>
          </View>
          <Switch
            value={twoFactorSettings.emailEnabled || isEmailVerificationPending}
            onValueChange={handleEmailTwoFactorToggle}
            disabled={loading || isEmailVerificationPending}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.surface}
          />
        </View>

        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Email adresinize gönderilen 6 haneli kod ile doğrulama yapın
        </Text>

        {user?.email && (
          <Text style={[styles.emailText, { color: theme.colors.text }]}>
            📧 {user.email}
          </Text>
        )}

        {/* Email Verification */}
        {isEmailVerificationPending && (
          <View style={styles.verificationContainer}>
            <Text style={[styles.verificationTitle, { color: theme.colors.text }]}>
              Doğrulama Kodu
            </Text>

            <TextInput
              style={[styles.codeInput, {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              value={emailCode}
              onChangeText={setEmailCode}
              placeholder="6 haneli kod"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
              maxLength={6}
              autoFocus
            />

            {verificationStatus?.expiresAt && (
              <Text style={[styles.timerText, { color: theme.colors.textSecondary }]}>
                Kalan süre: {formatTimeRemaining(verificationStatus.expiresAt)}
              </Text>
            )}

            {verificationStatus?.attemptsLeft && (
              <Text style={[styles.attemptsText, { color: theme.colors.warning }]}>
                Kalan deneme hakkı: {verificationStatus.attemptsLeft}
              </Text>
            )}

            <View style={styles.verificationButtons}>
              <TouchableOpacity
                style={[styles.verifyButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleVerifyEmailCode}
                disabled={loading || !emailCode.trim()}
              >
                <Text style={[styles.verifyButtonText, { color: theme.colors.surface }]}>
                  {loading ? 'Doğrulanıyor...' : 'Doğrula'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.resendButton, { borderColor: theme.colors.border }]}
                onPress={handleResendCode}
                disabled={loading}
              >
                <Text style={[styles.resendButtonText, { color: theme.colors.primary }]}>
                  Tekrar Gönder
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.cancelButton]}
                onPress={handleCancelVerification}
                disabled={loading}
              >
                <Text style={[styles.cancelButtonText, { color: theme.colors.error }]}>
                  İptal
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* SMS 2FA */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="chatbubble" size={24} color={theme.colors.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>SMS Doğrulama</Text>
          </View>
          <Switch
            value={twoFactorSettings.smsEnabled || isSmsVerificationPending}
            onValueChange={handleSMSTwoFactorToggle}
            disabled={loading || isSmsVerificationPending}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.surface}
          />
        </View>

        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Telefon numaranıza gönderilen 6 haneli SMS kod ile doğrulama yapın
        </Text>

        {/* Phone Number Input */}
        {!twoFactorSettings.smsEnabled && !isSmsVerificationPending && (
          <View style={styles.phoneInputContainer}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Telefon Numarası
            </Text>
            <TextInput
              style={[styles.phoneInput, {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="+90 5XX XXX XX XX"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="phone-pad"
            />
          </View>
        )}

        {/* Registered Phone Display */}
        {registeredPhone && twoFactorSettings.smsEnabled && (
          <Text style={[styles.phoneText, { color: theme.colors.text }]}>
            📱 {registeredPhone}
          </Text>
        )}

        {/* SMS Verification */}
        {isSmsVerificationPending && (
          <View style={styles.verificationContainer}>
            <Text style={[styles.verificationTitle, { color: theme.colors.text }]}>
              SMS Doğrulama Kodu
            </Text>

            <TextInput
              style={[styles.codeInput, {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              value={smsCode}
              onChangeText={setSmsCode}
              placeholder="6 haneli SMS kod"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
              maxLength={6}
              autoFocus
            />

            {smsVerificationStatus?.expiresAt && (
              <Text style={[styles.timerText, { color: theme.colors.textSecondary }]}>
                Kalan süre: {formatTimeRemaining(smsVerificationStatus.expiresAt)}
              </Text>
            )}

            {smsVerificationStatus?.attemptsLeft && (
              <Text style={[styles.attemptsText, { color: theme.colors.warning }]}>
                Kalan deneme hakkı: {smsVerificationStatus.attemptsLeft}
              </Text>
            )}

            <View style={styles.verificationButtons}>
              <TouchableOpacity
                style={[styles.verifyButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleVerifySMSCode}
                disabled={loading || !smsCode.trim()}
              >
                <Text style={[styles.verifyButtonText, { color: theme.colors.surface }]}>
                  {loading ? 'Doğrulanıyor...' : 'SMS Kodunu Doğrula'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.resendButton, { borderColor: theme.colors.border }]}
                onPress={handleResendSMSCode}
                disabled={loading}
              >
                <Text style={[styles.resendButtonText, { color: theme.colors.primary }]}>
                  SMS Tekrar Gönder
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.cancelButton]}
                onPress={handleCancelSMSVerification}
                disabled={loading}
              >
                <Text style={[styles.cancelButtonText, { color: theme.colors.error }]}>
                  İptal
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Authenticator 2FA */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="key" size={24} color={theme.colors.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Authenticator App</Text>
          </View>
          <Switch
            value={twoFactorSettings.authenticatorEnabled || isAuthenticatorSetupPending}
            onValueChange={handleAuthenticatorTwoFactorToggle}
            disabled={loading || isAuthenticatorSetupPending}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
            thumbColor={theme.colors.surface}
          />
        </View>

        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Google Authenticator, Authy veya benzeri uygulamalar ile doğrulama
        </Text>

        {/* Authenticator Setup */}
        {isAuthenticatorSetupPending && authenticatorSetup && (
          <View style={styles.authenticatorSetupContainer}>
            <Text style={[styles.setupTitle, { color: theme.colors.text }]}>
              1. Authenticator Uygulamasını Kurun
            </Text>
            <Text style={[styles.setupDescription, { color: theme.colors.textSecondary }]}>
              Google Authenticator, Authy, Microsoft Authenticator veya benzeri bir uygulama indirin.
            </Text>

            <Text style={[styles.setupTitle, { color: theme.colors.text }]}>
              2. QR Kodu Tarayın
            </Text>
            <View style={styles.qrCodeContainer}>
              <QRCode
                value={authenticatorSetup.qrCodeUrl}
                size={200}
                backgroundColor={theme.colors.surface}
                color={theme.colors.text}
              />
            </View>

            <Text style={[styles.setupTitle, { color: theme.colors.text }]}>
              3. Manuel Giriş (Opsiyonel)
            </Text>
            <View style={styles.manualKeyContainer}>
              <Text style={[styles.manualKeyLabel, { color: theme.colors.textSecondary }]}>
                Secret Key:
              </Text>
              <TouchableOpacity
                style={[styles.manualKeyBox, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
                onPress={() => handleCopyToClipboard(authenticatorSetup.manualEntryKey)}
              >
                <Text style={[styles.manualKeyText, { color: theme.colors.text }]}>
                  {authenticatorSetup.manualEntryKey}
                </Text>
                <Ionicons name="copy-outline" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>

            <Text style={[styles.setupTitle, { color: theme.colors.text }]}>
              4. Doğrulama Kodu Girin
            </Text>
            <TextInput
              style={[styles.codeInput, {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text
              }]}
              value={authenticatorCode}
              onChangeText={setAuthenticatorCode}
              placeholder="6 haneli kod"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
              maxLength={6}
            />

            <View style={styles.verificationButtons}>
              <TouchableOpacity
                style={[styles.verifyButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleVerifyAuthenticatorCode}
                disabled={loading || !authenticatorCode.trim()}
              >
                <Text style={[styles.verifyButtonText, { color: theme.colors.surface }]}>
                  {loading ? 'Doğrulanıyor...' : 'Authenticator\'ı Etkinleştir'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.cancelButton]}
                onPress={handleCancelAuthenticatorSetup}
                disabled={loading}
              >
                <Text style={[styles.cancelButtonText, { color: theme.colors.error }]}>
                  İptal
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Backup Codes Display */}
        {showBackupCodes && authenticatorSetup && (
          <View style={styles.backupCodesContainer}>
            <Text style={[styles.backupCodesTitle, { color: theme.colors.text }]}>
              🔑 Backup Kodları
            </Text>
            <Text style={[styles.backupCodesDescription, { color: theme.colors.textSecondary }]}>
              Bu kodları güvenli bir yerde saklayın. Authenticator uygulamanıza erişiminizi kaybederseniz kullanabilirsiniz.
            </Text>
            <View style={styles.backupCodesGrid}>
              {authenticatorSetup.backupCodes.map((code, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.backupCodeItem, { backgroundColor: theme.colors.background, borderColor: theme.colors.border }]}
                  onPress={() => handleCopyToClipboard(code)}
                >
                  <Text style={[styles.backupCodeText, { color: theme.colors.text }]}>
                    {code}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            <TouchableOpacity
              style={[styles.hideBackupButton, { borderColor: theme.colors.border }]}
              onPress={() => setShowBackupCodes(false)}
            >
              <Text style={[styles.hideBackupButtonText, { color: theme.colors.primary }]}>
                Kodları Gizle
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Authenticator Status */}
        {twoFactorSettings.authenticatorEnabled && !isAuthenticatorSetupPending && (
          <View style={styles.authenticatorStatusContainer}>
            <Text style={[styles.statusText, { color: theme.colors.success }]}>
              ✅ Authenticator aktif
            </Text>
            <TouchableOpacity
              style={[styles.showBackupButton, { borderColor: theme.colors.border }]}
              onPress={async () => {
                const codes = await TwoFactorService.getInstance().getRemainingBackupCodes();
                if (codes.length > 0) {
                  setAuthenticatorSetup({ ...authenticatorSetup!, backupCodes: codes });
                  setShowBackupCodes(true);
                } else {
                  Alert.alert('Bilgi', 'Backup kodları bulunamadı');
                }
              }}
            >
              <Text style={[styles.showBackupButtonText, { color: theme.colors.primary }]}>
                Backup Kodları Göster
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Trusted Devices */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons name="shield-checkmark" size={24} color={theme.colors.primary} />
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Güvenilir Cihazlar</Text>
          </View>
          <TouchableOpacity
            style={[styles.manageButton, { borderColor: theme.colors.border }]}
            onPress={() => navigation.navigate('TrustedDevices' as never)}
          >
            <Text style={[styles.manageButtonText, { color: theme.colors.primary }]}>
              Yönet
            </Text>
          </TouchableOpacity>
        </View>

        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Güvenilir cihazlarda 2FA doğrulaması atlanır. Cihazlar 30 gün sonra otomatik olarak listeden çıkarılır.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 16,
  },
  infoCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 8,
  },
  emailText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 16,
  },
  comingSoon: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  verificationContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  verificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  codeInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 4,
    marginBottom: 12,
  },
  timerText: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  attemptsText: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 16,
  },
  verificationButtons: {
    gap: 8,
  },
  verifyButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  resendButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  resendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButton: {
    padding: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
  },
  // SMS specific styles
  phoneInputContainer: {
    marginTop: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  phoneInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  phoneText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  // Authenticator specific styles
  authenticatorSetupContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  setupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  setupDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 16,
  },
  qrCodeContainer: {
    alignItems: 'center',
    padding: 20,
    marginBottom: 16,
  },
  manualKeyContainer: {
    marginBottom: 16,
  },
  manualKeyLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  manualKeyBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  manualKeyText: {
    fontSize: 14,
    fontFamily: 'monospace',
    flex: 1,
  },
  backupCodesContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F59E0B',
    backgroundColor: '#FEF3C7',
  },
  backupCodesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  backupCodesDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 16,
  },
  backupCodesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  backupCodeItem: {
    width: '48%',
    padding: 8,
    borderWidth: 1,
    borderRadius: 4,
    alignItems: 'center',
  },
  backupCodeText: {
    fontSize: 12,
    fontFamily: 'monospace',
    fontWeight: 'bold',
  },
  hideBackupButton: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  hideBackupButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  authenticatorStatusContainer: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#F0FDF4',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  showBackupButton: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  showBackupButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Trusted devices styles
  manageButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderRadius: 6,
  },
  manageButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default TwoFactorSetupScreen;
