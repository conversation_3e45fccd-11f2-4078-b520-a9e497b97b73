// Investment Detail Screen - Yatırım detay ekranı

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface RouteParams {
  investmentId: string;
}

const InvestmentDetailScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { investmentId } = route.params as RouteParams;

  // Mock investment data - replace with real API call
  const investment = {
    id: investmentId,
    name: 'Apple Inc.',
    symbol: 'AAPL',
    type: 'stock',
    description: 'Apple Inc. teknoloji şirketi, iPhone, iPad, Mac ve diğer tüketici elektroniği ürünleri tasarlar ve üretir.',
    riskLevel: 'medium',
    expectedReturn: 12.5,
    minimumInvestment: 1000,
    currency: 'USD',
    provider: 'Nasdaq',
    performance: {
      oneMonth: 2.5,
      threeMonths: 8.2,
      sixMonths: 15.7,
      oneYear: 22.3,
      threeYears: 45.8,
    },
    fees: {
      managementFee: 0.5,
      transactionFee: 5,
      performanceFee: 1.0,
    },
    tags: ['Teknoloji', 'Büyük Şirket', 'Temettü', 'S&P 500'],
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return theme.colors.success;
      case 'medium': return theme.colors.warning;
      case 'high': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getRiskText = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'Düşük Risk';
      case 'medium': return 'Orta Risk';
      case 'high': return 'Yüksek Risk';
      default: return 'Bilinmiyor';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'stock': return 'Hisse Senedi';
      case 'bond': return 'Tahvil';
      case 'fund': return 'Fon';
      case 'etf': return 'ETF';
      case 'crypto': return 'Kripto Para';
      case 'commodity': return 'Emtia';
      case 'real_estate': return 'Gayrimenkul';
      default: return type;
    }
  };

  const handleInvest = () => {
    Alert.alert(
      'Yatırım Yap',
      `${investment?.name} için yatırım yapmak istiyor musunuz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Yatırım Yap',
          onPress: () => {
            // TODO: Yatırım yapma işlemi
            Alert.alert('Bilgi', 'Yatırım özelliği yakında eklenecek.');
          }
        }
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 24,
      paddingBottom: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerInfo: {
      flex: 1,
    },
    investmentName: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    investmentSymbol: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '500',
      marginBottom: 2,
    },
    investmentType: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    performanceContainer: {
      alignItems: 'flex-end',
    },
    performanceValue: {
      fontSize: 24,
      fontWeight: 'bold',
    },
    performanceLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    description: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    infoGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    infoItem: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    infoLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    infoValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    performanceGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    performanceItem: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginBottom: 8,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    feesContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    feeItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    feeLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    feeValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    tag: {
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 16,
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
    tagText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    actionContainer: {
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    investButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
    },
    investButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  if (!investment) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: theme.colors.text }}>Yatırım bulunamadı</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerInfo}>
            <Text style={styles.investmentName}>{investment.name}</Text>
            <Text style={styles.investmentSymbol}>{investment.symbol}</Text>
            <Text style={styles.investmentType}>{getTypeText(investment.type)}</Text>
          </View>
          <View style={styles.performanceContainer}>
            <Text style={[
              styles.performanceValue,
              { color: investment.performance.oneYear >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {investment.performance.oneYear >= 0 ? '+' : ''}{investment.performance.oneYear.toFixed(1)}%
            </Text>
            <Text style={styles.performanceLabel}>1 Yıl</Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Açıklama</Text>
          <Text style={styles.description}>{investment.description}</Text>
        </View>

        {/* Key Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Temel Bilgiler</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Risk Seviyesi</Text>
              <Text style={[styles.infoValue, { color: getRiskColor(investment.riskLevel) }]}>
                {getRiskText(investment.riskLevel)}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Beklenen Getiri</Text>
              <Text style={styles.infoValue}>%{investment.expectedReturn.toFixed(1)}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Min. Yatırım</Text>
              <Text style={styles.infoValue}>₺{investment.minimumInvestment}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Para Birimi</Text>
              <Text style={styles.infoValue}>{investment.currency}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Sağlayıcı</Text>
              <Text style={styles.infoValue}>{investment.provider}</Text>
            </View>
          </View>
        </View>

        {/* Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performans</Text>
          <View style={styles.performanceGrid}>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>1 Ay</Text>
              <Text style={[
                styles.performanceValue,
                { color: investment.performance.oneMonth >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {investment.performance.oneMonth >= 0 ? '+' : ''}{investment.performance.oneMonth.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>3 Ay</Text>
              <Text style={[
                styles.performanceValue,
                { color: investment.performance.threeMonths >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {investment.performance.threeMonths >= 0 ? '+' : ''}{investment.performance.threeMonths.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>6 Ay</Text>
              <Text style={[
                styles.performanceValue,
                { color: investment.performance.sixMonths >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {investment.performance.sixMonths >= 0 ? '+' : ''}{investment.performance.sixMonths.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>1 Yıl</Text>
              <Text style={[
                styles.performanceValue,
                { color: investment.performance.oneYear >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {investment.performance.oneYear >= 0 ? '+' : ''}{investment.performance.oneYear.toFixed(1)}%
              </Text>
            </View>
            {investment.performance.threeYears && (
              <View style={styles.performanceItem}>
                <Text style={styles.performanceLabel}>3 Yıl</Text>
                <Text style={[
                  styles.performanceValue,
                  { color: investment.performance.threeYears >= 0 ? theme.colors.success : theme.colors.error }
                ]}>
                  {investment.performance.threeYears >= 0 ? '+' : ''}{investment.performance.threeYears.toFixed(1)}%
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Fees */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ücretler</Text>
          <View style={styles.feesContainer}>
            {investment.fees.managementFee && (
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>Yönetim Ücreti</Text>
                <Text style={styles.feeValue}>%{investment.fees.managementFee}</Text>
              </View>
            )}
            {investment.fees.transactionFee && (
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>İşlem Ücreti</Text>
                <Text style={styles.feeValue}>₺{investment.fees.transactionFee}</Text>
              </View>
            )}
            {investment.fees.performanceFee && (
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>Performans Ücreti</Text>
                <Text style={styles.feeValue}>%{investment.fees.performanceFee}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Tags */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Etiketler</Text>
          <View style={styles.tagsContainer}>
            {investment.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={styles.investButton}
          onPress={handleInvest}
        >
          <Ionicons name="trending-up" size={20} color={theme.colors.surface} />
          <Text style={styles.investButtonText}>Yatırım Yap</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default InvestmentDetailScreen;
