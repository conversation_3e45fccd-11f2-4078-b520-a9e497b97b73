// ML Auto-categorization Service - Akıllı Kategori Tahmini

export interface CategoryPrediction {
  category: string;
  confidence: number;
  reason: string;
  subcategory?: string;
}

export interface MLCategorizationResult {
  predictions: CategoryPrediction[];
  selectedCategory: string;
  confidence: number;
  learningData?: {
    merchantPattern: string;
    amountRange: string;
    timePattern: string;
  };
}

export interface TransactionPattern {
  description: string;
  merchant?: string;
  amount: number;
  category: string;
  frequency: number;
  lastUsed: string;
}

class MLCategorizationService {
  
  /**
   * Ana kategori tahmini fonksiyonu
   */
  static async predictCategory(
    description: string,
    merchant?: string,
    amount?: number,
    userId?: string
  ): Promise<MLCategorizationResult> {
    try {
      console.log('🤖 ML: Starting category prediction for:', description);
      
      // Farklı ML yöntemlerini kombine et
      const predictions: CategoryPrediction[] = [];
      
      // 1. Merchant-based prediction (en yüksek öncelik)
      if (merchant) {
        const merchantPrediction = this.predictByMerchant(merchant);
        if (merchantPrediction) {
          predictions.push(merchantPrediction);
        }
      }
      
      // 2. Keyword-based prediction
      const keywordPrediction = this.predictByKeywords(description);
      if (keywordPrediction) {
        predictions.push(keywordPrediction);
      }
      
      // 3. Amount-based prediction
      if (amount) {
        const amountPrediction = this.predictByAmount(amount, description);
        if (amountPrediction) {
          predictions.push(amountPrediction);
        }
      }
      
      // 4. User behavior learning (gelecekte implement edilecek)
      if (userId) {
        const userPrediction = await this.predictByUserBehavior(userId, description, merchant);
        if (userPrediction) {
          predictions.push(userPrediction);
        }
      }
      
      // 5. Pattern matching
      const patternPrediction = this.predictByPatterns(description, amount);
      if (patternPrediction) {
        predictions.push(patternPrediction);
      }
      
      // En iyi tahmini seç
      const bestPrediction = this.selectBestPrediction(predictions);
      
      console.log('🎯 ML: Best prediction:', bestPrediction);
      
      return {
        predictions,
        selectedCategory: bestPrediction.category,
        confidence: bestPrediction.confidence,
        learningData: {
          merchantPattern: merchant || 'unknown',
          amountRange: this.getAmountRange(amount || 0),
          timePattern: new Date().toISOString()
        }
      };
      
    } catch (error) {
      console.error('❌ ML: Category prediction error:', error);
      
      // Fallback to basic categorization
      return {
        predictions: [{
          category: 'other',
          confidence: 0.3,
          reason: 'Fallback categorization due to error'
        }],
        selectedCategory: 'other',
        confidence: 0.3
      };
    }
  }
  
  /**
   * Merchant bazlı kategori tahmini
   */
  private static predictByMerchant(merchant: string): CategoryPrediction | null {
    const merchantLower = merchant.toLowerCase();
    
    // RFC-003 Code Bazlı Merchant Patterns
    const merchantPatterns = {
      // Yiyecek & İçecek
      food: [
        'migros', 'carrefour', 'bim', 'a101', 'şok', 'metro', 'real',
        'market', 'süpermarket', 'bakkal', 'manav', 'kasap', 'balık',
        'ekmek', 'fırın', 'pastane', 'şarküteri', 'starbucks', 'mcdonald',
        'burger king', 'kfc', 'dominos', 'pizza', 'restoran', 'kafe',
        'kahve', 'yemek', 'lokanta', 'döner', 'kebap', 'pide', 'lahmacun'
      ],

      // Ulaşım
      transportation: [
        'shell', 'bp', 'petrol ofisi', 'opet', 'total', 'lukoil',
        'benzin', 'motorin', 'lpg', 'akaryakıt', 'istasyon',
        'uber', 'bitaksi', 'taksi', 'otobüs', 'metro', 'dolmuş',
        'bilet', 'akbil', 'istanbulkart', 'ulaşım', 'park',
        'otopark', 'köprü', 'geçiş'
      ],

      // Faturalar
      bills: [
        'elektrik', 'su', 'doğalgaz', 'telefon', 'internet',
        'fatura', 'aidat', 'vergi', 'harç', 'belediye'
      ],

      // Alışveriş
      shopping: [
        'zara', 'h&m', 'lcw', 'koton', 'defacto', 'mango',
        'nike', 'adidas', 'teknosa', 'vatan', 'media markt',
        'giyim', 'ayakkabı', 'çanta', 'aksesuar', 'elektronik'
      ],

      // Sağlık
      health: [
        'eczane', 'hastane', 'doktor', 'diş', 'göz', 'sağlık',
        'ilaç', 'vitamin', 'tıbbi', 'muayene', 'tahlil'
      ],

      // Eğlence
      entertainment: [
        'sinema', 'tiyatro', 'konser', 'müze', 'park', 'lunapark',
        'oyun', 'netflix', 'spotify', 'youtube', 'eğlence'
      ],

      // Eğitim
      education: [
        'okul', 'üniversite', 'kurs', 'kitap', 'eğitim', 'ders',
        'sertifika', 'akademi', 'dil kursu'
      ],

      // Kira
      rent: [
        'kira', 'emlak', 'gayrimenkul', 'ev kirası', 'ofis kirası'
      ],

      // Sigorta
      insurance: [
        'sigorta', 'axa', 'allianz', 'aksigorta', 'mapfre', 'güneş sigorta'
      ],

      // Kişisel Bakım
      personal_care: [
        'kuaför', 'berber', 'güzellik', 'spa', 'masaj', 'kozmetik',
        'parfüm', 'makyaj'
      ],

      // Seyahat
      travel: [
        'otel', 'uçak', 'havayolu', 'thy', 'pegasus', 'tatil',
        'seyahat', 'tur', 'vize', 'pasaport'
      ]
    };
    
    for (const [category, patterns] of Object.entries(merchantPatterns)) {
      for (const pattern of patterns) {
        if (merchantLower.includes(pattern)) {
          return {
            category,
            confidence: 0.9,
            reason: `Merchant pattern match: ${pattern}`,
            subcategory: this.getSubcategory(category, pattern)
          };
        }
      }
    }
    
    return null;
  }
  
  /**
   * Anahtar kelime bazlı kategori tahmini
   */
  private static predictByKeywords(description: string): CategoryPrediction | null {
    const descLower = description.toLowerCase();
    
    const keywordPatterns = {
      food: [
        'ekmek', 'süt', 'peynir', 'et', 'tavuk', 'balık', 'sebze',
        'meyve', 'yumurta', 'yoğurt', 'tereyağı', 'zeytinyağı',
        'pirinç', 'makarna', 'bulgur', 'un', 'şeker', 'tuz',
        'yemek', 'kahvaltı', 'öğle', 'akşam', 'içecek', 'çay',
        'kahve', 'su', 'meze', 'ana yemek', 'tatlı'
      ],

      transportation: [
        'benzin', 'motorin', 'lpg', 'yakıt', 'depo', 'litre',
        'bilet', 'yolculuk', 'taksi', 'otobüs', 'uçak', 'tren',
        'gemi', 'park ücreti', 'köprü geçiş'
      ],

      bills: [
        'elektrik faturası', 'su faturası', 'gaz faturası',
        'telefon faturası', 'internet', 'aidat', 'vergi'
      ],

      shopping: [
        'giysi', 'pantolon', 'gömlek', 'elbise', 'ayakkabı',
        'çanta', 'saat', 'telefon', 'bilgisayar', 'elektronik'
      ],

      entertainment: [
        'sinema', 'tiyatro', 'konser', 'oyun', 'eğlence',
        'netflix', 'spotify', 'müzik', 'film'
      ],

      health: [
        'ilaç', 'vitamin', 'doktor', 'muayene', 'tahlil',
        'ameliyat', 'tedavi', 'sağlık', 'diş'
      ],

      education: [
        'kitap', 'kurs', 'eğitim', 'ders', 'okul', 'üniversite',
        'sertifika', 'öğrenim'
      ],

      rent: [
        'kira', 'ev kirası', 'ofis kirası', 'dükkan kirası'
      ],

      insurance: [
        'sigorta', 'prim', 'kasko', 'trafik sigortası'
      ],

      personal_care: [
        'kuaför', 'berber', 'güzellik', 'kozmetik', 'parfüm'
      ],

      travel: [
        'otel', 'tatil', 'seyahat', 'tur', 'vize', 'pasaport'
      ],

      gifts: [
        'hediye', 'doğum günü', 'düğün', 'bayram'
      ],

      donation: [
        'bağış', 'yardım', 'zakat', 'sadaka'
      ],

      tax: [
        'vergi', 'gelir vergisi', 'kdv', 'mtv', 'emlak vergisi'
      ],

      housing: [
        'tadilat', 'mobilya', 'ev eşyası', 'dekorasyon'
      ]
    };
    
    let bestMatch = { category: '', confidence: 0, matchedKeyword: '' };
    
    for (const [category, keywords] of Object.entries(keywordPatterns)) {
      for (const keyword of keywords) {
        if (descLower.includes(keyword)) {
          const confidence = keyword.length > 5 ? 0.8 : 0.6; // Longer keywords = higher confidence
          if (confidence > bestMatch.confidence) {
            bestMatch = { category, confidence, matchedKeyword: keyword };
          }
        }
      }
    }
    
    if (bestMatch.confidence > 0) {
      return {
        category: bestMatch.category,
        confidence: bestMatch.confidence,
        reason: `Keyword match: ${bestMatch.matchedKeyword}`
      };
    }
    
    return null;
  }
  
  /**
   * Tutar bazlı kategori tahmini
   */
  private static predictByAmount(amount: number, description: string): CategoryPrediction | null {
    // Türkiye TL bazında tipik tutarlar - RFC-003 Code Bazlı
    const amountPatterns = {
      food: { min: 10, max: 500, confidence: 0.4 },
      transportation: { min: 5, max: 2000, confidence: 0.4 },
      bills: { min: 50, max: 1000, confidence: 0.5 },
      shopping: { min: 50, max: 5000, confidence: 0.3 },
      entertainment: { min: 20, max: 500, confidence: 0.3 },
      health: { min: 20, max: 1000, confidence: 0.3 },
      education: { min: 100, max: 5000, confidence: 0.4 },
      rent: { min: 1000, max: 20000, confidence: 0.7 },
      insurance: { min: 100, max: 2000, confidence: 0.5 },
      personal_care: { min: 50, max: 1000, confidence: 0.4 },
      travel: { min: 500, max: 10000, confidence: 0.4 },
      gifts: { min: 50, max: 2000, confidence: 0.3 },
      tax: { min: 100, max: 50000, confidence: 0.5 },
      housing: { min: 200, max: 10000, confidence: 0.4 }
    };
    
    for (const [category, pattern] of Object.entries(amountPatterns)) {
      if (amount >= pattern.min && amount <= pattern.max) {
        return {
          category,
          confidence: pattern.confidence,
          reason: `Amount range match: ${pattern.min}-${pattern.max} TL`
        };
      }
    }
    
    return null;
  }
  
  /**
   * Kullanıcı davranışı bazlı tahmin (gelecekte implement edilecek)
   */
  private static async predictByUserBehavior(
    userId: string,
    description: string,
    merchant?: string
  ): Promise<CategoryPrediction | null> {
    // Bu kısım kullanıcı transaction history'si analiz edilerek implement edilecek
    // Şimdilik null döndür
    return null;
  }
  
  /**
   * Pattern matching bazlı tahmin
   */
  private static predictByPatterns(description: string, amount?: number): CategoryPrediction | null {
    const descLower = description.toLowerCase();
    
    // Fiş numarası pattern'i - RFC-003 Code Bazlı
    if (descLower.includes('fiş no') || descLower.includes('receipt')) {
      if (amount && amount > 1000) {
        return {
          category: 'transportation', // Yakıt genelde transportation altında
          confidence: 0.6,
          reason: 'Receipt pattern + high amount suggests fuel/transportation'
        };
      } else if (amount && amount < 200) {
        return {
          category: 'food',
          confidence: 0.5,
          reason: 'Receipt pattern + low amount suggests food'
        };
      }
    }

    // Zaman pattern'i
    const hour = new Date().getHours();
    if (hour >= 6 && hour <= 10) {
      return {
        category: 'food',
        confidence: 0.3,
        reason: 'Morning time suggests breakfast/coffee'
      };
    }
    
    return null;
  }
  
  /**
   * En iyi tahmini seç
   */
  private static selectBestPrediction(predictions: CategoryPrediction[]): CategoryPrediction {
    if (predictions.length === 0) {
      return {
        category: 'other',
        confidence: 0.3,
        reason: 'No predictions available'
      };
    }
    
    // En yüksek confidence'a sahip tahmini seç
    return predictions.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );
  }
  
  /**
   * Alt kategori belirle
   */
  private static getSubcategory(category: string, pattern: string): string | undefined {
    const subcategories: { [key: string]: { [key: string]: string } } = {
      groceries: {
        'market': 'supermarket',
        'bakkal': 'local_store',
        'fırın': 'bakery',
        'kasap': 'butcher',
        'manav': 'greengrocer'
      },
      dining: {
        'kafe': 'cafe',
        'restoran': 'restaurant',
        'pizza': 'fast_food',
        'döner': 'fast_food'
      },
      fuel: {
        'shell': 'gas_station',
        'bp': 'gas_station',
        'petrol': 'gas_station'
      }
    };
    
    return subcategories[category]?.[pattern];
  }
  
  /**
   * Tutar aralığı belirle
   */
  private static getAmountRange(amount: number): string {
    if (amount < 50) return 'low';
    if (amount < 200) return 'medium';
    if (amount < 500) return 'high';
    return 'very_high';
  }
  
  /**
   * Kullanıcı feedback'i ile öğrenme (gelecekte implement edilecek)
   */
  static async learnFromUserFeedback(
    userId: string,
    description: string,
    merchant: string,
    predictedCategory: string,
    actualCategory: string,
    confidence: number
  ): Promise<void> {
    // Bu kısım kullanıcı düzeltmeleri ile ML modelini geliştirmek için kullanılacak
    console.log('📚 ML: Learning from user feedback:', {
      userId,
      description,
      merchant,
      predictedCategory,
      actualCategory,
      confidence
    });
    
    // Gelecekte database'e kaydet ve model'i güncelle
  }
  
  /**
   * Kategori önerisi al
   */
  static async getSuggestedCategories(
    description: string,
    merchant?: string,
    amount?: number
  ): Promise<CategoryPrediction[]> {
    const result = await this.predictCategory(description, merchant, amount);
    return result.predictions.slice(0, 3); // En iyi 3 öneri
  }
}

export default MLCategorizationService;
