// Goal Slice - Finansal hedef state yönetimi

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { FinancialGoal } from '../../types';

interface GoalState {
  goals: FinancialGoal[];
  isLoading: boolean;
  error: string | null;
}

const initialState: GoalState = {
  goals: [],
  isLoading: false,
  error: null,
};

export const fetchGoals = createAsyncThunk(
  'goals/fetchGoals',
  async (_, { rejectWithValue }) => {
    try {
      const mockGoals: FinancialGoal[] = [
        {
          id: '1',
          userId: '1',
          title: 'Acil Durum Fonu',
          description: '6 aylık gider karşılığı acil durum fonu',
          targetAmount: 30000,
          currentAmount: 15000,
          targetDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          category: 'emergency',
          priority: 'high',
          isCompleted: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      return mockGoals;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Hedefler yüklenirken hata oluştu');
    }
  }
);

export const addGoal = createAsyncThunk(
  'goals/addGoal',
  async (goalData: Omit<FinancialGoal, 'id' | 'userId' | 'currentAmount' | 'isCompleted' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const newGoal: FinancialGoal = {
        ...goalData,
        id: Date.now().toString(),
        userId: '1',
        currentAmount: 0,
        isCompleted: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      return newGoal;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Hedef eklenirken hata oluştu');
    }
  }
);

export const updateGoal = createAsyncThunk(
  'goals/updateGoal',
  async (params: { id: string; data: Partial<FinancialGoal> }, { rejectWithValue }) => {
    try {
      const updatedGoal: FinancialGoal = {
        ...params.data,
        id: params.id,
        updatedAt: new Date().toISOString(),
      } as FinancialGoal;
      return updatedGoal;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Hedef güncellenirken hata oluştu');
    }
  }
);

export const deleteGoal = createAsyncThunk(
  'goals/deleteGoal',
  async (goalId: string, { rejectWithValue }) => {
    try {
      return goalId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Hedef silinirken hata oluştu');
    }
  }
);

const goalSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchGoals.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchGoals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.goals = action.payload;
      })
      .addCase(fetchGoals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(addGoal.fulfilled, (state, action) => {
        state.goals.push(action.payload);
      })
      .addCase(updateGoal.fulfilled, (state, action) => {
        const index = state.goals.findIndex(g => g.id === action.payload.id);
        if (index !== -1) {
          state.goals[index] = action.payload;
        }
      })
      .addCase(deleteGoal.fulfilled, (state, action) => {
        state.goals = state.goals.filter(g => g.id !== action.payload);
      });
  },
});

export const { clearError } = goalSlice.actions;
export default goalSlice.reducer;
