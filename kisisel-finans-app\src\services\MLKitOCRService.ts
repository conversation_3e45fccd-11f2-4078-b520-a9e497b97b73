// ML Kit OCR Service - Offline gerçek OCR

// ML Kit sadece development build'de çalışır, Expo Go'da çalışmaz
// import { recognize } from '@react-native-ml-kit/text-recognition';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';

export interface MLKitOCRResult {
  success: boolean;
  text?: string;
  blocks?: TextBlock[];
  confidence?: number;
  error?: string;
}

export interface TextBlock {
  text: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  confidence: number;
}

class MLKitOCRService {
  /**
   * ML Kit ile gerçek OCR - Offline çalışır
   */
  static async recognizeText(imageUri: string): Promise<MLKitOCRResult> {
    try {
      console.log('🔍 ML Kit: Starting real OCR recognition for:', imageUri);

      // Görüntüyü optimize et
      const optimizedImage = await this.optimizeImageForMLKit(imageUri);
      if (!optimizedImage) {
        return {
          success: false,
          error: 'Image optimization failed'
        };
      }

      console.log('📊 ML Kit: Processing optimized image...');

      // ML Kit Text Recognition - Expo Go'da çalışmaz
      // const result = await recognize(optimizedImage);

      // Expo Go'da ML Kit çalışmadığı için hata fırlat
      throw new Error('ML Kit is not supported in Expo Go. Use development build.');

      if (result && result.text) {
        console.log('✅ ML Kit: Text recognition successful');
        console.log('📄 ML Kit: Recognized text length:', result.text.length);
        console.log('📄 ML Kit: First 100 chars:', result.text.substring(0, 100));

        // Text blocks'ları parse et
        const blocks: TextBlock[] = [];
        if (result.blocks) {
          result.blocks.forEach(block => {
            if (block.text && block.frame) {
              blocks.push({
                text: block.text,
                boundingBox: {
                  x: block.frame.x || 0,
                  y: block.frame.y || 0,
                  width: block.frame.width || 0,
                  height: block.frame.height || 0
                },
                confidence: block.confidence || 0.8
              });
            }
          });
        }

        // Genel confidence hesapla
        const avgConfidence = blocks.length > 0
          ? blocks.reduce((sum, block) => sum + block.confidence, 0) / blocks.length
          : 0.8;

        return {
          success: true,
          text: result.text,
          blocks,
          confidence: avgConfidence
        };

      } else {
        console.log('❌ ML Kit: No text recognized');
        return {
          success: false,
          error: 'No text found in image'
        };
      }

    } catch (error) {
      console.error('❌ ML Kit OCR Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'ML Kit OCR failed'
      };
    }
  }

  /**
   * Görüntüyü ML Kit için optimize et
   */
  private static async optimizeImageForMLKit(imageUri: string): Promise<string | null> {
    try {
      console.log('🖼️ ML Kit: Optimizing image for OCR...');

      // Dosya bilgilerini kontrol et
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        throw new Error('Image file not found');
      }

      console.log('📊 ML Kit: Original image size:', Math.round(fileInfo.size! / 1024), 'KB');

      // ML Kit için optimal ayarlar
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          // Görüntüyü yeniden boyutlandır (ML Kit için optimal)
          { resize: { width: 1600 } }, // ML Kit için daha yüksek çözünürlük
        ],
        {
          compress: 0.9, // Yüksek kalite (ML Kit için)
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );

      console.log('✅ ML Kit: Image optimized:', manipulatedImage.uri);

      // Optimize edilmiş dosya boyutunu kontrol et
      const optimizedFileInfo = await FileSystem.getInfoAsync(manipulatedImage.uri);
      if (optimizedFileInfo.exists && optimizedFileInfo.size) {
        console.log('📊 ML Kit: Optimized size:', Math.round(optimizedFileInfo.size / 1024), 'KB');
      }

      return manipulatedImage.uri;

    } catch (error) {
      console.error('❌ ML Kit: Image optimization error:', error);
      return imageUri; // Fallback to original
    }
  }

  /**
   * OCR sonucunu fiş verisi olarak parse et
   */
  static parseReceiptFromMLKit(mlkitResult: MLKitOCRResult): {
    amount?: number;
    merchant?: string;
    date?: string;
    items?: Array<{ name: string; price: number }>;
    taxAmount?: number;
    receiptNumber?: string;
    paymentMethod?: string;
    confidence: number;
  } {
    if (!mlkitResult.success || !mlkitResult.text) {
      return { confidence: 0 };
    }

    const text = mlkitResult.text;
    console.log('📄 ML Kit: Parsing receipt data from text...');

    const result: any = {
      confidence: mlkitResult.confidence || 0.8
    };

    try {
      // Tutar çıkarma - Türkçe fiş formatları
      const amountPatterns = [
        /(?:TOPLAM|TOTAL|GENEL TOPLAM|GRAND TOTAL)[\s:]*(\d+[,.]?\d*)\s*(?:TL|₺)/gi,
        /(\d+[,.]?\d*)\s*(?:TL|₺)\s*(?:TOPLAM|TOTAL)/gi,
        /(?:AMOUNT|TUTAR)[\s:]*(\d+[,.]?\d*)/gi
      ];

      for (const pattern of amountPatterns) {
        const matches = text.match(pattern);
        if (matches && matches.length > 0) {
          const amountStr = matches[0].match(/(\d+[,.]?\d*)/)?.[0];
          if (amountStr) {
            result.amount = parseFloat(amountStr.replace(',', '.'));
            console.log('💰 ML Kit: Found amount:', result.amount);
            break;
          }
        }
      }

      // Merchant/Mağaza adı çıkarma
      const lines = text.split('\n').filter(line => line.trim());
      if (lines.length > 0) {
        // İlk birkaç satırdan mağaza adını bul
        for (let i = 0; i < Math.min(5, lines.length); i++) {
          const line = lines[i].trim();
          // Mağaza adı kriterleri: 2-30 karakter, çok sayı içermemeli
          if (line.length >= 2 && line.length <= 30 && !line.match(/\d{3,}/) && !line.match(/[./-]{2,}/)) {
            result.merchant = line;
            console.log('🏪 ML Kit: Found merchant:', result.merchant);
            break;
          }
        }
      }

      // Tarih çıkarma
      const datePatterns = [
        /(?:TARİH|DATE|TARIH)[\s:]*(\d{1,2}[./-]\d{1,2}[./-]\d{2,4})/gi,
        /(\d{1,2}[./-]\d{1,2}[./-]\d{4})/g,
        /(\d{2}[./-]\d{2}[./-]\d{2})/g
      ];

      for (const pattern of datePatterns) {
        const matches = text.match(pattern);
        if (matches && matches.length > 0) {
          const dateStr = matches[0].match(/(\d{1,2}[./-]\d{1,2}[./-]\d{2,4})/)?.[0];
          if (dateStr) {
            result.date = this.parseDate(dateStr);
            console.log('📅 ML Kit: Found date:', result.date);
            break;
          }
        }
      }

      // KDV çıkarma
      const taxPatterns = [
        /(?:KDV|VAT|TAX)[\s:]*(\d+[,.]?\d*)/gi,
        /(\d+[,.]?\d*)\s*(?:KDV|VAT)/gi
      ];

      for (const pattern of taxPatterns) {
        const matches = text.match(pattern);
        if (matches && matches.length > 0) {
          const taxStr = matches[0].match(/(\d+[,.]?\d*)/)?.[0];
          if (taxStr) {
            result.taxAmount = parseFloat(taxStr.replace(',', '.'));
            console.log('💸 ML Kit: Found tax:', result.taxAmount);
            break;
          }
        }
      }

      // Fiş numarası
      const receiptPatterns = [
        /(?:FİŞ NO|RECEIPT|NO|FIŞ)[\s:]*(\d+)/gi,
        /(?:REF|REFERENCE)[\s:]*(\d+)/gi
      ];

      for (const pattern of receiptPatterns) {
        const matches = text.match(pattern);
        if (matches && matches.length > 0) {
          const numberStr = matches[0].match(/(\d+)/)?.[0];
          if (numberStr) {
            result.receiptNumber = numberStr;
            console.log('🧾 ML Kit: Found receipt number:', result.receiptNumber);
            break;
          }
        }
      }

      // Ödeme yöntemi
      if (text.match(/NAKİT|CASH|NAKIT/gi)) {
        result.paymentMethod = 'cash';
        console.log('💵 ML Kit: Payment method: cash');
      } else if (text.match(/KART|CARD|VISA|MASTER/gi)) {
        result.paymentMethod = 'credit_card';
        console.log('💳 ML Kit: Payment method: card');
      }

      // Ürün listesi çıkarma (basit)
      const items: Array<{ name: string; price: number }> = [];
      const itemLines = lines.filter(line => {
        // Ürün satırı kriterleri: fiyat içeren satırlar
        return line.match(/\d+[,.]?\d*\s*(?:TL|₺)/) &&
               !line.match(/(?:TOPLAM|TOTAL|KDV|VAT)/gi) &&
               line.length > 5;
      });

      itemLines.forEach(line => {
        const priceMatch = line.match(/(\d+[,.]?\d*)\s*(?:TL|₺)/);
        if (priceMatch) {
          const price = parseFloat(priceMatch[1].replace(',', '.'));
          const name = line.replace(/\d+[,.]?\d*\s*(?:TL|₺).*/, '').trim();
          if (name.length > 1 && price > 0) {
            items.push({ name, price });
          }
        }
      });

      if (items.length > 0) {
        result.items = items;
        console.log('🛒 ML Kit: Found', items.length, 'items');
      }

      console.log('✅ ML Kit: Receipt parsing completed');
      return result;

    } catch (error) {
      console.error('❌ ML Kit: Receipt parsing error:', error);
      return { confidence: 0.3 };
    }
  }

  /**
   * Tarih parse et
   */
  private static parseDate(dateStr: string): string {
    try {
      const parts = dateStr.split(/[./-]/);
      if (parts.length === 3) {
        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const year = parseInt(parts[2]);

        const fullYear = year < 100 ? (year > 50 ? 1900 + year : 2000 + year) : year;
        const date = new Date(fullYear, month, day);
        return date.toISOString();
      }
    } catch (error) {
      console.error('❌ ML Kit: Date parsing error:', error);
    }

    return new Date().toISOString();
  }

  /**
   * ML Kit OCR kalitesini değerlendir
   */
  static evaluateOCRQuality(result: MLKitOCRResult): {
    quality: 'excellent' | 'good' | 'fair' | 'poor';
    score: number;
    suggestions: string[];
  } {
    if (!result.success || !result.text) {
      return {
        quality: 'poor',
        score: 0,
        suggestions: ['Görüntü okunamadı. Daha net bir fotoğraf çekin.']
      };
    }

    const suggestions: string[] = [];
    let score = 0;

    // Text uzunluğu kontrolü
    if (result.text.length > 50) score += 25;
    else suggestions.push('Metin çok kısa. Fişin tamamını çekin.');

    // Confidence kontrolü
    if (result.confidence && result.confidence > 0.8) score += 25;
    else if (result.confidence && result.confidence > 0.6) score += 15;
    else suggestions.push('OCR güven skoru düşük. Daha iyi ışıkta çekin.');

    // Sayı varlığı kontrolü
    if (result.text.match(/\d+/g)) score += 25;
    else suggestions.push('Fiyat bilgisi bulunamadı.');

    // Türkçe fiş kelimeleri kontrolü
    const turkishWords = ['TOPLAM', 'KDV', 'FİŞ', 'TARİH', 'TEŞEKKÜR'];
    const foundWords = turkishWords.filter(word => result.text.includes(word));
    if (foundWords.length > 0) score += 25;
    else suggestions.push('Fiş formatı tanınmadı.');

    let quality: 'excellent' | 'good' | 'fair' | 'poor';
    if (score >= 80) quality = 'excellent';
    else if (score >= 60) quality = 'good';
    else if (score >= 40) quality = 'fair';
    else quality = 'poor';

    return { quality, score, suggestions };
  }
}

export default MLKitOCRService;
