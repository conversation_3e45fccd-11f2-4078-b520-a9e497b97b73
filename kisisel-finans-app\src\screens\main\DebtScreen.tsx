// Debt Screen - <PERSON><PERSON><PERSON> yönetimi ekranı

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { Debt } from '../../types';

const DebtScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [debts, setDebts] = useState<Debt[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - gerçek uygulamada API'den gelecek
  const mockDebts: Debt[] = [
    {
      id: '1',
      userId: 'user1',
      name: '<PERSON><PERSON><PERSON>',
      creditor: 'ABC Bank',
      totalAmount: 15000,
      remainingAmount: 8500,
      interestRate: 2.5,
      minimumPayment: 1200,
      dueDate: '2024-02-15',
      type: 'credit_card',
      isActive: true,
      createdAt: '2023-06-01',
      updatedAt: '2024-01-01',
    },
    {
      id: '2',
      userId: 'user1',
      name: 'Konut Kredisi',
      creditor: 'XYZ Bank',
      totalAmount: 250000,
      remainingAmount: 180000,
      interestRate: 1.8,
      minimumPayment: 2800,
      dueDate: '2024-02-01',
      type: 'mortgage',
      isActive: true,
      createdAt: '2020-01-15',
      updatedAt: '2024-01-01',
    },
  ];

  useEffect(() => {
    loadDebts();
  }, []);

  const loadDebts = async () => {
    // TODO: API çağrısı
    setDebts(mockDebts);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDebts();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getDebtTypeIcon = (type: string) => {
    switch (type) {
      case 'credit_card': return 'card';
      case 'loan': return 'cash';
      case 'mortgage': return 'home';
      case 'personal': return 'person';
      default: return 'document';
    }
  };

  const getDebtTypeColor = (type: string) => {
    switch (type) {
      case 'credit_card': return theme.colors.error;
      case 'personal_loan': return theme.colors.warning;
      case 'mortgage': return theme.colors.primary;
      case 'student_loan': return theme.colors.info;
      default: return theme.colors.textSecondary;
    }
  };

  const calculateProgress = (remaining: number, total: number) => {
    return ((total - remaining) / total) * 100;
  };

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const totalDebt = debts.reduce((sum, debt) => sum + debt.remainingAmount, 0);
  const totalMonthlyPayment = debts.reduce((sum, debt) => sum + debt.minimumPayment, 0);

  const renderDebt = ({ item }: { item: Debt }) => {
    const progress = calculateProgress(item.remainingAmount, item.totalAmount);
    const daysUntilDue = getDaysUntilDue(item.dueDate);
    const typeColor = getDebtTypeColor(item.type);

    return (
      <TouchableOpacity
        style={styles.debtItem}
        onPress={() => {/* navigation.navigate('DebtDetail', { debtId: item.id }) */}}
      >
        <View style={styles.debtHeader}>
          <View style={styles.debtTitleContainer}>
            <View style={[styles.debtIcon, { backgroundColor: typeColor }]}>
              <Ionicons name={getDebtTypeIcon(item.type) as any} size={20} color={theme.colors.surface} />
            </View>
            <View style={styles.debtInfo}>
              <Text style={styles.debtName}>{item.name}</Text>
              <Text style={styles.debtCreditor}>{item.creditor}</Text>
            </View>
          </View>
          <View style={styles.debtAmount}>
            <Text style={styles.remainingAmount}>
              {formatCurrency(item.remainingAmount)}
            </Text>
            <Text style={styles.totalAmount}>
              / {formatCurrency(item.totalAmount)}
            </Text>
          </View>
        </View>

        <View style={styles.debtProgress}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressLabel}>Ödenen: {progress.toFixed(0)}%</Text>
            <Text style={styles.interestRate}>Faiz: %{item.interestRate}</Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${progress}%`,
                  backgroundColor: theme.colors.success
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.debtFooter}>
          <View style={styles.paymentInfo}>
            <Ionicons name="calendar-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={styles.paymentText}>
              Aylık: {formatCurrency(item.minimumPayment)}
            </Text>
          </View>
          <View style={[
            styles.dueDateInfo,
            daysUntilDue <= 7 && styles.dueDateWarning
          ]}>
            <Ionicons
              name="time-outline"
              size={16}
              color={daysUntilDue <= 7 ? theme.colors.error : theme.colors.textSecondary}
            />
            <Text style={[
              styles.dueDateText,
              daysUntilDue <= 7 && styles.dueDateTextWarning
            ]}>
              {daysUntilDue > 0 ? `${daysUntilDue} gün kaldı` : 'Vadesi geçti'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 60,
      paddingBottom: 20,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    addButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    summaryContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    summaryCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 20,
      alignItems: 'center',
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    summaryTitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    summaryAmount: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.colors.error,
      marginBottom: 4,
    },
    summarySubtitle: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    debtItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    debtHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    debtTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    debtIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    debtInfo: {
      flex: 1,
    },
    debtName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    debtCreditor: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    debtAmount: {
      alignItems: 'flex-end',
    },
    remainingAmount: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.error,
    },
    totalAmount: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    debtProgress: {
      marginBottom: 12,
    },
    progressInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    progressLabel: {
      fontSize: 12,
      color: theme.colors.text,
      fontWeight: '500',
    },
    interestRate: {
      fontSize: 12,
      color: theme.colors.warning,
      fontWeight: '500',
    },
    progressBar: {
      height: 6,
      backgroundColor: theme.colors.border,
      borderRadius: 3,
    },
    progressFill: {
      height: '100%',
      borderRadius: 3,
    },
    debtFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    paymentInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    paymentText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: 4,
    },
    dueDateInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dueDateWarning: {
      backgroundColor: theme.colors.error + '20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 8,
    },
    dueDateText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: 4,
    },
    dueDateTextWarning: {
      color: theme.colors.error,
      fontWeight: '500',
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 60,
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptySubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 20,
    },
  });

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Borç Yönetimi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('AddDebt' as never)}
        >
          <Ionicons name="add" size={24} color={theme.colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Summary */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Toplam Borç</Text>
          <Text style={styles.summaryAmount}>
            {formatCurrency(totalDebt)}
          </Text>
          <Text style={styles.summarySubtitle}>
            Aylık ödeme: {formatCurrency(totalMonthlyPayment)}
          </Text>
        </View>
      </View>

      {/* Debts List */}
      <FlatList
        data={debts}
        renderItem={renderDebt}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="document-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyTitle}>Henüz borç kaydı yok</Text>
            <Text style={styles.emptySubtitle}>
              İlk borç kaydınızı oluşturmak için + butonuna dokunun
            </Text>
          </View>
        }
      />
    </View>
  );
};

export default DebtScreen;
