// Recurring Transactions Screen - Tekrarlayan işlem yönetimi
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import RecurringTransactionService from '../../services/RecurringTransactionService';
import { RecurringTemplate } from '../../types/transaction';

const RecurringTransactionsScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const [templates, setTemplates] = useState<RecurringTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Tekrarlayan şablonları yükle
  const loadRecurringTemplates = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const userTemplates = await RecurringTransactionService.getUserRecurringTemplates(user.id);
      setTemplates(userTemplates);
    } catch (error) {
      console.error('❌ Error loading recurring templates:', error);
      Alert.alert('Hata', 'Tekrarlayan işlemler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sayfa odaklandığında yenile
  useFocusEffect(
    useCallback(() => {
      loadRecurringTemplates();
    }, [user?.id])
  );

  // Pull to refresh
  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadRecurringTemplates();
    setIsRefreshing(false);
  };

  // Şablonu aktif/pasif yap
  const toggleTemplate = async (templateId: string, isActive: boolean) => {
    try {
      await RecurringTransactionService.toggleRecurringTemplate(templateId, !isActive);
      await loadRecurringTemplates(); // Listeyi yenile

      Alert.alert(
        'Başarılı',
        `Tekrarlayan işlem ${!isActive ? 'aktif' : 'pasif'} hale getirildi.`
      );
    } catch (error) {
      console.error('❌ Error toggling template:', error);
      Alert.alert('Hata', 'İşlem durumu değiştirilirken bir hata oluştu.');
    }
  };

  // Şablonu sil
  const deleteTemplate = async (templateId: string, templateName: string) => {
    Alert.alert(
      'Silme Onayı',
      `"${templateName}" tekrarlayan işlemini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await RecurringTransactionService.deleteRecurringTemplate(templateId);
              await loadRecurringTemplates(); // Listeyi yenile
              Alert.alert('Başarılı', 'Tekrarlayan işlem silindi.');
            } catch (error) {
              console.error('❌ Error deleting template:', error);
              Alert.alert('Hata', 'Tekrarlayan işlem silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  // Template item render
  const renderTemplateItem = ({ item }: { item: RecurringTemplate }) => {
    const isIncome = item.type === 'income';
    const nextExecution = RecurringTransactionService.formatNextExecutionDate(item.nextExecutionDate);
    const frequencyLabel = RecurringTransactionService.getFrequencyLabel(item.recurrence.frequency);

    return (
      <View style={[styles.templateItem, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.templateHeader}>
          <View style={styles.templateLeft}>
            <View style={[
              styles.templateIcon,
              { backgroundColor: isIncome ? theme.colors.income : theme.colors.expense }
            ]}>
              <Ionicons
                name={isIncome ? 'trending-up' : 'trending-down'}
                size={20}
                color={theme.colors.surface}
              />
            </View>
            <View style={styles.templateInfo}>
              <Text style={[styles.templateName, { color: theme.colors.text }]}>
                {item.name}
              </Text>
              <Text style={[styles.templateCategory, { color: theme.colors.textSecondary }]}>
                {item.category} • {frequencyLabel}
              </Text>
            </View>
          </View>
          <View style={styles.templateRight}>
            <Text style={[
              styles.templateAmount,
              { color: isIncome ? theme.colors.income : theme.colors.expense }
            ]}>
              {isIncome ? '+' : '-'}{item.amount.toLocaleString('tr-TR')} {item.currency}
            </Text>
            <Text style={[styles.nextExecution, { color: theme.colors.textSecondary }]}>
              {nextExecution}
            </Text>
          </View>
        </View>

        <View style={styles.templateActions}>
          <TouchableOpacity
            style={[
              styles.statusButton,
              { backgroundColor: item.isActive ? theme.colors.success : theme.colors.warning }
            ]}
            onPress={() => toggleTemplate(item.id, item.isActive)}
          >
            <Ionicons
              name={item.isActive ? 'pause' : 'play'}
              size={16}
              color={theme.colors.surface}
            />
            <Text style={[styles.statusButtonText, { color: theme.colors.surface }]}>
              {item.isActive ? 'Duraklat' : 'Başlat'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.deleteButton, { backgroundColor: theme.colors.error }]}
            onPress={() => deleteTemplate(item.id, item.name)}
          >
            <Ionicons name="trash-outline" size={16} color={theme.colors.surface} />
            <Text style={[styles.deleteButtonText, { color: theme.colors.surface }]}>
              Sil
            </Text>
          </TouchableOpacity>
        </View>

        {item.description && (
          <Text style={[styles.templateDescription, { color: theme.colors.textSecondary }]}>
            {item.description}
          </Text>
        )}
      </View>
    );
  };

  // Empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="repeat-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
        Tekrarlayan İşlem Yok
      </Text>
      <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
        Henüz hiç tekrarlayan işlem oluşturmadınız.{'\n'}
        Yeni işlem eklerken "Tekrarlanan İşlem" seçeneğini aktif edin.
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          Tekrarlayan işlemler yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={templates}
        renderItem={renderTemplateItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  listContainer: {
    padding: 16,
    paddingTop: 30, // Üstten daha fazla boşluk
    flexGrow: 1,
  },
  templateItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  templateLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  templateCategory: {
    fontSize: 14,
  },
  templateRight: {
    alignItems: 'flex-end',
  },
  templateAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  nextExecution: {
    fontSize: 12,
  },
  templateActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
  },
  statusButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    justifyContent: 'center',
  },
  deleteButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  templateDescription: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default RecurringTransactionsScreen;
