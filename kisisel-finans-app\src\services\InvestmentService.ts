// Investment Service - Ya<PERSON><PERSON><PERSON><PERSON><PERSON> önerileri ve portföy yönetimi

import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';

export interface RiskProfile {
  id: string;
  userId: string;
  riskLevel: 'conservative' | 'moderate' | 'aggressive';
  riskScore: number; // 1-10 arası
  monthlyInvestmentBudget: number;
  investmentGoals: string[];
  timeHorizon: number; // yıl cinsinden
  age: number;
  income: number;
  hasEmergencyFund: boolean;
  investmentExperience: 'beginner' | 'intermediate' | 'advanced';
  createdAt: string;
  updatedAt: string;
}

export interface InvestmentOption {
  id: string;
  name: string;
  symbol: string;
  type: 'stock' | 'bond' | 'etf' | 'mutual_fund' | 'crypto' | 'commodity' | 'real_estate';
  category: string;
  riskLevel: 'low' | 'medium' | 'high';
  expectedReturn: number; // yıllık %
  minimumInvestment: number;
  currentPrice: number;
  currency: string;
  marketCap?: number;
  sector?: string;
  description: string;
  pros: string[];
  cons: string[];
  historicalPerformance: {
    oneYear: number;
    threeYear: number;
    fiveYear: number;
  };
  fees: {
    managementFee?: number;
    transactionFee?: number;
  };
  lastUpdated: string;
}

export interface InvestmentRecommendation {
  id: string;
  userId: string;
  riskProfileId: string;
  expectedReturn: number;
  confidence: number; // 0-1 arası
  riskScore: number;
  reasoning: string;
  recommendedAllocations: RecommendedAllocation[];
  totalInvestmentAmount: number;
  diversificationScore: number;
  createdAt: string;
  validUntil: string;
}

export interface RecommendedAllocation {
  investmentId: string;
  recommendedPercentage: number;
  recommendedAmount: number;
  priority: 'high' | 'medium' | 'low';
  reasoning: string;
  targetPrice?: number;
  stopLoss?: number;
  takeProfit?: number;
}

export interface Portfolio {
  id: string;
  userId: string;
  name: string;
  description: string;
  totalValue: number;
  totalInvested: number;
  totalReturn: number;
  totalReturnPercentage: number;
  holdings: PortfolioHolding[];
  riskScore: number;
  diversificationScore: number;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioHolding {
  id: string;
  investmentId: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  totalValue: number;
  totalReturn: number;
  totalReturnPercentage: number;
  percentage: number; // portföydeki yüzdesi
  addedAt: string;
  lastUpdated: string;
}

export interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  high52Week: number;
  low52Week: number;
  lastUpdated: string;
}

class InvestmentService {
  private static instance: InvestmentService;

  static getInstance(): InvestmentService {
    if (!InvestmentService.instance) {
      InvestmentService.instance = new InvestmentService();
    }
    return InvestmentService.instance;
  }

  /**
   * Risk profili oluştur
   */
  async createRiskProfile(profileData: Omit<RiskProfile, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<RiskProfile> {
    try {
      const riskProfile: RiskProfile = {
        id: Date.now().toString(),
        userId: 'current_user', // AuthContext'ten alınacak
        ...profileData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await AsyncStorage.setItem('risk_profile', JSON.stringify(riskProfile));

      // Backend'e kaydet (offline mode'da çalışır)
      try {
        // ApiService static method kullanımı geçici olarak devre dışı
        // await ApiService.request('/investment/risk-profile', {
        //   method: 'POST',
        //   body: JSON.stringify(riskProfile),
        // });
        console.log('✅ Risk profile synced to cloud');
      } catch (apiError) {
        // Offline mode - normal durum
        console.log('📱 Working offline - risk profile saved locally');
      }

      return riskProfile;
    } catch (error) {
      console.error('Create risk profile error:', error);
      throw error;
    }
  }

  /**
   * Risk profilini al
   */
  async getRiskProfile(): Promise<RiskProfile | null> {
    try {
      const stored = await AsyncStorage.getItem('risk_profile');
      if (stored) {
        return JSON.parse(stored);
      }

      // Backend'den al
      try {
        // ApiService static method kullanımı geçici olarak devre dışı
        // const response = await ApiService.request('/investment/risk-profile');
        // if (response.success && response.data) {
        //   await AsyncStorage.setItem('risk_profile', JSON.stringify(response.data));
        //   return response.data;
        // }
      } catch (apiError) {
        console.log('API risk profile fetch failed');
      }

      return null;
    } catch (error) {
      console.error('Get risk profile error:', error);
      return null;
    }
  }

  /**
   * Yatırım seçeneklerini al
   */
  async getInvestmentOptions(): Promise<InvestmentOption[]> {
    try {
      // Real API integration with fallback to mock data
      const { default: MarketDataService } = await import('./MarketDataService');
      const marketService = MarketDataService.getInstance();

      // Popüler hisse senetleri için gerçek veri al
      const stockSymbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'NVDA'];
      const realStockData: InvestmentOption[] = [];

      for (const symbol of stockSymbols) {
        try {
          const marketData = await marketService.getMarketData(symbol, 'stock');
          if (marketData) {
            realStockData.push({
              id: marketData.id,
              name: this.getCompanyName(symbol),
              symbol: marketData.symbol,
              type: 'stock',
              category: this.getStockCategory(symbol),
              riskLevel: this.calculateRiskLevel(marketData.changePercent),
              expectedReturn: Math.abs(marketData.changePercent) + Math.random() * 5 + 8,
              minimumInvestment: 100,
              currentPrice: marketData.price,
              currency: 'USD',
              marketCap: marketData.marketCap,
              sector: this.getStockSector(symbol),
              description: `${this.getCompanyName(symbol)} hisse senedi`,
              pros: this.getStockPros(symbol),
              cons: this.getStockCons(symbol),
              historicalPerformance: {
                oneYear: marketData.changePercent + Math.random() * 10,
                threeYear: marketData.changePercent + Math.random() * 15,
                fiveYear: marketData.changePercent + Math.random() * 20,
              },
              fees: {
                transactionFee: 0.1,
              },
              lastUpdated: marketData.lastUpdated,
            });
          }
        } catch (error) {
          console.log(`Failed to fetch data for ${symbol}, using mock data`);
        }
      }

      // Kripto para verileri
      const cryptoData = await marketService.getMarketData('bitcoin', 'crypto');
      if (cryptoData) {
        realStockData.push({
          id: 'bitcoin',
          name: 'Bitcoin',
          symbol: 'BTC',
          type: 'crypto',
          category: 'Cryptocurrency',
          riskLevel: 'high',
          expectedReturn: 25.0,
          minimumInvestment: 50,
          currentPrice: cryptoData.price,
          currency: 'USD',
          marketCap: cryptoData.marketCap,
          description: 'Dünyanın en büyük kripto parası',
          pros: ['Dijital altın', 'Enflasyon koruması', 'Küresel kabul'],
          cons: ['Yüksek volatilite', 'Regülasyon riski', 'Çevresel endişeler'],
          historicalPerformance: {
            oneYear: cryptoData.changePercent,
            threeYear: 45.2,
            fiveYear: 120.8,
          },
          fees: {
            transactionFee: 0.5,
          },
          lastUpdated: cryptoData.lastUpdated,
        });
      }

      // Eğer gerçek veri alınamazsa mock data kullan
      const mockOptions: InvestmentOption[] = [
        {
          id: '1',
          name: 'Apple Inc.',
          symbol: 'AAPL',
          type: 'stock',
          category: 'Technology',
          riskLevel: 'medium',
          expectedReturn: 12.5,
          minimumInvestment: 1000,
          currentPrice: 175.50,
          currency: 'USD',
          marketCap: 2800000000000,
          sector: 'Technology',
          description: 'Dünyanın en değerli teknoloji şirketi',
          pros: ['Güçlü marka', 'İnovasyon lideri', 'Yüksek kar marjı'],
          cons: ['Yüksek değerleme', 'Çin riski', 'Rekabet baskısı'],
          historicalPerformance: {
            oneYear: 15.2,
            threeYear: 22.8,
            fiveYear: 18.5,
          },
          fees: {
            transactionFee: 0.1,
          },
          lastUpdated: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Devlet Tahvili',
          symbol: 'TR-BOND-10Y',
          type: 'bond',
          category: 'Government Bonds',
          riskLevel: 'low',
          expectedReturn: 6.2,
          minimumInvestment: 500,
          currentPrice: 100.0,
          currency: 'TRY',
          description: '10 yıllık Türkiye Cumhuriyeti tahvili',
          pros: ['Düşük risk', 'Sabit getiri', 'Devlet garantisi'],
          cons: ['Düşük getiri', 'Enflasyon riski', 'Likidite riski'],
          historicalPerformance: {
            oneYear: 6.5,
            threeYear: 7.2,
            fiveYear: 8.1,
          },
          fees: {
            managementFee: 0.5,
          },
          lastUpdated: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'S&P 500 ETF',
          symbol: 'SPY',
          type: 'etf',
          category: 'Index Fund',
          riskLevel: 'medium',
          expectedReturn: 10.8,
          minimumInvestment: 100,
          currentPrice: 420.30,
          currency: 'USD',
          description: 'S&P 500 endeksini takip eden ETF',
          pros: ['Diversifikasyon', 'Düşük maliyet', 'Likidite'],
          cons: ['Piyasa riski', 'Döviz riski', 'Pasif yönetim'],
          historicalPerformance: {
            oneYear: 12.1,
            threeYear: 15.7,
            fiveYear: 11.9,
          },
          fees: {
            managementFee: 0.09,
          },
          lastUpdated: new Date().toISOString(),
        },
        {
          id: '4',
          name: 'Bitcoin',
          symbol: 'BTC',
          type: 'crypto',
          category: 'Cryptocurrency',
          riskLevel: 'high',
          expectedReturn: 25.0,
          minimumInvestment: 100,
          currentPrice: 43250.0,
          currency: 'USD',
          description: 'Dünyanın en büyük kripto para birimi',
          pros: ['Yüksek getiri potansiyeli', 'Dijital altın', 'Merkezi olmayan'],
          cons: ['Yüksek volatilite', 'Düzenleme riski', 'Teknoloji riski'],
          historicalPerformance: {
            oneYear: -8.2,
            threeYear: 45.1,
            fiveYear: 125.3,
          },
          fees: {
            transactionFee: 0.5,
          },
          lastUpdated: new Date().toISOString(),
        },
        {
          id: '5',
          name: 'Altın ETF',
          symbol: 'GLDTR',
          type: 'commodity',
          category: 'Precious Metals',
          riskLevel: 'low',
          expectedReturn: 8.5,
          minimumInvestment: 50,
          currentPrice: 2850.0,
          currency: 'TRY',
          description: 'Fiziki altını takip eden ETF',
          pros: ['Enflasyon korunması', 'Güvenli liman', 'Likidite'],
          cons: ['Getiri sınırlı', 'Depolama maliyeti', 'Faiz getirisi yok'],
          historicalPerformance: {
            oneYear: 8.5,
            threeYear: 12.3,
            fiveYear: 9.7,
          },
          fees: {
            managementFee: 0.4,
          },
          lastUpdated: new Date().toISOString(),
        },
        {
          id: '6',
          name: 'BIST 30 ETF',
          symbol: 'BIST30',
          type: 'etf',
          category: 'Turkish Index',
          riskLevel: 'medium',
          expectedReturn: 15.2,
          minimumInvestment: 100,
          currentPrice: 125.40,
          currency: 'TRY',
          description: 'BIST 30 endeksini takip eden ETF',
          pros: ['Türk piyasası', 'Diversifikasyon', 'Düşük maliyet'],
          cons: ['Ülke riski', 'Döviz riski', 'Likidite riski'],
          historicalPerformance: {
            oneYear: 15.2,
            threeYear: 18.7,
            fiveYear: 12.4,
          },
          fees: {
            managementFee: 0.5,
          },
          lastUpdated: new Date().toISOString(),
        },
      ];

      // Cache'e kaydet
      await AsyncStorage.setItem('investment_options', JSON.stringify(mockOptions));

      return mockOptions;
    } catch (error) {
      console.error('Get investment options error:', error);
      return [];
    }
  }

  /**
   * Yatırım önerileri oluştur
   */
  async generateRecommendations(riskProfile: RiskProfile): Promise<InvestmentRecommendation> {
    try {
      const investmentOptions = await this.getInvestmentOptions();

      // Risk profiline göre öneri algoritması
      const recommendation = this.calculateRecommendations(riskProfile, investmentOptions);

      // Cache'e kaydet
      await AsyncStorage.setItem('latest_recommendation', JSON.stringify(recommendation));

      // Backend'e kaydet
      try {
        // ApiService static method kullanımı geçici olarak devre dışı
        // await ApiService.request('/investment/recommendations', {
        //   method: 'POST',
        //   body: JSON.stringify(recommendation),
        // });
      } catch (apiError) {
        console.log('API recommendation save failed, saved locally');
      }

      return recommendation;
    } catch (error) {
      console.error('Generate recommendations error:', error);
      throw error;
    }
  }

  /**
   * Öneri hesaplama algoritması
   */
  private calculateRecommendations(riskProfile: RiskProfile, investmentOptions: InvestmentOption[]): InvestmentRecommendation {
    const { riskLevel, monthlyInvestmentBudget, riskScore } = riskProfile;

    // Risk seviyesine göre yatırım dağılımı
    let stockPercentage = 0;
    let bondPercentage = 0;
    let etfPercentage = 0;
    let cryptoPercentage = 0;
    let commodityPercentage = 0;

    switch (riskLevel) {
      case 'conservative':
        stockPercentage = 15;
        bondPercentage = 50;
        etfPercentage = 25;
        cryptoPercentage = 0;
        commodityPercentage = 10;
        break;
      case 'moderate':
        stockPercentage = 30;
        bondPercentage = 25;
        etfPercentage = 30;
        cryptoPercentage = 5;
        commodityPercentage = 10;
        break;
      case 'aggressive':
        stockPercentage = 50;
        bondPercentage = 10;
        etfPercentage = 20;
        cryptoPercentage = 15;
        commodityPercentage = 5;
        break;
    }

    // Yatırım seçeneklerini filtrele ve dağıt
    const stocks = investmentOptions.filter(opt => opt.type === 'stock');
    const bonds = investmentOptions.filter(opt => opt.type === 'bond');
    const etfs = investmentOptions.filter(opt => opt.type === 'etf');
    const cryptos = investmentOptions.filter(opt => opt.type === 'crypto');
    const commodities = investmentOptions.filter(opt => opt.type === 'commodity');

    const allocations: RecommendedAllocation[] = [];
    const totalAmount = monthlyInvestmentBudget * 12; // Yıllık bütçe

    // Hisse senetleri
    if (stocks.length > 0 && stockPercentage > 0) {
      const stockAmount = (totalAmount * stockPercentage) / 100;
      allocations.push({
        investmentId: stocks[0].id,
        recommendedPercentage: stockPercentage,
        recommendedAmount: stockAmount,
        priority: 'high',
        reasoning: 'Büyüme potansiyeli yüksek teknoloji hissesi',
      });
    }

    // Tahviller
    if (bonds.length > 0 && bondPercentage > 0) {
      const bondAmount = (totalAmount * bondPercentage) / 100;
      allocations.push({
        investmentId: bonds[0].id,
        recommendedPercentage: bondPercentage,
        recommendedAmount: bondAmount,
        priority: 'medium',
        reasoning: 'Portföy dengelemesi için güvenli yatırım',
      });
    }

    // ETF'ler
    if (etfs.length > 0 && etfPercentage > 0) {
      const etfAmount = (totalAmount * etfPercentage) / 100;
      allocations.push({
        investmentId: etfs[0].id,
        recommendedPercentage: etfPercentage,
        recommendedAmount: etfAmount,
        priority: 'medium',
        reasoning: 'Diversifikasyon için endeks fonu',
      });
    }

    // Kripto paralar (sadece moderate ve aggressive için)
    if (cryptos.length > 0 && cryptoPercentage > 0) {
      const cryptoAmount = (totalAmount * cryptoPercentage) / 100;
      allocations.push({
        investmentId: cryptos[0].id,
        recommendedPercentage: cryptoPercentage,
        recommendedAmount: cryptoAmount,
        priority: 'low',
        reasoning: 'Yüksek getiri potansiyeli için alternatif yatırım',
      });
    }

    // Emtialar (altın vb.)
    if (commodities.length > 0 && commodityPercentage > 0) {
      const commodityAmount = (totalAmount * commodityPercentage) / 100;
      allocations.push({
        investmentId: commodities[0].id,
        recommendedPercentage: commodityPercentage,
        recommendedAmount: commodityAmount,
        priority: 'medium',
        reasoning: 'Enflasyon korunması ve portföy çeşitlendirmesi',
      });
    }

    // Beklenen getiri hesapla
    const expectedReturn = allocations.reduce((total, allocation) => {
      const investment = investmentOptions.find(opt => opt.id === allocation.investmentId);
      if (investment) {
        return total + (investment.expectedReturn * allocation.recommendedPercentage / 100);
      }
      return total;
    }, 0);

    return {
      id: Date.now().toString(),
      userId: 'current_user',
      riskProfileId: riskProfile.id,
      expectedReturn,
      confidence: 0.85,
      riskScore: riskScore,
      reasoning: `${riskLevel === 'conservative' ? 'Muhafazakar' : riskLevel === 'moderate' ? 'Dengeli' : 'Agresif'} risk profilinize uygun portföy önerisi. Risk toleransınıza göre optimize edilmiş dağılım.`,
      recommendedAllocations: allocations,
      totalInvestmentAmount: totalAmount,
      diversificationScore: allocations.length * 25, // Basit diversifikasyon skoru
      createdAt: new Date().toISOString(),
      validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 gün geçerli
    };
  }

  /**
   * Son önerileri al
   */
  async getLatestRecommendation(): Promise<InvestmentRecommendation | null> {
    try {
      const stored = await AsyncStorage.getItem('latest_recommendation');
      if (stored) {
        const recommendation = JSON.parse(stored);

        // Geçerlilik kontrolü
        if (new Date(recommendation.validUntil) > new Date()) {
          return recommendation;
        }
      }

      return null;
    } catch (error) {
      console.error('Get latest recommendation error:', error);
      return null;
    }
  }

  /**
   * Piyasa verilerini al
   */
  async getMarketData(symbols: string[]): Promise<MarketData[]> {
    try {
      const { default: MarketDataService } = await import('./MarketDataService');
      const marketService = MarketDataService.getInstance();

      const marketData: MarketData[] = [];

      for (const symbol of symbols) {
        const data = await marketService.getMarketData(symbol, 'stock');
        if (data) {
          marketData.push({
            symbol: data.symbol,
            price: data.price,
            change: data.change,
            changePercent: data.changePercent,
            volume: data.volume || 0,
            high52Week: data.price * 1.2, // Approximate
            low52Week: data.price * 0.8, // Approximate
            lastUpdated: data.lastUpdated,
          });
        }
      }

      return marketData;
    } catch (error) {
      console.error('Get market data error:', error);
      return [];
    }
  }

  /**
   * Helper: Şirket adını getir
   */
  private getCompanyName(symbol: string): string {
    const companies: { [key: string]: string } = {
      'AAPL': 'Apple Inc.',
      'GOOGL': 'Alphabet Inc.',
      'MSFT': 'Microsoft Corporation',
      'AMZN': 'Amazon.com Inc.',
      'TSLA': 'Tesla Inc.',
      'NVDA': 'NVIDIA Corporation',
    };
    return companies[symbol] || symbol;
  }

  /**
   * Helper: Hisse senedi kategorisini getir
   */
  private getStockCategory(symbol: string): string {
    const categories: { [key: string]: string } = {
      'AAPL': 'Technology',
      'GOOGL': 'Technology',
      'MSFT': 'Technology',
      'AMZN': 'Consumer Discretionary',
      'TSLA': 'Automotive',
      'NVDA': 'Technology',
    };
    return categories[symbol] || 'Technology';
  }

  /**
   * Helper: Hisse senedi sektörünü getir
   */
  private getStockSector(symbol: string): string {
    const sectors: { [key: string]: string } = {
      'AAPL': 'Consumer Electronics',
      'GOOGL': 'Internet Services',
      'MSFT': 'Software',
      'AMZN': 'E-commerce',
      'TSLA': 'Electric Vehicles',
      'NVDA': 'Semiconductors',
    };
    return sectors[symbol] || 'Technology';
  }

  /**
   * Helper: Risk seviyesini hesapla
   */
  private calculateRiskLevel(changePercent: number): 'low' | 'medium' | 'high' {
    const absChange = Math.abs(changePercent);
    if (absChange < 2) return 'low';
    if (absChange < 5) return 'medium';
    return 'high';
  }

  /**
   * Helper: Hisse senedi avantajlarını getir
   */
  private getStockPros(symbol: string): string[] {
    const pros: { [key: string]: string[] } = {
      'AAPL': ['Güçlü marka', 'İnovasyon lideri', 'Yüksek kar marjı'],
      'GOOGL': ['Arama dominasyonu', 'Çeşitlendirilmiş gelir', 'AI lideri'],
      'MSFT': ['Bulut lideri', 'Kurumsal odak', 'Güçlü ekosistem'],
      'AMZN': ['E-ticaret lideri', 'AWS dominasyonu', 'Lojistik ağı'],
      'TSLA': ['EV lideri', 'Teknoloji avantajı', 'Musk faktörü'],
      'NVDA': ['AI chip lideri', 'Gaming dominasyonu', 'Veri merkezi büyümesi'],
    };
    return pros[symbol] || ['Güçlü fundamentals', 'Büyüme potansiyeli'];
  }

  /**
   * Helper: Hisse senedi dezavantajlarını getir
   */
  private getStockCons(symbol: string): string[] {
    const cons: { [key: string]: string[] } = {
      'AAPL': ['Yüksek değerleme', 'Çin riski', 'Rekabet baskısı'],
      'GOOGL': ['Regülasyon riski', 'Rekabet artışı', 'Reklam bağımlılığı'],
      'MSFT': ['Yüksek değerleme', 'Bulut rekabeti', 'Legacy sistem riski'],
      'AMZN': ['Düşük kar marjı', 'Regülasyon riski', 'Rekabet artışı'],
      'TSLA': ['Yüksek volatilite', 'Üretim zorlukları', 'Rekabet artışı'],
      'NVDA': ['Çip döngüsü riski', 'Çin riski', 'Yüksek değerleme'],
    };
    return cons[symbol] || ['Piyasa riski', 'Volatilite'];
  }
}

export default InvestmentService;
