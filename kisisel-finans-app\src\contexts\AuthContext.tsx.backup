// Authentication Context - Kullanıcı kimlik doğrulama yönetimi

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import TwoFactorService from '../services/TwoFactorService';
import SocialAuthService from '../services/SocialAuthService';
import JWTService from '../services/JWTService';
import SessionManager from '../services/SessionManager';
import EncryptionService from '../services/EncryptionService';
import SecureStorageWrapper from '../services/SecureStorageWrapper';
import RBACService from '../services/RBACService';
import SecurityMonitoringService from '../services/SecurityMonitoringService';

import { RoleType } from '../types/rbac';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  roles?: RoleType[];
  preferences: {
    currency: string;
    language: string;
    notifications: boolean;
    biometric: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (name: string, email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  refreshToken: () => Promise<boolean>;
  getActiveSessions: () => Promise<any[]>;
  endSession: (sessionId?: string) => Promise<void>;
  rotateEncryptionKeys: () => Promise<void>;
  getEncryptionStatus: () => Promise<any>;
  // RBAC methods
  assignRole: (userId: string, role: RoleType, context?: any) => Promise<boolean>;
  removeRole: (userId: string, role: RoleType) => Promise<boolean>;
  getUserRoles: (userId: string) => Promise<RoleType[]>;
  getRBACStatus: () => any;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Service instances
  const jwtService = JWTService.getInstance();
  const sessionManager = SessionManager.getInstance();
  const rbacService = RBACService;
  const securityService = SecurityMonitoringService;

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      // JWT token kontrolü
      const accessToken = await jwtService.getAccessToken();

      if (accessToken) {
        const isValid = await jwtService.isTokenValid(accessToken);

        if (isValid) {
          // Token geçerli, kullanıcı bilgilerini decode et
          const payload = await jwtService.decodeToken(accessToken);
          if (payload) {
            const user: User = {
              id: payload.userId,
              email: payload.email,
              name: payload.name,
              avatar: undefined,
              createdAt: new Date().toISOString(),
              preferences: {
                currency: 'TRY',
                language: 'tr',
                notifications: true,
                biometric: false,
              }
            };
            setUser(user);

            // Session aktivitesini güncelle
            await jwtService.updateSessionActivity();
          }
        } else {
          // Token geçersiz, refresh dene
          const refreshed = await refreshToken();
          if (!refreshed) {
            // Refresh başarısız, logout
            await logout();
          }
        }
      } else {
        // Token yok, session kontrolü yap
        const isSessionActive = await jwtService.isSessionActive();
        if (!isSessionActive) {
          await logout();
        }
      }
    } catch (error) {
      console.error('Auth state check error:', error);
      await logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);

      // Simulated API call - replace with real API
      // For demo purposes, accept any email and password with minimum length
      if (email.includes('@') && password.length >= 6) {
        // 2FA kontrolü yap (Test amaçlı bypass)
        const twoFactorService = TwoFactorService.getInstance();
        const requires2FA = await twoFactorService.requiresTwoFactor(email);

        if (requires2FA && email !== '<EMAIL>') {
          // Test kullanıcısı için 2FA bypass et
          // Remember device ayarını kontrol et
          const rememberDeviceEnabled = await twoFactorService.isRememberDeviceEnabled();

          if (rememberDeviceEnabled) {
            // Bu cihazın güvenilir olup olmadığını kontrol et
            const currentDeviceId = await twoFactorService.getCurrentDeviceId();
            const trustedDevices = await twoFactorService.getTrustedDevices();
            const isCurrentDeviceTrusted = trustedDevices.some(device =>
              device.id === currentDeviceId && !isDeviceExpired(device)
            );

            if (!isCurrentDeviceTrusted) {
              // 2FA gerekli, kullanıcıyı 2FA ekranına yönlendir
              return { success: false, error: '2FA_REQUIRED' };
            }
          } else {
            // Remember device kapalı, her zaman 2FA iste
            return { success: false, error: '2FA_REQUIRED' };
          }
        }

        // Test kullanıcısı için özel log
        if (email === '<EMAIL>') {
          console.log('🧪 Test user login - 2FA bypassed for testing');
        }

        // Test user için sabit ID kullan, gerçek kullanıcılar için unique ID
        const userId = email === '<EMAIL>' ? 'test_user_123' : Date.now().toString();
        const userName = email.split('@')[0];

        // JWT token oluştur
        const tokenPair = await jwtService.generateTokenPair(userId, email, userName);

        // Session başlat
        await sessionManager.startSession(userId, 'Mobile App');

        // Varsayılan rol ata (basic)
        await rbacService.assignRole(userId, 'basic', 'system');

        // Kullanıcı rollerini al
        const userRoles = await rbacService.getUserRoles(userId);
        const roleTypes = userRoles.map(ur => ur.roleName);

        const mockUser: User = {
          id: userId,
          email: email,
          name: userName,
          avatar: undefined,
          createdAt: new Date().toISOString(),
          roles: roleTypes,
          preferences: {
            currency: 'TRY',
            language: 'tr',
            notifications: true,
            biometric: false,
          }
        };

        setUser(mockUser);

        // Security monitoring - successful login
        await securityService.logLoginAttempt({
          userId: userId,
          email: email,
          ipAddress: '***********', // Mock IP
          userAgent: 'FinanceApp/1.0',
          success: true,
          blocked: false,
          location: {
            country: 'Turkey',
            city: 'Istanbul',
          },
        });

        console.log('🛡️ Security: Login success logged for:', email);
        return { success: true };
      } else {
        // Security monitoring - failed login
        await securityService.logLoginAttempt({
          email: email,
          ipAddress: '***********', // Mock IP
          userAgent: 'FinanceApp/1.0',
          success: false,
          failureReason: 'Invalid credentials',
          blocked: false,
          location: {
            country: 'Turkey',
            city: 'Istanbul',
          },
        });

        console.log('🛡️ Security: Login failure logged for:', email);
        return { success: false, error: 'Geçersiz email formatı veya şifre çok kısa (en az 6 karakter)' };
      }
    } catch (error) {
      return { success: false, error: 'Giriş yapılırken bir hata oluştu' };
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if device is expired
  const isDeviceExpired = (device: any): boolean => {
    if (!device.expiresAt) return false;
    return new Date(device.expiresAt) < new Date();
  };

  const register = async (name: string, email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);

      const userId = Date.now().toString();

      // JWT token oluştur
      const tokenPair = await jwtService.generateTokenPair(userId, email, name);

      // Session başlat
      await sessionManager.startSession(userId, 'Mobile App');

      // Varsayılan rol ata (basic)
      await rbacService.assignRole(userId, 'basic', 'system');

      // Kullanıcı rollerini al
      const userRoles = await rbacService.getUserRoles(userId);
      const roleTypes = userRoles.map(ur => ur.roleName);

      // Simulated API call - replace with real API
      const mockUser: User = {
        id: userId,
        email: email,
        name: name,
        avatar: undefined,
        createdAt: new Date().toISOString(),
        roles: roleTypes,
        preferences: {
          currency: 'TRY',
          language: 'tr',
          notifications: true,
          biometric: false,
        }
      };

      setUser(mockUser);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Kayıt olurken bir hata oluştu' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    const currentUser = user;

    try {
      setIsLoading(true);

      // Security monitoring - logout event
      if (currentUser) {
        await securityService.logSecurityEvent({
          type: 'logout',
          level: 'low',
          category: 'authentication',
          userId: currentUser.id,
          ipAddress: '***********', // Mock IP
          userAgent: 'FinanceApp/1.0',
          details: {
            action: 'user_logout',
            result: 'success',
            metadata: {
              email: currentUser.email,
              sessionDuration: 'unknown', // Could calculate from login time
            },
          },
        });
      }

      // Session'ı sonlandır
      await sessionManager.endSession();

      // JWT token'ları temizle
      await jwtService.clearTokens();

      // Encryption cache'i temizle (anahtarları silme)
      EncryptionService.clearCache();

      // RBAC cache'i temizle
      rbacService.clearCache();

      // Cache verilerini temizle
      await SecureStorageWrapper.clearCache();

      setUser(null);
      console.log('🛡️ Security: Logout logged for:', currentUser?.email);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) return;

    try {
      const updatedUser = { ...user, ...updates };

      // Kullanıcı verilerini şifrelenmiş olarak sakla
      await SecureStorageWrapper.setItem('user_data', updatedUser, {
        encrypt: true,
        useSecureStore: false,
      });

      setUser(updatedUser);
    } catch (error) {
      console.error('Profile update error:', error);
    }
  };

  const resetPassword = async (email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // Simulated API call - replace with real API
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Şifre sıfırlama emaili gönderilemedi' };
    }
  };

  const signInWithGoogle = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      const socialAuthService = SocialAuthService.getInstance();
      const result = await socialAuthService.signInWithGoogle();

      if (result.success && result.user) {
        // JWT token oluştur
        const tokenPair = await jwtService.generateTokenPair(
          result.user.id,
          result.user.email,
          result.user.name
        );

        // Session başlat
        await sessionManager.startSession(result.user.id, 'Mobile App (Google)');

        // Varsayılan rol ata (basic)
        await rbacService.assignRole(result.user.id, 'basic', 'system');

        // Kullanıcı rollerini al
        const userRoles = await rbacService.getUserRoles(result.user.id);
        const roleTypes = userRoles.map(ur => ur.roleName);

        const mockUser: User = {
          id: result.user.id,
          email: result.user.email,
          name: result.user.name,
          avatar: result.user.avatar,
          createdAt: new Date().toISOString(),
          roles: roleTypes,
          preferences: {
            currency: 'TRY',
            language: 'tr',
            notifications: true,
            biometric: false,
          }
        };

        setUser(mockUser);
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      return { success: false, error: 'Google ile giriş başarısız oldu' };
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const newTokenPair = await jwtService.refreshAccessToken();
      if (newTokenPair) {
        // Token başarıyla yenilendi
        await jwtService.updateSessionActivity();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  };

  const getActiveSessions = async (): Promise<any[]> => {
    try {
      return await sessionManager.getActiveSessions();
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  };

  const endSession = async (sessionId?: string): Promise<void> => {
    try {
      await sessionManager.endSession(sessionId);

      // Eğer current session sonlandırılıyorsa kullanıcıyı logout et
      if (!sessionId) {
        setUser(null);
      }
    } catch (error) {
      console.error('End session error:', error);
    }
  };

  const rotateEncryptionKeys = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await EncryptionService.rotateKeys();
      console.log('Encryption keys rotated successfully');
    } catch (error) {
      console.error('Key rotation error:', error);
      throw new Error('Şifreleme anahtarları döndürülemedi');
    } finally {
      setIsLoading(false);
    }
  };

  const getEncryptionStatus = async (): Promise<any> => {
    try {
      return await EncryptionService.getEncryptionStatus();
    } catch (error) {
      console.error('Get encryption status error:', error);
      return {
        isInitialized: false,
        cacheSize: 0,
      };
    }
  };

  // RBAC methods
  const assignRole = async (userId: string, role: RoleType, context?: any): Promise<boolean> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const success = await rbacService.assignRole(userId, role, user.id, context);

      if (success && userId === user.id) {
        // Cache'i temizle
        rbacService.clearCache();

        // Eğer mevcut kullanıcıya rol atandıysa user objesini güncelle
        const userRoles = await rbacService.getUserRoles(userId);
        const roleTypes = userRoles.map(ur => ur.roleName);

        setUser(prevUser => ({
          ...prevUser!,
          roles: roleTypes,
        }));
      }

      return success;
    } catch (error) {
      console.error('Assign role error:', error);
      return false;
    }
  };

  const removeRole = async (userId: string, role: RoleType): Promise<boolean> => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const success = await rbacService.removeRole(userId, role, user.id);

      if (success && userId === user.id) {
        // Cache'i temizle
        rbacService.clearCache();

        // Eğer mevcut kullanıcıdan rol kaldırıldıysa user objesini güncelle
        const userRoles = await rbacService.getUserRoles(userId);
        const roleTypes = userRoles.map(ur => ur.roleName);

        setUser(prevUser => ({
          ...prevUser!,
          roles: roleTypes,
        }));
      }

      return success;
    } catch (error) {
      console.error('Remove role error:', error);
      return false;
    }
  };

  const getUserRoles = async (userId: string): Promise<RoleType[]> => {
    try {
      const userRoles = await rbacService.getUserRoles(userId);
      return userRoles.map(ur => ur.roleName);
    } catch (error) {
      console.error('Get user roles error:', error);
      return [];
    }
  };

  const getRBACStatus = (): any => {
    return rbacService.getRBACStatus();
  };



  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    signInWithGoogle,
    refreshToken,
    getActiveSessions,
    endSession,
    rotateEncryptionKeys,
    getEncryptionStatus,
    // RBAC methods
    assignRole,
    removeRole,
    getUserRoles,
    getRBACStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
