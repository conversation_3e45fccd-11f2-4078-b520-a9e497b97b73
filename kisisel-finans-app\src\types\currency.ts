// Currency Types - Para birimi ve döviz kuru tipleri

import { CurrencyCode } from './transaction';

// Para birimi bilgisi
export interface Currency {
  code: CurrencyCode;
  name: string;
  symbol: string;
  decimalPlaces: number;
  isActive: boolean;
  flag?: string;              // Ülke bayrağı emoji
  country?: string;
}

// Döviz kuru
export interface ExchangeRate {
  baseCurrency: CurrencyCode;
  targetCurrency: CurrencyCode;
  rate: number;
  timestamp: string;
  source: string;             // API kaynağı
  bid?: number;               // Alış kuru
  ask?: number;               // Satış kuru
}

// Döviz kuru geçmişi
export interface ExchangeRateHistory {
  baseCurrency: CurrencyCode;
  targetCurrency: CurrencyCode;
  rates: Array<{
    date: string;             // YYYY-MM-DD
    rate: number;
    high?: number;
    low?: number;
    open?: number;
    close?: number;
  }>;
}

// Para birimi dönüştürme
export interface CurrencyConversion {
  fromCurrency: CurrencyCode;
  toCurrency: CurrencyCode;
  fromAmount: number;
  toAmount: number;
  rate: number;
  timestamp: string;
  fee?: number;               // Dönüştürme ücreti
}

// Çoklu para birimi bakiye
export interface MultiCurrencyBalance {
  userId: string;
  balances: Record<CurrencyCode, {
    amount: number;
    lastUpdated: string;
  }>;
  baseCurrency: CurrencyCode;
  totalInBaseCurrency: number;
}

// Para birimi tercihleri
export interface CurrencyPreferences {
  userId: string;
  baseCurrency: CurrencyCode;
  displayCurrencies: CurrencyCode[];
  autoConvert: boolean;
  showMultipleCurrencies: boolean;
  roundingMode: 'up' | 'down' | 'nearest';
  decimalPlaces: number;
}

// Desteklenen para birimleri
export const SUPPORTED_CURRENCIES: Currency[] = [
  {
    code: 'TRY',
    name: 'Türk Lirası',
    symbol: '₺',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇹🇷',
    country: 'Türkiye',
  },
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇺🇸',
    country: 'United States',
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇪🇺',
    country: 'European Union',
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇬🇧',
    country: 'United Kingdom',
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥',
    decimalPlaces: 0,
    isActive: true,
    flag: '🇯🇵',
    country: 'Japan',
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    symbol: 'C$',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇨🇦',
    country: 'Canada',
  },
  {
    code: 'AUD',
    name: 'Australian Dollar',
    symbol: 'A$',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇦🇺',
    country: 'Australia',
  },
  {
    code: 'CHF',
    name: 'Swiss Franc',
    symbol: 'CHF',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇨🇭',
    country: 'Switzerland',
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇨🇳',
    country: 'China',
  },
  {
    code: 'INR',
    name: 'Indian Rupee',
    symbol: '₹',
    decimalPlaces: 2,
    isActive: true,
    flag: '🇮🇳',
    country: 'India',
  },
];

// Para birimi helper fonksiyonları
export const getCurrencyByCode = (code: CurrencyCode): Currency | undefined => {
  return SUPPORTED_CURRENCIES.find(currency => currency.code === code);
};

export const formatCurrency = (
  amount: number, 
  currencyCode: CurrencyCode, 
  locale: string = 'tr-TR'
): string => {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) return amount.toString();

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: currency.decimalPlaces,
      maximumFractionDigits: currency.decimalPlaces,
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    return `${currency.symbol}${amount.toFixed(currency.decimalPlaces)}`;
  }
};

export const formatAmount = (
  amount: number,
  currencyCode: CurrencyCode,
  showSymbol: boolean = true
): string => {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) return amount.toString();

  const formattedAmount = amount.toFixed(currency.decimalPlaces);
  return showSymbol ? `${currency.symbol}${formattedAmount}` : formattedAmount;
};

// Para birimi dönüştürme helper'ı
export const convertCurrency = (
  amount: number,
  fromCurrency: CurrencyCode,
  toCurrency: CurrencyCode,
  exchangeRate: number
): number => {
  if (fromCurrency === toCurrency) return amount;
  return amount * exchangeRate;
};

// Para birimi validasyon
export const isValidCurrency = (code: string): code is CurrencyCode => {
  return SUPPORTED_CURRENCIES.some(currency => currency.code === code);
};

// Varsayılan döviz kurları (fallback)
export const DEFAULT_EXCHANGE_RATES: Record<string, number> = {
  'USD_TRY': 39.0,
  'EUR_TRY': 42.0,
  'GBP_TRY': 48.0,
  'JPY_TRY': 0.27,
  'CAD_TRY': 28.5,
  'AUD_TRY': 25.0,
  'CHF_TRY': 42.5,
  'CNY_TRY': 5.4,
  'INR_TRY': 0.46,
  // USD bazlı kurlar
  'EUR_USD': 1.08,
  'GBP_USD': 1.23,
  'JPY_USD': 0.0069,
  'CAD_USD': 0.73,
  'AUD_USD': 0.64,
  'CHF_USD': 1.09,
  'CNY_USD': 0.14,
  'INR_USD': 0.012,
};

// Döviz kuru cache süresi (milisaniye)
export const EXCHANGE_RATE_CACHE_DURATION = 15 * 60 * 1000; // 15 dakika

// API endpoint'leri
export const EXCHANGE_RATE_APIS = {
  primary: 'https://api.exchangerate-api.com/v4/latest/',
  fallback: 'https://api.fixer.io/latest',
  crypto: 'https://api.coingecko.com/api/v3/simple/price',
};

// Döviz kuru güncelleme durumu
export interface ExchangeRateUpdateStatus {
  lastUpdate: string;
  nextUpdate: string;
  isUpdating: boolean;
  source: string;
  success: boolean;
  error?: string;
}

// Çoklu para birimi işlem özeti
export interface MultiCurrencyTransactionSummary {
  baseCurrency: CurrencyCode;
  summaries: Record<CurrencyCode, {
    totalIncome: number;
    totalExpense: number;
    netAmount: number;
    transactionCount: number;
    inBaseCurrency: {
      totalIncome: number;
      totalExpense: number;
      netAmount: number;
    };
  }>;
  grandTotal: {
    totalIncome: number;
    totalExpense: number;
    netAmount: number;
    transactionCount: number;
  };
  exchangeRates: Record<string, number>; // "FROM_TO" format
  lastUpdated: string;
}

// Para birimi trend analizi
export interface CurrencyTrend {
  currencyPair: string;        // "USD_TRY" format
  currentRate: number;
  previousRate: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
  period: '1d' | '1w' | '1m' | '3m' | '1y';
  high: number;
  low: number;
  volatility: number;          // Volatilite skoru (0-100)
}

// Para birimi uyarıları
export interface CurrencyAlert {
  id: string;
  userId: string;
  currencyPair: string;
  alertType: 'above' | 'below' | 'change_percent';
  targetValue: number;
  currentValue: number;
  isActive: boolean;
  isTriggered: boolean;
  triggeredAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Offline para birimi desteği
export interface OfflineCurrencyData {
  rates: Record<string, number>;
  lastSync: string;
  baseCurrency: CurrencyCode;
  isStale: boolean;
  staleDuration: number;       // Milisaniye
}

export default {
  SUPPORTED_CURRENCIES,
  DEFAULT_EXCHANGE_RATES,
  EXCHANGE_RATE_CACHE_DURATION,
  EXCHANGE_RATE_APIS,
  getCurrencyByCode,
  formatCurrency,
  formatAmount,
  convertCurrency,
  isValidCurrency,
};
