// Receipts Screen - Tüm makbuzlar sayfası

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Modal,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import { Ionicons } from '@expo/vector-icons';
import TransactionRepository from '../../database/repositories/TransactionRepository';
import { Transaction } from '../../types/transaction';

interface ReceiptItem {
  id: string;
  imageUri: string;
  transactionId: string;
  transactionDescription: string;
  transactionAmount: number;
  transactionCurrency: string;
  transactionDate: string;
  transactionType: 'income' | 'expense';
  uploadedAt: string;
}

const ReceiptsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const { formatAmount } = useCurrency();

  const [receipts, setReceipts] = useState<ReceiptItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReceipt, setSelectedReceipt] = useState<ReceiptItem | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    loadReceipts();
  }, []);

  const loadReceipts = async () => {
    try {
      if (!user) return;

      setLoading(true);
      console.log('📄 Loading all receipts...');

      // Tüm transaction'ları al ve receipt'i olanları filtrele
      const transactions = await TransactionRepository.getByUserId(user.id);
      const transactionsWithReceipts = transactions.filter(t => t.receipt);

      const receiptItems: ReceiptItem[] = transactionsWithReceipts.map(transaction => ({
        id: transaction.receipt!.id,
        imageUri: transaction.receipt!.imageUri,
        transactionId: transaction.id,
        transactionDescription: transaction.description || 'Açıklama yok',
        transactionAmount: transaction.amount,
        transactionCurrency: transaction.currency,
        transactionDate: transaction.date,
        transactionType: transaction.type,
        uploadedAt: transaction.receipt!.uploadedAt,
      }));

      // Tarihe göre sırala (en yeni önce)
      receiptItems.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

      setReceipts(receiptItems);
      console.log(`✅ Loaded ${receiptItems.length} receipts`);
    } catch (error) {
      console.error('❌ Error loading receipts:', error);
      Alert.alert('Hata', 'Makbuzlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // formatCurrency artık formatAmount ile değiştirildi

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const openReceiptModal = (receipt: ReceiptItem) => {
    setSelectedReceipt(receipt);
    setModalVisible(true);
  };

  const navigateToTransaction = (transactionId: string) => {
    setModalVisible(false);
    navigation.navigate('TransactionDetail', { transactionId });
  };

  const renderReceiptItem = ({ item }: { item: ReceiptItem }) => (
    <TouchableOpacity
      style={styles.receiptItem}
      onPress={() => openReceiptModal(item)}
    >
      <Image source={{ uri: item.imageUri }} style={styles.receiptThumbnail} />
      <View style={styles.receiptInfo}>
        <Text style={styles.receiptDescription} numberOfLines={2}>
          {item.transactionDescription}
        </Text>
        <Text style={[
          styles.receiptAmount,
          { color: item.transactionType === 'income' ? theme.colors.success : theme.colors.error }
        ]}>
          {item.transactionType === 'income' ? '+' : '-'}{formatAmount(item.transactionAmount, item.transactionCurrency)}
        </Text>
        <Text style={styles.receiptDate}>
          {formatDate(item.transactionDate)}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    headerSubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyIcon: {
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    emptySubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 20,
    },
    receiptItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      marginHorizontal: 16,
      marginVertical: 4,
      borderRadius: 12,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    receiptThumbnail: {
      width: 60,
      height: 60,
      borderRadius: 8,
      backgroundColor: theme.colors.border,
    },
    receiptInfo: {
      flex: 1,
      marginLeft: 12,
    },
    receiptDescription: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 4,
    },
    receiptAmount: {
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 2,
    },
    receiptDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    // Modal Styles
    modal: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalHeader: {
      position: 'absolute',
      top: 50,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      zIndex: 1,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: 'white',
    },
    modalCloseButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 20,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalImage: {
      width: Dimensions.get('window').width - 40,
      height: Dimensions.get('window').height - 200,
      resizeMode: 'contain',
    },
    modalFooter: {
      position: 'absolute',
      bottom: 50,
      left: 20,
      right: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 12,
      padding: 16,
    },
    modalTransactionInfo: {
      alignItems: 'center',
    },
    modalTransactionDescription: {
      fontSize: 16,
      fontWeight: '600',
      color: 'white',
      marginBottom: 4,
    },
    modalTransactionAmount: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    modalTransactionDate: {
      fontSize: 14,
      color: 'rgba(255, 255, 255, 0.8)',
      marginBottom: 12,
    },
    modalViewButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    modalViewButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  if (loading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.emptySubtitle, { marginTop: 16 }]}>Makbuzlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Makbuzlar</Text>
          <Text style={styles.headerSubtitle}>
            {receipts.length} makbuz bulundu
          </Text>
        </View>
        <TouchableOpacity onPress={loadReceipts}>
          <Ionicons name="refresh" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Content */}
      {receipts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="document-outline"
            size={64}
            color={theme.colors.textSecondary}
            style={styles.emptyIcon}
          />
          <Text style={styles.emptyTitle}>Henüz makbuz yok</Text>
          <Text style={styles.emptySubtitle}>
            İşlem eklerken makbuz fotoğrafı çektiğinizde burada görünecek.
          </Text>
        </View>
      ) : (
        <FlatList
          data={receipts}
          renderItem={renderReceiptItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: 8 }}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Receipt Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Makbuz</Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {selectedReceipt && (
            <>
              <Image
                source={{ uri: selectedReceipt.imageUri }}
                style={styles.modalImage}
                onError={() => {
                  Alert.alert('Hata', 'Makbuz görüntülenirken bir hata oluştu.');
                  setModalVisible(false);
                }}
              />

              <View style={styles.modalFooter}>
                <View style={styles.modalTransactionInfo}>
                  <Text style={styles.modalTransactionDescription}>
                    {selectedReceipt.transactionDescription}
                  </Text>
                  <Text style={[
                    styles.modalTransactionAmount,
                    { color: selectedReceipt.transactionType === 'income' ? '#4CAF50' : '#F44336' }
                  ]}>
                    {selectedReceipt.transactionType === 'income' ? '+' : '-'}
                    {formatAmount(selectedReceipt.transactionAmount, selectedReceipt.transactionCurrency)}
                  </Text>
                  <Text style={styles.modalTransactionDate}>
                    {formatDate(selectedReceipt.transactionDate)}
                  </Text>
                  <TouchableOpacity
                    style={styles.modalViewButton}
                    onPress={() => navigateToTransaction(selectedReceipt.transactionId)}
                  >
                    <Text style={styles.modalViewButtonText}>İşlemi Görüntüle</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}
        </View>
      </Modal>
    </View>
  );
};

export default ReceiptsScreen;
