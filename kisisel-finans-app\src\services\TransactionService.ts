// RFC-002 Transaction Service - Gerçek Veritabanı Entegrasyonu

import TransactionRepository from '../database/repositories/TransactionRepository';
import { Transaction, TransactionFilter, TransactionSort, Pagination } from '../types/transaction';

class TransactionService {
  private repository: TransactionRepository;

  constructor() {
    this.repository = TransactionRepository;
  }

  /**
   * <PERSON><PERSON>ı<PERSON>ının tüm işlemlerini getir
   */
  async getUserTransactions(userId: string): Promise<Transaction[]> {
    try {
      console.log(`📊 Getting transactions for user: ${userId}`);
      const transactions = await this.repository.getByUserId(userId);
      console.log(`✅ Retrieved ${transactions.length} transactions`);
      return transactions;
    } catch (error) {
      console.error('❌ Error getting user transactions:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> kategorilerdeki işlemleri getir (Bütçe detayı için)
   */
  async getTransactionsByCategories(
    userId: string,
    categoryIds: string[],
    startDate?: string,
    endDate?: string,
    limit: number = 10
  ): Promise<Transaction[]> {
    try {
      console.log(`📊 Getting transactions for categories:`, categoryIds);
      console.log(`📊 User ID:`, userId);

      const filter: TransactionFilter = {
        categories: categoryIds as any,
        dateRange: startDate && endDate ? { start: startDate, end: endDate } : undefined
      };

      const sort: TransactionSort = { field: 'date', direction: 'desc' };
      const pagination: Pagination = { page: 1, limit };

      const result = await this.repository.getList(userId, filter, sort, pagination);
      console.log(`✅ Retrieved ${result.transactions.length} transactions for categories`);

      return result.transactions;
    } catch (error) {
      console.error('❌ Error getting transactions by categories:', error);
      throw error;
    }
  }

  /**
   * Bütçe kategorilerinin harcama toplamlarını hesapla
   */
  async calculateCategorySpending(
    userId: string,
    categoryIds: string[],
    startDate: string,
    endDate: string
  ): Promise<Record<string, number>> {
    try {
      console.log(`📊 Calculating spending for categories:`, categoryIds);
      console.log(`📊 Date range: ${startDate} to ${endDate}`);

      // Önce tüm expense transaction'ları al (filter olmadan)
      const allExpenseFilter: TransactionFilter = {
        type: 'expense',
        dateRange: { start: startDate, end: endDate }
      };

      const result = await this.repository.getList(userId, allExpenseFilter);
      console.log(`📊 Found ${result.transactions.length} expense transactions`);

      // Debug: Tüm transaction'ları logla
      result.transactions.forEach((transaction, index) => {
        console.log(`📊 Transaction ${index + 1}:`, {
          id: transaction.id,
          type: transaction.type,
          amount: transaction.amount,
          category: transaction.category,
          categoryId: (transaction as any).categoryId,
          date: transaction.date,
          description: transaction.description
        });
      });

      // Kategori bazında toplamları hesapla
      const categorySpending: Record<string, number> = {};

      categoryIds.forEach(categoryId => {
        categorySpending[categoryId] = 0;
        console.log(`📊 Initialized category ${categoryId} with 0`);
      });

      result.transactions.forEach(transaction => {
        if (transaction.type === 'expense') {
          console.log(`📊 Processing expense transaction:`, {
            category: transaction.category,
            categoryId: (transaction as any).categoryId,
            amount: transaction.amount
          });

          // Akıllı kategori eşleştirme
          const matchingCategoryId = categoryIds.find(catId => {
            // 1. Direct ID match
            if (catId === (transaction as any).categoryId) {
              console.log(`📊 ✅ Direct ID match: ${catId}`);
              return true;
            }

            // 2. Category string match
            if (catId === transaction.category) {
              console.log(`📊 ✅ Category string match: ${catId}`);
              return true;
            }

            // 3. Category name mapping (shopping -> Alışveriş)
            const categoryMapping: Record<string, string> = {
              'shopping': 'Alışveriş',
              'food': 'Yiyecek',
              'transportation': 'transport',
              'bills': 'Faturalar',
              'education': 'Eğitim',
              'personal_care': 'personal',
              'gifts': 'Hediyeler'
            };

            const mappedCategory = categoryMapping[transaction.category];
            if (mappedCategory && catId.includes(mappedCategory)) {
              console.log(`📊 ✅ Mapped category match: ${transaction.category} -> ${mappedCategory} in ${catId}`);
              return true;
            }

            console.log(`📊 ❌ No match for ${catId} against ${transaction.category}/${(transaction as any).categoryId}`);
            return false;
          });

          if (matchingCategoryId) {
            categorySpending[matchingCategoryId] += transaction.amount;
            console.log(`💰 Added ${transaction.amount} to category ${matchingCategoryId} (from transaction: ${transaction.category})`);
          } else {
            console.log(`❌ No matching category found for transaction: ${transaction.category}/${(transaction as any).categoryId}`);
          }
        }
      });

      console.log(`✅ Final category spending:`, categorySpending);
      return categorySpending;
    } catch (error) {
      console.error('❌ Error calculating category spending:', error);
      throw error;
    }
  }

  /**
   * Yeni işlem oluştur (temiz hibrit sistem)
   */
  async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log(`💾 TransactionService: Creating new transaction:`, {
        type: transaction.type,
        amount: transaction.amount,
        category: transaction.category,
        description: transaction.description,
        budgetId: transaction.budgetId
      });

      console.log(`🔍 STEP 1: About to find category_id for category: "${transaction.category}"`);
      console.log(`🔍 STEP 1: User ID: "${transaction.userId}"`);
      console.log(`🔍 STEP 1: Category type: ${typeof transaction.category}`);

      // 1. Kategori string'ini category_id'ye çevir
      const categoryId = await this.findCategoryIdByName(transaction.userId, transaction.category, transaction.type);

      console.log(`🔍 STEP 1 RESULT: categoryId = ${categoryId}`);
      if (!categoryId) {
        console.error(`❌ Category not found: ${transaction.category}`);
        throw new Error(`Kategori bulunamadı: ${transaction.category}`);
      }
      console.log(`🏷️ Found category_id: ${categoryId} for category: ${transaction.category}`);

      // 2. Otomatik bütçe eşleştirme (sadece expense için)
      let budgetId = transaction.budgetId;
      if (!budgetId && transaction.type === 'expense') {
        budgetId = await this.autoMatchBudget(transaction.userId, categoryId, transaction.date);
        if (budgetId) {
          console.log(`🎯 Auto-matched expense to budget: ${budgetId}`);
        }
      }

      // 3. İşlemi oluştur (temiz şema ile)
      const cleanTransaction = {
        ...transaction,
        categoryId: categoryId,
        budgetId: budgetId
      };

      const transactionId = await this.repository.create(cleanTransaction);
      console.log(`✅ TransactionService: Transaction created with ID: ${transactionId}`);

      // 4. Gelir işlemi için otomatik bütçe bağlama (işlem oluşturulduktan sonra)
      if (transaction.type === 'income' && !budgetId) {
        try {
          const { default: IncomeHybridService } = await import('./IncomeHybridService');
          const linkedBudgetId = await IncomeHybridService.linkIncomeTransactionToBudget(
            transactionId,
            transaction.userId,
            categoryId,
            transaction.amount,
            transaction.date
          );
          if (linkedBudgetId) {
            console.log(`💰 Auto-linked income to budget: ${linkedBudgetId}`);
          }
        } catch (error) {
          console.error('❌ Error auto-linking income to budget:', error);
          // Bağlama hatası işlem oluşturmayı iptal etmez
        }
      }

      return transactionId;
    } catch (error) {
      console.error('❌ TransactionService: Error creating transaction:', error);
      throw error;
    }
  }

  /**
   * Otomatik bütçe eşleştirme (senin önerdiğin temiz yöntem)
   */
  private async autoMatchBudget(userId: string, categoryId: string, transactionDate: string): Promise<string | null> {
    try {
      console.log('🔍 Auto-matching budget:', { userId, categoryId, transactionDate });

      const db = this.repository['dbManager'].getDatabase();

      const result = await db.getFirstAsync(
        `SELECT b.id
         FROM budgets b
         JOIN budget_categories bc ON b.id = bc.budget_id
         WHERE b.user_id = ?
           AND bc.category_id = ?
           AND date(?) BETWEEN date(b.start_date) AND date(b.end_date)
           AND b.is_active = 1
         ORDER BY b.start_date DESC
         LIMIT 1`,
        [userId, categoryId, transactionDate]
      );

      if (result?.id) {
        console.log(`🎯 Auto-matched budget found: ${result.id}`);
        return result.id;
      }

      console.log('🔍 No matching budget found for auto-linking');
      return null;
    } catch (error) {
      console.error('❌ Error in auto-matching budget:', error);
      return null;
    }
  }

  /**
   * Kategori adından category_id bul - RFC-003 Code Bazlı Sistem
   */
  private async findCategoryIdByName(userId: string, categoryName: string, transactionType: 'income' | 'expense'): Promise<string | null> {
    try {
      const db = this.repository['dbManager'].getDatabase();

      console.log(`🔍 Looking for category: "${categoryName}" for user: ${userId}`);

      // 1. Önce code ile ara (en hızlı ve güvenilir)
      const categoryCode = this.mapNameToCode(categoryName);
      if (categoryCode !== categoryName) {
        console.log(`🔍 Step 1: Code mapping: "${categoryName}" → "${categoryCode}"`);

        let result = await db.getFirstAsync(
          'SELECT id, name, code FROM categories WHERE code = ? AND type = ?',
          [categoryCode, transactionType]
        );
        console.log(`🔍 Step 1 result:`, result);

        if (result) {
          console.log(`✅ Found category by code: ${result.id} (${result.name})`);
          return result.id;
        }
      }

      // 2. Code bulunamazsa, direkt isimle ara (case-insensitive)
      console.log(`🔍 Step 2: Searching by name: "${categoryName}"`);
      let result = await db.getFirstAsync(
        'SELECT id, name, code FROM categories WHERE LOWER(name) = LOWER(?) AND type = ?',
        [categoryName, transactionType]
      );
      console.log(`🔍 Step 2 result:`, result);

      if (result) {
        console.log(`✅ Found category by name: ${result.id} (${result.name})`);
        return result.id;
      }

      // 3. Türkçe mapping ile ara (backward compatibility)
      const turkishName = this.mapEnglishToTurkish(categoryName);
      if (turkishName !== categoryName) {
        console.log(`🔍 Step 3: Turkish mapping: "${categoryName}" → "${turkishName}"`);

        result = await db.getFirstAsync(
          'SELECT id, name, code FROM categories WHERE LOWER(name) = LOWER(?) AND type = ?',
          [turkishName, transactionType]
        );
        console.log(`🔍 Step 3 result:`, result);

        if (result) {
          console.log(`✅ Found category by Turkish name: ${result.id} (${result.name})`);
          return result.id;
        }
      }

      // Debug: Tüm kategorileri listele
      const allCategories = await db.getAllAsync(
        'SELECT id, name, code FROM categories WHERE type = ?',
        [transactionType]
      );
      console.log(`❌ Category not found. Available categories:`, allCategories);

      return null;
    } catch (error) {
      console.error('❌ Error finding category ID:', error);
      return null;
    }
  }

  /**
   * Kategori adını code'a çevir - RFC-003 Code Bazlı Sistem
   */
  private mapNameToCode(categoryName: string): string {
    const nameToCodeMapping: Record<string, string> = {
      // INCOME CATEGORIES - İngilizce → Code
      'salary': 'salary',
      'investment': 'investment_income',
      'investment_income': 'investment_income',
      'side_income': 'side_income',
      'freelance': 'freelance',
      'rental_income': 'rental_income',
      'donation_income': 'donation_income',
      'other_income': 'other_income',

      // INCOME CATEGORIES - Türkçe → Code
      'maaş': 'salary',
      'yatırım geliri': 'investment_income',
      'yan gelir': 'side_income',
      'kira geliri': 'rental_income',
      'bağış geliri': 'donation_income',
      'diğer gelir': 'other_income',

      // EXPENSE CATEGORIES - İngilizce → Code
      'food': 'food',
      'transportation': 'transportation',
      'transport': 'transportation',  // Alternatif
      'bills': 'bills',
      'shopping': 'shopping',
      'entertainment': 'entertainment',
      'health': 'health',
      'education': 'education',
      'rent': 'rent',
      'insurance': 'insurance',
      'personal_care': 'personal_care',
      'gifts': 'gifts',
      'travel': 'travel',
      'donation': 'donation',
      'charity': 'donation',  // Alternatif
      'tax': 'tax',
      'taxes': 'tax',  // Alternatif
      'housing': 'housing',
      'other': 'other',

      // EXPENSE CATEGORIES - Türkçe → Code (backward compatibility)
      'yiyecek': 'food',
      'yiyecek & içecek': 'food',
      'ulaşım': 'transportation',
      'faturalar': 'bills',
      'alışveriş': 'shopping',
      'eğlence': 'entertainment',
      'sağlık': 'health',
      'eğitim': 'education',
      'kira': 'rent',
      'sigorta': 'insurance',
      'kişisel bakım': 'personal_care',
      'hediyeler': 'gifts',
      'seyahat': 'travel',
      'bağış': 'donation',
      'vergiler': 'tax',
      'konut': 'housing',
      'diğer': 'other'
    };

    return nameToCodeMapping[categoryName.toLowerCase()] || categoryName;
  }

  /**
   * İngilizce kategori adını Türkçe'ye çevir (backward compatibility)
   */
  private mapEnglishToTurkish(englishName: string): string {
    const mapping: Record<string, string> = {
      // INCOME CATEGORIES
      'salary': 'Maaş',
      'investment_income': 'Yatırım Geliri',
      'side_income': 'Yan Gelir',
      'freelance': 'Freelance',
      'rental_income': 'Kira Geliri',
      'donation_income': 'Bağış Geliri',
      'other_income': 'Diğer Gelir',

      // EXPENSE CATEGORIES
      'shopping': 'Alışveriş',
      'food': 'Yiyecek & İçecek',
      'transport': 'Ulaşım',        // ✅ "transport" (doğru)
      'transportation': 'Ulaşım',   // ✅ "transportation" (alternatif)
      'bills': 'Faturalar',
      'entertainment': 'Eğlence',
      'health': 'Sağlık',
      'education': 'Eğitim',
      'travel': 'Seyahat',
      'clothing': 'Giyim',
      'home': 'Ev & Yaşam',
      'groceries': 'Market',
      'fuel': 'Yakıt',
      'insurance': 'Sigorta',
      'investment': 'Yatırım',
      'gift': 'Hediye',
      'charity': 'Bağış',
      'rent': 'Kira',
      'personal_care': 'Kişisel Bakım',
      'gifts': 'Hediyeler',
      'donation': 'Bağış',
      'tax': 'Vergiler',
      'housing': 'Konut',
      'other': 'Diğer'
    };

    return mapping[englishName.toLowerCase()] || englishName;
  }

  /**
   * İşlem güncelle
   */
  async updateTransaction(id: string, updates: Partial<Transaction>): Promise<void> {
    try {
      console.log(`📝 Updating transaction: ${id}`);
      await this.repository.update(id, updates);
      console.log(`✅ Transaction updated: ${id}`);
    } catch (error) {
      console.error('❌ Error updating transaction:', error);
      throw error;
    }
  }

  /**
   * İşlem sil
   */
  async deleteTransaction(id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting transaction: ${id}`);
      await this.repository.delete(id);
      console.log(`✅ Transaction deleted: ${id}`);
    } catch (error) {
      console.error('❌ Error deleting transaction:', error);
      throw error;
    }
  }

  /**
   * İşlem detayını getir
   */
  async getTransactionById(id: string): Promise<Transaction | null> {
    try {
      console.log(`📊 Getting transaction by ID: ${id}`);
      const transaction = await this.repository.getById(id);
      console.log(`✅ Transaction retrieved:`, transaction?.id);
      return transaction;
    } catch (error) {
      console.error('❌ Error getting transaction by ID:', error);
      throw error;
    }
  }

  /**
   * İşlem özeti getir
   */
  async getTransactionSummary(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<any> {
    try {
      console.log(`📊 Getting transaction summary for user: ${userId}`);
      
      const filter: TransactionFilter = {
        dateRange: { start: startDate, end: endDate }
      };

      const summary = await this.repository.getSummary(filter);
      console.log(`✅ Transaction summary calculated`);
      
      return summary;
    } catch (error) {
      console.error('❌ Error getting transaction summary:', error);
      throw error;
    }
  }

  /**
   * Son işlemleri getir (Dashboard için)
   */
  async getRecentTransactions(userId: string, limit: number = 5): Promise<Transaction[]> {
    try {
      console.log(`📊 Getting recent transactions for user: ${userId}`);
      
      const sort: TransactionSort = { field: 'date', direction: 'desc' };
      const pagination: Pagination = { page: 1, limit };

      const result = await this.repository.getList(userId, {}, sort, pagination);
      console.log(`✅ Retrieved ${result.transactions.length} recent transactions`);
      
      return result.transactions;
    } catch (error) {
      console.error('❌ Error getting recent transactions:', error);
      throw error;
    }
  }
}

export default new TransactionService();
