// Settings Slice - Ayarlar state yönetimi

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import * as SecureStore from 'expo-secure-store';

interface SettingsState {
  theme: 'light' | 'dark' | 'system';
  currency: string;
  language: string;
  notifications: {
    budgetAlerts: boolean;
    goalReminders: boolean;
    debtReminders: boolean;
    transactionReminders: boolean;
    weeklyReports: boolean;
    monthlyReports: boolean;
  };
  security: {
    biometricEnabled: boolean;
    autoLockEnabled: boolean;
    autoLockTime: number; // dakika
    requireAuthForTransactions: boolean;
  };
  privacy: {
    dataCollection: boolean;
    analytics: boolean;
    crashReports: boolean;
  };
  display: {
    showBalance: boolean;
    compactView: boolean;
    chartAnimations: boolean;
  };
  backup: {
    autoBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    lastBackup: string | null;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: SettingsState = {
  theme: 'system',
  currency: 'TRY',
  language: 'tr',
  notifications: {
    budgetAlerts: true,
    goalReminders: true,
    debtReminders: true,
    transactionReminders: false,
    weeklyReports: true,
    monthlyReports: true,
  },
  security: {
    biometricEnabled: false,
    autoLockEnabled: true,
    autoLockTime: 5,
    requireAuthForTransactions: false,
  },
  privacy: {
    dataCollection: true,
    analytics: true,
    crashReports: true,
  },
  display: {
    showBalance: true,
    compactView: false,
    chartAnimations: true,
  },
  backup: {
    autoBackup: true,
    backupFrequency: 'weekly',
    lastBackup: null,
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const loadSettings = createAsyncThunk(
  'settings/loadSettings',
  async (_, { rejectWithValue }) => {
    try {
      const settingsJson = await SecureStore.getItemAsync('userSettings');
      if (settingsJson) {
        return JSON.parse(settingsJson);
      }
      return initialState;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Ayarlar yüklenirken hata oluştu');
    }
  }
);

export const saveSettings = createAsyncThunk(
  'settings/saveSettings',
  async (settings: Partial<SettingsState>, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { settings: SettingsState };
      const updatedSettings = { ...state.settings, ...settings };
      
      await SecureStore.setItemAsync('userSettings', JSON.stringify(updatedSettings));
      return updatedSettings;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Ayarlar kaydedilirken hata oluştu');
    }
  }
);

export const resetSettings = createAsyncThunk(
  'settings/resetSettings',
  async (_, { rejectWithValue }) => {
    try {
      await SecureStore.deleteItemAsync('userSettings');
      return initialState;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Ayarlar sıfırlanırken hata oluştu');
    }
  }
);

export const exportSettings = createAsyncThunk(
  'settings/exportSettings',
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { settings: SettingsState };
      const settingsData = {
        ...state.settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
      
      return JSON.stringify(settingsData, null, 2);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Ayarlar dışa aktarılırken hata oluştu');
    }
  }
);

export const importSettings = createAsyncThunk(
  'settings/importSettings',
  async (settingsJson: string, { rejectWithValue }) => {
    try {
      const importedSettings = JSON.parse(settingsJson);
      
      // Güvenlik kontrolü - sadece bilinen ayarları kabul et
      const validSettings = {
        theme: importedSettings.theme || initialState.theme,
        currency: importedSettings.currency || initialState.currency,
        language: importedSettings.language || initialState.language,
        notifications: { ...initialState.notifications, ...importedSettings.notifications },
        security: { ...initialState.security, ...importedSettings.security },
        privacy: { ...initialState.privacy, ...importedSettings.privacy },
        display: { ...initialState.display, ...importedSettings.display },
        backup: { ...initialState.backup, ...importedSettings.backup },
      };
      
      await SecureStore.setItemAsync('userSettings', JSON.stringify(validSettings));
      return validSettings;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Ayarlar içe aktarılırken hata oluştu');
    }
  }
);

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    updateNotificationSettings: (state, action: PayloadAction<Partial<SettingsState['notifications']>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    updateSecuritySettings: (state, action: PayloadAction<Partial<SettingsState['security']>>) => {
      state.security = { ...state.security, ...action.payload };
    },
    updatePrivacySettings: (state, action: PayloadAction<Partial<SettingsState['privacy']>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    updateDisplaySettings: (state, action: PayloadAction<Partial<SettingsState['display']>>) => {
      state.display = { ...state.display, ...action.payload };
    },
    updateBackupSettings: (state, action: PayloadAction<Partial<SettingsState['backup']>>) => {
      state.backup = { ...state.backup, ...action.payload };
    },
    setLastBackup: (state, action: PayloadAction<string>) => {
      state.backup.lastBackup = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Load settings
    builder
      .addCase(loadSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        Object.assign(state, action.payload);
      })
      .addCase(loadSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Save settings
    builder
      .addCase(saveSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        Object.assign(state, action.payload);
      })
      .addCase(saveSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Reset settings
    builder
      .addCase(resetSettings.fulfilled, (state, action) => {
        Object.assign(state, action.payload);
      });

    // Import settings
    builder
      .addCase(importSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(importSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        Object.assign(state, action.payload);
      })
      .addCase(importSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setTheme,
  setCurrency,
  setLanguage,
  updateNotificationSettings,
  updateSecuritySettings,
  updatePrivacySettings,
  updateDisplaySettings,
  updateBackupSettings,
  setLastBackup,
} = settingsSlice.actions;

export default settingsSlice.reducer;
