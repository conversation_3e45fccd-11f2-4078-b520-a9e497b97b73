// OCR Modal - Fiş okuma ve düzenleme modal'ı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import OCRService, { ExtractedReceiptData } from '../services/OCRService';

interface OCRModalProps {
  visible: boolean;
  onClose: () => void;
  imageUri: string;
  onDataExtracted: (data: ExtractedReceiptData) => void;
}

const OCRModal: React.FC<OCRModalProps> = ({
  visible,
  onClose,
  imageUri,
  onDataExtracted,
}) => {
  const { theme } = useTheme();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedData, setExtractedData] = useState<ExtractedReceiptData | null>(null);
  const [rawText, setRawText] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);

  // Form state for editing
  const [editData, setEditData] = useState<ExtractedReceiptData | null>(null);

  // OCR işlemini başlat
  const handleProcessOCR = async () => {
    try {
      setIsProcessing(true);
      console.log('📄 Starting OCR process for image:', imageUri);

      const result = await OCRService.processReceiptImage(imageUri);
      
      if (result.success && result.extractedData) {
        setExtractedData(result.extractedData);
        setEditData({ ...result.extractedData });
        setRawText(result.rawText || '');
        
        // Sonucu doğrula
        const validation = OCRService.validateOCRResult(result.extractedData);
        if (!validation.isValid) {
          Alert.alert(
            'OCR Uyarısı',
            `Bazı veriler eksik olabilir:\n${validation.issues.join('\n')}\n\nLütfen verileri kontrol edin.`,
            [{ text: 'Tamam' }]
          );
        }
      } else {
        Alert.alert(
          'OCR Hatası',
          result.error || 'Fiş okunamadı. Lütfen daha net bir fotoğraf çekin.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error) {
      console.error('❌ OCR Modal Error:', error);
      Alert.alert(
        'Hata',
        'OCR işlemi sırasında bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Düzenleme modunu aç/kapat
  const toggleEdit = () => {
    if (isEditing) {
      // Düzenleme bitiriliyor, değişiklikleri kaydet
      if (editData) {
        setExtractedData({ ...editData });
      }
    }
    setIsEditing(!isEditing);
  };

  // Verileri onayla ve geri gönder
  const handleConfirm = () => {
    if (extractedData) {
      onDataExtracted(extractedData);
      onClose();
    }
  };

  // Modal'ı kapat
  const handleClose = () => {
    setExtractedData(null);
    setEditData(null);
    setRawText('');
    setIsEditing(false);
    onClose();
  };

  // Input değişikliklerini handle et
  const handleInputChange = (field: keyof ExtractedReceiptData, value: string | number) => {
    if (editData) {
      setEditData({
        ...editData,
        [field]: value
      });
    }
  };

  // Tarih formatını düzenle
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('tr-TR');
    } catch {
      return dateString;
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Fiş Okuma (OCR)
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Fiş Görüntüsü */}
          <View style={styles.imageSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Fiş Fotoğrafı
            </Text>
            <Image source={{ uri: imageUri }} style={styles.receiptImage} />
          </View>

          {/* OCR İşlem Butonu */}
          {!extractedData && (
            <TouchableOpacity
              style={[
                styles.processButton,
                { backgroundColor: theme.colors.primary },
                isProcessing && { opacity: 0.7 }
              ]}
              onPress={handleProcessOCR}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color={theme.colors.surface} />
              ) : (
                <Ionicons name="scan" size={20} color={theme.colors.surface} />
              )}
              <Text style={[styles.processButtonText, { color: theme.colors.surface }]}>
                {isProcessing ? 'Fiş Okunuyor...' : 'Fişi Oku'}
              </Text>
            </TouchableOpacity>
          )}

          {/* OCR Sonuçları */}
          {extractedData && (
            <View style={styles.resultsSection}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Çıkarılan Veriler
                </Text>
                <TouchableOpacity
                  style={[styles.editButton, { backgroundColor: theme.colors.primary }]}
                  onPress={toggleEdit}
                >
                  <Ionicons 
                    name={isEditing ? "checkmark" : "pencil"} 
                    size={16} 
                    color={theme.colors.surface} 
                  />
                  <Text style={[styles.editButtonText, { color: theme.colors.surface }]}>
                    {isEditing ? 'Kaydet' : 'Düzenle'}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Güven Skoru */}
              <View style={[styles.confidenceCard, { backgroundColor: theme.colors.surface }]}>
                <Ionicons 
                  name="analytics" 
                  size={20} 
                  color={extractedData.confidence > 0.7 ? theme.colors.success : theme.colors.warning} 
                />
                <Text style={[styles.confidenceText, { color: theme.colors.text }]}>
                  Güven Skoru: %{Math.round(extractedData.confidence * 100)}
                </Text>
              </View>

              {/* Veri Alanları */}
              <View style={styles.dataFields}>
                {/* Tutar */}
                <View style={styles.fieldRow}>
                  <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Tutar:</Text>
                  {isEditing ? (
                    <TextInput
                      style={[styles.fieldInput, { 
                        backgroundColor: theme.colors.surface,
                        color: theme.colors.text,
                        borderColor: theme.colors.border
                      }]}
                      value={editData?.amount?.toString() || ''}
                      onChangeText={(text) => handleInputChange('amount', parseFloat(text) || 0)}
                      keyboardType="numeric"
                      placeholder="0.00"
                    />
                  ) : (
                    <Text style={[styles.fieldValue, { color: theme.colors.text }]}>
                      {extractedData.amount?.toFixed(2) || 'Bulunamadı'} {extractedData.currency || 'TRY'}
                    </Text>
                  )}
                </View>

                {/* Mağaza */}
                <View style={styles.fieldRow}>
                  <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Mağaza:</Text>
                  {isEditing ? (
                    <TextInput
                      style={[styles.fieldInput, { 
                        backgroundColor: theme.colors.surface,
                        color: theme.colors.text,
                        borderColor: theme.colors.border
                      }]}
                      value={editData?.merchant || ''}
                      onChangeText={(text) => handleInputChange('merchant', text)}
                      placeholder="Mağaza adı"
                    />
                  ) : (
                    <Text style={[styles.fieldValue, { color: theme.colors.text }]}>
                      {extractedData.merchant || 'Bulunamadı'}
                    </Text>
                  )}
                </View>

                {/* Tarih */}
                <View style={styles.fieldRow}>
                  <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Tarih:</Text>
                  <Text style={[styles.fieldValue, { color: theme.colors.text }]}>
                    {formatDate(extractedData.date) || 'Bulunamadı'}
                  </Text>
                </View>

                {/* KDV */}
                {extractedData.taxAmount && (
                  <View style={styles.fieldRow}>
                    <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>KDV:</Text>
                    <Text style={[styles.fieldValue, { color: theme.colors.text }]}>
                      {extractedData.taxAmount.toFixed(2)} {extractedData.currency || 'TRY'}
                    </Text>
                  </View>
                )}

                {/* Fiş No */}
                {extractedData.receiptNumber && (
                  <View style={styles.fieldRow}>
                    <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>Fiş No:</Text>
                    <Text style={[styles.fieldValue, { color: theme.colors.text }]}>
                      {extractedData.receiptNumber}
                    </Text>
                  </View>
                )}
              </View>

              {/* Ham Metin */}
              {rawText && (
                <View style={styles.rawTextSection}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                    Ham OCR Metni
                  </Text>
                  <ScrollView 
                    style={[styles.rawTextContainer, { backgroundColor: theme.colors.surface }]}
                    nestedScrollEnabled
                  >
                    <Text style={[styles.rawText, { color: theme.colors.textSecondary }]}>
                      {rawText}
                    </Text>
                  </ScrollView>
                </View>
              )}
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        {extractedData && (
          <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: theme.colors.success }]}
              onPress={handleConfirm}
            >
              <Ionicons name="checkmark" size={20} color={theme.colors.surface} />
              <Text style={[styles.confirmButtonText, { color: theme.colors.surface }]}>
                Verileri Onayla
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  imageSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    resizeMode: 'contain',
  },
  processButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
    marginBottom: 24,
  },
  processButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  resultsSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  confidenceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
    marginBottom: 16,
  },
  confidenceText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dataFields: {
    gap: 12,
    marginBottom: 16,
  },
  fieldRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  fieldValue: {
    fontSize: 14,
    flex: 2,
    textAlign: 'right',
  },
  fieldInput: {
    flex: 2,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    textAlign: 'right',
  },
  rawTextSection: {
    marginTop: 16,
  },
  rawTextContainer: {
    maxHeight: 120,
    borderRadius: 8,
    padding: 12,
  },
  rawText: {
    fontSize: 12,
    lineHeight: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  confirmButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OCRModal;
