// Encryption Test Screen - Şifreleme test ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
// import { useAuth } from '../../contexts/AuthContext';
import EncryptionService from '../../services/EncryptionService';
import SecureStorageWrapper from '../../services/SecureStorageWrapper';

const EncryptionTestScreen: React.FC = () => {
  const { theme } = useTheme();
  // Enterprise encryption features geçici olarak kapatıldı
  // const { rotateEncryptionKeys, getEncryptionStatus } = useAuth();

  const [testData, setTestData] = useState('Bu hassas bir test verisidir! 🔐');
  const [encryptedResult, setEncryptedResult] = useState('');
  const [decryptedResult, setDecryptedResult] = useState('');
  const [encryptionStatus, setEncryptionStatus] = useState<any>(null);
  const [storageStats, setStorageStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Temel şifreleme testi
  const testBasicEncryption = async () => {
    try {
      setIsLoading(true);

      // Şifrele
      const encrypted = await EncryptionService.encrypt(testData);
      setEncryptedResult(JSON.stringify(encrypted, null, 2));

      // Çöz
      const decrypted = await EncryptionService.decrypt(encrypted);
      setDecryptedResult(decrypted);

      // Sonuç kontrolü
      if (testData === decrypted) {
        Alert.alert('✅ Başarılı', 'Şifreleme ve çözme işlemi başarılı!');
      } else {
        Alert.alert('❌ Hata', 'Şifreleme/çözme işleminde hata!');
      }
    } catch (error) {
      Alert.alert('Hata', `Şifreleme testi başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Finansal veri testi
  const testFinancialData = async () => {
    try {
      setIsLoading(true);

      const financialData = {
        amount: 1500.50,
        description: 'Test finansal işlem',
        category: 'Gıda',
        date: new Date().toISOString(),
        type: 'expense',
      };

      // Finansal veri sakla
      await SecureStorageWrapper.setFinancialData('test_transaction', financialData);

      // Geri al
      const retrieved = await SecureStorageWrapper.getFinancialData('test_transaction');

      if (JSON.stringify(financialData) === JSON.stringify(retrieved)) {
        Alert.alert('✅ Başarılı', 'Finansal veri şifreleme başarılı!');
      } else {
        Alert.alert('❌ Hata', 'Finansal veri test başarısız!');
      }
    } catch (error) {
      Alert.alert('Hata', `Finansal veri testi başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Kullanıcı ayarları testi
  const testUserSettings = async () => {
    try {
      setIsLoading(true);

      const settings = {
        currency: 'TRY',
        language: 'tr',
        notifications: true,
        theme: 'dark',
        biometric: false,
      };

      // Ayarları sakla
      await SecureStorageWrapper.setUserSettings(settings);

      // Geri al
      const retrieved = await SecureStorageWrapper.getUserSettings();

      if (JSON.stringify(settings) === JSON.stringify(retrieved)) {
        Alert.alert('✅ Başarılı', 'Kullanıcı ayarları şifreleme başarılı!');
      } else {
        Alert.alert('❌ Hata', 'Kullanıcı ayarları test başarısız!');
      }
    } catch (error) {
      Alert.alert('Hata', `Kullanıcı ayarları testi başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Cache testi
  const testCacheData = async () => {
    try {
      setIsLoading(true);

      const cacheData = {
        exchangeRates: { USD: 1, EUR: 0.85, TRY: 30 },
        lastUpdated: Date.now(),
      };

      // Cache'e sakla (5 dakika)
      await SecureStorageWrapper.setCacheData('exchange_rates', cacheData, 5);

      // Geri al
      const retrieved = await SecureStorageWrapper.getCacheData('exchange_rates');

      if (JSON.stringify(cacheData) === JSON.stringify(retrieved)) {
        Alert.alert('✅ Başarılı', 'Cache veri test başarılı!');
      } else {
        Alert.alert('❌ Hata', 'Cache veri test başarısız!');
      }
    } catch (error) {
      Alert.alert('Hata', `Cache testi başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Key rotation testi
  const testKeyRotation = async () => {
    try {
      setIsLoading(true);

      // Mevcut durum
      const statusBefore = await getEncryptionStatus();

      // Key rotation
      await rotateEncryptionKeys();

      // Yeni durum
      const statusAfter = await getEncryptionStatus();

      Alert.alert(
        '✅ Key Rotation Tamamlandı',
        `Rotation Count: ${statusBefore.keyInfo?.rotationCount || 0} → ${statusAfter.keyInfo?.rotationCount || 0}`
      );
    } catch (error) {
      Alert.alert('Hata', `Key rotation başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Encryption durumunu al
  const checkEncryptionStatus = async () => {
    try {
      const status = await getEncryptionStatus();
      setEncryptionStatus(status);
    } catch (error) {
      Alert.alert('Hata', `Durum kontrolü başarısız: ${error}`);
    }
  };

  // Storage istatistikleri al
  const checkStorageStats = async () => {
    try {
      const stats = await SecureStorageWrapper.getStorageStats();
      setStorageStats(stats);
    } catch (error) {
      Alert.alert('Hata', `İstatistik kontrolü başarısız: ${error}`);
    }
  };

  // Cache temizle
  const clearCache = async () => {
    try {
      await SecureStorageWrapper.clearCache();
      Alert.alert('✅ Başarılı', 'Cache temizlendi!');
    } catch (error) {
      Alert.alert('Hata', `Cache temizleme başarısız: ${error}`);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      marginBottom: 10,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
    },
    resultBox: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginTop: 10,
    },
    resultText: {
      color: theme.colors.text,
      fontSize: 12,
      fontFamily: 'monospace',
    },
    statusBox: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginTop: 10,
    },
    statusText: {
      color: theme.colors.text,
      fontSize: 14,
    },
  });

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔐 Encryption Test</Text>

      {/* Test Data Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Verisi:</Text>
        <TextInput
          style={styles.input}
          value={testData}
          onChangeText={setTestData}
          placeholder="Test edilecek veriyi girin..."
          placeholderTextColor={theme.colors.textSecondary}
          multiline
        />
      </View>

      {/* Basic Encryption Tests */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Temel Şifreleme Testleri:</Text>

        <TouchableOpacity
          style={styles.button}
          onPress={testBasicEncryption}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Test Ediliyor...' : 'Temel Şifreleme Testi'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testFinancialData}>
          <Text style={styles.buttonText}>Finansal Veri Testi</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testUserSettings}>
          <Text style={styles.buttonText}>Kullanıcı Ayarları Testi</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testCacheData}>
          <Text style={styles.buttonText}>Cache Veri Testi</Text>
        </TouchableOpacity>
      </View>

      {/* Advanced Tests */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Gelişmiş Testler:</Text>

        <TouchableOpacity style={styles.button} onPress={testKeyRotation}>
          <Text style={styles.buttonText}>Key Rotation Testi</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={checkEncryptionStatus}>
          <Text style={styles.buttonText}>Encryption Durumu</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={checkStorageStats}>
          <Text style={styles.buttonText}>Storage İstatistikleri</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={clearCache}>
          <Text style={styles.buttonText}>Cache Temizle</Text>
        </TouchableOpacity>
      </View>

      {/* Results */}
      {encryptedResult && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Şifrelenmiş Veri:</Text>
          <View style={styles.resultBox}>
            <Text style={styles.resultText}>{encryptedResult}</Text>
          </View>
        </View>
      )}

      {decryptedResult && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Çözülmüş Veri:</Text>
          <View style={styles.resultBox}>
            <Text style={styles.resultText}>{decryptedResult}</Text>
          </View>
        </View>
      )}

      {encryptionStatus && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Encryption Durumu:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(encryptionStatus, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {storageStats && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Storage İstatistikleri:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(storageStats, null, 2)}
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default EncryptionTestScreen;
