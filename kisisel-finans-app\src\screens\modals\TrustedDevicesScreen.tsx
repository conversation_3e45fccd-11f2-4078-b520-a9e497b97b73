// Trusted Devices Screen - Güvenilir cihaz yönetimi

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Switch,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import TwoFactorService, { TrustedDevice } from '../../services/TwoFactorService';

const TrustedDevicesScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [trustedDevices, setTrustedDevices] = useState<TrustedDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [rememberDeviceEnabled, setRememberDeviceEnabled] = useState(true);

  useEffect(() => {
    loadTrustedDevices();
    loadRememberDeviceSetting();
  }, []);

  const loadRememberDeviceSetting = async () => {
    try {
      const setting = await AsyncStorage.getItem('rememberDevice');
      setRememberDeviceEnabled(setting !== 'false');
    } catch (error) {
      console.error('Remember device ayarı yüklenirken hata:', error);
    }
  };

  const saveRememberDeviceSetting = async (enabled: boolean) => {
    try {
      await AsyncStorage.setItem('rememberDevice', enabled.toString());
      setRememberDeviceEnabled(enabled);
    } catch (error) {
      console.error('Remember device ayarı kaydedilirken hata:', error);
    }
  };

  const loadTrustedDevices = async () => {
    try {
      setLoading(true);
      const devices = await TwoFactorService.getInstance().getTrustedDevices();
      setTrustedDevices(devices);
    } catch (error) {
      console.error('Load trusted devices error:', error);
      Alert.alert('Hata', 'Güvenilir cihazlar yüklenemedi');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTrustedDevices();
    setRefreshing(false);
  };

  const handleRemoveDevice = (device: TrustedDevice) => {
    Alert.alert(
      'Cihazı Kaldır',
      `"${device.name}" cihazını güvenilir listesinden kaldırmak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Kaldır',
          style: 'destructive',
          onPress: async () => {
            const success = await TwoFactorService.getInstance().removeTrustedDevice(device.id);
            if (success) {
              await loadTrustedDevices();
              Alert.alert('Başarılı', 'Cihaz güvenilir listesinden kaldırıldı');
            } else {
              Alert.alert('Hata', 'Cihaz kaldırılamadı');
            }
          }
        }
      ]
    );
  };

  const handleClearAllDevices = () => {
    Alert.alert(
      'Tüm Cihazları Kaldır',
      'Tüm güvenilir cihazları kaldırmak istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Tümünü Kaldır',
          style: 'destructive',
          onPress: async () => {
            const success = await TwoFactorService.getInstance().clearAllTrustedDevices();
            if (success) {
              await loadTrustedDevices();
              Alert.alert('Başarılı', 'Tüm güvenilir cihazlar kaldırıldı');
            } else {
              Alert.alert('Hata', 'Cihazlar kaldırılamadı');
            }
          }
        }
      ]
    );
  };

  // Test için mevcut cihazı güvenilir olarak ekle
  const handleAddCurrentDevice = async () => {
    try {
      setLoading(true);
      const success = await TwoFactorService.getInstance().trustCurrentDevice(30);
      if (success) {
        await loadTrustedDevices();
        Alert.alert('Başarılı', 'Bu cihaz güvenilir listesine eklendi');
      } else {
        Alert.alert('Hata', 'Cihaz eklenemedi');
      }
    } catch (error) {
      console.error('Add current device error:', error);
      Alert.alert('Hata', 'Cihaz eklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDeviceIcon = (deviceInfo: string): keyof typeof Ionicons.glyphMap => {
    const info = deviceInfo.toLowerCase();
    if (info.includes('ios') || info.includes('iphone')) return 'phone-portrait';
    if (info.includes('android')) return 'phone-portrait';
    if (info.includes('windows')) return 'desktop';
    if (info.includes('mac')) return 'laptop';
    return 'hardware-chip';
  };

  const isDeviceExpired = (device: TrustedDevice): boolean => {
    if (!device.expiresAt) return false;
    return new Date(device.expiresAt) < new Date();
  };

  const getDaysUntilExpiry = (device: TrustedDevice): number => {
    if (!device.expiresAt) return -1;
    const now = new Date();
    const expiry = new Date(device.expiresAt);
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Güvenilir Cihazlar
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Settings */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Cihazları Hatırla
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Güvenilir cihazlarda 2FA atla
              </Text>
            </View>
            <Switch
              value={rememberDeviceEnabled}
              onValueChange={saveRememberDeviceSetting}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          </View>
        </View>

        {/* Info */}
        <View style={[styles.infoContainer, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="information-circle" size={20} color={theme.colors.primary} />
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            Güvenilir olarak işaretlenen cihazlarda 2FA doğrulaması atlanır.
            Cihazlar 30 gün sonra otomatik olarak listeden çıkarılır.
          </Text>
        </View>

        {/* Devices List */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Güvenilir Cihazlar ({trustedDevices.length})
            </Text>
            {trustedDevices.length > 0 && (
              <TouchableOpacity
                style={[styles.clearButton, { borderColor: theme.colors.error }]}
                onPress={handleClearAllDevices}
              >
                <Text style={[styles.clearButtonText, { color: theme.colors.error }]}>
                  Tümünü Kaldır
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {trustedDevices.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="shield-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                Güvenilir Cihaz Yok
              </Text>
              <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
                Henüz hiçbir cihaz güvenilir olarak işaretlenmemiş.
                Giriş yaparken "Bu cihazı hatırla" seçeneğini kullanabilirsiniz.
              </Text>

              {/* Test butonu */}
              <TouchableOpacity
                style={[styles.addDeviceButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleAddCurrentDevice}
                disabled={loading}
              >
                <Ionicons name="add-circle-outline" size={20} color={theme.colors.surface} />
                <Text style={[styles.addDeviceButtonText, { color: theme.colors.surface }]}>
                  Bu Cihazı Güvenilir Olarak Ekle (Test)
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            trustedDevices.map((device) => (
              <View
                key={device.id}
                style={[
                  styles.deviceItem,
                  { borderBottomColor: theme.colors.border },
                  isDeviceExpired(device) && { opacity: 0.6 }
                ]}
              >
                <View style={styles.deviceIcon}>
                  <Ionicons
                    name={getDeviceIcon(device.deviceInfo)}
                    size={24}
                    color={device.isCurrentDevice ? theme.colors.primary : theme.colors.textSecondary}
                  />
                </View>

                <View style={styles.deviceInfo}>
                  <View style={styles.deviceHeader}>
                    <Text style={[styles.deviceName, { color: theme.colors.text }]}>
                      {device.name}
                      {device.isCurrentDevice && (
                        <Text style={[styles.currentBadge, { color: theme.colors.primary }]}>
                          {' '}(Bu Cihaz)
                        </Text>
                      )}
                    </Text>
                    {isDeviceExpired(device) && (
                      <Text style={[styles.expiredBadge, { color: theme.colors.error }]}>
                        Süresi Dolmuş
                      </Text>
                    )}
                  </View>

                  <Text style={[styles.deviceDetails, { color: theme.colors.textSecondary }]}>
                    {device.deviceInfo}
                  </Text>

                  <Text style={[styles.deviceDates, { color: theme.colors.textSecondary }]}>
                    Eklenme: {formatDate(device.addedAt)}
                  </Text>

                  <Text style={[styles.deviceDates, { color: theme.colors.textSecondary }]}>
                    Son Kullanım: {formatDate(device.lastUsed)}
                  </Text>

                  {device.expiresAt && !isDeviceExpired(device) && (
                    <Text style={[styles.expiryText, { color: theme.colors.warning }]}>
                      {getDaysUntilExpiry(device)} gün sonra süresi dolacak
                    </Text>
                  )}
                </View>

                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => handleRemoveDevice(device)}
                >
                  <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderRadius: 6,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  infoContainer: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  deviceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderBottomWidth: 1,
  },
  deviceIcon: {
    marginRight: 12,
    marginTop: 4,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  currentBadge: {
    fontSize: 14,
    fontWeight: '500',
  },
  expiredBadge: {
    fontSize: 12,
    fontWeight: '500',
  },
  deviceDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  deviceDates: {
    fontSize: 12,
    marginBottom: 2,
  },
  expiryText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  addDeviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 20,
  },
  addDeviceButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default TrustedDevicesScreen;
