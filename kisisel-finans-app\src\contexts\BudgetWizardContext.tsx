// RFC-003 Bütçe Oluşturma Sihirbazı - Context

import React, { createContext, useContext, useReducer, ReactNode, useCallback, useMemo, useEffect } from 'react';
import { CreateBudgetInput, CreateBudgetCategoryInput, BudgetPeriod } from '../types/budget';
import { CreateBudgetIncomeCategoryInput } from '../services/IncomeHybridService';

// Wizard adımları
export type WizardStep = 1 | 2 | 3 | 4 | 5;

// Wizard state
export interface BudgetWizardState {
  currentStep: WizardStep;
  totalSteps: number;
  
  // Adım 1: Temel Bilgiler
  name: string;
  period: BudgetPeriod;
  startDate: string;
  endDate: string;
  currency: string;
  notes: string;
  
  // Adım 2: Gelir & Tasarruf He<PERSON>
  totalIncomeTarget: number;
  totalExpenseLimit: number;
  savingsTarget: number;

  // Adım 2B: <PERSON><PERSON><PERSON> (Hibrit Sistem)
  incomeCategories: CreateBudgetIncomeCategoryInput[];

  // Adım 3: Kategori Seçimi
  selectedCategories: CreateBudgetCategoryInput[];
  
  // Adım 4: Uyarı Eşikleri (global defaults)
  defaultWarningThreshold: number;
  defaultCriticalThreshold: number;
  defaultLimitThreshold: number;
  
  // Sihirbaz seçenekleri
  useTemplate: boolean;
  templateId?: string;
  copyFromBudget: boolean;
  sourceBudgetId?: string;
  useAutoSuggestion: boolean;
  
  // Validation
  errors: Record<string, string>;
  isValid: boolean;
}

// Wizard actions
export type BudgetWizardAction =
  | { type: 'SET_STEP'; payload: WizardStep }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' }
  | { type: 'SET_BASIC_INFO'; payload: Partial<Pick<BudgetWizardState, 'name' | 'period' | 'startDate' | 'endDate' | 'currency' | 'notes'>> }
  | { type: 'SET_TARGETS'; payload: Partial<Pick<BudgetWizardState, 'totalIncomeTarget' | 'totalExpenseLimit' | 'savingsTarget'>> }
  | { type: 'SET_INCOME_CATEGORIES'; payload: { incomeCategories: CreateBudgetIncomeCategoryInput[]; totalIncomeTarget?: number } }
  | { type: 'SET_CATEGORIES'; payload: CreateBudgetCategoryInput[] }
  | { type: 'ADD_CATEGORY'; payload: CreateBudgetCategoryInput }
  | { type: 'UPDATE_CATEGORY'; payload: { index: number; category: CreateBudgetCategoryInput } }
  | { type: 'REMOVE_CATEGORY'; payload: number }
  | { type: 'SET_THRESHOLDS'; payload: Partial<Pick<BudgetWizardState, 'defaultWarningThreshold' | 'defaultCriticalThreshold' | 'defaultLimitThreshold'>> }
  | { type: 'SET_TEMPLATE_MODE'; payload: { useTemplate: boolean; templateId?: string } }
  | { type: 'SET_COPY_MODE'; payload: { copyFromBudget: boolean; sourceBudgetId?: string } }
  | { type: 'SET_AUTO_SUGGESTION'; payload: boolean }
  | { type: 'LOAD_TEMPLATE'; payload: CreateBudgetInput }
  | { type: 'LOAD_BUDGET_COPY'; payload: CreateBudgetInput }
  | { type: 'LOAD_AUTO_SUGGESTION'; payload: CreateBudgetInput }
  | { type: 'SET_ERRORS'; payload: Record<string, string> }
  | { type: 'CLEAR_ERRORS' }
  | { type: 'RESET_WIZARD' };

// Initial state
const initialState: BudgetWizardState = {
  currentStep: 1,
  totalSteps: 5,
  
  // Temel bilgiler
  name: '',
  period: 'monthly',
  startDate: new Date().toISOString().split('T')[0],
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  currency: 'TRY',
  notes: '',
  
  // Hedefler
  totalIncomeTarget: 0,
  totalExpenseLimit: 0,
  savingsTarget: 0,

  // Gelir kategorileri
  incomeCategories: [],

  // Kategoriler
  selectedCategories: [],
  
  // Eşikler
  defaultWarningThreshold: 75,
  defaultCriticalThreshold: 90,
  defaultLimitThreshold: 100,
  
  // Seçenekler
  useTemplate: false,
  copyFromBudget: false,
  useAutoSuggestion: false,
  
  // Validation
  errors: {},
  isValid: false,
};

// Reducer
function budgetWizardReducer(state: BudgetWizardState, action: BudgetWizardAction): BudgetWizardState {
  switch (action.type) {
    case 'SET_STEP':
      return { ...state, currentStep: action.payload };
      
    case 'NEXT_STEP':
      return { 
        ...state, 
        currentStep: Math.min(state.currentStep + 1, state.totalSteps) as WizardStep 
      };
      
    case 'PREV_STEP':
      return { 
        ...state, 
        currentStep: Math.max(state.currentStep - 1, 1) as WizardStep 
      };
      
    case 'SET_BASIC_INFO':
      return { ...state, ...action.payload };
      
    case 'SET_TARGETS':
      return { ...state, ...action.payload };

    case 'SET_INCOME_CATEGORIES':
      return {
        ...state,
        incomeCategories: action.payload.incomeCategories,
        ...(action.payload.totalIncomeTarget !== undefined && { totalIncomeTarget: action.payload.totalIncomeTarget })
      };

    case 'SET_CATEGORIES':
      return { ...state, selectedCategories: action.payload };
      
    case 'ADD_CATEGORY':
      return { 
        ...state, 
        selectedCategories: [...state.selectedCategories, action.payload] 
      };
      
    case 'UPDATE_CATEGORY':
      const updatedCategories = [...state.selectedCategories];
      updatedCategories[action.payload.index] = action.payload.category;
      return { ...state, selectedCategories: updatedCategories };
      
    case 'REMOVE_CATEGORY':
      return { 
        ...state, 
        selectedCategories: state.selectedCategories.filter((_, index) => index !== action.payload) 
      };
      
    case 'SET_THRESHOLDS':
      return { ...state, ...action.payload };
      
    case 'SET_TEMPLATE_MODE':
      return { 
        ...state, 
        useTemplate: action.payload.useTemplate,
        templateId: action.payload.templateId,
        copyFromBudget: false,
        useAutoSuggestion: false
      };
      
    case 'SET_COPY_MODE':
      return { 
        ...state, 
        copyFromBudget: action.payload.copyFromBudget,
        sourceBudgetId: action.payload.sourceBudgetId,
        useTemplate: false,
        useAutoSuggestion: false
      };
      
    case 'SET_AUTO_SUGGESTION':
      return { 
        ...state, 
        useAutoSuggestion: action.payload,
        useTemplate: false,
        copyFromBudget: false
      };
      
    case 'LOAD_TEMPLATE':
    case 'LOAD_BUDGET_COPY':
    case 'LOAD_AUTO_SUGGESTION':
      return {
        ...state,
        name: action.payload.name,
        period: action.payload.period,
        startDate: action.payload.startDate,
        endDate: action.payload.endDate,
        totalIncomeTarget: action.payload.totalIncomeTarget || 0,
        totalExpenseLimit: action.payload.totalExpenseLimit || 0,
        savingsTarget: action.payload.savingsTarget || 0,
        currency: action.payload.currency || 'TRY',
        notes: action.payload.notes || '',
        selectedCategories: action.payload.categories,
      };
      
    case 'SET_ERRORS':
      return { ...state, errors: action.payload, isValid: Object.keys(action.payload).length === 0 };
      
    case 'CLEAR_ERRORS':
      return { ...state, errors: {}, isValid: true };
      
    case 'RESET_WIZARD':
      return initialState;
      
    default:
      return state;
  }
}

// Context
interface BudgetWizardContextType {
  state: BudgetWizardState;
  dispatch: React.Dispatch<BudgetWizardAction>;
  
  // Helper functions
  goToStep: (step: WizardStep) => void;
  nextStep: () => void;
  prevStep: () => void;
  canGoNext: () => boolean;
  canGoPrev: () => boolean;
  getProgress: () => number;
  validateCurrentStep: () => boolean;
  getBudgetInput: () => CreateBudgetInput;
  resetWizard: () => void;
}

const BudgetWizardContext = createContext<BudgetWizardContextType | undefined>(undefined);

// Provider
interface BudgetWizardProviderProps {
  children: ReactNode;
}

export const BudgetWizardProvider: React.FC<BudgetWizardProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(budgetWizardReducer, initialState);

  // Error state'i güncellemek için useEffect
  useEffect(() => {
    const errors: Record<string, string> = {};

    switch (state.currentStep) {
      case 1: // Temel Bilgiler
        if (!state.name.trim()) errors.name = 'Bütçe adı gerekli';
        if (!state.startDate) errors.startDate = 'Başlangıç tarihi gerekli';
        if (!state.endDate) errors.endDate = 'Bitiş tarihi gerekli';
        if (new Date(state.startDate) >= new Date(state.endDate)) {
          errors.endDate = 'Bitiş tarihi başlangıç tarihinden sonra olmalı';
        }
        break;

      case 2: // Gelir & Tasarruf
        if (state.totalIncomeTarget < 0) errors.totalIncomeTarget = 'Gelir hedefi negatif olamaz';
        if (state.totalExpenseLimit < 0) errors.totalExpenseLimit = 'Gider limiti negatif olamaz';
        if (state.savingsTarget < 0) errors.savingsTarget = 'Tasarruf hedefi negatif olamaz';
        break;

      case 3: // Kategoriler
        if (state.selectedCategories.length === 0) {
          errors.categories = 'En az bir kategori seçmelisiniz';
        }
        break;

      case 4: // Eşikler
        if (state.defaultWarningThreshold >= state.defaultCriticalThreshold) {
          errors.thresholds = 'Uyarı eşiği kritik eşikten küçük olmalı';
        }
        if (state.defaultCriticalThreshold >= state.defaultLimitThreshold) {
          errors.thresholds = 'Kritik eşik limit eşikten küçük olmalı';
        }
        break;

      case 5: // Özet - validation yok, her zaman geçerli
        break;
    }

    // Sadece errors değiştiyse dispatch et
    if (JSON.stringify(errors) !== JSON.stringify(state.errors)) {
      dispatch({ type: 'SET_ERRORS', payload: errors });
    }
  }, [state.currentStep, state.name, state.startDate, state.endDate, state.totalIncomeTarget, state.totalExpenseLimit, state.savingsTarget, state.selectedCategories, state.defaultWarningThreshold, state.defaultCriticalThreshold, state.defaultLimitThreshold, state.errors]);

  const goToStep = useCallback((step: WizardStep) => {
    dispatch({ type: 'SET_STEP', payload: step });
  }, []);

  const validateCurrentStep = useCallback(() => {
    const errors: Record<string, string> = {};

    switch (state.currentStep) {
      case 1: // Temel Bilgiler
        if (!state.name.trim()) errors.name = 'Bütçe adı gerekli';
        if (!state.startDate) errors.startDate = 'Başlangıç tarihi gerekli';
        if (!state.endDate) errors.endDate = 'Bitiş tarihi gerekli';
        if (new Date(state.startDate) >= new Date(state.endDate)) {
          errors.endDate = 'Bitiş tarihi başlangıç tarihinden sonra olmalı';
        }
        break;

      case 2: // Gelir & Tasarruf
        if (state.totalIncomeTarget < 0) errors.totalIncomeTarget = 'Gelir hedefi negatif olamaz';
        if (state.totalExpenseLimit < 0) errors.totalExpenseLimit = 'Gider limiti negatif olamaz';
        if (state.savingsTarget < 0) errors.savingsTarget = 'Tasarruf hedefi negatif olamaz';
        break;

      case 3: // Kategoriler
        if (state.selectedCategories.length === 0) {
          errors.categories = 'En az bir kategori seçmelisiniz';
        }
        break;

      case 4: // Eşikler
        if (state.defaultWarningThreshold >= state.defaultCriticalThreshold) {
          errors.thresholds = 'Uyarı eşiği kritik eşikten küçük olmalı';
        }
        if (state.defaultCriticalThreshold >= state.defaultLimitThreshold) {
          errors.thresholds = 'Kritik eşik limit eşikten küçük olmalı';
        }
        break;

      case 5: // Özet - validation yok, her zaman geçerli
        break;
    }

    const hasErrors = Object.keys(errors).length > 0;

    // Error state'i güncellemek için useEffect kullanacağız
    // Burada sadece validation sonucunu döndürüyoruz
    return !hasErrors;
  }, [state.currentStep, state.name, state.startDate, state.endDate, state.totalIncomeTarget, state.totalExpenseLimit, state.savingsTarget, state.selectedCategories, state.defaultWarningThreshold, state.defaultCriticalThreshold, state.defaultLimitThreshold]);

  const nextStep = useCallback(() => {
    if (Object.keys(state.errors).length === 0) {
      dispatch({ type: 'NEXT_STEP' });
    }
  }, [state.errors]);

  const prevStep = useCallback(() => {
    dispatch({ type: 'PREV_STEP' });
  }, []);

  const canGoNext = useCallback(() => {
    const result = state.currentStep < state.totalSteps && Object.keys(state.errors).length === 0;
    console.log('🔍 canGoNext check:', {
      currentStep: state.currentStep,
      totalSteps: state.totalSteps,
      stepCheck: state.currentStep < state.totalSteps,
      errorsCount: Object.keys(state.errors).length,
      errors: state.errors,
      result,
    });
    return result;
  }, [state.currentStep, state.totalSteps, state.errors]);

  const canGoPrev = useCallback(() => {
    return state.currentStep > 1;
  }, [state.currentStep]);

  const getProgress = useCallback(() => {
    return (state.currentStep / state.totalSteps) * 100;
  }, [state.currentStep, state.totalSteps]);

  const getBudgetInput = useCallback((): CreateBudgetInput => {
    return {
      name: state.name,
      period: state.period,
      startDate: state.startDate,
      endDate: state.endDate,
      totalIncomeTarget: state.totalIncomeTarget,
      totalExpenseLimit: state.totalExpenseLimit,
      savingsTarget: state.savingsTarget,
      currency: state.currency,
      notes: state.notes,
      categories: state.selectedCategories,
    };
  }, [state.name, state.period, state.startDate, state.endDate, state.totalIncomeTarget, state.totalExpenseLimit, state.savingsTarget, state.currency, state.notes, state.selectedCategories]);

  const resetWizard = useCallback(() => {
    dispatch({ type: 'RESET_WIZARD' });
  }, []);

  const value: BudgetWizardContextType = useMemo(() => ({
    state,
    dispatch,
    goToStep,
    nextStep,
    prevStep,
    canGoNext,
    canGoPrev,
    getProgress,
    validateCurrentStep,
    getBudgetInput,
    resetWizard,
  }), [state, dispatch, goToStep, nextStep, prevStep, canGoNext, canGoPrev, getProgress, validateCurrentStep, getBudgetInput, resetWizard]);

  return (
    <BudgetWizardContext.Provider value={value}>
      {children}
    </BudgetWizardContext.Provider>
  );
};

// Hook
export const useBudgetWizard = () => {
  const context = useContext(BudgetWizardContext);
  if (context === undefined) {
    throw new Error('useBudgetWizard must be used within a BudgetWizardProvider');
  }
  return context;
};
