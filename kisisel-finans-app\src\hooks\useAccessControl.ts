// Access Control Hook - <PERSON><PERSON><PERSON><PERSON> k<PERSON> hook'u

import { useState, useEffect, useCallback } from 'react';
import { useSimpleAuth } from '../contexts/SimpleAuthContext';
// Enterprise RBAC service geçici olarak kapatıldı
// import RBACService from '../services/RBACService';
import {
  Permission,
  RoleType,
  PermissionCheck,
  AccessControlResult,
  UserRole,
} from '../types/rbac';

export interface UseAccessControlReturn {
  // Permission checking
  hasPermission: (permission: Permission, context?: any) => Promise<boolean>;
  checkPermission: (permissionCheck: PermissionCheck) => Promise<AccessControlResult>;

  // Role checking
  hasRole: (role: RoleType) => boolean;
  hasAnyRole: (roles: RoleType[]) => boolean;
  hasAllRoles: (roles: RoleType[]) => boolean;

  // User data
  userRoles: RoleType[];
  userPermissions: Permission[];

  // Loading states
  isLoading: boolean;
  isInitialized: boolean;

  // Actions
  refreshPermissions: () => Promise<void>;

  // Utilities
  canAccess: (resource: string, action: string) => Promise<boolean>;
  getRequiredRole: (permission: Permission) => RoleType | null;
}

export const useAccessControl = (): UseAccessControlReturn => {
  const { user, isAuthenticated } = useSimpleAuth();
  const [userRoles, setUserRoles] = useState<RoleType[]>([]);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  /**
   * Kullanıcı izinlerini yükle
   */
  const loadUserPermissions = useCallback(async () => {
    if (!user || !isAuthenticated) {
      setUserRoles([]);
      setUserPermissions([]);
      setIsLoading(false);
      setIsInitialized(true);
      return;
    }

    try {
      setIsLoading(true);

      // Mock RBAC - Enterprise features geçici olarak kapatıldı
      // RBACService.clearCache();

      // Mock kullanıcı rolleri (basit kullanıcı için)
      const roleTypes: RoleType[] = ['user']; // Sadece temel kullanıcı rolü
      setUserRoles(roleTypes);

      // Mock kullanıcı izinleri (temel izinler)
      const permissions: Permission[] = [
        'transactions:read',
        'transactions:create',
        'transactions:update',
        'transactions:delete',
        'reports:read',
        'budget:read',
        'budget:create',
        'budget:update'
      ];
      setUserPermissions(permissions);

    } catch (error) {
      console.error('Error loading user permissions:', error);
      setUserRoles([]);
      setUserPermissions([]);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [user, isAuthenticated]);

  /**
   * İzin kontrolü yap
   */
  const hasPermission = useCallback(async (
    permission: Permission,
    context?: any
  ): Promise<boolean> => {
    if (!user || !isAuthenticated) {
      return false;
    }

    try {
      const permissionCheck: PermissionCheck = {
        permission,
        context,
      };

      // Mock permission check - basit kullanıcı için tüm temel izinler var
      const basicPermissions: Permission[] = [
        'transactions:read', 'transactions:create', 'transactions:update', 'transactions:delete',
        'reports:read', 'budget:read', 'budget:create', 'budget:update'
      ];
      return basicPermissions.includes(permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }, [user, isAuthenticated]);

  /**
   * Detaylı izin kontrolü
   */
  const checkPermission = useCallback(async (
    permissionCheck: PermissionCheck
  ): Promise<AccessControlResult> => {
    if (!user || !isAuthenticated) {
      return {
        allowed: false,
        reason: 'User not authenticated',
        currentRoles: [],
        currentPermissions: [],
      };
    }

    try {
      // Mock detailed permission check
      const basicPermissions: Permission[] = [
        'transactions:read', 'transactions:create', 'transactions:update', 'transactions:delete',
        'reports:read', 'budget:read', 'budget:create', 'budget:update'
      ];

      return {
        allowed: basicPermissions.includes(permissionCheck.permission),
        reason: basicPermissions.includes(permissionCheck.permission) ? 'Permission granted' : 'Permission denied',
        currentRoles: ['user'],
        currentPermissions: basicPermissions,
      };
    } catch (error) {
      console.error('Error checking permission:', error);
      return {
        allowed: false,
        reason: 'Permission check failed',
        currentRoles: userRoles,
        currentPermissions: userPermissions,
      };
    }
  }, [user, isAuthenticated, userRoles, userPermissions]);

  /**
   * Rol kontrolü
   */
  const hasRole = useCallback((role: RoleType): boolean => {
    return userRoles.includes(role);
  }, [userRoles]);

  /**
   * Herhangi bir rol kontrolü
   */
  const hasAnyRole = useCallback((roles: RoleType[]): boolean => {
    return roles.some(role => userRoles.includes(role));
  }, [userRoles]);

  /**
   * Tüm roller kontrolü
   */
  const hasAllRoles = useCallback((roles: RoleType[]): boolean => {
    return roles.every(role => userRoles.includes(role));
  }, [userRoles]);

  /**
   * İzinleri yenile
   */
  const refreshPermissions = useCallback(async (): Promise<void> => {
    // Mock refresh - Enterprise RBAC kapatıldı
    // RBACService.clearCache();
    // Yeniden yükle
    await loadUserPermissions();
  }, [loadUserPermissions]);

  /**
   * Resource ve action bazlı erişim kontrolü
   */
  const canAccess = useCallback(async (
    resource: string,
    action: string
  ): Promise<boolean> => {
    // Resource ve action'ı permission'a çevir
    const permission = `${resource}:${action}` as Permission;

    // Mock permission validation
    const allPermissions: Permission[] = [
      'transactions:read', 'transactions:create', 'transactions:update', 'transactions:delete',
      'reports:read', 'budget:read', 'budget:create', 'budget:update',
      'admin:read', 'admin:create', 'admin:update', 'admin:delete'
    ];

    if (!allPermissions.includes(permission)) {
      console.warn(`Unknown permission: ${permission}`);
      return false;
    }

    return await hasPermission(permission);
  }, [hasPermission]);

  /**
   * İzin için gerekli rolü bul
   */
  const getRequiredRole = useCallback((permission: Permission): RoleType | null => {
    // Mock role requirements
    const basicPermissions: Permission[] = [
      'transactions:read', 'transactions:create', 'transactions:update', 'transactions:delete',
      'reports:read', 'budget:read', 'budget:create', 'budget:update'
    ];

    if (basicPermissions.includes(permission)) {
      return 'user';
    }

    // Admin permissions
    if (permission.startsWith('admin:')) {
      return 'admin';
    }

    return null;
  }, []);

  // User değiştiğinde izinleri yükle
  useEffect(() => {
    loadUserPermissions();
  }, [loadUserPermissions]);

  // User roles değiştiğinde de yenile
  useEffect(() => {
    if (user?.roles) {
      loadUserPermissions();
    }
  }, [user?.roles, loadUserPermissions]);

  return {
    // Permission checking
    hasPermission,
    checkPermission,

    // Role checking
    hasRole,
    hasAnyRole,
    hasAllRoles,

    // User data
    userRoles,
    userPermissions,

    // Loading states
    isLoading,
    isInitialized,

    // Actions
    refreshPermissions,

    // Utilities
    canAccess,
    getRequiredRole,
  };
};

// HOC for component-level access control - Bu kısım ayrı bir .tsx dosyasında implement edilecek
export interface WithAccessControlProps {
  requiredPermission?: Permission;
  requiredRole?: RoleType;
  requiredAnyRole?: RoleType[];
  fallbackComponent?: any;
  loadingComponent?: any;
}

// Utility hook for quick permission checks
export const usePermission = (permission: Permission, context?: any) => {
  const { hasPermission } = useAccessControl();
  const [allowed, setAllowed] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      setIsChecking(true);
      try {
        const result = await hasPermission(permission, context);
        setAllowed(result);
      } catch (error) {
        console.error('Permission check error:', error);
        setAllowed(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkPermission();
  }, [hasPermission, permission, context]);

  return { allowed, isChecking };
};

// Utility hook for role checks
export const useRole = (role: RoleType) => {
  const { hasRole, userRoles, isLoading } = useAccessControl();

  return {
    hasRole: hasRole(role),
    userRoles,
    isLoading,
  };
};

export default useAccessControl;
