// Risk Assessment Screen - Risk profili değerlendirme ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface RiskQuestionnaireAnswer {
  questionId: string;
  answer: string;
  score: number;
}

interface RiskQuestion {
  id: string;
  question: string;
  weight: number;
  options?: {
    id: string;
    text: string;
    score: number;
  }[];
}

// Comprehensive risk questions data
const RISK_QUESTIONS: RiskQuestion[] = [
  {
    id: '1',
    question: 'Yaşınız kaç?',
    weight: 0.8,
    options: [
      { id: '1a', text: '18-25 yaş', score: 4 },
      { id: '1b', text: '26-35 yaş', score: 4 },
      { id: '1c', text: '36-45 yaş', score: 3 },
      { id: '1d', text: '46-55 yaş', score: 2 },
      { id: '1e', text: '56-65 yaş', score: 1 },
      { id: '1f', text: '65+ yaş', score: 1 },
    ],
  },
  {
    id: '2',
    question: 'Aylık yatırım bütçeniz ne kadar?',
    weight: 0.9,
    options: [
      { id: '2a', text: '500₺ - 1.000₺', score: 1 },
      { id: '2b', text: '1.000₺ - 3.000₺', score: 2 },
      { id: '2c', text: '3.000₺ - 5.000₺', score: 3 },
      { id: '2d', text: '5.000₺ - 10.000₺', score: 4 },
      { id: '2e', text: '10.000₺+', score: 4 },
    ],
  },
  {
    id: '3',
    question: 'Yatırım deneyiminiz nasıl?',
    weight: 1.3,
    options: [
      { id: '3a', text: 'Hiç deneyimim yok', score: 1 },
      { id: '3b', text: 'Az deneyimim var (1-2 yıl)', score: 2 },
      { id: '3c', text: 'Orta düzeyde deneyimim var (3-5 yıl)', score: 3 },
      { id: '3d', text: 'Çok deneyimliyim (5+ yıl)', score: 4 },
    ],
  },
  {
    id: '4',
    question: 'Acil durum fonunuz var mı?',
    weight: 1.0,
    options: [
      { id: '4a', text: 'Hayır, acil durum fonum yok', score: 1 },
      { id: '4b', text: 'Evet, 1-3 ay giderim var', score: 2 },
      { id: '4c', text: 'Evet, 3-6 ay giderim var', score: 3 },
      { id: '4d', text: 'Evet, 6+ ay giderim var', score: 4 },
    ],
  },
  {
    id: '5',
    question: 'Yatırım hedefiniz nedir?',
    weight: 1.1,
    options: [
      { id: '5a', text: 'Sermaye korunması', score: 1 },
      { id: '5b', text: 'Düzenli gelir elde etme', score: 2 },
      { id: '5c', text: 'Emeklilik için birikim', score: 3 },
      { id: '5d', text: 'Servet artışı', score: 4 },
    ],
  },
  {
    id: '6',
    question: 'Yatırım süreniz ne kadar?',
    weight: 1.2,
    options: [
      { id: '6a', text: '1 yıldan az', score: 1 },
      { id: '6b', text: '1-3 yıl', score: 2 },
      { id: '6c', text: '3-5 yıl', score: 3 },
      { id: '6d', text: '5-10 yıl', score: 4 },
      { id: '6e', text: '10+ yıl', score: 4 },
    ],
  },
  {
    id: '7',
    question: 'Yatırımınızın %20 değer kaybetmesi durumunda ne yaparsınız?',
    weight: 1.4,
    options: [
      { id: '7a', text: 'Hemen satarım, zarar etmek istemem', score: 1 },
      { id: '7b', text: 'Endişelenirim ama beklerim', score: 2 },
      { id: '7c', text: 'Normal karşılarım, uzun vadeli düşünürüm', score: 3 },
      { id: '7d', text: 'Fırsat olarak görür, daha fazla alırım', score: 4 },
    ],
  },
  {
    id: '8',
    question: 'Hangi yatırım araçları hakkında bilginiz var?',
    weight: 1.0,
    options: [
      { id: '8a', text: 'Sadece mevduat/altın', score: 1 },
      { id: '8b', text: 'Tahvil ve fonlar', score: 2 },
      { id: '8c', text: 'Hisse senetleri', score: 3 },
      { id: '8d', text: 'Türev araçlar ve kripto', score: 4 },
    ],
  },
  {
    id: '9',
    question: 'Aylık gelirinizin ne kadarını yatırıma ayırabilirsiniz?',
    weight: 0.9,
    options: [
      { id: '9a', text: '%5\'ten az', score: 1 },
      { id: '9b', text: '%5-10 arası', score: 2 },
      { id: '9c', text: '%10-20 arası', score: 3 },
      { id: '9d', text: '%20\'den fazla', score: 4 },
    ],
  },
  {
    id: '10',
    question: 'Yatırım kararlarınızı nasıl verirsiniz?',
    weight: 1.1,
    options: [
      { id: '10a', text: 'Uzmanlardan tavsiye alırım', score: 2 },
      { id: '10b', text: 'Araştırma yapar, analiz ederim', score: 3 },
      { id: '10c', text: 'Sezgilerime güvenirim', score: 1 },
      { id: '10d', text: 'Hem araştırır hem uzman görüşü alırım', score: 4 },
    ],
  },
];

const RiskAssessmentScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<RiskQuestionnaireAnswer[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const currentQuestion = RISK_QUESTIONS[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === RISK_QUESTIONS.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  const handleAnswerSelect = (optionId: string, score: number) => {
    const newAnswer: RiskQuestionnaireAnswer = {
      questionId: currentQuestion.id,
      answer: optionId,
      score: score * currentQuestion.weight,
    };

    const updatedAnswers = answers.filter(a => a.questionId !== currentQuestion.id);
    updatedAnswers.push(newAnswer);
    setAnswers(updatedAnswers);
  };

  const getCurrentAnswer = () => {
    return answers.find(a => a.questionId === currentQuestion.id);
  };

  const handleNext = () => {
    const currentAnswer = getCurrentAnswer();
    if (!currentAnswer) {
      Alert.alert('Uyarı', 'Lütfen bir seçenek seçin.');
      return;
    }

    if (isLastQuestion) {
      handleFinishAssessment();
    } else {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (!isFirstQuestion) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const calculateRiskScore = () => {
    const totalScore = answers.reduce((sum, answer) => sum + answer.score, 0);
    const maxPossibleScore = RISK_QUESTIONS.reduce((sum, q) => sum + (4 * q.weight), 0);
    return (totalScore / maxPossibleScore) * 10;
  };

  const getRiskLevel = (score: number): 'conservative' | 'moderate' | 'aggressive' => {
    if (score <= 3.5) return 'conservative';
    if (score <= 7) return 'moderate';
    return 'aggressive';
  };

  const getInvestmentExperience = (): 'beginner' | 'intermediate' | 'advanced' => {
    const experienceAnswer = answers.find(a => a.questionId === '3');
    if (!experienceAnswer) return 'beginner';

    const experienceOption = experienceAnswer.answer;
    if (experienceOption === '3a') return 'beginner';
    if (experienceOption === '3b') return 'beginner';
    if (experienceOption === '3c') return 'intermediate';
    return 'advanced';
  };

  const getInvestmentGoal = (): string => {
    const goalAnswer = answers.find(a => a.questionId === '5');
    if (!goalAnswer) return 'preservation';

    const goalOption = goalAnswer.answer;
    if (goalOption === '5a') return 'preservation';
    if (goalOption === '5b') return 'income';
    if (goalOption === '5c') return 'retirement';
    return 'wealth_growth';
  };

  const getTimeHorizonYears = (): number => {
    const timeAnswer = answers.find(a => a.questionId === '6');
    if (!timeAnswer) return 1;

    const timeOption = timeAnswer.answer;
    if (timeOption === '6a') return 1;
    if (timeOption === '6b') return 2;
    if (timeOption === '6c') return 4;
    if (timeOption === '6d') return 7;
    return 15;
  };

  const getEmergencyFundStatus = (): boolean => {
    const emergencyAnswer = answers.find(a => a.questionId === '4');
    if (!emergencyAnswer) return false;

    const emergencyOption = emergencyAnswer.answer;
    return emergencyOption !== '4a'; // Sadece 'Hayır' seçeneği false döner
  };

  const getRiskLevelText = (level: string): string => {
    switch (level) {
      case 'conservative': return 'Muhafazakar';
      case 'moderate': return 'Dengeli';
      case 'aggressive': return 'Agresif';
      default: return level;
    }
  };

  const handleFinishAssessment = async () => {
    setIsLoading(true);

    try {
      const riskScore = calculateRiskScore();
      const riskLevel = getRiskLevel(riskScore);

      // Cevaplardan yaş ve bütçe bilgilerini çıkar
      const ageAnswer = answers.find(a => a.questionId === '1');
      const budgetAnswer = answers.find(a => a.questionId === '2');

      let age = 30; // Default
      let monthlyBudget = 2000; // Default

      // Yaş hesaplama
      if (ageAnswer) {
        switch (ageAnswer.answer) {
          case '1a': age = 22; break;
          case '1b': age = 30; break;
          case '1c': age = 40; break;
          case '1d': age = 50; break;
          case '1e': age = 60; break;
          case '1f': age = 70; break;
        }
      }

      // Bütçe hesaplama
      if (budgetAnswer) {
        switch (budgetAnswer.answer) {
          case '2a': monthlyBudget = 750; break;
          case '2b': monthlyBudget = 2000; break;
          case '2c': monthlyBudget = 4000; break;
          case '2d': monthlyBudget = 7500; break;
          case '2e': monthlyBudget = 15000; break;
        }
      }

      const riskProfileData = {
        riskLevel,
        riskScore,
        investmentExperience: getInvestmentExperience(),
        investmentGoals: [getInvestmentGoal()],
        timeHorizon: getTimeHorizonYears(),
        monthlyInvestmentBudget: monthlyBudget,
        age: age,
        income: monthlyBudget * 5, // Estimate income as 5x investment budget
        hasEmergencyFund: getEmergencyFundStatus(),
      };

      // InvestmentService ile risk profili oluştur
      const InvestmentService = (await import('../../services/InvestmentService')).default;
      const investmentService = InvestmentService.getInstance();
      await investmentService.createRiskProfile(riskProfileData);

      Alert.alert(
        'Başarılı! 🎉',
        `Risk profiliniz oluşturuldu!\n\nRisk Seviyesi: ${getRiskLevelText(riskLevel)}\nRisk Skoru: ${riskScore.toFixed(1)}/10\n\nŞimdi size özel yatırım önerileri hazırlayabiliriz.`,
        [
          {
            text: 'Önerileri Gör',
            onPress: () => {
              navigation.goBack();
              // InvestmentRecommendations ekranına yönlendir
              setTimeout(() => {
                navigation.navigate('InvestmentRecommendations' as never);
              }, 100);
            }
          }
        ]
      );
    } catch (error: any) {
      console.error('Risk assessment error:', error);
      Alert.alert('Hata', error.message || 'Risk profili oluşturulamadı.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderProgressBar = () => {
    const progress = ((currentQuestionIndex + 1) / RISK_QUESTIONS.length) * 100;
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentQuestionIndex + 1} / {RISK_QUESTIONS.length}
        </Text>
      </View>
    );
  };

  const renderQuestion = () => {
    return (
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{currentQuestion.question}</Text>
        <View style={styles.optionsContainer}>
          {currentQuestion.options?.map((option) => {
            const isSelected = getCurrentAnswer()?.answer === option.id;
            return (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  isSelected && styles.optionButtonSelected
                ]}
                onPress={() => handleAnswerSelect(option.id, option.score)}
              >
                <View style={styles.optionContent}>
                  <Text style={[
                    styles.optionText,
                    isSelected && styles.optionTextSelected
                  ]}>
                    {option.text}
                  </Text>
                  <View style={[
                    styles.radioButton,
                    isSelected && styles.radioButtonSelected
                  ]}>
                    {isSelected && <View style={styles.radioButtonInner} />}
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };



  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      flex: 1,
      fontSize: 18,
      fontWeight: '600',
      textAlign: 'center',
      marginHorizontal: 16,
    },
    headerRight: {
      width: 40,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    introContainer: {
      marginBottom: 30,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    loadingText: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    loadingSubtext: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      lineHeight: 22,
    },
    progressContainer: {
      marginBottom: 30,
    },
    progressBar: {
      height: 8,
      backgroundColor: theme.colors.border,
      borderRadius: 4,
      marginBottom: 8,
    },
    progressFill: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 4,
    },
    progressText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    questionContainer: {
      marginBottom: 30,
    },
    questionText: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 20,
      lineHeight: 24,
    },
    optionsContainer: {
      gap: 12,
    },
    optionButton: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      padding: 16,
    },
    optionButtonSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.surfaceSecondary,
    },
    optionContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    optionText: {
      fontSize: 16,
      color: theme.colors.text,
      flex: 1,
      marginRight: 12,
    },
    optionTextSelected: {
      color: theme.colors.primary,
      fontWeight: '500',
    },
    radioButton: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.colors.primary,
    },
    radioButtonInner: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
    },
    additionalInfoContainer: {
      marginBottom: 30,
    },
    additionalInfoTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    summaryContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    summaryTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    summaryItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    summaryLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    summaryValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    navigationContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 40,
    },
    previousButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    previousButtonText: {
      fontSize: 16,
      color: theme.colors.primary,
      marginLeft: 4,
    },
    nextButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 24,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      marginLeft: 16,
    },
    nextButtonDisabled: {
      opacity: 0.5,
    },
    nextButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
      marginRight: 8,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Ionicons name="analytics" size={48} color={theme.colors.primary} />
          <Text style={styles.loadingText}>Risk profiliniz oluşturuluyor...</Text>
          <Text style={styles.loadingSubtext}>
            Cevaplarınız analiz ediliyor ve size özel yatırım profili hazırlanıyor.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Risk Değerlendirmesi
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        <View style={styles.introContainer}>
          <Text style={styles.title}>Risk Değerlendirmesi</Text>
          <Text style={styles.subtitle}>
            Size uygun yatırım önerileri için {RISK_QUESTIONS.length} soru yanıtlayın
          </Text>
        </View>

        {renderProgressBar()}

        {renderQuestion()}

        <View style={styles.navigationContainer}>
          {!isFirstQuestion && (
            <TouchableOpacity
              style={styles.previousButton}
              onPress={handlePrevious}
            >
              <Ionicons name="chevron-back" size={20} color={theme.colors.primary} />
              <Text style={styles.previousButtonText}>Önceki</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.nextButton,
              !getCurrentAnswer() && styles.nextButtonDisabled,
              isLoading && styles.nextButtonDisabled
            ]}
            onPress={handleNext}
            disabled={!getCurrentAnswer() || isLoading}
          >
            <Text style={styles.nextButtonText}>
              {isLoading ? 'Profil Oluşturuluyor...' :
               isLastQuestion ? 'Profili Oluştur' : 'Sonraki'}
            </Text>
            {!isLoading && (
              <Ionicons
                name={isLastQuestion ? "checkmark" : "chevron-forward"}
                size={20}
                color={theme.colors.surface}
              />
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};



export default RiskAssessmentScreen;
