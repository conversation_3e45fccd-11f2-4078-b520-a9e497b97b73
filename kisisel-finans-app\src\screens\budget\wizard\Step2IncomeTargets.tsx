// RFC-003 Bütçe Sihirbazı - Adım 2: <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>

import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';

const Step2IncomeTargets: React.FC = () => {
  const { theme } = useTheme();
  const { state, dispatch } = useBudgetWizard();
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 50/30/20 kuralı önerileri
  const apply503020Rule = () => {
    if (state.totalIncomeTarget <= 0) {
      Alert.alert('Uyarı', 'Önce gelir hedefini girin.');
      return;
    }

    const income = state.totalIncomeTarget;
    const needs = Math.round(income * 0.5); // %50 ihtiyaçlar
    const wants = Math.round(income * 0.3); // %30 istekler
    const savings = Math.round(income * 0.2); // %20 tasarruf

    Alert.alert(
      '50/30/20 Kuralı',
      `Gelir: ${formatCurrency(income)}\n\n` +
      `• İhtiyaçlar (%50): ${formatCurrency(needs)}\n` +
      `• İstekler (%30): ${formatCurrency(wants)}\n` +
      `• Tasarruf (%20): ${formatCurrency(savings)}\n\n` +
      'Bu değerler uygulansin mi?',
      [
        { text: 'Hayır', style: 'cancel' },
        {
          text: 'Evet',
          onPress: () => {
            handleTargetChange('totalExpenseLimit', needs + wants);
            handleTargetChange('savingsTarget', savings);
          }
        }
      ]
    );
  };

  const formatCurrency = useCallback((amount: number): string => {
    const symbol = state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€';
    return `${amount.toLocaleString('tr-TR')} ${symbol}`;
  }, [state.currency]);

  const handleTargetChange = useCallback((field: string, value: string | number) => {
    const numericValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.]/g, '')) || 0 : value;

    dispatch({
      type: 'SET_TARGETS',
      payload: { [field]: numericValue }
    });
  }, [dispatch]);

  const calculateSavingsRate = (): number => {
    if (state.totalIncomeTarget <= 0) return 0;
    return (state.savingsTarget / state.totalIncomeTarget) * 100;
  };

  const calculateExpenseRate = (): number => {
    if (state.totalIncomeTarget <= 0) return 0;
    return (state.totalExpenseLimit / state.totalIncomeTarget) * 100;
  };

  const getRemainingAmount = (): number => {
    return state.totalIncomeTarget - state.totalExpenseLimit - state.savingsTarget;
  };

  const getSavingsRateColor = (): string => {
    const rate = calculateSavingsRate();
    if (rate >= 20) return theme.colors.success;
    if (rate >= 10) return theme.colors.warning;
    return theme.colors.error;
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Gelir & Tasarruf Hedefleri
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          {state.period === 'monthly' ? 'Aylık' : 
           state.period === 'weekly' ? 'Haftalık' :
           state.period === 'quarterly' ? '3 Aylık' : 'Yıllık'} gelir ve tasarruf hedeflerinizi belirleyin
        </Text>
      </View>

      {/* Gelir Hedefi */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Toplam Gelir Hedefi
        </Text>
        <View style={[
          styles.inputContainer,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: state.errors.totalIncomeTarget ? theme.colors.error : theme.colors.border
          }
        ]}>
          <Ionicons name="trending-up" size={20} color={theme.colors.success} />
          <TextInput
            style={[styles.amountInput, { color: theme.colors.text }]}
            value={state.totalIncomeTarget > 0 ? state.totalIncomeTarget.toString() : ''}
            onChangeText={(text) => handleTargetChange('totalIncomeTarget', text)}
            placeholder="0"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
          />
          <Text style={[styles.currencySymbol, { color: theme.colors.textSecondary }]}>
            {state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€'}
          </Text>
        </View>
        {state.errors.totalIncomeTarget && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.totalIncomeTarget}
          </Text>
        )}
      </View>

      {/* Gider Limiti */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Toplam Gider Limiti
        </Text>
        <View style={[
          styles.inputContainer,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: state.errors.totalExpenseLimit ? theme.colors.error : theme.colors.border
          }
        ]}>
          <Ionicons name="trending-down" size={20} color={theme.colors.error} />
          <TextInput
            style={[styles.amountInput, { color: theme.colors.text }]}
            value={state.totalExpenseLimit > 0 ? state.totalExpenseLimit.toString() : ''}
            onChangeText={(text) => handleTargetChange('totalExpenseLimit', text)}
            placeholder="0"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
          />
          <Text style={[styles.currencySymbol, { color: theme.colors.textSecondary }]}>
            {state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€'}
          </Text>
        </View>
        {state.totalIncomeTarget > 0 && (
          <Text style={[styles.percentageText, { color: theme.colors.textSecondary }]}>
            Gelirin %{calculateExpenseRate().toFixed(1)}'i
          </Text>
        )}
        {state.errors.totalExpenseLimit && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.totalExpenseLimit}
          </Text>
        )}
      </View>

      {/* Tasarruf Hedefi */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Tasarruf Hedefi
        </Text>
        <View style={[
          styles.inputContainer,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: state.errors.savingsTarget ? theme.colors.error : theme.colors.border
          }
        ]}>
          <Ionicons name="wallet" size={20} color={theme.colors.primary} />
          <TextInput
            style={[styles.amountInput, { color: theme.colors.text }]}
            value={state.savingsTarget > 0 ? state.savingsTarget.toString() : ''}
            onChangeText={(text) => handleTargetChange('savingsTarget', text)}
            placeholder="0"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
          />
          <Text style={[styles.currencySymbol, { color: theme.colors.textSecondary }]}>
            {state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€'}
          </Text>
        </View>
        {state.totalIncomeTarget > 0 && (
          <Text style={[styles.percentageText, { color: getSavingsRateColor() }]}>
            Gelirin %{calculateSavingsRate().toFixed(1)}'i
          </Text>
        )}
        {state.errors.savingsTarget && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.savingsTarget}
          </Text>
        )}
      </View>

      {/* Özet Kartı */}
      {state.totalIncomeTarget > 0 && (
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
            Bütçe Özeti
          </Text>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Toplam Gelir:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.success }]}>
              {formatCurrency(state.totalIncomeTarget)}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Toplam Gider:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.error }]}>
              {formatCurrency(state.totalExpenseLimit)}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Tasarruf:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
              {formatCurrency(state.savingsTarget)}
            </Text>
          </View>
          
          <View style={[styles.summaryRow, styles.summaryTotal]}>
            <Text style={[styles.summaryLabel, styles.summaryTotalLabel, { color: theme.colors.text }]}>
              Kalan:
            </Text>
            <Text style={[
              styles.summaryValue, 
              styles.summaryTotalValue,
              { color: getRemainingAmount() >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {formatCurrency(getRemainingAmount())}
            </Text>
          </View>
        </View>
      )}

      {/* 50/30/20 Kuralı */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[
            styles.ruleCard,
            { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }
          ]}
          onPress={apply503020Rule}
        >
          <View style={styles.ruleHeader}>
            <Ionicons name="pie-chart" size={24} color={theme.colors.primary} />
            <View style={styles.ruleContent}>
              <Text style={[styles.ruleTitle, { color: theme.colors.text }]}>
                50/30/20 Kuralı
              </Text>
              <Text style={[styles.ruleDescription, { color: theme.colors.textSecondary }]}>
                %50 ihtiyaçlar, %30 istekler, %20 tasarruf
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Gelişmiş Seçenekler */}
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.advancedToggle}
          onPress={() => setShowAdvanced(!showAdvanced)}
        >
          <Text style={[styles.advancedToggleText, { color: theme.colors.primary }]}>
            Gelişmiş Seçenekler
          </Text>
          <Ionicons 
            name={showAdvanced ? "chevron-up" : "chevron-down"} 
            size={20} 
            color={theme.colors.primary} 
          />
        </TouchableOpacity>

        {showAdvanced && (
          <View style={[styles.advancedContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.advancedTitle, { color: theme.colors.text }]}>
              Tasarruf Önerileri
            </Text>
            
            <View style={styles.tipItem}>
              <Ionicons name="bulb-outline" size={16} color={theme.colors.warning} />
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                Uzmanlar gelirin en az %20'sini tasarruf etmenizi önerir
              </Text>
            </View>
            
            <View style={styles.tipItem}>
              <Ionicons name="shield-checkmark-outline" size={16} color={theme.colors.success} />
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                Acil durum fonu için 3-6 aylık giderinizi ayırın
              </Text>
            </View>
            
            <View style={styles.tipItem}>
              <Ionicons name="trending-up-outline" size={16} color={theme.colors.primary} />
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                Yatırım için tasarrufunuzun bir kısmını değerlendirin
              </Text>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  percentageText: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'right',
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  summaryTotal: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: 8,
    marginTop: 4,
  },
  summaryTotalLabel: {
    fontWeight: 'bold',
  },
  summaryTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  ruleCard: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
  },
  ruleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ruleContent: {
    flex: 1,
    marginLeft: 12,
  },
  ruleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  ruleDescription: {
    fontSize: 14,
  },
  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  advancedToggleText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 4,
  },
  advancedContent: {
    marginTop: 12,
    padding: 16,
    borderRadius: 8,
  },
  advancedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
});

export default Step2IncomeTargets;
