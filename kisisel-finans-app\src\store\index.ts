// Redux Store Konfigürasyonu

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Slice'ları import et
import transactionSlice from './slices/transactionSlice';
import budgetSlice from './slices/budgetSlice';
import goalSlice from './slices/goalSlice';
import debtSlice from './slices/debtSlice';
import investmentSlice from './slices/investmentSlice';
import settingsSlice from './slices/settingsSlice';

// Store'u oluştur
export const store = configureStore({
  reducer: {
    transactions: transactionSlice,
    budgets: budgetSlice,
    goals: goalSlice,
    debts: debtSlice,
    investments: investmentSlice,
    settings: settingsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

// Store tiplerini export et
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export const useAppDispatch = () => useDispatch<AppDispatch>();

export default store;
