// Investment Recommendations Screen - Yatırım önerileri ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import InvestmentService, {
  RiskProfile,
  InvestmentRecommendation,
  InvestmentOption
} from '../../services/InvestmentService';

const InvestmentRecommendationsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [riskProfile, setRiskProfile] = useState<RiskProfile | null>(null);
  const [recommendation, setRecommendation] = useState<InvestmentRecommendation | null>(null);
  const [investmentOptions, setInvestmentOptions] = useState<InvestmentOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const investmentService = InvestmentService.getInstance();

      // Risk profilini yükle
      const profile = await investmentService.getRiskProfile();
      setRiskProfile(profile);

      // Yatırım seçeneklerini yükle
      const options = await investmentService.getInvestmentOptions();
      setInvestmentOptions(options);

      // Mevcut önerileri yükle
      const existingRecommendation = await investmentService.getLatestRecommendation();
      setRecommendation(existingRecommendation);

    } catch (error) {
      console.error('Load data error:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateRecommendations = async () => {
    if (!riskProfile) {
      Alert.alert('Risk Profili Gerekli', 'Önce risk değerlendirmesi yapmanız gerekiyor.');
      return;
    }

    setIsGenerating(true);
    try {
      const investmentService = InvestmentService.getInstance();
      const newRecommendation = await investmentService.generateRecommendations(riskProfile);
      setRecommendation(newRecommendation);
      Alert.alert('Başarılı', 'Yeni yatırım önerileri oluşturuldu!');
    } catch (error: any) {
      console.error('Generate recommendations error:', error);
      Alert.alert('Hata', error.message || 'Öneriler oluşturulamadı.');
    } finally {
      setIsGenerating(false);
    }
  };

  const getRiskLevelText = (level: string) => {
    switch (level) {
      case 'conservative': return 'Muhafazakar';
      case 'moderate': return 'Dengeli';
      case 'aggressive': return 'Agresif';
      default: return level;
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'conservative': return theme.colors.success;
      case 'moderate': return theme.colors.warning;
      case 'aggressive': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.textSecondary;
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return 'Yüksek';
      case 'medium': return 'Orta';
      case 'low': return 'Düşük';
      default: return priority;
    }
  };

  const handleCreatePortfolio = () => {
    Alert.alert(
      'Portföy Oluştur',
      'Bu önerilere dayalı bir portföy oluşturmak istiyor musunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Oluştur',
          onPress: () => {
            navigation.navigate('CreatePortfolio' as never);
          }
        }
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    refreshButton: {
      padding: 8,
    },
    riskProfileCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    riskProfileContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    riskProfileItem: {
      alignItems: 'center',
    },
    riskProfileLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    riskProfileValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    recommendationCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    recommendationSummary: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    summaryItem: {
      alignItems: 'center',
    },
    summaryLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    reasoningContainer: {
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: 16,
    },
    reasoningTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    reasoningText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    allocationsCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    allocationItem: {
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      padding: 12,
      marginBottom: 12,
    },
    allocationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    allocationInfo: {
      flex: 1,
    },
    allocationName: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    allocationType: {
      fontSize: 10,
      color: theme.colors.textSecondary,
    },
    allocationPercentage: {
      alignItems: 'flex-end',
    },
    percentageText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    priorityBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
    },
    priorityText: {
      fontSize: 10,
      fontWeight: '600',
      color: theme.colors.surface,
    },
    allocationReasoning: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
      lineHeight: 16,
    },
    allocationDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    allocationDetail: {
      fontSize: 10,
      color: theme.colors.textSecondary,
    },
    allocationBar: {
      height: 4,
      backgroundColor: theme.colors.border,
      borderRadius: 2,
    },
    allocationFill: {
      height: '100%',
      borderRadius: 2,
    },
    analysisCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    analysisGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
    },
    analysisItem: {
      flex: 1,
      minWidth: '45%',
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
    },
    analysisLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
      textAlign: 'center',
    },
    analysisValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    scoreBarContainer: {
      width: '100%',
      height: 4,
      backgroundColor: theme.colors.border,
      borderRadius: 2,
    },
    scoreBar: {
      height: '100%',
      borderRadius: 2,
    },
    actionContainer: {
      gap: 12,
      marginBottom: 20,
    },
    createPortfolioButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
    },
    createPortfolioButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
    regenerateButton: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      paddingVertical: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    regenerateButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 60,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateSubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 20,
    },
    createProfileButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    createProfileButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.surface,
    },
    generateButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    generateButtonText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.emptyStateTitle}>Yükleniyor...</Text>
        </View>
      </View>
    );
  }

  if (!riskProfile) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Ionicons name="analytics-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>Risk Profili Gerekli</Text>
          <Text style={styles.emptyStateSubtitle}>
            Yatırım önerileri için önce risk profilinizi oluşturmanız gerekiyor.
          </Text>
          <TouchableOpacity
            style={styles.createProfileButton}
            onPress={() => navigation.navigate('RiskAssessment' as never)}
          >
            <Text style={styles.createProfileButtonText}>Risk Profili Oluştur</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (isGenerating) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.emptyStateTitle}>Öneriler oluşturuluyor...</Text>
          <Text style={styles.emptyStateSubtitle}>
            Risk profilinize uygun yatırım önerileri hazırlanıyor...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Yatırım Önerileri</Text>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleGenerateRecommendations}
          >
            <Ionicons name="refresh" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Risk Profile Summary */}
        <View style={styles.riskProfileCard}>
          <Text style={styles.cardTitle}>Risk Profiliniz</Text>
          <View style={styles.riskProfileContent}>
            <View style={styles.riskProfileItem}>
              <Text style={styles.riskProfileLabel}>Risk Seviyesi</Text>
              <Text style={[
                styles.riskProfileValue,
                { color: getRiskLevelColor(riskProfile.riskLevel) }
              ]}>
                {getRiskLevelText(riskProfile.riskLevel)}
              </Text>
            </View>
            <View style={styles.riskProfileItem}>
              <Text style={styles.riskProfileLabel}>Risk Skoru</Text>
              <Text style={styles.riskProfileValue}>
                {riskProfile.riskScore.toFixed(1)}/10
              </Text>
            </View>
            <View style={styles.riskProfileItem}>
              <Text style={styles.riskProfileLabel}>Aylık Bütçe</Text>
              <Text style={styles.riskProfileValue}>
                ₺{riskProfile.monthlyInvestmentBudget.toLocaleString('tr-TR')}
              </Text>
            </View>
          </View>
        </View>

        {recommendation ? (
          <>
            {/* Recommendation Summary */}
            <View style={styles.recommendationCard}>
              <Text style={styles.cardTitle}>Öneri Özeti</Text>
              <View style={styles.recommendationSummary}>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Beklenen Getiri</Text>
                  <Text style={styles.summaryValue}>
                    %{recommendation.expectedReturn.toFixed(1)}
                  </Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Güven Skoru</Text>
                  <Text style={styles.summaryValue}>
                    {(recommendation.confidence * 100).toFixed(0)}%
                  </Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text style={styles.summaryLabel}>Risk Skoru</Text>
                  <Text style={styles.summaryValue}>
                    {recommendation.riskScore.toFixed(1)}/10
                  </Text>
                </View>
              </View>

              <View style={styles.reasoningContainer}>
                <Text style={styles.reasoningTitle}>Öneri Gerekçesi</Text>
                <Text style={styles.reasoningText}>
                  {recommendation.reasoning}
                </Text>
              </View>
            </View>

            {/* Recommended Allocations */}
            <View style={styles.allocationsCard}>
              <Text style={styles.cardTitle}>Önerilen Dağılım</Text>
              {recommendation.recommendedAllocations.map((allocation, index) => {
                const investment = investmentOptions.find(opt => opt.id === allocation.investmentId);
                if (!investment) return null;

                return (
                  <TouchableOpacity
                    key={index}
                    style={styles.allocationItem}
                    onPress={() => navigation.navigate('InvestmentDetail' as never, { investmentId: investment.id })}
                  >
                    <View style={styles.allocationHeader}>
                      <View style={styles.allocationInfo}>
                        <Text style={styles.allocationName}>{investment.name}</Text>
                        <Text style={styles.allocationType}>{investment.type.toUpperCase()}</Text>
                      </View>
                      <View style={styles.allocationPercentage}>
                        <Text style={styles.percentageText}>
                          %{allocation.recommendedPercentage}
                        </Text>
                        <View style={[
                          styles.priorityBadge,
                          { backgroundColor: getPriorityColor(allocation.priority) }
                        ]}>
                          <Text style={styles.priorityText}>
                            {getPriorityText(allocation.priority)}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <Text style={styles.allocationReasoning}>
                      {allocation.reasoning}
                    </Text>

                    <View style={styles.allocationDetails}>
                      <Text style={styles.allocationDetail}>
                        Risk: {investment.riskLevel === 'low' ? 'Düşük' :
                               investment.riskLevel === 'medium' ? 'Orta' : 'Yüksek'}
                      </Text>
                      <Text style={styles.allocationDetail}>
                        Beklenen: %{investment.expectedReturn.toFixed(1)}
                      </Text>
                      <Text style={styles.allocationDetail}>
                        Min: ₺{investment.minimumInvestment}
                      </Text>
                    </View>

                    {/* Allocation Bar */}
                    <View style={styles.allocationBar}>
                      <View
                        style={[
                          styles.allocationFill,
                          {
                            width: `${allocation.recommendedPercentage}%`,
                            backgroundColor: getPriorityColor(allocation.priority)
                          }
                        ]}
                      />
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>

            {/* Portfolio Analysis */}
            <View style={styles.analysisCard}>
              <Text style={styles.cardTitle}>Portföy Analizi</Text>
              <View style={styles.analysisGrid}>
                <View style={styles.analysisItem}>
                  <Text style={styles.analysisLabel}>Çeşitlendirme Skoru</Text>
                  <Text style={styles.analysisValue}>
                    {recommendation.diversificationScore}%
                  </Text>
                  <View style={styles.scoreBarContainer}>
                    <View style={[styles.scoreBar, {
                      width: `${recommendation.diversificationScore}%`,
                      backgroundColor: recommendation.diversificationScore >= 75 ? theme.colors.success :
                                     recommendation.diversificationScore >= 50 ? theme.colors.warning : theme.colors.error
                    }]} />
                  </View>
                </View>
                <View style={styles.analysisItem}>
                  <Text style={styles.analysisLabel}>Toplam Yatırım</Text>
                  <Text style={styles.analysisValue}>
                    ₺{recommendation.totalInvestmentAmount.toLocaleString()}
                  </Text>
                </View>
                <View style={styles.analysisItem}>
                  <Text style={styles.analysisLabel}>Risk/Getiri Oranı</Text>
                  <Text style={styles.analysisValue}>
                    {(recommendation.expectedReturn / recommendation.riskScore).toFixed(1)}
                  </Text>
                </View>
                <View style={styles.analysisItem}>
                  <Text style={styles.analysisLabel}>Geçerlilik</Text>
                  <Text style={styles.analysisValue}>
                    {Math.ceil((new Date(recommendation.validUntil).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} gün
                  </Text>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <TouchableOpacity
                style={styles.createPortfolioButton}
                onPress={handleCreatePortfolio}
              >
                <Ionicons name="briefcase" size={20} color={theme.colors.surface} />
                <Text style={styles.createPortfolioButtonText}>
                  Bu Önerilerle Portföy Oluştur
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.regenerateButton}
                onPress={handleGenerateRecommendations}
              >
                <Ionicons name="refresh" size={20} color={theme.colors.primary} />
                <Text style={styles.regenerateButtonText}>
                  Yeni Öneriler Oluştur
                </Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="bulb-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>Henüz öneri yok</Text>
            <Text style={styles.emptyStateSubtitle}>
              Size özel yatırım önerileri oluşturmak için butona tıklayın.
            </Text>
            <TouchableOpacity
              style={styles.generateButton}
              onPress={handleGenerateRecommendations}
            >
              <Text style={styles.generateButtonText}>Öneriler Oluştur</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default InvestmentRecommendationsScreen;
