// Kategori Senkronizasyon Migration - Frontend ile DB'yi senkronize et

import DatabaseManager from '../DatabaseManager';
import { INCOME_CATEGORIES, EXPENSE_CATEGORIES } from '../../constants/categories';

export class CategorySyncMigration {
  private getDb() {
    return DatabaseManager.getDatabase();
  }

  async execute(): Promise<void> {
    console.log('🔄 Starting category synchronization...');

    try {
      // 1. Mevcut kategorileri temizle
      await this.cleanupExistingCategories();

      // 2. Frontend kategorilerini ekle
      await this.insertFrontendCategories();

      // 3. Orphan transaction'ları düzelt
      await this.fixOrphanTransactions();

      console.log('✅ Category synchronization completed!');
    } catch (error) {
      console.error('❌ Category synchronization failed:', error);
      throw error;
    }
  }

  private async cleanupExistingCategories(): Promise<void> {
    console.log('🧹 Cleaning up existing categories...');

    // Tüm kategorileri sil (cascade ile budget_categories da silinecek)
    await this.getDb().runAsync('DELETE FROM categories');

    console.log('✅ Existing categories cleaned up');
  }

  private async insertFrontendCategories(): Promise<void> {
    console.log('📥 Inserting frontend categories...');
    
    const now = new Date().toISOString();
    
    // Income kategorileri ekle
    for (const category of INCOME_CATEGORIES) {
      const categoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      await this.getDb().runAsync(
        `INSERT INTO categories (
          id, user_id, name, type, code, icon, color,
          description, sort_order, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          categoryId,
          'system', // System kategorileri
          category.name,
          'income',
          category.code,
          category.icon,
          category.color,
          `System income category: ${category.name}`,
          INCOME_CATEGORIES.indexOf(category),
          1,
          now,
          now
        ]
      );
      
      console.log(`✅ Added income category: ${category.name} (${category.code})`);
    }
    
    // Expense kategorileri ekle
    for (const category of EXPENSE_CATEGORIES) {
      const categoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      await this.getDb().runAsync(
        `INSERT INTO categories (
          id, user_id, name, type, code, icon, color,
          description, sort_order, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          categoryId,
          'system', // System kategorileri
          category.name,
          'expense',
          category.code,
          category.icon,
          category.color,
          `System expense category: ${category.name}`,
          EXPENSE_CATEGORIES.indexOf(category),
          1,
          now,
          now
        ]
      );
      
      console.log(`✅ Added expense category: ${category.name} (${category.code})`);
    }
    
    console.log('✅ Frontend categories inserted');
  }

  private async fixOrphanTransactions(): Promise<void> {
    console.log('🔧 Fixing orphan transactions...');
    
    // Transaction'lardaki category string'lerini yeni category_id'lere map et
    const categoryMapping: Record<string, string> = {};
    
    // Tüm kategorileri al
    const categories = await this.getDb().getAllAsync<any>(
      'SELECT id, code, name FROM categories'
    );
    
    // Code bazlı mapping oluştur
    for (const cat of categories) {
      if (cat.code) {
        categoryMapping[cat.code] = cat.id;
      }
    }
    
    console.log('📊 Category mapping:', categoryMapping);
    
    // Transaction'ları güncelle
    for (const [code, categoryId] of Object.entries(categoryMapping)) {
      await this.getDb().runAsync(
        'UPDATE transactions SET category_id = ? WHERE category = ? AND category_id IS NULL',
        [categoryId, code]
      );

      const result = await this.getDb().getFirstAsync<{count: number}>(
        'SELECT COUNT(*) as count FROM transactions WHERE category_id = ?',
        [categoryId]
      );

      console.log(`✅ Updated ${result?.count || 0} transactions for category: ${code}`);
    }
    
    console.log('✅ Orphan transactions fixed');
  }

  // Kullanıcı kategorilerini de senkronize et
  async syncUserCategories(userId: string): Promise<void> {
    console.log(`🔄 Syncing categories for user: ${userId}`);
    
    const now = new Date().toISOString();
    
    // Kullanıcının kategorilerini kontrol et
    const userCategories = await this.getDb().getAllAsync<any>(
      'SELECT * FROM categories WHERE user_id = ?',
      [userId]
    );

    if (userCategories.length === 0) {
      console.log('📥 Creating user categories from system categories...');

      // System kategorilerini kullanıcı için kopyala
      const systemCategories = await this.getDb().getAllAsync<any>(
        'SELECT * FROM categories WHERE user_id = ?',
        ['system']
      );
      
      for (const sysCat of systemCategories) {
        const userCategoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        await this.getDb().runAsync(
          `INSERT INTO categories (
            id, user_id, name, type, code, icon, color,
            description, sort_order, is_active, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userCategoryId,
            userId,
            sysCat.name,
            sysCat.type,
            sysCat.code,
            sysCat.icon,
            sysCat.color,
            sysCat.description,
            sysCat.sort_order,
            sysCat.is_active,
            now,
            now
          ]
        );
      }
      
      console.log(`✅ Created ${systemCategories.length} user categories`);
    }
  }
}

export default new CategorySyncMigration();
