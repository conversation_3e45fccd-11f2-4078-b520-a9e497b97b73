// Transaction Detail Screen - İşlem detay ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import TransactionRepository from '../../database/repositories/TransactionRepository';
import {
  Transaction,
  INCOME_CATEGORY_LABELS,
  EXPENSE_CATEGORY_LABELS,
  PAYMENT_METHOD_LABELS,
  RECURRENCE_LABELS,
  CurrencyCode,
} from '../../types/transaction';

const TransactionDetailScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [receiptModalVisible, setReceiptModalVisible] = useState(false);

  // Get transaction ID from route params
  const { transactionId } = route.params as { transactionId: string };

  useEffect(() => {
    loadTransaction();
  }, [transactionId]);

  const loadTransaction = async () => {
    try {
      console.log('🔍 Loading transaction for ID:', transactionId);
      setLoading(true);
      setError(null);

      const result = await TransactionRepository.getById(transactionId);
      if (result) {
        console.log('✅ Transaction loaded:', result);
        setTransaction(result);
      } else {
        console.log('❌ Transaction not found');
        setError('İşlem bulunamadı');
      }
    } catch (err) {
      console.error('❌ Error loading transaction:', err);
      setError('İşlem yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: CurrencyCode = 'TRY') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getCategoryLabel = (transaction: Transaction) => {
    if (transaction.type === 'income') {
      return INCOME_CATEGORY_LABELS[transaction.category as keyof typeof INCOME_CATEGORY_LABELS] || transaction.category;
    } else {
      return EXPENSE_CATEGORY_LABELS[transaction.category as keyof typeof EXPENSE_CATEGORY_LABELS] || transaction.category;
    }
  };

  const getSubcategoryLabel = (transaction: Transaction) => {
    if (transaction.type === 'expense' && 'subcategory' in transaction && transaction.subcategory) {
      // Alt kategori etiketleri için basit mapping
      const subcategoryLabels: Record<string, string> = {
        // Food subcategories
        'groceries': 'Market',
        'restaurant': 'Restoran',
        'fast_food': 'Fast Food',
        'coffee': 'Kahve',
        'alcohol': 'Alkol',
        // Transportation subcategories
        'fuel': 'Yakıt',
        'public_transport': 'Toplu Taşıma',
        'taxi': 'Taksi',
        'parking': 'Park',
        'maintenance': 'Bakım',
        // Bills subcategories
        'electricity': 'Elektrik',
        'water': 'Su',
        'gas': 'Doğalgaz',
        'internet': 'İnternet',
        'phone': 'Telefon',
        'rent': 'Kira',
        // Add more as needed
        'other': 'Diğer',
      };
      return subcategoryLabels[transaction.subcategory] || transaction.subcategory;
    }
    return null;
  };

  const handleDelete = () => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ Deleting transaction:', transactionId);

              await TransactionRepository.delete(transactionId);

              console.log('✅ Transaction deleted successfully');
              Alert.alert('Başarılı', 'İşlem başarıyla silindi.', [
                { text: 'Tamam', onPress: () => navigation.goBack() }
              ]);
            } catch (error) {
              console.error('❌ Error deleting transaction:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleEdit = () => {
    if (transaction) {
      // AddTransactionScreen'e transaction verisiyle yönlendir
      navigation.navigate('AddTransaction', {
        editTransaction: transaction
      });
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      alignItems: 'center',
      paddingVertical: 40,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.surface,
    },
    typeIcon: {
      width: 80,
      height: 80,
      borderRadius: 40,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    amount: {
      fontSize: 32,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    description: {
      fontSize: 18,
      color: theme.colors.text,
      textAlign: 'center',
    },
    detailsContainer: {
      padding: 20,
    },
    detailItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    detailLabel: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 12,
    },
    detailValue: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'right',
      flex: 1,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      justifyContent: 'flex-end',
    },
    tag: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginBottom: 4,
    },
    tagText: {
      fontSize: 12,
      color: theme.colors.surface,
      fontWeight: '500',
    },
    actionsContainer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    editButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
    },
    editButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.primary,
      marginLeft: 8,
    },
    deleteButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.error,
      borderRadius: 12,
      paddingVertical: 16,
    },
    deleteButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.error,
      marginLeft: 8,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    errorSubtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    receiptButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 6,
      gap: 4,
    },
    receiptButtonText: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    // Receipt Modal Styles
    receiptModal: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    receiptModalHeader: {
      position: 'absolute',
      top: 50,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      zIndex: 1,
    },
    receiptModalTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: 'white',
    },
    receiptModalCloseButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 20,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
    receiptImage: {
      width: Dimensions.get('window').width - 40,
      height: Dimensions.get('window').height - 200,
      resizeMode: 'contain',
    },
    receiptImageError: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 40,
      margin: 20,
    },
    receiptErrorText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
    },
  });

  // Loading state
  if (loading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.description, { marginTop: 16 }]}>İşlem yükleniyor...</Text>
      </View>
    );
  }

  // Error state
  if (error || !transaction) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.errorTitle}>İşlem Bulunamadı</Text>
          <Text style={styles.errorSubtitle}>
            {error || 'Bu işlem silinmiş olabilir.'}
          </Text>
          <TouchableOpacity
            style={[styles.editButton, { marginTop: 20 }]}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={20} color={theme.colors.primary} />
            <Text style={styles.editButtonText}>Geri Dön</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[
          styles.typeIcon,
          { backgroundColor: transaction.type === 'income' ? theme.colors.success : theme.colors.error }
        ]}>
          <Ionicons
            name={transaction.type === 'income' ? 'trending-up' : 'trending-down'}
            size={32}
            color={theme.colors.surface}
          />
        </View>
        <Text style={[
          styles.amount,
          { color: transaction.type === 'income' ? theme.colors.success : theme.colors.error }
        ]}>
          {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount, transaction.currency)}
        </Text>
        <Text style={styles.description}>{transaction.description || 'Açıklama yok'}</Text>
      </View>

      {/* Details */}
      <View style={styles.detailsContainer}>
        <View style={styles.detailItem}>
          <View style={styles.detailLeft}>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.detailLabel}>Tarih</Text>
          </View>
          <Text style={styles.detailValue}>
            {new Date(transaction.date).toLocaleDateString('tr-TR', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>

        <View style={styles.detailItem}>
          <View style={styles.detailLeft}>
            <Ionicons name="pricetag-outline" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.detailLabel}>Kategori</Text>
          </View>
          <Text style={styles.detailValue}>{getCategoryLabel(transaction)}</Text>
        </View>

        {getSubcategoryLabel(transaction) && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="bookmark-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Alt Kategori</Text>
            </View>
            <Text style={styles.detailValue}>{getSubcategoryLabel(transaction)}</Text>
          </View>
        )}

        {transaction.paymentMethod && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="card-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Ödeme Yöntemi</Text>
            </View>
            <Text style={styles.detailValue}>
              {PAYMENT_METHOD_LABELS[transaction.paymentMethod] || transaction.paymentMethod}
            </Text>
          </View>
        )}

        {transaction.type === 'income' && 'source' in transaction && transaction.source && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="business-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Gelir Kaynağı</Text>
            </View>
            <Text style={styles.detailValue}>{transaction.source}</Text>
          </View>
        )}

        {transaction.type === 'expense' && 'merchant' in transaction && transaction.merchant && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="storefront-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>İşyeri/Mağaza</Text>
            </View>
            <Text style={styles.detailValue}>{transaction.merchant}</Text>
          </View>
        )}

        {transaction.location && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="location-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Konum</Text>
            </View>
            <Text style={styles.detailValue}>
              {typeof transaction.location === 'string'
                ? transaction.location
                : transaction.location.placeName || transaction.location.address || 'Konum bilgisi'}
            </Text>
          </View>
        )}

        <View style={styles.detailItem}>
          <View style={styles.detailLeft}>
            <Ionicons name="cash-outline" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.detailLabel}>Para Birimi</Text>
          </View>
          <Text style={styles.detailValue}>{transaction.currency}</Text>
        </View>

        {/* Vergi Bilgileri */}
        {transaction.type === 'income' && 'taxAmount' in transaction && transaction.taxAmount && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="receipt-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Vergi Tutarı</Text>
            </View>
            <Text style={styles.detailValue}>
              {formatCurrency(transaction.taxAmount, transaction.currency)}
            </Text>
          </View>
        )}

        {transaction.type === 'income' && 'taxable' in transaction && transaction.taxable !== undefined && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="document-text-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Vergiye Tabi</Text>
            </View>
            <Text style={styles.detailValue}>
              {transaction.taxable ? 'Evet' : 'Hayır'}
            </Text>
          </View>
        )}

        {transaction.type === 'expense' && 'isBusinessExpense' in transaction && transaction.isBusinessExpense !== undefined && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="briefcase-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>İş Gideri</Text>
            </View>
            <Text style={styles.detailValue}>
              {transaction.isBusinessExpense ? 'Evet' : 'Hayır'}
            </Text>
          </View>
        )}

        {transaction.type === 'expense' && 'isDeductible' in transaction && transaction.isDeductible !== undefined && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="calculator-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Vergiden Düşülebilir</Text>
            </View>
            <Text style={styles.detailValue}>
              {transaction.isDeductible ? 'Evet' : 'Hayır'}
            </Text>
          </View>
        )}

        {transaction.recurrence && transaction.recurrence.frequency !== 'none' && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="repeat-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Tekrarlama</Text>
            </View>
            <Text style={styles.detailValue}>
              {RECURRENCE_LABELS[transaction.recurrence.frequency]}
            </Text>
          </View>
        )}

        {/* Makbuz/Fatura Bilgisi */}
        {transaction.receipt && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="document-attach-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Makbuz/Fatura</Text>
            </View>
            <TouchableOpacity
              style={styles.receiptButton}
              onPress={() => setReceiptModalVisible(true)}
            >
              <Ionicons name="eye-outline" size={16} color={theme.colors.primary} />
              <Text style={styles.receiptButtonText}>Görüntüle</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Garanti Bilgisi */}
        {transaction.type === 'expense' && 'warranty' in transaction && transaction.warranty && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="shield-checkmark-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Garanti</Text>
            </View>
            <Text style={styles.detailValue}>
              {new Date(transaction.warranty.expiryDate).toLocaleDateString('tr-TR')} tarihine kadar
            </Text>
          </View>
        )}

        {/* İşlem Durumu */}
        <View style={styles.detailItem}>
          <View style={styles.detailLeft}>
            <Ionicons
              name={
                transaction.status === 'completed' ? 'checkmark-circle-outline' :
                transaction.status === 'pending' ? 'time-outline' :
                transaction.status === 'cancelled' ? 'close-circle-outline' :
                'alert-circle-outline'
              }
              size={20}
              color={
                transaction.status === 'completed' ? theme.colors.success :
                transaction.status === 'pending' ? theme.colors.warning :
                theme.colors.error
              }
            />
            <Text style={styles.detailLabel}>Durum</Text>
          </View>
          <Text style={[
            styles.detailValue,
            {
              color: transaction.status === 'completed' ? theme.colors.success :
                     transaction.status === 'pending' ? theme.colors.warning :
                     theme.colors.error
            }
          ]}>
            {transaction.status === 'completed' && 'Tamamlandı'}
            {transaction.status === 'pending' && 'Beklemede'}
            {transaction.status === 'cancelled' && 'İptal Edildi'}
            {transaction.status === 'failed' && 'Başarısız'}
          </Text>
        </View>

        {transaction.tags && transaction.tags.length > 0 && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="pricetags-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Etiketler</Text>
            </View>
            <View style={styles.tagsContainer}>
              {transaction.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>
                    {typeof tag === 'string' ? tag : tag.name || 'Etiket'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        <View style={styles.detailItem}>
          <View style={styles.detailLeft}>
            <Ionicons name="time-outline" size={20} color={theme.colors.textSecondary} />
            <Text style={styles.detailLabel}>Oluşturulma</Text>
          </View>
          <Text style={styles.detailValue}>
            {new Date(transaction.createdAt).toLocaleDateString('tr-TR')}
          </Text>
        </View>

        {transaction.updatedAt !== transaction.createdAt && (
          <View style={styles.detailItem}>
            <View style={styles.detailLeft}>
              <Ionicons name="create-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.detailLabel}>Güncellenme</Text>
            </View>
            <Text style={styles.detailValue}>
              {new Date(transaction.updatedAt).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        )}
      </View>

      {/* Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
          <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.editButtonText}>Düzenle</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
          <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
          <Text style={styles.deleteButtonText}>Sil</Text>
        </TouchableOpacity>
      </View>

      {/* Receipt Modal */}
      <Modal
        visible={receiptModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setReceiptModalVisible(false)}
      >
        <View style={styles.receiptModal}>
          <View style={styles.receiptModalHeader}>
            <Text style={styles.receiptModalTitle}>Makbuz/Fatura</Text>
            <TouchableOpacity
              style={styles.receiptModalCloseButton}
              onPress={() => setReceiptModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {transaction?.receipt ? (
            <Image
              source={{ uri: transaction.receipt.imageUri }}
              style={styles.receiptImage}
              onError={() => {
                Alert.alert('Hata', 'Makbuz görüntülenirken bir hata oluştu.');
                setReceiptModalVisible(false);
              }}
            />
          ) : (
            <View style={styles.receiptImageError}>
              <Ionicons name="document-outline" size={64} color={theme.colors.textSecondary} />
              <Text style={styles.receiptErrorText}>Makbuz bulunamadı</Text>
            </View>
          )}
        </View>
      </Modal>
    </ScrollView>
  );
};



export default TransactionDetailScreen;
