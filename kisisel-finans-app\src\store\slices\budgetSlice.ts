// Budget Slice - Bütçe state yönetimi

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Budget } from '../../types';

interface BudgetState {
  budgets: Budget[];
  isLoading: boolean;
  error: string | null;
}

const initialState: BudgetState = {
  budgets: [],
  isLoading: false,
  error: null,
};

export const fetchBudgets = createAsyncThunk(
  'budgets/fetchBudgets',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: API çağrısı
      const mockBudgets: Budget[] = [
        {
          id: '1',
          userId: '1',
          name: '<PERSON><PERSON><PERSON>k Bütçesi',
          categoryId: 'food',
          amount: 1000,
          period: 'monthly',
          startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
          endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString(),
          spent: 650,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      return mockBudgets;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Bütçeler yüklenirken hata oluştu');
    }
  }
);

export const addBudget = createAsyncThunk(
  'budgets/addBudget',
  async (budgetData: Omit<Budget, 'id' | 'userId' | 'spent' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const newBudget: Budget = {
        ...budgetData,
        id: Date.now().toString(),
        userId: '1',
        spent: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      return newBudget;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Bütçe eklenirken hata oluştu');
    }
  }
);

export const updateBudget = createAsyncThunk(
  'budgets/updateBudget',
  async (params: { id: string; data: Partial<Budget> }, { rejectWithValue }) => {
    try {
      const updatedBudget: Budget = {
        ...params.data,
        id: params.id,
        updatedAt: new Date().toISOString(),
      } as Budget;
      return updatedBudget;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Bütçe güncellenirken hata oluştu');
    }
  }
);

export const deleteBudget = createAsyncThunk(
  'budgets/deleteBudget',
  async (budgetId: string, { rejectWithValue }) => {
    try {
      return budgetId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Bütçe silinirken hata oluştu');
    }
  }
);

const budgetSlice = createSlice({
  name: 'budgets',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateBudgetSpent: (state, action: PayloadAction<{ budgetId: string; amount: number }>) => {
      const budget = state.budgets.find(b => b.id === action.payload.budgetId);
      if (budget) {
        budget.spent += action.payload.amount;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBudgets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBudgets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.budgets = action.payload;
      })
      .addCase(fetchBudgets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(addBudget.fulfilled, (state, action) => {
        state.budgets.push(action.payload);
      })
      .addCase(updateBudget.fulfilled, (state, action) => {
        const index = state.budgets.findIndex(b => b.id === action.payload.id);
        if (index !== -1) {
          state.budgets[index] = action.payload;
        }
      })
      .addCase(deleteBudget.fulfilled, (state, action) => {
        state.budgets = state.budgets.filter(b => b.id !== action.payload);
      });
  },
});

export const { clearError, updateBudgetSpent } = budgetSlice.actions;
export default budgetSlice.reducer;
