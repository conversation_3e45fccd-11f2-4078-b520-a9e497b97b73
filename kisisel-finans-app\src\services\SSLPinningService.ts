// SSL Pinning Service - SSL sertifika sabitleme servisi

import * as Crypto from 'expo-crypto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface CertificatePin {
  hostname: string;
  publicKeyHash: string;
  certificateHash: string;
  algorithm: 'SHA-256' | 'SHA-1';
  expiryDate: string;
  isBackup: boolean;
}

export interface PinningConfig {
  enabled: boolean;
  enforceOnProduction: boolean;
  allowBypassInDevelopment: boolean;
  pinValidationTimeout: number;
  maxRetryAttempts: number;
  fallbackEnabled: boolean;
}

export interface PinningResult {
  success: boolean;
  hostname: string;
  pinMatched: boolean;
  certificateValid: boolean;
  error?: string;
  bypassReason?: string;
}

export interface SSLPinningStatus {
  isEnabled: boolean;
  pinnedHosts: string[];
  lastValidation: number;
  validationCount: number;
  bypassCount: number;
  failureCount: number;
}

class SSLPinningService {
  private static instance: SSLPinningService;
  private readonly PINS_STORAGE_KEY = 'ssl_certificate_pins';
  private readonly CONFIG_STORAGE_KEY = 'ssl_pinning_config';
  private readonly STATUS_STORAGE_KEY = 'ssl_pinning_status';
  
  // Default configuration
  private config: PinningConfig = {
    enabled: true,
    enforceOnProduction: true,
    allowBypassInDevelopment: __DEV__,
    pinValidationTimeout: 10000, // 10 seconds
    maxRetryAttempts: 3,
    fallbackEnabled: true,
  };

  // Certificate pins storage
  private certificatePins: Map<string, CertificatePin[]> = new Map();
  
  // Status tracking
  private status: SSLPinningStatus = {
    isEnabled: true,
    pinnedHosts: [],
    lastValidation: 0,
    validationCount: 0,
    bypassCount: 0,
    failureCount: 0,
  };

  static getInstance(): SSLPinningService {
    if (!SSLPinningService.instance) {
      SSLPinningService.instance = new SSLPinningService();
    }
    return SSLPinningService.instance;
  }

  constructor() {
    this.initializeService();
  }

  /**
   * Service'i başlat
   */
  private async initializeService(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.loadCertificatePins();
      await this.loadStatus();
      await this.setupDefaultPins();
    } catch (error) {
      console.error('SSL Pinning Service initialization error:', error);
    }
  }

  /**
   * Konfigürasyonu yükle
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const configStr = await AsyncStorage.getItem(this.CONFIG_STORAGE_KEY);
      if (configStr) {
        this.config = { ...this.config, ...JSON.parse(configStr) };
      }
    } catch (error) {
      console.error('Error loading SSL pinning config:', error);
    }
  }

  /**
   * Sertifika pin'lerini yükle
   */
  private async loadCertificatePins(): Promise<void> {
    try {
      const pinsStr = await AsyncStorage.getItem(this.PINS_STORAGE_KEY);
      if (pinsStr) {
        const pinsData = JSON.parse(pinsStr);
        this.certificatePins = new Map(Object.entries(pinsData));
      }
    } catch (error) {
      console.error('Error loading certificate pins:', error);
    }
  }

  /**
   * Durum bilgisini yükle
   */
  private async loadStatus(): Promise<void> {
    try {
      const statusStr = await AsyncStorage.getItem(this.STATUS_STORAGE_KEY);
      if (statusStr) {
        this.status = { ...this.status, ...JSON.parse(statusStr) };
      }
    } catch (error) {
      console.error('Error loading SSL pinning status:', error);
    }
  }

  /**
   * Varsayılan pin'leri ayarla
   */
  private async setupDefaultPins(): Promise<void> {
    try {
      // Production API pins
      await this.addCertificatePin({
        hostname: 'api.yourfinanceapp.com',
        publicKeyHash: 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Replace with actual hash
        certificateHash: 'BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=', // Replace with actual hash
        algorithm: 'SHA-256',
        expiryDate: '2025-12-31',
        isBackup: false,
      });

      // Backup pin
      await this.addCertificatePin({
        hostname: 'api.yourfinanceapp.com',
        publicKeyHash: 'CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=', // Replace with backup hash
        certificateHash: 'DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD=', // Replace with backup hash
        algorithm: 'SHA-256',
        expiryDate: '2025-12-31',
        isBackup: true,
      });

      // External API pins (örnek)
      await this.addCertificatePin({
        hostname: 'api.exchangerate-api.com',
        publicKeyHash: 'EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE=', // Replace with actual hash
        certificateHash: 'FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF=', // Replace with actual hash
        algorithm: 'SHA-256',
        expiryDate: '2025-12-31',
        isBackup: false,
      });
    } catch (error) {
      console.error('Error setting up default pins:', error);
    }
  }

  /**
   * Sertifika pin'i ekle
   */
  async addCertificatePin(pin: CertificatePin): Promise<void> {
    try {
      const hostname = pin.hostname;
      const existingPins = this.certificatePins.get(hostname) || [];
      
      // Aynı pin varsa güncelle, yoksa ekle
      const existingIndex = existingPins.findIndex(p => 
        p.publicKeyHash === pin.publicKeyHash && p.isBackup === pin.isBackup
      );
      
      if (existingIndex >= 0) {
        existingPins[existingIndex] = pin;
      } else {
        existingPins.push(pin);
      }
      
      this.certificatePins.set(hostname, existingPins);
      await this.saveCertificatePins();
      
      // Status güncelle
      this.status.pinnedHosts = Array.from(this.certificatePins.keys());
      await this.saveStatus();
    } catch (error) {
      console.error('Error adding certificate pin:', error);
      throw new Error('Sertifika pin eklenemedi');
    }
  }

  /**
   * Sertifika pin'lerini kaydet
   */
  private async saveCertificatePins(): Promise<void> {
    try {
      const pinsData = Object.fromEntries(this.certificatePins);
      await AsyncStorage.setItem(this.PINS_STORAGE_KEY, JSON.stringify(pinsData));
    } catch (error) {
      console.error('Error saving certificate pins:', error);
    }
  }

  /**
   * Durum bilgisini kaydet
   */
  private async saveStatus(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STATUS_STORAGE_KEY, JSON.stringify(this.status));
    } catch (error) {
      console.error('Error saving SSL pinning status:', error);
    }
  }

  /**
   * SSL bağlantısını doğrula (Mock implementation)
   */
  async validateSSLConnection(hostname: string, certificateData?: string): Promise<PinningResult> {
    try {
      // Development bypass kontrolü
      if (this.config.allowBypassInDevelopment && __DEV__) {
        this.status.bypassCount++;
        await this.saveStatus();
        
        return {
          success: true,
          hostname,
          pinMatched: true,
          certificateValid: true,
          bypassReason: 'Development mode bypass',
        };
      }

      // Pinning disabled kontrolü
      if (!this.config.enabled) {
        this.status.bypassCount++;
        await this.saveStatus();
        
        return {
          success: true,
          hostname,
          pinMatched: true,
          certificateValid: true,
          bypassReason: 'SSL pinning disabled',
        };
      }

      // Pin'leri al
      const pins = this.certificatePins.get(hostname);
      if (!pins || pins.length === 0) {
        // Pin yoksa ve fallback enabled ise geç
        if (this.config.fallbackEnabled) {
          this.status.bypassCount++;
          await this.saveStatus();
          
          return {
            success: true,
            hostname,
            pinMatched: false,
            certificateValid: true,
            bypassReason: 'No pins configured, fallback enabled',
          };
        } else {
          this.status.failureCount++;
          await this.saveStatus();
          
          return {
            success: false,
            hostname,
            pinMatched: false,
            certificateValid: false,
            error: 'No certificate pins configured for hostname',
          };
        }
      }

      // Mock certificate validation
      const mockCertificateHash = await this.generateMockCertificateHash(hostname);
      const mockPublicKeyHash = await this.generateMockPublicKeyHash(hostname);

      // Pin matching kontrolü
      const matchingPin = pins.find(pin => 
        pin.publicKeyHash === mockPublicKeyHash || 
        pin.certificateHash === mockCertificateHash
      );

      if (matchingPin) {
        // Pin eşleşti
        this.status.validationCount++;
        this.status.lastValidation = Date.now();
        await this.saveStatus();
        
        return {
          success: true,
          hostname,
          pinMatched: true,
          certificateValid: true,
        };
      } else {
        // Pin eşleşmedi
        this.status.failureCount++;
        await this.saveStatus();
        
        return {
          success: false,
          hostname,
          pinMatched: false,
          certificateValid: false,
          error: 'Certificate pin validation failed',
        };
      }
    } catch (error) {
      console.error('SSL validation error:', error);
      this.status.failureCount++;
      await this.saveStatus();
      
      return {
        success: false,
        hostname,
        pinMatched: false,
        certificateValid: false,
        error: `SSL validation error: ${error}`,
      };
    }
  }

  /**
   * Mock sertifika hash'i oluştur (gerçek uygulamada native'den gelecek)
   */
  private async generateMockCertificateHash(hostname: string): Promise<string> {
    const mockData = `mock_certificate_${hostname}_${Date.now()}`;
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      mockData,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );
  }

  /**
   * Mock public key hash'i oluştur
   */
  private async generateMockPublicKeyHash(hostname: string): Promise<string> {
    const mockData = `mock_public_key_${hostname}_${Date.now()}`;
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      mockData,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );
  }

  /**
   * Hostname için pin'leri al
   */
  getCertificatePins(hostname: string): CertificatePin[] {
    return this.certificatePins.get(hostname) || [];
  }

  /**
   * Tüm pin'leri al
   */
  getAllCertificatePins(): Map<string, CertificatePin[]> {
    return new Map(this.certificatePins);
  }

  /**
   * SSL pinning durumunu al
   */
  getSSLPinningStatus(): SSLPinningStatus {
    return { ...this.status };
  }

  /**
   * Konfigürasyonu al
   */
  getConfiguration(): PinningConfig {
    return { ...this.config };
  }

  /**
   * Konfigürasyonu güncelle
   */
  async updateConfiguration(newConfig: Partial<PinningConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      await AsyncStorage.setItem(this.CONFIG_STORAGE_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('Error updating SSL pinning config:', error);
      throw new Error('SSL pinning konfigürasyonu güncellenemedi');
    }
  }

  /**
   * Pin'leri temizle
   */
  async clearAllPins(): Promise<void> {
    try {
      this.certificatePins.clear();
      await AsyncStorage.removeItem(this.PINS_STORAGE_KEY);
      
      this.status.pinnedHosts = [];
      await this.saveStatus();
    } catch (error) {
      console.error('Error clearing certificate pins:', error);
      throw new Error('Sertifika pin\'leri temizlenemedi');
    }
  }

  /**
   * İstatistikleri sıfırla
   */
  async resetStatistics(): Promise<void> {
    try {
      this.status.validationCount = 0;
      this.status.bypassCount = 0;
      this.status.failureCount = 0;
      this.status.lastValidation = 0;
      await this.saveStatus();
    } catch (error) {
      console.error('Error resetting SSL pinning statistics:', error);
      throw new Error('SSL pinning istatistikleri sıfırlanamadı');
    }
  }
}

export default SSLPinningService.getInstance();
