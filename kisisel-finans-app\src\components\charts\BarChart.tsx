// Bar Chart Component - Çubuk grafik bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { BarChart as RNBarChart } from 'react-native-chart-kit';
import { useTheme } from '../../contexts/ThemeContext';

const { width } = Dimensions.get('window');

export interface BarChartDataset {
  data: number[];
  color?: (opacity: number) => string;
}

interface BarChartProps {
  data: {
    labels: string[];
    datasets: BarChartDataset[];
  };
  title?: string;
  yAxisSuffix?: string;
  yAxisPrefix?: string;
  showGrid?: boolean;
  showValues?: boolean;
  size?: number;
  style?: any;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  yAxisSuffix = '',
  yAxisPrefix = '',
  showGrid = true,
  showValues = false,
  size = width - 40,
  style,
}) => {
  const { theme } = useTheme();

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.colors.primary,
    labelColor: (opacity = 1) => theme.colors.text,
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeDasharray: showGrid ? '' : '0',
      stroke: theme.colors.border,
      strokeWidth: 1,
    },
    propsForLabels: {
      fontSize: 12,
      fontWeight: '500',
    },
    barPercentage: 0.7,
    fillShadowGradient: theme.colors.primary,
    fillShadowGradientOpacity: 1,
  };

  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 16,
      marginBottom: 16,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    chartContainer: {
      alignItems: 'center',
      overflow: 'hidden',
      borderRadius: 12,
    },
    chart: {
      marginVertical: 8,
      borderRadius: 16,
    },
    emptyContainer: {
      height: 200,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
  });

  if (!data || !data.datasets || data.datasets.length === 0) {
    return (
      <View style={[styles.container, style]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Gösterilecek veri yok</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {title && <Text style={styles.title}>{title}</Text>}

      <View style={styles.chartContainer}>
        <RNBarChart
          data={data}
          width={size}
          height={220}
          yAxisLabel=""
          chartConfig={chartConfig}
          style={styles.chart}
          yAxisSuffix={yAxisSuffix}
          withInnerLines={showGrid}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          showValuesOnTopOfBars={showValues}
          flatColor={true}
        />
      </View>
    </View>
  );
};

export default BarChart;
