// Security Test Screen - Güvenlik izleme test ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import SecurityMonitoringService from '../../services/SecurityMonitoringService';
import { SecurityMetrics, SecurityEvent, SecurityAlert } from '../../types/security';

const SecurityTestScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();

  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
  const [activeAlerts, setActiveAlerts] = useState<SecurityAlert[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setIsLoading(true);

      // Security metrics al
      const securityMetrics = await SecurityMonitoringService.getSecurityMetrics();
      setMetrics(securityMetrics);

      // Recent events al
      const events = await SecurityMonitoringService.getRecentSecurityEvents(20);
      setRecentEvents(events);

      // Active alerts al
      const alerts = await SecurityMonitoringService.getActiveSecurityAlerts();
      setActiveAlerts(alerts);

    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSecurityData();
    setRefreshing(false);
  };

  // Test functions
  const testBruteForceDetection = async () => {
    try {
      Alert.alert(
        '🔒 Brute Force Test',
        'Birden fazla başarısız login denemesi simüle edilecek',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Başlat',
            onPress: async () => {
              for (let i = 0; i < 6; i++) {
                await SecurityMonitoringService.logLoginAttempt({
                  email: '<EMAIL>',
                  ipAddress: '*************',
                  userAgent: 'TestAgent/1.0',
                  success: false,
                  failureReason: 'Invalid password',
                  blocked: false,
                  location: { country: 'Turkey', city: 'Istanbul' },
                });
              }
              Alert.alert('✅ Test Tamamlandı', 'Brute force detection test edildi');
              await loadSecurityData();
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('❌ Hata', 'Test sırasında hata oluştu');
    }
  };

  const testSuspiciousActivity = async () => {
    try {
      if (!user) return;

      // Suspicious login from unusual location
      await SecurityMonitoringService.logSecurityEvent({
        type: 'login_success',
        level: 'medium',
        category: 'authentication',
        userId: user.id,
        ipAddress: '***********', // Different IP
        userAgent: 'UnknownBrowser/1.0',
        location: {
          country: 'Unknown',
          city: 'Unknown',
        },
        details: {
          action: 'suspicious_login',
          result: 'success',
          reason: 'Login from unusual location',
          metadata: {
            riskScore: 75,
            anomalyDetected: true,
          },
        },
        context: {
          riskScore: 75,
          anomalyDetected: true,
        },
      });

      Alert.alert('✅ Test Tamamlandı', 'Suspicious activity test edildi');
      await loadSecurityData();
    } catch (error) {
      Alert.alert('❌ Hata', 'Test sırasında hata oluştu');
    }
  };

  const testPrivilegeEscalation = async () => {
    try {
      if (!user) return;

      await SecurityMonitoringService.logSecurityEvent({
        type: 'role_assigned',
        level: 'critical',
        category: 'authorization',
        userId: user.id,
        details: {
          action: 'privilege_escalation',
          result: 'success',
          metadata: {
            oldRole: 'basic',
            newRole: 'admin',
            assignedBy: 'unknown',
          },
        },
      });

      Alert.alert('✅ Test Tamamlandı', 'Privilege escalation test edildi');
      await loadSecurityData();
    } catch (error) {
      Alert.alert('❌ Hata', 'Test sırasında hata oluştu');
    }
  };

  const testDataExport = async () => {
    try {
      if (!user) return;

      for (let i = 0; i < 4; i++) {
        await SecurityMonitoringService.logSecurityEvent({
          type: 'data_export',
          level: 'medium',
          category: 'data_breach',
          userId: user.id,
          details: {
            action: 'export_transactions',
            result: 'success',
            resource: 'transactions',
            metadata: {
              exportFormat: 'CSV',
              recordCount: 1000 + i * 100,
            },
          },
        });
      }

      Alert.alert('✅ Test Tamamlandı', 'Data export monitoring test edildi');
      await loadSecurityData();
    } catch (error) {
      Alert.alert('❌ Hata', 'Test sırasında hata oluştu');
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const success = await SecurityMonitoringService.resolveSecurityAlert(alertId, user?.id || 'system');
      if (success) {
        Alert.alert('✅ Başarılı', 'Alert çözüldü');
        await loadSecurityData();
      } else {
        Alert.alert('❌ Hata', 'Alert çözülemedi');
      }
    } catch (error) {
      Alert.alert('❌ Hata', 'Alert çözme sırasında hata oluştu');
    }
  };

  const clearAllData = async () => {
    Alert.alert(
      '🗑️ Verileri Temizle',
      'Tüm güvenlik verileri silinecek. Emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            // This would require a method in SecurityMonitoringService
            Alert.alert('ℹ️ Bilgi', 'Veri temizleme özelliği implement edilecek');
          }
        }
      ]
    );
  };

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return '#F44336';
      case 'high': return '#FF9800';
      case 'medium': return '#FFC107';
      case 'low': return '#4CAF50';
      default: return theme.colors.text;
    }
  };

  const getThreatLevelIcon = (level: string) => {
    switch (level) {
      case 'critical': return 'alert-circle';
      case 'high': return 'warning';
      case 'medium': return 'information-circle';
      case 'low': return 'checkmark-circle';
      default: return 'help-circle';
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    metricCard: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    metricValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    metricLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    threatScore: {
      fontSize: 32,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 8,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    buttonTextSecondary: {
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    eventItem: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      borderLeftWidth: 4,
    },
    eventHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    eventType: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    eventTime: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    eventDetails: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 4,
    },
    alertItem: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      borderLeftWidth: 4,
    },
    alertHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    alertTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    alertDescription: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    resolveButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
    },
    resolveButtonText: {
      color: theme.colors.background,
      fontSize: 12,
      fontWeight: '600',
    },
    emptyState: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontStyle: 'italic',
      padding: 20,
    },
  });

  if (isLoading && !metrics) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Ionicons name="shield-outline" size={64} color={theme.colors.primary} />
        <Text style={[styles.title, { marginTop: 16 }]}>Güvenlik Verileri Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <Text style={styles.title}>🛡️ Security Monitoring</Text>

      {/* Security Metrics */}
      {metrics && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Güvenlik Metrikleri:</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricCard}>
              <Text style={[styles.threatScore, { color: getThreatLevelColor(
                metrics.threatScore > 70 ? 'critical' :
                metrics.threatScore > 40 ? 'high' :
                metrics.threatScore > 20 ? 'medium' : 'low'
              )}]}>
                {metrics.threatScore}
              </Text>
              <Text style={styles.metricLabel}>Threat Score</Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{metrics.totalEvents}</Text>
              <Text style={styles.metricLabel}>Toplam Event</Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{metrics.activeAlerts}</Text>
              <Text style={styles.metricLabel}>Aktif Alert</Text>
            </View>

            <View style={styles.metricCard}>
              <Text style={styles.metricValue}>{metrics.resolvedAlerts}</Text>
              <Text style={styles.metricLabel}>Çözülen Alert</Text>
            </View>
          </View>
        </View>
      )}

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Güvenlik Testleri:</Text>

        <TouchableOpacity style={styles.button} onPress={testBruteForceDetection}>
          <Ionicons name="shield-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Brute Force Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testSuspiciousActivity}>
          <Ionicons name="warning-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Suspicious Activity Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testPrivilegeEscalation}>
          <Ionicons name="arrow-up-circle-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Privilege Escalation Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testDataExport}>
          <Ionicons name="download-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Data Export Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={loadSecurityData}>
          <Ionicons name="refresh-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Verileri Yenile</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={clearAllData}>
          <Ionicons name="trash-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Verileri Temizle</Text>
        </TouchableOpacity>
      </View>

      {/* Active Alerts */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Aktif Alertler ({activeAlerts.length}):</Text>
        {activeAlerts.length > 0 ? (
          activeAlerts.map((alert) => (
            <View
              key={alert.id}
              style={[styles.alertItem, { borderLeftColor: getThreatLevelColor(alert.level) }]}
            >
              <View style={styles.alertHeader}>
                <Text style={styles.alertTitle}>{alert.title}</Text>
                <TouchableOpacity
                  style={styles.resolveButton}
                  onPress={() => resolveAlert(alert.id)}
                >
                  <Text style={styles.resolveButtonText}>Çözüldü</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.alertDescription}>{alert.description}</Text>
              <Text style={styles.eventTime}>
                {new Date(alert.createdAt).toLocaleString('tr-TR')} • {alert.level.toUpperCase()}
              </Text>
            </View>
          ))
        ) : (
          <Text style={styles.emptyState}>Aktif alert bulunmuyor</Text>
        )}
      </View>

      {/* Recent Events */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Son Güvenlik Olayları ({recentEvents.length}):</Text>
        {recentEvents.length > 0 ? (
          recentEvents.slice(0, 10).map((event) => (
            <View
              key={event.id}
              style={[styles.eventItem, { borderLeftColor: getThreatLevelColor(event.level) }]}
            >
              <View style={styles.eventHeader}>
                <Text style={styles.eventType}>{event.type.replace(/_/g, ' ').toUpperCase()}</Text>
                <Text style={styles.eventTime}>
                  {new Date(event.timestamp).toLocaleString('tr-TR')}
                </Text>
              </View>
              <Text style={styles.eventDetails}>
                {event.details.action} • {event.details.result} • Level: {event.level}
              </Text>
              {event.userId && (
                <Text style={styles.eventDetails}>User: {event.userId}</Text>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.emptyState}>Güvenlik olayı bulunmuyor</Text>
        )}
      </View>
    </ScrollView>
  );
};

export default SecurityTestScreen;
