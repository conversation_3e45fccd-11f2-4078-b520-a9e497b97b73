// Database Test Screen - SQLite veritabanı test ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import DatabaseManager from '../../database/DatabaseManager';
import TransactionRepository from '../../database/repositories/TransactionRepository';
import { Transaction, IncomeTransaction, ExpenseTransaction } from '../../types/transaction';

const DatabaseTestScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();

  const [dbStats, setDbStats] = useState<any>(null);
  const [healthCheck, setHealthCheck] = useState<any>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDatabaseInfo();
  }, []);

  const loadDatabaseInfo = async () => {
    try {
      setIsLoading(true);

      // Database stats
      const stats = await DatabaseManager.getStats();
      setDbStats(stats);

      // Health check
      const health = await DatabaseManager.healthCheck();
      setHealthCheck(health);

      // Sample transactions
      if (user) {
        const result = await TransactionRepository.getList(user.id, undefined, undefined, { page: 1, limit: 5 });
        setTransactions(result.transactions);
      }

    } catch (error) {
      console.error('Error loading database info:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDatabaseInfo();
    setRefreshing(false);
  };

  // Test functions
  const testCreateTransaction = async () => {
    try {
      if (!user) {
        Alert.alert('❌ Hata', 'Kullanıcı girişi gerekli');
        return;
      }

      const sampleTransaction: Omit<IncomeTransaction, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: user.id,
        type: 'income',
        amount: 5000,
        currency: 'TRY',
        category: 'salary',
        source: 'Test Company',
        date: new Date().toISOString(),
        description: 'Test maaş geliri',
        tags: [],
        status: 'completed',
        isDeleted: false,
        taxable: true,
        taxAmount: 750,
      };

      const transactionId = await TransactionRepository.create(sampleTransaction);

      Alert.alert('✅ Başarılı', `Transaction oluşturuldu: ${transactionId}`);
      await loadDatabaseInfo();
    } catch (error) {
      Alert.alert('❌ Hata', `Transaction oluşturulamadı: ${error}`);
    }
  };

  const testCreateExpense = async () => {
    try {
      if (!user) {
        Alert.alert('❌ Hata', 'Kullanıcı girişi gerekli');
        return;
      }

      const sampleExpense: Omit<ExpenseTransaction, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: user.id,
        type: 'expense',
        amount: 150,
        currency: 'TRY',
        category: 'food',
        subcategory: 'restaurant',
        merchant: 'Test Restaurant',
        date: new Date().toISOString(),
        description: 'Test yemek gideri',
        tags: [],
        paymentMethod: 'credit_card',
        status: 'completed',
        isDeleted: false,
        isBusinessExpense: false,
        isDeductible: false,
      };

      const transactionId = await TransactionRepository.create(sampleExpense);

      Alert.alert('✅ Başarılı', `Expense oluşturuldu: ${transactionId}`);
      await loadDatabaseInfo();
    } catch (error) {
      Alert.alert('❌ Hata', `Expense oluşturulamadı: ${error}`);
    }
  };

  const testTransactionList = async () => {
    try {
      if (!user) {
        Alert.alert('❌ Hata', 'Kullanıcı girişi gerekli');
        return;
      }

      const result = await TransactionRepository.getList(
        user.id,
        {
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString(),
          },
        },
        { field: 'date', direction: 'desc' },
        { page: 1, limit: 10 }
      );

      Alert.alert(
        '📊 Transaction List',
        `Toplam: ${result.summary.transactionCount}\nGelir: ₺${result.summary.totalIncome}\nGider: ₺${result.summary.totalExpense}\nNet: ₺${result.summary.netAmount}`
      );
    } catch (error) {
      Alert.alert('❌ Hata', `Transaction list alınamadı: ${error}`);
    }
  };

  const testBackup = async () => {
    try {
      if (!user) {
        Alert.alert('❌ Hata', 'Kullanıcı girişi gerekli');
        return;
      }

      const backupJson = await DatabaseManager.createBackup(user.id);
      const backupSize = new Blob([backupJson]).size;

      Alert.alert(
        '💾 Backup Oluşturuldu',
        `Backup boyutu: ${(backupSize / 1024).toFixed(2)} KB\n\nBackup JSON formatında oluşturuldu.`
      );
    } catch (error) {
      Alert.alert('❌ Hata', `Backup oluşturulamadı: ${error}`);
    }
  };

  const testClearDatabase = async () => {
    Alert.alert(
      '🗑️ Database Temizle',
      'Tüm veriler silinecek. Emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await DatabaseManager.clearDatabase();
              Alert.alert('✅ Başarılı', 'Database temizlendi');
              await loadDatabaseInfo();
            } catch (error) {
              Alert.alert('❌ Hata', `Database temizlenemedi: ${error}`);
            }
          }
        }
      ]
    );
  };

  const getHealthStatusColor = (isHealthy: boolean) => {
    return isHealthy ? '#4CAF50' : '#F44336';
  };

  const getHealthStatusIcon = (isHealthy: boolean) => {
    return isHealthy ? 'checkmark-circle' : 'alert-circle';
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    statCard: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    statValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    healthCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    healthHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    healthStatus: {
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    healthIssues: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 4,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    buttonTextSecondary: {
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    transactionItem: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      borderLeftWidth: 4,
    },
    transactionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    transactionAmount: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    transactionCategory: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    transactionDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 4,
    },
    emptyState: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontStyle: 'italic',
      padding: 20,
    },
  });

  if (isLoading && !dbStats) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Ionicons name="server-outline" size={64} color={theme.colors.primary} />
        <Text style={[styles.title, { marginTop: 16 }]}>Database Bilgileri Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <Text style={styles.title}>🗄️ Database Test</Text>

      {/* Database Stats */}
      {dbStats && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Database İstatistikleri:</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{dbStats.version}</Text>
              <Text style={styles.statLabel}>Database Version</Text>
            </View>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>{(dbStats.totalSize / 1024).toFixed(1)}KB</Text>
              <Text style={styles.statLabel}>Database Size</Text>
            </View>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>{dbStats.tables.transactions || 0}</Text>
              <Text style={styles.statLabel}>Transactions</Text>
            </View>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>{dbStats.tables.categories || 0}</Text>
              <Text style={styles.statLabel}>Categories</Text>
            </View>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>{dbStats.tables.budgets || 0}</Text>
              <Text style={styles.statLabel}>Budgets</Text>
            </View>

            <View style={styles.statCard}>
              <Text style={styles.statValue}>{dbStats.tables.goals || 0}</Text>
              <Text style={styles.statLabel}>Goals</Text>
            </View>
          </View>
        </View>
      )}

      {/* Health Check */}
      {healthCheck && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Database Sağlığı:</Text>
          <View style={styles.healthCard}>
            <View style={styles.healthHeader}>
              <Ionicons
                name={getHealthStatusIcon(healthCheck.isHealthy)}
                size={24}
                color={getHealthStatusColor(healthCheck.isHealthy)}
              />
              <Text style={[styles.healthStatus, { color: getHealthStatusColor(healthCheck.isHealthy) }]}>
                {healthCheck.isHealthy ? 'Sağlıklı' : 'Sorunlu'}
              </Text>
            </View>
            {healthCheck.issues && healthCheck.issues.length > 0 && (
              <Text style={styles.healthIssues}>
                Sorunlar: {healthCheck.issues.join(', ')}
              </Text>
            )}
          </View>
        </View>
      )}

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Database Testleri:</Text>

        <TouchableOpacity style={styles.button} onPress={testCreateTransaction}>
          <Ionicons name="add-circle-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Test Income Oluştur</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testCreateExpense}>
          <Ionicons name="remove-circle-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Test Expense Oluştur</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={testTransactionList}>
          <Ionicons name="list-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Transaction List Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={testBackup}>
          <Ionicons name="cloud-download-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Backup Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={loadDatabaseInfo}>
          <Ionicons name="refresh-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Bilgileri Yenile</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={testClearDatabase}>
          <Ionicons name="trash-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Database Temizle</Text>
        </TouchableOpacity>
      </View>

      {/* Recent Transactions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Son Transactions ({transactions.length}):</Text>
        {transactions.length > 0 ? (
          transactions.map((transaction) => (
            <View
              key={transaction.id}
              style={[
                styles.transactionItem,
                { borderLeftColor: transaction.type === 'income' ? '#4CAF50' : '#F44336' }
              ]}
            >
              <View style={styles.transactionHeader}>
                <Text style={[
                  styles.transactionAmount,
                  { color: transaction.type === 'income' ? '#4CAF50' : '#F44336' }
                ]}>
                  {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount}
                </Text>
                <Text style={styles.transactionCategory}>
                  {transaction.category}
                </Text>
              </View>
              {transaction.description && (
                <Text style={styles.transactionCategory}>
                  {transaction.description}
                </Text>
              )}
              <Text style={styles.transactionDate}>
                {new Date(transaction.date).toLocaleDateString('tr-TR')}
              </Text>
            </View>
          ))
        ) : (
          <Text style={styles.emptyState}>Henüz transaction bulunmuyor</Text>
        )}
      </View>
    </ScrollView>
  );
};

export default DatabaseTestScreen;
