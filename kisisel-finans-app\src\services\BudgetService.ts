// RFC-003 Bütçe Planlama - Service Layer

import databaseManager from '../database/DatabaseManager';
import { 
  Budget, 
  BudgetCategory, 
  CreateBudgetInput, 
  CreateBudgetCategoryInput,
  UpdateBudgetInput,
  UpdateBudgetCategoryInput,
  BudgetSummary,
  BudgetCategoryWithDetails,
  BudgetAnalysis,
  BudgetRecommendation,
  BudgetTemplate,
  BudgetFilters,
  BudgetSortOptions
} from '../types/budget';

class BudgetService {
  private static instance: BudgetService;

  static getInstance(): BudgetService {
    if (!BudgetService.instance) {
      BudgetService.instance = new BudgetService();
    }
    return BudgetService.instance;
  }

  /**
   * Bütçe oluştur
   */
  async createBudget(userId: string, input: CreateBudgetInput): Promise<string> {
    try {
      console.log('🔥 BudgetService.createBudget called with:', { userId, input });

      const db = databaseManager.getDatabase();
      console.log('🔥 Database instance obtained:', !!db);

      const budgetId = `budget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();

      console.log('🔥 Generated budget ID:', budgetId);
      console.log('🔥 Timestamp:', now);

      // Ana bütçeyi oluştur
      console.log('🔥 Inserting main budget...');
      await db.runAsync(
        `INSERT INTO budgets (
          id, user_id, name, period, start_date, end_date,
          total_income_target, total_expense_limit, savings_target,
          currency, notes, template_name, copied_from_budget_id,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          budgetId, userId, input.name, input.period, input.startDate, input.endDate,
          input.totalIncomeTarget || 0, input.totalExpenseLimit || 0, input.savingsTarget || 0,
          input.currency || 'TRY', input.notes, input.templateName, input.copiedFromBudgetId,
          1, now, now
        ]
      );
      console.log('🔥 Main budget inserted successfully');

      // Kategori bütçelerini oluştur
      console.log(`🔥 Creating ${input.categories.length} budget categories...`);
      for (const category of input.categories) {
        const budgetCategoryId = `budget_cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log(`🔥 Creating category: ${category.categoryId} with amount: ${category.plannedAmount}`);

        // Önce kategori var mı kontrol et (code ile), yoksa oluştur
        let existingCategory = await db.getFirstAsync(
          'SELECT id FROM categories WHERE code = ? AND type = ?',
          [category.categoryId, 'expense']
        );

        if (!existingCategory) {
          console.log(`🔥 Category ${category.categoryId} not found, creating...`);
          const newCategoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          await db.runAsync(
            `INSERT INTO categories (id, user_id, name, type, code, icon, color, sort_order, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              newCategoryId,
              userId,
              this.getCategoryName(category.categoryId),
              'expense',
              category.categoryId, // Code olarak categoryId kullan
              this.getCategoryIcon(category.categoryId),
              this.getCategoryColor(category.categoryId),
              0,
              now,
              now,
            ]
          );
          existingCategory = { id: newCategoryId };
          console.log(`🔥 Created new category with ID: ${newCategoryId}`);
        }

        await db.runAsync(
          `INSERT INTO budget_categories (
            id, budget_id, category_id, planned_amount,
            warning_threshold, critical_threshold, limit_threshold,
            warning_enabled, critical_enabled, limit_enabled, daily_digest_enabled,
            is_active, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            budgetCategoryId, budgetId, existingCategory.id, category.plannedAmount,
            category.warningThreshold || 50, category.criticalThreshold || 80, category.limitThreshold || 100,
            category.warningEnabled !== false ? 1 : 0,
            category.criticalEnabled !== false ? 1 : 0,
            category.limitEnabled !== false ? 1 : 0,
            category.dailyDigestEnabled ? 1 : 0,
            1, now, now
          ]
        );
        console.log(`🔥 Budget category ${category.categoryId} created with ID: ${budgetCategoryId}`);
      }
      console.log('🔥 All budget categories created successfully');

      // Oluşturulan bütçeyi doğrula
      const createdBudget = await db.getFirstAsync(
        'SELECT * FROM budgets WHERE id = ?',
        [budgetId]
      );
      console.log('🔥 Created budget verification:', createdBudget);

      const createdCategories = await db.getAllAsync(
        'SELECT * FROM budget_categories WHERE budget_id = ?',
        [budgetId]
      );
      console.log(`🔥 Created categories verification (${createdCategories.length}):`, createdCategories);

      console.log(`✅ Budget created with ID: ${budgetId}`);
      return budgetId;
    } catch (error) {
      console.error('❌ Error creating budget:', error);
      throw error;
    }
  }

  /**
   * Kategori code'undan kategori adını al - RFC-003 Code Bazlı Sistem
   */
  public getCategoryName(categoryCode: string): string {
    const categoryMap: Record<string, string> = {
      'food': 'Yiyecek & İçecek',
      'transportation': 'Ulaşım',
      'shopping': 'Alışveriş',
      'entertainment': 'Eğlence',
      'health': 'Sağlık',
      'education': 'Eğitim',
      'bills': 'Faturalar',
      'rent': 'Kira',
      'insurance': 'Sigorta',
      'personal_care': 'Kişisel Bakım',
      'gifts': 'Hediyeler',
      'travel': 'Seyahat',
      'donation': 'Bağış',
      'tax': 'Vergiler',
      'housing': 'Konut',
      'other': 'Diğer',
      // Veritabanında bulunan ek kategoriler
      'personal': 'Personal',
      'transport': 'Transport',
    };
    return categoryMap[categoryCode] || categoryCode;
  }

  /**
   * Kategori code'undan icon adını al - RFC-003 Code Bazlı Sistem
   */
  public getCategoryIcon(categoryCode: string): string {
    const iconMap: Record<string, string> = {
      'food': 'restaurant-outline',
      'transportation': 'car-outline',
      'shopping': 'bag-outline',
      'entertainment': 'game-controller-outline',
      'health': 'medical-outline',
      'education': 'school-outline',
      'bills': 'receipt-outline',
      'rent': 'home-outline',
      'insurance': 'shield-outline',
      'personal_care': 'person-outline',
      'gifts': 'gift-outline',
      'travel': 'airplane-outline',
      'donation': 'heart-outline',
      'tax': 'document-text-outline',
      'housing': 'business-outline',
      'other': 'ellipsis-horizontal-outline',
      // Veritabanında bulunan ek kategoriler
      'personal': 'person-outline',
      'transport': 'car-outline',
    };
    return iconMap[categoryCode] || 'ellipsis-horizontal-outline';
  }

  /**
   * Kategori code'undan renk kodunu al - RFC-003 Code Bazlı Sistem
   */
  public getCategoryColor(categoryCode: string): string {
    const colorMap: Record<string, string> = {
      'food': '#FF6B6B',
      'transportation': '#4ECDC4',
      'shopping': '#45B7D1',
      'entertainment': '#96CEB4',
      'health': '#FFEAA7',
      'education': '#DDA0DD',
      'bills': '#FFB347',
      'rent': '#F7DC6F',
      'insurance': '#BB8FCE',
      'personal_care': '#85C1E9',
      'gifts': '#F8C471',
      'travel': '#00BCD4',
      'donation': '#4CAF50',
      'tax': '#9E9E9E',
      'housing': '#795548',
      'other': '#AED6F1',
      // Veritabanında bulunan ek kategoriler
      'personal': '#85C1E9',
      'transport': '#4ECDC4',
    };
    return colorMap[categoryCode] || '#AED6F1';
  }

  /**
   * Kullanıcının bütçelerini listele
   */
  async getUserBudgets(userId: string): Promise<any[]> {
    try {
      const db = databaseManager.getDatabase();

      const budgets = await db.getAllAsync(`
        SELECT
          b.*,
          COUNT(CASE WHEN bc.is_active = 1 THEN bc.id END) as category_count,
          COALESCE(b.total_expense_limit, 0) as total_planned,
          COALESCE(SUM(CASE WHEN bc.is_active = 1 THEN bc.spent_amount ELSE 0 END), 0) as total_spent
        FROM budgets b
        LEFT JOIN budget_categories bc ON b.id = bc.budget_id
        WHERE b.user_id = ? AND b.is_active = 1
        GROUP BY b.id
        ORDER BY b.created_at DESC
      `, [userId]);

      console.log(`📊 Raw budget query result:`, budgets);

      // Debug: Her bütçe için detaylı log
      for (const budget of budgets) {
        console.log(`📊 Budget ${budget.name}:`, {
          id: budget.id,
          category_count: budget.category_count,
          total_planned: budget.total_planned,
          total_spent: budget.total_spent,
          is_active: budget.is_active
        });
      }

      console.log(`📊 Found ${budgets.length} budgets for user ${userId}`);
      return budgets;
    } catch (error) {
      console.error('❌ Error fetching user budgets:', error);
      throw error;
    }
  }

  /**
   * Bütçe detayını kategorilerle birlikte getir
   */
  async getBudgetWithCategories(budgetId: string): Promise<any> {
    try {
      const db = databaseManager.getDatabase();

      // Ana bütçe bilgisi
      const budget = await db.getFirstAsync(
        'SELECT * FROM budgets WHERE id = ? AND is_active = 1',
        [budgetId]
      );

      if (!budget) {
        throw new Error('Budget not found');
      }

      // Kategori bütçeleri
      const categories = await db.getAllAsync(`
        SELECT
          bc.*,
          c.name as category_name,
          c.icon as category_icon,
          c.color as category_color
        FROM budget_categories bc
        LEFT JOIN categories c ON bc.category_id = c.id
        WHERE bc.budget_id = ? AND bc.is_active = 1
        ORDER BY bc.created_at
      `, [budgetId]);

      return {
        ...budget,
        categories
      };
    } catch (error) {
      console.error('❌ Error fetching budget details:', error);
      throw error;
    }
  }

  /**
   * Bütçeyi sil (soft delete)
   */
  async deleteBudget(budgetId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      const now = new Date().toISOString();

      // Ana bütçeyi deaktive et
      await db.runAsync(
        'UPDATE budgets SET is_active = 0, updated_at = ? WHERE id = ?',
        [now, budgetId]
      );

      // Kategori bütçelerini deaktive et
      await db.runAsync(
        'UPDATE budget_categories SET is_active = 0, updated_at = ? WHERE budget_id = ?',
        [now, budgetId]
      );

      console.log(`🗑️ Budget ${budgetId} deleted successfully`);
    } catch (error) {
      console.error('❌ Error deleting budget:', error);
      throw error;
    }
  }

  /**
   * Kategori bütçesi oluştur (standalone kullanım için)
   */
  private async createBudgetCategory(
    budgetId: string,
    input: CreateBudgetCategoryInput
  ): Promise<string> {
    try {
      const db = databaseManager.getDatabase();
      const categoryId = `budget_cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();

      await db.runAsync(
        `INSERT INTO budget_categories (
          id, budget_id, category_id, planned_amount,
          warning_threshold, critical_threshold, limit_threshold,
          warning_enabled, critical_enabled, limit_enabled, daily_digest_enabled,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          categoryId, budgetId, input.categoryId, input.plannedAmount,
          input.warningThreshold || 50, input.criticalThreshold || 80, input.limitThreshold || 100,
          input.warningEnabled !== false ? 1 : 0,
          input.criticalEnabled !== false ? 1 : 0,
          input.limitEnabled !== false ? 1 : 0,
          input.dailyDigestEnabled ? 1 : 0,
          1, now, now
        ]
      );

      return categoryId;
    } catch (error) {
      console.error('❌ Error creating budget category:', error);
      throw error;
    }
  }



  /**
   * Bütçe detayını getir
   */
  async getBudgetById(budgetId: string): Promise<Budget | null> {
    try {
      const db = databaseManager.getDatabase();
      
      const row = await db.getFirstAsync(
        'SELECT * FROM budgets WHERE id = ? AND is_active = 1',
        [budgetId]
      );

      return row ? this.mapRowToBudget(row) : null;
    } catch (error) {
      console.error('❌ Error fetching budget:', error);
      throw error;
    }
  }

  /**
   * Bütçe kategorilerini getir
   */
  async getBudgetCategories(budgetId: string): Promise<BudgetCategory[]> {
    try {
      const db = databaseManager.getDatabase();
      
      const rows = await db.getAllAsync(
        'SELECT * FROM budget_categories WHERE budget_id = ? AND is_active = 1 ORDER BY created_at',
        [budgetId]
      );

      return rows.map(row => this.mapRowToBudgetCategory(row));
    } catch (error) {
      console.error('❌ Error fetching budget categories:', error);
      throw error;
    }
  }

  /**
   * Bütçe özetini getir
   */
  async getBudgetSummary(budgetId: string): Promise<BudgetSummary | null> {
    try {
      const budget = await this.getBudgetById(budgetId);
      if (!budget) return null;

      const categories = await this.getBudgetCategoriesWithDetails(budgetId);

      // DÜZELTME: Toplam gider limitini kullan, kategori toplamını değil
      const totalPlanned = budget.totalExpenseLimit || 0; // Ana bütçeden al
      const totalSpent = categories.reduce((sum, cat) => sum + cat.spentAmount, 0);
      const totalRemaining = totalPlanned - totalSpent;
      const overallProgress = totalPlanned > 0 ? (totalSpent / totalPlanned) * 100 : 0;

      // Kalan gün hesaplama
      const now = new Date();
      const endDate = new Date(budget.endDate);
      const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

      // Günlük ortalama harcama
      const startDate = new Date(budget.startDate);
      const totalDays = Math.max(1, Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
      const averageDailySpending = totalSpent / totalDays;

      // Tahmini bitiş tutarı
      const projectedEndAmount = daysRemaining > 0 ? totalSpent + (averageDailySpending * daysRemaining) : totalSpent;

      // Durum belirleme
      let status: 'active' | 'completed' | 'paused' | 'draft' = 'active';
      if (now > endDate) status = 'completed';
      else if (overallProgress >= 100) status = 'completed';

      return {
        budget,
        categories,
        totalPlanned,
        totalSpent,
        totalRemaining,
        overallProgress,
        status,
        daysRemaining,
        averageDailySpending,
        projectedEndAmount
      };
    } catch (error) {
      console.error('❌ Error getting budget summary:', error);
      throw error;
    }
  }

  /**
   * Detaylı kategori bütçelerini getir
   */
  async getBudgetCategoriesWithDetails(budgetId: string): Promise<BudgetCategoryWithDetails[]> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`🔍 DEBUG: Loading budget categories for budget ID: ${budgetId}`);

      // Önce budget_categories tablosunu kontrol et
      const budgetCategories = await db.getAllAsync(`
        SELECT * FROM budget_categories
        WHERE budget_id = ? AND is_active = 1
      `, [budgetId]);

      console.log(`🔍 DEBUG: Found ${budgetCategories.length} budget categories`);
      budgetCategories.forEach(bc => {
        console.log(`  - Budget Category: ${bc.id}, Category ID: ${bc.category_id}, Amount: ${bc.planned_amount}`);
      });

      // Şimdi JOIN ile kategori detaylarını al
      const rows = await db.getAllAsync(`
        SELECT
          bc.*,
          c.name as category_name,
          c.icon as category_icon,
          c.color as category_color
        FROM budget_categories bc
        LEFT JOIN categories c ON bc.category_id = c.id
        WHERE bc.budget_id = ? AND bc.is_active = 1
        ORDER BY bc.created_at
      `, [budgetId]);

      console.log(`🔍 DEBUG: JOIN result: ${rows.length} rows`);
      rows.forEach(row => {
        console.log(`  - Row: ${row.id}, Category: ${row.category_name || 'NULL'}, Icon: ${row.category_icon || 'NULL'}`);
      });

      return rows.map(row => {
        const progress = row.planned_amount > 0 ? (row.spent_amount / row.planned_amount) * 100 : 0;

        let status: 'safe' | 'warning' | 'critical' | 'exceeded' = 'safe';
        if (progress >= row.limit_threshold) status = 'exceeded';
        else if (progress >= row.critical_threshold) status = 'critical';
        else if (progress >= row.warning_threshold) status = 'warning';

        // Kategori bilgileri bulunamazsa fallback kullan
        let categoryName = row.category_name;
        let categoryIcon = row.category_icon;
        let categoryColor = row.category_color;

        if (!categoryName) {
          console.log(`⚠️ DEBUG: Category not found for ID: ${row.category_id}, using fallback`);
          categoryName = this.getCategoryName(row.category_id);
          categoryIcon = this.getCategoryIcon(row.category_id);
          categoryColor = this.getCategoryColor(row.category_id);
        }

        return {
          ...this.mapRowToBudgetCategory(row),
          categoryName,
          categoryIcon,
          categoryColor,
          progress,
          status,
          dailyAverage: 0, // TODO: Calculate from transactions
          weeklyAverage: 0, // TODO: Calculate from transactions
          monthlyAverage: 0, // TODO: Calculate from transactions
        };
      });
    } catch (error) {
      console.error('❌ Error fetching budget categories with details:', error);
      throw error;
    }
  }

  /**
   * Bütçe güncelle
   */
  async updateBudget(budgetId: string, input: UpdateBudgetInput): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      const now = new Date().toISOString();

      const fields: string[] = [];
      const values: any[] = [];

      if (input.name !== undefined) {
        fields.push('name = ?');
        values.push(input.name);
      }
      if (input.totalIncomeTarget !== undefined) {
        fields.push('total_income_target = ?');
        values.push(input.totalIncomeTarget);
      }
      if (input.totalExpenseLimit !== undefined) {
        fields.push('total_expense_limit = ?');
        values.push(input.totalExpenseLimit);
      }
      if (input.savingsTarget !== undefined) {
        fields.push('savings_target = ?');
        values.push(input.savingsTarget);
      }
      if (input.notes !== undefined) {
        fields.push('notes = ?');
        values.push(input.notes);
      }
      if (input.isActive !== undefined) {
        fields.push('is_active = ?');
        values.push(input.isActive ? 1 : 0);
      }

      if (fields.length === 0) return;

      fields.push('updated_at = ?');
      values.push(now);
      values.push(budgetId);

      await db.runAsync(
        `UPDATE budgets SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      console.log(`✅ Budget updated: ${budgetId}`);
    } catch (error) {
      console.error('❌ Error updating budget:', error);
      throw error;
    }
  }

  /**
   * Row'u Budget objesine dönüştür
   */
  private mapRowToBudget(row: any): Budget {
    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      period: row.period,
      startDate: row.start_date,
      endDate: row.end_date,
      totalIncomeTarget: row.total_income_target || 0,
      totalExpenseLimit: row.total_expense_limit || 0,
      savingsTarget: row.savings_target || 0,
      currency: row.currency,
      notes: row.notes,
      templateName: row.template_name,
      copiedFromBudgetId: row.copied_from_budget_id,
      isActive: row.is_active === 1,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Row'u BudgetCategory objesine dönüştür
   */
  private mapRowToBudgetCategory(row: any): BudgetCategory {
    return {
      id: row.id,
      budgetId: row.budget_id,
      categoryId: row.category_id,
      plannedAmount: row.planned_amount,
      spentAmount: row.spent_amount || 0,
      remainingAmount: row.remaining_amount || (row.planned_amount - (row.spent_amount || 0)),
      warningThreshold: row.warning_threshold,
      criticalThreshold: row.critical_threshold,
      limitThreshold: row.limit_threshold,
      warningEnabled: row.warning_enabled === 1,
      criticalEnabled: row.critical_enabled === 1,
      limitEnabled: row.limit_enabled === 1,
      dailyDigestEnabled: row.daily_digest_enabled === 1,
      isActive: row.is_active === 1,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Bütçeye kategori ekle
   */
  async addCategoryToBudget(budgetId: string, input: CreateBudgetCategoryInput): Promise<string> {
    try {
      const db = databaseManager.getDatabase();
      const now = new Date().toISOString();
      const categoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      await db.runAsync(`
        INSERT INTO budget_categories (
          id, budget_id, category_id, planned_amount, spent_amount,
          warning_threshold, critical_threshold, limit_threshold,
          warning_enabled, critical_enabled, limit_enabled, daily_digest_enabled,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 0, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
      `, [
        categoryId,
        budgetId,
        input.categoryId,
        input.plannedAmount || 100, // Default 100 TL if not specified
        input.warningThreshold || 75,
        input.criticalThreshold || 90,
        input.limitThreshold || 100,
        input.warningEnabled !== false ? 1 : 0,
        input.criticalEnabled !== false ? 1 : 0,
        input.limitEnabled !== false ? 1 : 0,
        input.dailyDigestEnabled !== false ? 1 : 0,
        now,
        now
      ]);

      console.log(`✅ Category added to budget: ${categoryId}`);
      return categoryId;
    } catch (error) {
      console.error('❌ Error adding category to budget:', error);
      throw error;
    }
  }

  /**
   * Bütçeden kategori sil
   */
  async removeCategoryFromBudget(budgetCategoryId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log('🗑️ Removing category from budget:', budgetCategoryId);

      // Soft delete - is_active = 0 yap
      await db.runAsync(
        'UPDATE budget_categories SET is_active = 0, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), budgetCategoryId]
      );

      console.log('✅ Category removed from budget successfully');
    } catch (error) {
      console.error('❌ Error removing category from budget:', error);
      throw error;
    }
  }

  /**
   * Kategori bütçesi güncelle
   */
  async updateBudgetCategory(categoryId: string, input: UpdateBudgetCategoryInput): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      const now = new Date().toISOString();

      const fields: string[] = [];
      const values: any[] = [];

      if (input.plannedAmount !== undefined) {
        fields.push('planned_amount = ?');
        values.push(input.plannedAmount);
      }
      if (input.warningThreshold !== undefined) {
        fields.push('warning_threshold = ?');
        values.push(input.warningThreshold);
      }
      if (input.criticalThreshold !== undefined) {
        fields.push('critical_threshold = ?');
        values.push(input.criticalThreshold);
      }
      if (input.limitThreshold !== undefined) {
        fields.push('limit_threshold = ?');
        values.push(input.limitThreshold);
      }
      if (input.warningEnabled !== undefined) {
        fields.push('warning_enabled = ?');
        values.push(input.warningEnabled ? 1 : 0);
      }
      if (input.criticalEnabled !== undefined) {
        fields.push('critical_enabled = ?');
        values.push(input.criticalEnabled ? 1 : 0);
      }
      if (input.limitEnabled !== undefined) {
        fields.push('limit_enabled = ?');
        values.push(input.limitEnabled ? 1 : 0);
      }
      if (input.dailyDigestEnabled !== undefined) {
        fields.push('daily_digest_enabled = ?');
        values.push(input.dailyDigestEnabled ? 1 : 0);
      }
      if (input.isActive !== undefined) {
        fields.push('is_active = ?');
        values.push(input.isActive ? 1 : 0);
      }

      if (fields.length === 0) return;

      fields.push('updated_at = ?');
      values.push(now);
      values.push(categoryId);

      await db.runAsync(
        `UPDATE budget_categories SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      console.log(`✅ Budget category updated: ${categoryId}`);
    } catch (error) {
      console.error('❌ Error updating budget category:', error);
      throw error;
    }
  }



  /**
   * Önceki dönemden bütçe kopyala
   */
  async copyFromPreviousBudget(
    userId: string,
    sourceBudgetId: string,
    newBudgetData: Partial<CreateBudgetInput>
  ): Promise<string> {
    try {
      const sourceBudget = await this.getBudgetById(sourceBudgetId);
      if (!sourceBudget) {
        throw new Error('Source budget not found');
      }

      const sourceCategories = await this.getBudgetCategories(sourceBudgetId);

      const newBudgetInput: CreateBudgetInput = {
        name: newBudgetData.name || `${sourceBudget.name} (Kopya)`,
        period: newBudgetData.period || sourceBudget.period,
        startDate: newBudgetData.startDate || sourceBudget.startDate,
        endDate: newBudgetData.endDate || sourceBudget.endDate,
        totalIncomeTarget: newBudgetData.totalIncomeTarget || sourceBudget.totalIncomeTarget,
        totalExpenseLimit: newBudgetData.totalExpenseLimit || sourceBudget.totalExpenseLimit,
        savingsTarget: newBudgetData.savingsTarget || sourceBudget.savingsTarget,
        currency: newBudgetData.currency || sourceBudget.currency,
        notes: newBudgetData.notes || sourceBudget.notes,
        copiedFromBudgetId: sourceBudgetId,
        categories: sourceCategories.map(cat => ({
          categoryId: cat.categoryId,
          plannedAmount: cat.plannedAmount,
          warningThreshold: cat.warningThreshold,
          criticalThreshold: cat.criticalThreshold,
          limitThreshold: cat.limitThreshold,
          warningEnabled: cat.warningEnabled,
          criticalEnabled: cat.criticalEnabled,
          limitEnabled: cat.limitEnabled,
          dailyDigestEnabled: cat.dailyDigestEnabled,
        }))
      };

      return await this.createBudget(userId, newBudgetInput);
    } catch (error) {
      console.error('❌ Error copying budget:', error);
      throw error;
    }
  }

  /**
   * Geçmiş harcama verilerine dayalı otomatik bütçe önerisi
   */
  async generateBudgetSuggestion(
    userId: string,
    period: 'monthly' | 'quarterly' | 'annually',
    lookbackMonths: number = 6
  ): Promise<CreateBudgetInput> {
    try {
      const db = databaseManager.getDatabase();

      // Geçmiş harcama verilerini analiz et
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - lookbackMonths);

      const expenseData = await db.getAllAsync(`
        SELECT
          category,
          AVG(amount) as avg_amount,
          COUNT(*) as transaction_count,
          SUM(amount) as total_amount
        FROM transactions
        WHERE user_id = ?
          AND type = 'expense'
          AND is_deleted = 0
          AND date BETWEEN ? AND ?
        GROUP BY category
        ORDER BY total_amount DESC
      `, [userId, startDate.toISOString(), endDate.toISOString()]);

      const incomeData = await db.getFirstAsync(`
        SELECT
          AVG(amount) as avg_income,
          SUM(amount) as total_income
        FROM transactions
        WHERE user_id = ?
          AND type = 'income'
          AND is_deleted = 0
          AND date BETWEEN ? AND ?
      `, [userId, startDate.toISOString(), endDate.toISOString()]);

      // Dönem çarpanı
      const periodMultiplier = period === 'monthly' ? 1 : period === 'quarterly' ? 3 : 12;

      // Önerilen gelir hedefi
      const avgMonthlyIncome = (incomeData?.avg_income || 0) * periodMultiplier;

      // Kategori önerileri oluştur
      const categories: CreateBudgetCategoryInput[] = expenseData.map(row => ({
        categoryId: row.category,
        plannedAmount: Math.round((row.avg_amount || 0) * periodMultiplier * 1.1), // %10 buffer
        warningThreshold: 75,
        criticalThreshold: 90,
        limitThreshold: 100,
      }));

      const totalExpenseLimit = categories.reduce((sum, cat) => sum + cat.plannedAmount, 0);
      const savingsTarget = Math.max(0, avgMonthlyIncome - totalExpenseLimit);

      return {
        name: `${period === 'monthly' ? 'Aylık' : period === 'quarterly' ? '3 Aylık' : 'Yıllık'} Bütçe Önerisi`,
        period,
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + (periodMultiplier * 30 * 24 * 60 * 60 * 1000)).toISOString(),
        totalIncomeTarget: avgMonthlyIncome,
        totalExpenseLimit,
        savingsTarget,
        currency: 'TRY',
        notes: `Geçmiş ${lookbackMonths} aylık harcama verilerine dayalı otomatik öneri`,
        categories
      };
    } catch (error) {
      console.error('❌ Error generating budget suggestion:', error);
      throw error;
    }
  }

  // ========================================
  // HİBRİT BÜTÇE-İŞLEM SİSTEMİ
  // ========================================

  /**
   * İşlem için uygun bütçe bul (otomatik eşleştirme)
   * Kategori ID + tarih aralığı ile eşleştirme yapar
   */
  async findMatchingBudget(
    userId: string,
    categoryId: string,
    transactionDate: string
  ): Promise<string | null> {
    try {
      const db = databaseManager.getDatabase();

      console.log('🔍 Finding matching budget for:', { userId, categoryId, transactionDate });

      const result = await db.getFirstAsync(`
        SELECT b.id AS budget_id
        FROM budgets b
        JOIN budget_categories bc ON b.id = bc.budget_id
        WHERE b.user_id = ?
          AND bc.category_id = ?
          AND b.is_active = 1
          AND bc.is_active = 1
          AND date(?) BETWEEN date(b.start_date) AND date(b.end_date)
        ORDER BY b.created_at DESC
        LIMIT 1
      `, [userId, categoryId, transactionDate]);

      const budgetId = result?.budget_id || null;
      console.log('🎯 Matching budget found:', budgetId);

      return budgetId;
    } catch (error) {
      console.error('❌ Error finding matching budget:', error);
      return null;
    }
  }

  /**
   * İşlemi bütçeye manuel olarak bağla
   */
  async linkTransactionToBudget(
    transactionId: string,
    budgetId: string
  ): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log('🔗 Linking transaction to budget:', { transactionId, budgetId });

      await db.runAsync(
        'UPDATE transactions SET budget_id = ?, updated_at = ? WHERE id = ?',
        [budgetId, new Date().toISOString(), transactionId]
      );

      console.log('✅ Transaction linked to budget successfully');
    } catch (error) {
      console.error('❌ Error linking transaction to budget:', error);
      throw error;
    }
  }

  /**
   * İşlemin bütçe bağlantısını kaldır
   */
  async unlinkTransactionFromBudget(transactionId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log('🔓 Unlinking transaction from budget:', transactionId);

      await db.runAsync(
        'UPDATE transactions SET budget_id = NULL, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), transactionId]
      );

      console.log('✅ Transaction unlinked from budget successfully');
    } catch (error) {
      console.error('❌ Error unlinking transaction from budget:', error);
      throw error;
    }
  }

  /**
   * Kullanıcının aktif bütçelerini listele (işlem bağlama için)
   */
  async getActiveBudgetsForLinking(
    userId: string,
    transactionDate: string
  ): Promise<Array<{id: string, name: string, period: string}>> {
    try {
      const db = databaseManager.getDatabase();

      const budgets = await db.getAllAsync(`
        SELECT id, name, period, start_date, end_date
        FROM budgets
        WHERE user_id = ?
          AND is_active = 1
          AND date(?) BETWEEN date(start_date) AND date(end_date)
        ORDER BY created_at DESC
      `, [userId, transactionDate]);

      return budgets.map(budget => ({
        id: budget.id,
        name: budget.name,
        period: budget.period
      }));
    } catch (error) {
      console.error('❌ Error getting active budgets for linking:', error);
      return [];
    }
  }

  /**
   * Bütçesiz işlemleri listele
   */
  async getUnlinkedTransactions(userId: string): Promise<any[]> {
    try {
      const db = databaseManager.getDatabase();

      const transactions = await db.getAllAsync(`
        SELECT id, type, amount, category, description, date, created_at
        FROM transactions
        WHERE user_id = ?
          AND budget_id IS NULL
          AND is_deleted = 0
        ORDER BY date DESC
        LIMIT 50
      `, [userId]);

      console.log(`📊 Found ${transactions.length} unlinked transactions`);
      return transactions;
    } catch (error) {
      console.error('❌ Error getting unlinked transactions:', error);
      return [];
    }
  }

  /**
   * Aktif bütçelerin durumunu kontrol et ve uyarı gönder
   */
  async checkBudgetAlerts(userId: string): Promise<BudgetAlert[]> {
    try {
      const activeBudgets = await this.getUserBudgets(userId, {
        startDate: new Date().toISOString().split('T')[0]
      });

      const alerts: BudgetAlert[] = [];

      for (const budget of activeBudgets) {
        const categories = await this.getBudgetCategoriesWithDetails(budget.id);

        for (const category of categories) {
          const progress = category.progress;
          const spentAmount = category.spentAmount || 0;
          const plannedAmount = category.plannedAmount || 0;

          // Uyarı kontrolü (%75-89)
          if (category.warningEnabled && progress >= category.warningThreshold && progress < category.criticalThreshold) {
            const alert: BudgetAlert = {
              id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              budgetId: budget.id,
              budgetCategoryId: category.id,
              type: 'warning',
              title: '⚠️ Bütçe Uyarısı',
              message: `${category.categoryName} kategorisinde bütçenizin %${progress.toFixed(0)}'ini kullandınız (${spentAmount.toLocaleString('tr-TR')}₺/${plannedAmount.toLocaleString('tr-TR')}₺)`,
              percentage: progress,
              amount: spentAmount,
              isRead: false,
              createdAt: new Date().toISOString(),
            };
            alerts.push(alert);

            // Push notification gönder
            const { default: NotificationService } = await import('./NotificationService');
            await NotificationService.sendBudgetAlert(category.categoryName, progress);

            console.log(`⚠️ Warning Alert: ${category.categoryName} at ${progress.toFixed(1)}%`);
          }

          // Kritik uyarı kontrolü (%90-99)
          if (category.criticalEnabled && progress >= category.criticalThreshold && progress < category.limitThreshold) {
            const alert: BudgetAlert = {
              id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              budgetId: budget.id,
              budgetCategoryId: category.id,
              type: 'critical',
              title: '🚨 Kritik Bütçe Uyarısı',
              message: `${category.categoryName} kategorisinde bütçeniz bitiyor! %${progress.toFixed(0)} kullanıldı (${spentAmount.toLocaleString('tr-TR')}₺/${plannedAmount.toLocaleString('tr-TR')}₺)`,
              percentage: progress,
              amount: spentAmount,
              isRead: false,
              createdAt: new Date().toISOString(),
            };
            alerts.push(alert);

            // Push notification gönder
            const { default: NotificationService } = await import('./NotificationService');
            await NotificationService.sendBudgetAlert(category.categoryName, progress);

            console.log(`🚨 Critical Alert: ${category.categoryName} at ${progress.toFixed(1)}%`);
          }

          // Limit aşımı kontrolü (%100+)
          if (category.limitEnabled && progress >= category.limitThreshold) {
            const overAmount = spentAmount - plannedAmount;
            const alert: BudgetAlert = {
              id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              budgetId: budget.id,
              budgetCategoryId: category.id,
              type: 'limit',
              title: '🔴 Bütçe Aşımı!',
              message: `${category.categoryName} kategorisinde bütçenizi ${overAmount.toLocaleString('tr-TR')}₺ aştınız! (${spentAmount.toLocaleString('tr-TR')}₺/${plannedAmount.toLocaleString('tr-TR')}₺)`,
              percentage: progress,
              amount: spentAmount,
              isRead: false,
              createdAt: new Date().toISOString(),
            };
            alerts.push(alert);

            // Push notification gönder
            const { default: NotificationService } = await import('./NotificationService');
            await NotificationService.sendBudgetAlert(category.categoryName, progress);

            console.log(`🔴 Limit Exceeded Alert: ${category.categoryName} at ${progress.toFixed(1)}%`);
          }
        }
      }

      // Gelir hedef aşımı kontrolü
      for (const budget of activeBudgets) {
        const actualIncome = budget.actualIncome || 0;
        const targetIncome = budget.totalIncomeTarget || 0;

        if (targetIncome > 0 && actualIncome > targetIncome) {
          const exceedPercentage = ((actualIncome - targetIncome) / targetIncome) * 100;
          const exceedAmount = actualIncome - targetIncome;

          const incomeAlert: BudgetAlert = {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            budgetId: budget.id,
            budgetCategoryId: undefined,
            type: 'income_exceeded',
            title: '🎉 Gelir Hedefi Aşıldı!',
            message: `Tebrikler! "${budget.name}" bütçesinde gelir hedefinizi %${exceedPercentage.toFixed(0)} aştınız. Fazla gelir: ${exceedAmount.toLocaleString('tr-TR')}₺`,
            percentage: (actualIncome / targetIncome) * 100,
            amount: actualIncome,
            isRead: false,
            createdAt: new Date().toISOString(),
          };
          alerts.push(incomeAlert);

          // Push notification gönder
          const { default: NotificationService } = await import('./NotificationService');
          await NotificationService.sendIncomeExceededAlert(budget.name, exceedPercentage, exceedAmount);

          console.log(`🎉 Income Exceeded Alert: ${budget.name} - ${exceedPercentage.toFixed(1)}% over target`);
        }
      }

      // Alerts'leri kaydet (ileride kullanmak için)
      if (alerts.length > 0) {
        await this.saveBudgetAlerts(userId, alerts);
      }

      return alerts;
    } catch (error) {
      console.error('❌ Error checking budget alerts:', error);
      throw error;
    }
  }

  /**
   * Bütçe uyarılarını kaydet
   */
  private async saveBudgetAlerts(userId: string, alerts: BudgetAlert[]): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      for (const alert of alerts) {
        await db.runAsync(
          `INSERT OR REPLACE INTO budget_alerts (
            id, user_id, budget_id, budget_category_id, type, title, message,
            percentage, amount, is_read, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            alert.id, userId, alert.budgetId, alert.budgetCategoryId, alert.type,
            alert.title, alert.message, alert.percentage, alert.amount,
            alert.isRead ? 1 : 0, alert.createdAt
          ]
        );
      }

      console.log(`✅ Saved ${alerts.length} budget alerts to database`);
    } catch (error) {
      console.error('❌ Error saving budget alerts:', error);
      // Alert kaydetme hatası ana işlemi durdurmaz
    }
  }
}

export default BudgetService.getInstance();
