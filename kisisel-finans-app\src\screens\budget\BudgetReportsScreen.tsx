// RFC-003 Bütçe Rapor Ekranı

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import BudgetService from '../../services/BudgetService';
import ExportService from '../../services/ExportService';
import {
  Line<PERSON>hart,
  BarChart,
  PieChart,
  ProgressChart,
  ContributionGraph,
  StackedBarChart
} from 'react-native-chart-kit';

type BudgetReportsRouteProp = RouteProp<MainStackParamList, 'BudgetReports'>;

const { width } = Dimensions.get('window');

const BudgetReportsScreen: React.FC = () => {
  const route = useRoute<BudgetReportsRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { budgetId } = route.params;

  // State
  const [budget, setBudget] = useState<any>(null);
  const [budgetSummary, setBudgetSummary] = useState<any>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'charts' | 'trends'>('overview');

  // Load budget reports data
  const loadReportsData = useCallback(async () => {
    try {
      console.log(`📊 Loading budget reports for ID: ${budgetId}`);
      
      const [budgetData, summaryData, categoriesData] = await Promise.all([
        BudgetService.getBudgetById(budgetId),
        BudgetService.getBudgetSummary(budgetId),
        BudgetService.getBudgetCategoriesWithDetails(budgetId)
      ]);

      setBudget(budgetData);
      setBudgetSummary(summaryData);
      setCategories(categoriesData);

      // Tüm işlemleri yükle - BudgetReports için transaction'lara ihtiyaç yok
      // Sadece budget summary ve categories yeterli
      console.log('📊 Budget reports loaded without transactions');

      console.log('📊 Budget reports data loaded');
    } catch (error) {
      console.error('❌ Error loading budget reports:', error);
      Alert.alert('Hata', 'Bütçe raporu yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [budgetId]);

  useEffect(() => {
    loadReportsData();
  }, [loadReportsData]);

  // Helper functions
  const formatCurrency = useCallback((amount: number | string, currency: string = 'TRY') => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount || 0;
    const symbol = currency === 'TRY' ? '₺' : currency === 'USD' ? '$' : '€';
    return `${numAmount.toLocaleString('tr-TR')} ${symbol}`;
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  }, []);

  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'safe': return theme.colors.success;
      case 'warning': return theme.colors.warning;
      case 'critical': return '#FF6B35';
      case 'exceeded': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  }, [theme]);

  // Export handlers
  const handleExportPDF = useCallback(async () => {
    if (!budget || !budgetSummary || !categories.length) {
      Alert.alert('Hata', 'Rapor verileri henüz yüklenmedi.');
      return;
    }

    try {
      console.log('📄 Exporting budget report as PDF');

      // Bütçe verilerini transaction formatına çevir (ExportService için)
      const budgetTransactions = categories.map((category: any, index: number) => ({
        id: `budget_${category.id}_${index}`,
        type: 'expense' as const,
        amount: category.spentAmount || 0,
        category: category.categoryName || 'Kategori',
        description: `${category.categoryName} - Bütçe Harcaması`,
        date: new Date().toISOString(),
        paymentMethod: 'other' as const,
        currency: budget.currency || 'TRY',
      }));

      const exportOptions = {
        format: 'pdf' as const,
        dateRange: {
          start: budget.startDate,
          end: budget.endDate,
        },
        includeSummary: true,
        includeCharts: false,
      };

      const result = await ExportService.exportTransactions(budgetTransactions, exportOptions);

      if (result.success) {
        Alert.alert('Başarılı', 'Bütçe raporu PDF olarak kaydedildi ve paylaşım menüsü açıldı.');
      } else {
        Alert.alert('Hata', result.error || 'PDF oluşturulurken bir hata oluştu.');
      }
    } catch (error) {
      console.error('❌ PDF export error:', error);
      Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu.');
    }
  }, [budget, budgetSummary, categories]);

  const handleExportExcel = useCallback(async () => {
    if (!budget || !budgetSummary || !categories.length) {
      Alert.alert('Hata', 'Rapor verileri henüz yüklenmedi.');
      return;
    }

    try {
      console.log('📊 Exporting budget report as Excel');

      // Bütçe verilerini transaction formatına çevir
      const budgetTransactions = categories.map((category: any, index: number) => ({
        id: `budget_${category.id}_${index}`,
        type: 'expense' as const,
        amount: category.spentAmount || 0,
        category: category.categoryName || 'Kategori',
        description: `${category.categoryName} - Planlanan: ${formatCurrency(category.plannedAmount, budget.currency)}`,
        date: new Date().toISOString(),
        paymentMethod: 'other' as const,
        currency: budget.currency || 'TRY',
      }));

      const exportOptions = {
        format: 'excel' as const,
        dateRange: {
          start: budget.startDate,
          end: budget.endDate,
        },
        includeSummary: true,
        includeCharts: false,
      };

      const result = await ExportService.exportTransactions(budgetTransactions, exportOptions);

      if (result.success) {
        Alert.alert('Başarılı', 'Bütçe raporu Excel olarak kaydedildi ve paylaşım menüsü açıldı.');
      } else {
        Alert.alert('Hata', result.error || 'Excel oluşturulurken bir hata oluştu.');
      }
    } catch (error) {
      console.error('❌ Excel export error:', error);
      Alert.alert('Hata', 'Excel oluşturulurken bir hata oluştu.');
    }
  }, [budget, budgetSummary, categories, formatCurrency]);

  const handleShareReport = useCallback(async () => {
    if (!budget || !budgetSummary || !categories.length) {
      Alert.alert('Hata', 'Rapor verileri henüz yüklenmedi.');
      return;
    }

    try {
      console.log('📤 Sharing budget report');

      // Önce Excel formatında rapor oluştur
      const budgetTransactions = categories.map((category: any, index: number) => ({
        id: `budget_${category.id}_${index}`,
        type: 'expense' as const,
        amount: category.spentAmount || 0,
        category: category.categoryName || 'Kategori',
        description: `${category.categoryName} - Bütçe Raporu`,
        date: new Date().toISOString(),
        paymentMethod: 'other' as const,
        currency: budget.currency || 'TRY',
      }));

      const exportOptions = {
        format: 'excel' as const,
        dateRange: {
          start: budget.startDate,
          end: budget.endDate,
        },
        includeSummary: true,
        includeCharts: false,
      };

      const result = await ExportService.exportTransactions(budgetTransactions, exportOptions);

      if (result.success) {
        console.log('✅ Budget report shared successfully');
      } else {
        Alert.alert('Hata', result.error || 'Rapor paylaşılırken bir hata oluştu.');
      }
    } catch (error) {
      console.error('❌ Share report error:', error);
      Alert.alert('Hata', 'Rapor paylaşılırken bir hata oluştu.');
    }
  }, [budget, budgetSummary, categories]);

  // Chart data preparation
  const getPieChartData = useCallback(() => {
    return categories.map((category, index) => ({
      name: category.categoryName || 'Kategori',
      population: category.spentAmount || 0,
      color: category.categoryColor || `hsl(${index * 45}, 70%, 60%)`,
      legendFontColor: theme.colors.text,
      legendFontSize: 12,
    }));
  }, [categories, theme.colors.text]);

  const getBarChartData = useCallback(() => {
    const topCategories = categories.slice(0, 3); // Sadece 3 kategori göster
    const labels = topCategories.map(cat =>
      (cat.categoryName || 'Kategori').substring(0, 8)
    );

    const plannedData = topCategories.map(cat => cat.plannedAmount || 0);
    const spentData = topCategories.map(cat => cat.spentAmount || 0);

    console.log('📊 Bar Chart Data:', {
      labels,
      plannedData,
      spentData,
      categories: topCategories
    });

    return {
      labels,
      datasets: [
        {
          data: plannedData.length > 0 ? plannedData : [1000, 500, 800],
          color: (opacity = 1) => `rgba(74, 144, 226, ${opacity})`, // Mavi - Planlanan
          strokeWidth: 2,
        },
        {
          data: spentData.length > 0 ? spentData : [600, 300, 500],
          color: (opacity = 1) => `rgba(231, 76, 60, ${opacity})`, // Kırmızı - Harcanan
          strokeWidth: 2,
        }
      ],
      legend: ['Planlanan', 'Harcanan']
    };
  }, [categories]);

  const getProgressData = useCallback(() => {
    return categories.slice(0, 4).map(category => ({
      name: category.categoryName || 'Kategori',
      progress: Math.min((category.progress || 0) / 100, 1),
      color: getStatusColor(category.status),
    }));
  }, [categories, getStatusColor]);

  // Trend data preparation
  const trendData = useMemo(() => {
    if (!budget || !categories.length) return null;

    const startDate = new Date(budget.startDate);
    const endDate = new Date(budget.endDate);
    const today = new Date();

    // Calculate days passed and total days
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysPassed = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalDays - daysPassed);

    // Calculate expected vs actual spending
    const expectedSpendingRate = Math.min(daysPassed / totalDays, 1);
    const expectedSpending = budgetSummary.totalPlanned * expectedSpendingRate;
    const actualSpending = budgetSummary.totalSpent;
    const spendingVariance = actualSpending - expectedSpending;

    // Calculate daily averages
    const dailyBudget = budgetSummary.totalPlanned / totalDays;
    const actualDailySpending = budgetSummary.totalSpent / Math.max(daysPassed, 1);

    // Projected end spending
    const projectedEndSpending = actualDailySpending * totalDays;
    const projectedOverBudget = projectedEndSpending - budgetSummary.totalPlanned;

    // Category trends
    const categoryTrends = categories.map((category: any) => {
      const categoryExpectedSpending = category.plannedAmount * expectedSpendingRate;
      const categoryVariance = category.spentAmount - categoryExpectedSpending;
      const categoryDailyRate = category.spentAmount / Math.max(daysPassed, 1);
      const categoryProjected = categoryDailyRate * totalDays;

      return {
        name: category.categoryName,
        planned: category.plannedAmount,
        spent: category.spentAmount,
        expected: categoryExpectedSpending,
        variance: categoryVariance,
        projected: categoryProjected,
        status: categoryVariance > 0 ? 'over' : 'under',
        color: category.categoryColor || '#AED6F1',
      };
    });

    return {
      totalDays,
      daysPassed,
      daysRemaining,
      expectedSpending,
      actualSpending,
      spendingVariance,
      dailyBudget,
      actualDailySpending,
      projectedEndSpending,
      projectedOverBudget,
      categoryTrends,
      spendingTrend: spendingVariance > 0 ? 'over' : 'under',
      budgetHealth: Math.abs(spendingVariance) / budgetSummary.totalPlanned * 100,
    };
  }, [budget, categories, budgetSummary]);

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.colors.text,
    labelColor: (opacity = 1) => theme.colors.text,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    centered: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surface,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    headerSubtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    summaryContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 8,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
    },
    summaryGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    summaryCard: {
      flex: 1,
      minWidth: (width - 60) / 2,
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    summaryLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    categoriesContainer: {
      padding: 20,
    },
    categoryCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    categoryHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    categoryInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    categoryIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    categoryName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    categoryAmounts: {
      alignItems: 'flex-end',
    },
    categoryPlanned: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    categorySpent: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    progressBar: {
      height: 8,
      backgroundColor: theme.colors.border,
      borderRadius: 4,
      marginTop: 8,
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    actionsContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 8,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      marginBottom: 8,
    },
    actionButtonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    secondaryButton: {
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    secondaryButtonText: {
      color: theme.colors.primary,
    },
    // New styles for tabs and charts
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerContent: {
      flex: 1,
      alignItems: 'center',
    },
    tabsContainer: {
      flexDirection: 'row',
      padding: 16,
      gap: 8,
      backgroundColor: theme.colors.background,
    },
    tabButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 8,
      borderRadius: 8,
      borderWidth: 1,
      gap: 4,
    },
    tabButtonText: {
      fontSize: 12,
      fontWeight: '500',
    },
    content: {
      flex: 1,
    },
    chartsContainer: {
      padding: 16,
    },
    chartCard: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    chartTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 16,
      textAlign: 'center',
    },
    trendsContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noDataContainer: {
      height: 220,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noDataText: {
      fontSize: 16,
      textAlign: 'center',
    },
    // Trend styles
    healthContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    healthItem: {
      flex: 1,
      minWidth: (width - 80) / 2,
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    healthLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    healthValue: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    varianceContainer: {
      gap: 12,
    },
    varianceItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    varianceLabel: {
      fontSize: 14,
      color: theme.colors.text,
    },
    varianceValue: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    trendItem: {
      marginBottom: 16,
      padding: 12,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    trendHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    trendIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    trendName: {
      flex: 1,
      fontSize: 16,
      fontWeight: '600',
    },
    trendStatus: {
      fontSize: 12,
      fontWeight: '500',
    },
    trendDetails: {
      gap: 4,
    },
    trendDetail: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
  });

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>
          Bütçe raporu yükleniyor...
        </Text>
      </View>
    );
  }

  if (!budget || !budgetSummary) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Ionicons name="alert-circle-outline" size={64} color={theme.colors.textSecondary} />
        <Text style={styles.headerTitle}>Rapor Bulunamadı</Text>
      </View>
    );
  }

  // Tab render functions
  const renderTabButton = (tab: 'overview' | 'charts' | 'trends', title: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: selectedTab === tab ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border,
        }
      ]}
      onPress={() => setSelectedTab(tab)}
    >
      <Ionicons
        name={icon as any}
        size={18}
        color={selectedTab === tab ? 'white' : theme.colors.text}
      />
      <Text style={[
        styles.tabButtonText,
        { color: selectedTab === tab ? 'white' : theme.colors.text }
      ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderChartsTab = () => (
    <View style={styles.chartsContainer}>
      {/* Pie Chart - Category Distribution */}
      <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Kategori Dağılımı
        </Text>
        {categories.length > 0 && (
          <PieChart
            data={getPieChartData()}
            width={width - 60}
            height={220}
            chartConfig={chartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            center={[10, 50]}
            absolute
          />
        )}
      </View>

      {/* Bar Chart - Planned vs Spent */}
      <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Planlanan vs Harcanan
        </Text>
        {categories.length > 0 ? (
          <BarChart
            data={getBarChartData()}
            width={width - 60}
            height={220}
            chartConfig={{
              backgroundColor: 'transparent',
              backgroundGradientFrom: theme.colors.surface,
              backgroundGradientTo: theme.colors.surface,
              decimalPlaces: 0,
              color: (opacity = 1) => `rgba(74, 144, 226, ${opacity})`,
              labelColor: (opacity = 1) => theme.colors.text,
              style: {
                borderRadius: 16,
              },
              barPercentage: 0.6,
            }}
            verticalLabelRotation={0}
            yAxisLabel="₺"
            yAxisSuffix=""
            showValuesOnTopOfBars={false}
            fromZero={true}
            withInnerLines={true}
            withHorizontalLabels={true}
            withVerticalLabels={true}
          />
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: theme.colors.textSecondary }]}>
              Veri bulunamadı
            </Text>
          </View>
        )}
      </View>

      {/* Progress Chart */}
      <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Kategori İlerlemeleri
        </Text>
        {categories.length > 0 && (
          <ProgressChart
            data={{
              labels: getProgressData().map(item => item.name.substring(0, 8)),
              data: getProgressData().map(item => item.progress)
            }}
            width={width - 60}
            height={220}
            strokeWidth={16}
            radius={32}
            chartConfig={chartConfig}
            hideLegend={false}
          />
        )}
      </View>
    </View>
  );

  const renderTrendsTab = () => {
    if (!trendData) {
      return (
        <View style={styles.trendsContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Trend verileri yükleniyor...
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.chartsContainer}>
        {/* Budget Health Overview */}
        <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Bütçe Sağlığı
          </Text>

          <View style={styles.healthContainer}>
            <View style={styles.healthItem}>
              <Text style={styles.healthLabel}>Geçen Günler</Text>
              <Text style={[styles.healthValue, { color: theme.colors.primary }]}>
                {trendData.daysPassed} / {trendData.totalDays}
              </Text>
            </View>

            <View style={styles.healthItem}>
              <Text style={styles.healthLabel}>Harcama Durumu</Text>
              <Text style={[
                styles.healthValue,
                { color: trendData.spendingTrend === 'over' ? theme.colors.error : theme.colors.success }
              ]}>
                {trendData.spendingTrend === 'over' ? 'Fazla' : 'Normal'}
              </Text>
            </View>

            <View style={styles.healthItem}>
              <Text style={styles.healthLabel}>Günlük Ortalama</Text>
              <Text style={[styles.healthValue, { color: theme.colors.text }]}>
                {formatCurrency(trendData.actualDailySpending, budget.currency)}
              </Text>
            </View>

            <View style={styles.healthItem}>
              <Text style={styles.healthLabel}>Tahmini Bitiş</Text>
              <Text style={[
                styles.healthValue,
                { color: trendData.projectedOverBudget > 0 ? theme.colors.error : theme.colors.success }
              ]}>
                {formatCurrency(trendData.projectedEndSpending, budget.currency)}
              </Text>
            </View>
          </View>
        </View>

        {/* Spending Variance */}
        <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Harcama Analizi
          </Text>

          <View style={styles.varianceContainer}>
            <View style={styles.varianceItem}>
              <Text style={styles.varianceLabel}>Beklenen Harcama</Text>
              <Text style={[styles.varianceValue, { color: theme.colors.primary }]}>
                {formatCurrency(trendData.expectedSpending, budget.currency)}
              </Text>
            </View>

            <View style={styles.varianceItem}>
              <Text style={styles.varianceLabel}>Gerçek Harcama</Text>
              <Text style={[styles.varianceValue, { color: theme.colors.text }]}>
                {formatCurrency(trendData.actualSpending, budget.currency)}
              </Text>
            </View>

            <View style={styles.varianceItem}>
              <Text style={styles.varianceLabel}>Fark</Text>
              <Text style={[
                styles.varianceValue,
                { color: trendData.spendingVariance > 0 ? theme.colors.error : theme.colors.success }
              ]}>
                {trendData.spendingVariance > 0 ? '+' : ''}
                {formatCurrency(Math.abs(trendData.spendingVariance), budget.currency)}
              </Text>
            </View>
          </View>
        </View>

        {/* Category Trends */}
        <View style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Kategori Trendleri
          </Text>

          {trendData.categoryTrends.map((trend: any, index: number) => (
            <View key={index} style={styles.trendItem}>
              <View style={styles.trendHeader}>
                <View style={[styles.trendIcon, { backgroundColor: trend.color }]}>
                  <Ionicons name="trending-up" size={16} color="white" />
                </View>
                <Text style={[styles.trendName, { color: theme.colors.text }]}>
                  {trend.name}
                </Text>
                <Text style={[
                  styles.trendStatus,
                  { color: trend.status === 'over' ? theme.colors.error : theme.colors.success }
                ]}>
                  {trend.status === 'over' ? 'Fazla' : 'Normal'}
                </Text>
              </View>

              <View style={styles.trendDetails}>
                <Text style={styles.trendDetail}>
                  Planlanan: {formatCurrency(trend.planned, budget.currency)}
                </Text>
                <Text style={styles.trendDetail}>
                  Harcanan: {formatCurrency(trend.spent, budget.currency)}
                </Text>
                <Text style={styles.trendDetail}>
                  Tahmini: {formatCurrency(trend.projected, budget.currency)}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{budget.name} - Rapor</Text>
          <Text style={styles.headerSubtitle}>
            {formatDate(budget.startDate)} - {formatDate(budget.endDate)}
          </Text>
        </View>
        <View style={{ width: 40 }} />
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        {renderTabButton('overview', 'Özet', 'analytics-outline')}
        {renderTabButton('charts', 'Grafikler', 'bar-chart-outline')}
        {renderTabButton('trends', 'Trendler', 'trending-up-outline')}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && (
          <>
            {/* Summary */}
            <View style={styles.summaryContainer}>
              <Text style={styles.sectionTitle}>Özet</Text>
              <View style={styles.summaryGrid}>
                <View style={styles.summaryCard}>
                  <Text style={styles.summaryLabel}>Toplam Bütçe</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
                    {formatCurrency(budgetSummary.totalPlanned, budget.currency)}
                  </Text>
                </View>

                <View style={styles.summaryCard}>
                  <Text style={styles.summaryLabel}>Harcanan</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.error }]}>
                    {formatCurrency(budgetSummary.totalSpent, budget.currency)}
                  </Text>
                </View>

                <View style={styles.summaryCard}>
                  <Text style={styles.summaryLabel}>Kalan</Text>
                  <Text style={[
                    styles.summaryValue,
                    { color: budgetSummary.totalRemaining >= 0 ? theme.colors.success : theme.colors.error }
                  ]}>
                    {formatCurrency(budgetSummary.totalRemaining, budget.currency)}
                  </Text>
                </View>

                <View style={styles.summaryCard}>
                  <Text style={styles.summaryLabel}>İlerleme</Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                    {budgetSummary.overallProgress.toFixed(1)}%
                  </Text>
                </View>
              </View>
            </View>

            {/* Categories */}
            <View style={styles.categoriesContainer}>
              <Text style={styles.sectionTitle}>Kategori Detayları</Text>
              {categories.map((category: any) => (
                <View key={category.id} style={styles.categoryCard}>
                  <View style={styles.categoryHeader}>
                    <View style={styles.categoryInfo}>
                      <View style={[styles.categoryIcon, { backgroundColor: category.categoryColor || '#AED6F1' }]}>
                        <Ionicons
                          name={category.categoryIcon || 'ellipsis-horizontal-outline'}
                          size={20}
                          color="white"
                        />
                      </View>
                      <Text style={styles.categoryName}>
                        {category.categoryName || 'Kategori'}
                      </Text>
                    </View>
                    <View style={styles.categoryAmounts}>
                      <Text style={styles.categoryPlanned}>
                        {formatCurrency(category.plannedAmount, budget.currency)}
                      </Text>
                      <Text style={styles.categorySpent}>
                        {formatCurrency(category.spentAmount, budget.currency)} harcandı
                      </Text>
                    </View>
                  </View>

                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressFill,
                        {
                          width: `${Math.min(category.progress, 100)}%`,
                          backgroundColor: getStatusColor(category.status)
                        }
                      ]}
                    />
                  </View>
                </View>
              ))}
            </View>

            {/* Actions */}
            <View style={styles.actionsContainer}>
              <Text style={styles.sectionTitle}>Rapor İşlemleri</Text>

              <TouchableOpacity style={styles.actionButton} onPress={handleExportPDF}>
                <Ionicons name="document-text" size={20} color="white" />
                <Text style={styles.actionButtonText}>PDF Olarak İndir</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={handleExportExcel}
              >
                <Ionicons name="grid" size={20} color={theme.colors.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                  Excel Olarak İndir
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={handleShareReport}
              >
                <Ionicons name="share" size={20} color={theme.colors.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                  Raporu Paylaş
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {selectedTab === 'charts' && renderChartsTab()}

        {selectedTab === 'trends' && renderTrendsTab()}
      </ScrollView>
    </View>
  );
};

export default BudgetReportsScreen;
