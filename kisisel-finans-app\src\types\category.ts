// Category Types - Kategori yönetimi tipleri

import { 
  IncomeCategory, 
  ExpenseCategory, 
  TransactionType,
  CurrencyCode 
} from './transaction';

// Kategori tipi
export type CategoryType = IncomeCategory | ExpenseCategory;

// Özel kategori
export interface CustomCategory {
  id: string;
  userId: string;
  name: string;
  type: TransactionType;
  parentCategory?: CategoryType;
  icon: string;
  color: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// Kategori istatistikleri
export interface CategoryStatistics {
  category: CategoryType | string; // string for custom categories
  type: TransactionType;
  totalAmount: number;
  transactionCount: number;
  averageAmount: number;
  currency: CurrencyCode;
  period: {
    start: string;
    end: string;
  };
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    previousAmount: number;
  };
  monthlyBreakdown: Array<{
    month: string;              // YYYY-MM
    amount: number;
    count: number;
  }>;
  topMerchants?: Array<{
    name: string;
    amount: number;
    count: number;
  }>;
}

// Kategori bütçesi
export interface CategoryBudget {
  id: string;
  userId: string;
  category: ExpenseCategory | string; // string for custom categories
  budgetAmount: number;
  spentAmount: number;
  remainingAmount: number;
  currency: CurrencyCode;
  period: 'weekly' | 'monthly' | 'quarterly' | 'annually';
  startDate: string;
  endDate: string;
  alertThresholds: {
    warning: number;            // Uyarı eşiği (yüzde)
    critical: number;           // Kritik eşik (yüzde)
  };
  notifications: {
    warningEnabled: boolean;
    criticalEnabled: boolean;
    dailyDigest: boolean;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Kategori kuralı (otomatik sınıflandırma için)
export interface CategoryRule {
  id: string;
  userId: string;
  name: string;
  category: CategoryType | string;
  type: TransactionType;
  conditions: {
    merchantNames?: string[];   // İşyeri adları
    descriptions?: string[];    // Açıklama anahtar kelimeleri
    amountRange?: {
      min?: number;
      max?: number;
    };
    paymentMethods?: string[];
    locations?: Array<{
      latitude: number;
      longitude: number;
      radius: number;           // metre
    }>;
  };
  priority: number;             // Kural önceliği (1-10)
  confidence: number;           // Güven skoru (0-1)
  isActive: boolean;
  autoApply: boolean;
  createdAt: string;
  updatedAt: string;
  lastUsed?: string;
  usageCount: number;
}

// Kategori önerisi
export interface CategorySuggestion {
  category: CategoryType | string;
  confidence: number;           // 0-1 arası güven skoru
  reason: string;
  ruleId?: string;             // Hangi kural tetiklendi
  similarTransactions?: Array<{
    id: string;
    description: string;
    amount: number;
    date: string;
    merchant?: string;
  }>;
}

// Kategori analizi
export interface CategoryAnalysis {
  userId: string;
  period: {
    start: string;
    end: string;
  };
  currency: CurrencyCode;
  income: {
    total: number;
    categories: CategoryStatistics[];
    topCategory: {
      category: IncomeCategory | string;
      amount: number;
      percentage: number;
    };
  };
  expense: {
    total: number;
    categories: CategoryStatistics[];
    topCategory: {
      category: ExpenseCategory | string;
      amount: number;
      percentage: number;
    };
  };
  insights: Array<{
    type: 'spending_increase' | 'new_category' | 'budget_exceeded' | 'unusual_pattern';
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    category?: string;
    amount?: number;
    percentage?: number;
  }>;
  recommendations: Array<{
    type: 'budget_adjustment' | 'category_merge' | 'spending_reduction' | 'income_opportunity';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    category?: string;
    suggestedAmount?: number;
  }>;
}

// Kategori karşılaştırması
export interface CategoryComparison {
  category: CategoryType | string;
  type: TransactionType;
  periods: Array<{
    label: string;
    start: string;
    end: string;
    amount: number;
    count: number;
  }>;
  change: {
    amount: number;
    percentage: number;
    direction: 'up' | 'down' | 'stable';
  };
  trend: Array<{
    date: string;
    amount: number;
  }>;
}

// Kategori hedefi
export interface CategoryGoal {
  id: string;
  userId: string;
  category: CategoryType | string;
  type: 'reduce_spending' | 'increase_income' | 'maintain_level';
  targetAmount: number;
  currentAmount: number;
  currency: CurrencyCode;
  deadline: string;
  progress: number;             // 0-100 arası yüzde
  isAchieved: boolean;
  achievedAt?: string;
  description?: string;
  milestones: Array<{
    amount: number;
    date: string;
    achieved: boolean;
    achievedAt?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

// Kategori etiketleme
export interface CategoryTag {
  id: string;
  name: string;
  color: string;
  categories: (CategoryType | string)[];
  userId: string;
  createdAt: string;
}

// Kategori import/export
export interface CategoryExport {
  categories: CustomCategory[];
  rules: CategoryRule[];
  budgets: CategoryBudget[];
  goals: CategoryGoal[];
  exportedAt: string;
  version: string;
}

// Kategori validasyon
export interface CategoryValidation {
  name: {
    minLength: number;
    maxLength: number;
    required: boolean;
    unique: boolean;
  };
  budget: {
    minAmount: number;
    maxAmount: number;
    required: boolean;
  };
  rule: {
    maxConditions: number;
    minConfidence: number;
    maxPriority: number;
  };
}

// Varsayılan kategori renkleri
export const DEFAULT_CATEGORY_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
];

// Varsayılan kategori ikonları
export const DEFAULT_CATEGORY_ICONS = [
  'wallet-outline', 'card-outline', 'cash-outline', 'gift-outline',
  'home-outline', 'car-outline', 'restaurant-outline', 'medical-outline',
  'school-outline', 'fitness-outline', 'game-controller-outline', 'airplane-outline'
];

// Kategori sıralama seçenekleri
export type CategorySortOption = 
  | 'name_asc' | 'name_desc'
  | 'amount_asc' | 'amount_desc'
  | 'count_asc' | 'count_desc'
  | 'recent' | 'oldest'
  | 'custom';

// Kategori görünüm seçenekleri
export interface CategoryViewOptions {
  showCustomCategories: boolean;
  showInactiveCategories: boolean;
  groupByType: boolean;
  sortBy: CategorySortOption;
  showStatistics: boolean;
  showBudgets: boolean;
  showTrends: boolean;
}

// Kategori arama filtresi
export interface CategorySearchFilter {
  query?: string;
  type?: TransactionType;
  hasTransactions?: boolean;
  hasBudget?: boolean;
  isActive?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  amountRange?: {
    min: number;
    max: number;
  };
}

// Kategori öneri motoru ayarları
export interface CategorySuggestionSettings {
  userId: string;
  enabled: boolean;
  autoApply: boolean;
  minConfidence: number;        // Minimum güven skoru
  learningEnabled: boolean;     // Kullanıcı davranışından öğrenme
  merchantLearning: boolean;    // İşyeri bazlı öğrenme
  locationLearning: boolean;    // Konum bazlı öğrenme
  timePatternLearning: boolean; // Zaman deseni öğrenme
  maxSuggestions: number;       // Maksimum öneri sayısı
  suggestionExpiry: number;     // Öneri geçerlilik süresi (gün)
  createdAt: string;
  updatedAt: string;
}

// Kategori performans metrikleri
export interface CategoryPerformanceMetrics {
  totalCategories: number;
  activeCategories: number;
  customCategories: number;
  rulesCount: number;
  activeRules: number;
  autoClassificationRate: number;  // Otomatik sınıflandırma oranı
  averageConfidence: number;       // Ortalama güven skoru
  topPerformingRules: Array<{
    ruleId: string;
    name: string;
    accuracy: number;
    usageCount: number;
  }>;
  categoryDistribution: Record<string, number>;
  lastUpdated: string;
}

export default {
  DEFAULT_CATEGORY_COLORS,
  DEFAULT_CATEGORY_ICONS,
};
