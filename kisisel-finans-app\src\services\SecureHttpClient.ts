// Secure HTTP Client - SSL pinning ile güvenli HTTP istemcisi

import SSLPinningService from './SSLPinningService';
import EncryptionService from './EncryptionService';
import NetworkService from './NetworkService';

export interface SecureRequestOptions extends RequestInit {
  timeout?: number;
  retryAttempts?: number;
  validateSSL?: boolean;
  encryptPayload?: boolean;
  requireOnline?: boolean;
}

export interface SecureResponse<T = any> {
  success: boolean;
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  sslValidation?: {
    validated: boolean;
    pinMatched: boolean;
    hostname: string;
  };
  encrypted?: boolean;
  fromCache?: boolean;
}

export interface RequestInterceptor {
  onRequest?: (url: string, options: SecureRequestOptions) => Promise<{ url: string; options: SecureRequestOptions }>;
  onResponse?: <T>(response: SecureResponse<T>) => Promise<SecureResponse<T>>;
  onError?: (error: Error, url: string, options: SecureRequestOptions) => Promise<Error>;
}

class SecureHttpClient {
  private static instance: SecureHttpClient;
  private sslPinningService = SSLPinningService;
  private networkService = NetworkService.getInstance();
  private interceptors: RequestInterceptor[] = [];
  private requestCache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();

  // Default options
  private defaultOptions: SecureRequestOptions = {
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    validateSSL: true,
    encryptPayload: false,
    requireOnline: true,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'FinanceApp/1.0',
    },
  };

  static getInstance(): SecureHttpClient {
    if (!SecureHttpClient.instance) {
      SecureHttpClient.instance = new SecureHttpClient();
    }
    return SecureHttpClient.instance;
  }

  /**
   * Request interceptor ekle
   */
  addInterceptor(interceptor: RequestInterceptor): void {
    this.interceptors.push(interceptor);
  }

  /**
   * Request interceptor kaldır
   */
  removeInterceptor(interceptor: RequestInterceptor): void {
    const index = this.interceptors.indexOf(interceptor);
    if (index > -1) {
      this.interceptors.splice(index, 1);
    }
  }

  /**
   * Güvenli GET isteği
   */
  async get<T = any>(url: string, options: SecureRequestOptions = {}): Promise<SecureResponse<T>> {
    return this.request<T>(url, { ...options, method: 'GET' });
  }

  /**
   * Güvenli POST isteği
   */
  async post<T = any>(url: string, data?: any, options: SecureRequestOptions = {}): Promise<SecureResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Güvenli PUT isteği
   */
  async put<T = any>(url: string, data?: any, options: SecureRequestOptions = {}): Promise<SecureResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Güvenli DELETE isteği
   */
  async delete<T = any>(url: string, options: SecureRequestOptions = {}): Promise<SecureResponse<T>> {
    return this.request<T>(url, { ...options, method: 'DELETE' });
  }

  /**
   * Ana request metodu
   */
  async request<T = any>(url: string, options: SecureRequestOptions = {}): Promise<SecureResponse<T>> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    let finalUrl = url;
    let finalOptions = mergedOptions;

    try {
      // Online kontrolü
      if (mergedOptions.requireOnline && !this.networkService.isOnline()) {
        // Cache'den dene
        const cachedResponse = this.getCachedResponse<T>(url);
        if (cachedResponse) {
          return cachedResponse;
        }
        throw new Error('Device is offline and no cached data available');
      }

      // Request interceptors
      for (const interceptor of this.interceptors) {
        if (interceptor.onRequest) {
          const result = await interceptor.onRequest(finalUrl, finalOptions);
          finalUrl = result.url;
          finalOptions = result.options;
        }
      }

      // SSL validation
      let sslValidation;
      if (finalOptions.validateSSL) {
        const hostname = this.extractHostname(finalUrl);
        const validationResult = await this.sslPinningService.validateSSLConnection(hostname);
        
        sslValidation = {
          validated: validationResult.success,
          pinMatched: validationResult.pinMatched,
          hostname: validationResult.hostname,
        };

        if (!validationResult.success && !validationResult.bypassReason) {
          throw new Error(`SSL validation failed for ${hostname}: ${validationResult.error}`);
        }
      }

      // Payload encryption
      if (finalOptions.encryptPayload && finalOptions.body) {
        const encryptedPayload = await EncryptionService.encrypt(finalOptions.body);
        finalOptions.body = JSON.stringify(encryptedPayload);
        finalOptions.headers = {
          ...finalOptions.headers,
          'X-Encrypted-Payload': 'true',
        };
      }

      // Execute request with retry logic
      const response = await this.executeWithRetry<T>(finalUrl, finalOptions);

      // Response interceptors
      let finalResponse = response;
      for (const interceptor of this.interceptors) {
        if (interceptor.onResponse) {
          finalResponse = await interceptor.onResponse(finalResponse);
        }
      }

      // Cache successful responses
      if (finalResponse.success && finalOptions.method === 'GET') {
        this.cacheResponse(url, finalResponse, 5 * 60 * 1000); // 5 minutes
      }

      return {
        ...finalResponse,
        sslValidation,
      };
    } catch (error) {
      // Error interceptors
      let finalError = error as Error;
      for (const interceptor of this.interceptors) {
        if (interceptor.onError) {
          finalError = await interceptor.onError(finalError, finalUrl, finalOptions);
        }
      }

      // Return cached data on error if available
      const cachedResponse = this.getCachedResponse<T>(url);
      if (cachedResponse) {
        console.warn('Returning cached data due to request error:', finalError.message);
        return cachedResponse;
      }

      throw finalError;
    }
  }

  /**
   * Retry logic ile request execute et
   */
  private async executeWithRetry<T>(url: string, options: SecureRequestOptions): Promise<SecureResponse<T>> {
    const maxAttempts = options.retryAttempts || 1;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await this.executeRequest<T>(url, options);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxAttempts) {
          break;
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
        
        console.warn(`Request attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      }
    }

    throw lastError!;
  }

  /**
   * Gerçek request'i execute et
   */
  private async executeRequest<T>(url: string, options: SecureRequestOptions): Promise<SecureResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Headers'ı object'e çevir
      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      let data: T;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        const jsonData = await response.json();
        
        // Encrypted response kontrolü
        if (headers['x-encrypted-response'] === 'true') {
          const decryptedData = await EncryptionService.decrypt(jsonData);
          data = JSON.parse(decryptedData);
        } else {
          data = jsonData;
        }
      } else {
        data = await response.text() as unknown as T;
      }

      const secureResponse: SecureResponse<T> = {
        success: response.ok,
        data,
        status: response.status,
        statusText: response.statusText,
        headers,
        encrypted: headers['x-encrypted-response'] === 'true',
      };

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return secureResponse;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * URL'den hostname çıkar
   */
  private extractHostname(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      // Fallback for relative URLs
      return url.split('/')[2] || 'localhost';
    }
  }

  /**
   * Response'u cache'le
   */
  private cacheResponse<T>(url: string, response: SecureResponse<T>, ttl: number): void {
    this.requestCache.set(url, {
      data: response,
      timestamp: Date.now(),
      ttl,
    });

    // Cache cleanup (max 100 items)
    if (this.requestCache.size > 100) {
      const oldestKey = this.requestCache.keys().next().value;
      this.requestCache.delete(oldestKey);
    }
  }

  /**
   * Cache'den response al
   */
  private getCachedResponse<T>(url: string): SecureResponse<T> | null {
    const cached = this.requestCache.get(url);
    
    if (!cached) {
      return null;
    }

    const isExpired = Date.now() - cached.timestamp > cached.ttl;
    if (isExpired) {
      this.requestCache.delete(url);
      return null;
    }

    return {
      ...cached.data,
      fromCache: true,
    };
  }

  /**
   * Cache'i temizle
   */
  clearCache(): void {
    this.requestCache.clear();
  }

  /**
   * Cache istatistikleri
   */
  getCacheStats(): {
    size: number;
    entries: Array<{ url: string; timestamp: number; ttl: number }>;
  } {
    const entries = Array.from(this.requestCache.entries()).map(([url, data]) => ({
      url,
      timestamp: data.timestamp,
      ttl: data.ttl,
    }));

    return {
      size: this.requestCache.size,
      entries,
    };
  }

  /**
   * Default options'ı güncelle
   */
  updateDefaultOptions(options: Partial<SecureRequestOptions>): void {
    this.defaultOptions = { ...this.defaultOptions, ...options };
  }

  /**
   * SSL pinning durumunu al
   */
  getSSLPinningStatus() {
    return this.sslPinningService.getSSLPinningStatus();
  }
}

export default SecureHttpClient;
