// RFC-003 Bütçe Şablonları - Hızlı Bütçe Oluşturma

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  TextInput,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import BudgetService from '../../services/BudgetService';
import { CreateBudgetInput, BudgetPeriod } from '../../types/budget';

// Predefined Budget Templates
interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  targetAudience: string;
  suggestedIncome: number; // Önerilen gelir seviyesi
  categories: {
    categoryId: string;
    categoryName: string;
    percentage: number;
    suggestedAmount: number; // Önerilen tutar
    icon: string;
    color: string;
  }[];
}

const BUDGET_TEMPLATES: BudgetTemplate[] = [
  {
    id: 'student',
    name: 'Öğrenci Bütçesi',
    description: 'KYK yurdu + burs + aile desteği ile yaşayan öğrenciler',
    icon: 'school-outline',
    color: '#4CAF50',
    targetAudience: 'Üniversite öğrencileri, şehir dışı',
    suggestedIncome: 8000,
    categories: [
      { categoryId: 'housing', categoryName: 'KYK Yurt / Ev Payı', percentage: 25, suggestedAmount: 2000, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Yemek / Market', percentage: 31, suggestedAmount: 2500, icon: 'restaurant-outline', color: '#FF6B6B' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 9, suggestedAmount: 750, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'bills', categoryName: 'Telefon ve İnternet', percentage: 6, suggestedAmount: 500, icon: 'phone-portrait-outline', color: '#FFA726' },
      { categoryId: 'personal', categoryName: 'Kişisel Harcamalar', percentage: 13, suggestedAmount: 1000, icon: 'person-outline', color: '#85C1E9' },
      { categoryId: 'entertainment', categoryName: 'Eğlence / Sosyal', percentage: 13, suggestedAmount: 1000, icon: 'game-controller-outline', color: '#96CEB4' },
      { categoryId: 'savings', categoryName: 'Tasarruf', percentage: 3, suggestedAmount: 250, icon: 'wallet-outline', color: '#66BB6A' },
    ]
  },
  {
    id: 'professional',
    name: 'Bekar Profesyonel',
    description: 'Şehirde kirada yaşayan genç profesyoneller',
    icon: 'briefcase-outline',
    color: '#9C27B0',
    targetAudience: 'Bekar çalışanlar, 1+1 kirada',
    suggestedIncome: 25000,
    categories: [
      { categoryId: 'housing', categoryName: 'Kira (1+1)', percentage: 40, suggestedAmount: 10000, icon: 'home-outline', color: '#795548' },
      { categoryId: 'bills', categoryName: 'Faturalar', percentage: 8, suggestedAmount: 2000, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'food', categoryName: 'Market / Yemek', percentage: 16, suggestedAmount: 4000, icon: 'restaurant-outline', color: '#FF6B6B' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 4, suggestedAmount: 1000, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'entertainment', categoryName: 'Eğlence / Sosyal', percentage: 12, suggestedAmount: 3000, icon: 'game-controller-outline', color: '#96CEB4' },
      { categoryId: 'education', categoryName: 'Kişisel Gelişim / Eğitim', percentage: 4, suggestedAmount: 1000, icon: 'book-outline', color: '#DDA0DD' },
      { categoryId: 'savings', categoryName: 'Tasarruf', percentage: 8, suggestedAmount: 2000, icon: 'wallet-outline', color: '#66BB6A' },
      { categoryId: 'other', categoryName: 'Beklenmeyen Gider', percentage: 8, suggestedAmount: 2000, icon: 'alert-circle-outline', color: '#AED6F1' },
    ]
  },
  {
    id: 'retired',
    name: 'Emekli Bütçesi',
    description: 'Ev sahibi emekli bireyler için sağlık odaklı bütçe',
    icon: 'heart-outline',
    color: '#FF5722',
    targetAudience: 'Emekliler, tek kişi veya çift',
    suggestedIncome: 17000,
    categories: [
      { categoryId: 'food', categoryName: 'Market / Gıda', percentage: 29, suggestedAmount: 5000, icon: 'basket-outline', color: '#FF6B6B' },
      { categoryId: 'bills', categoryName: 'Faturalar', percentage: 15, suggestedAmount: 2500, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 3, suggestedAmount: 500, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'health', categoryName: 'İlaç / Sağlık', percentage: 18, suggestedAmount: 3000, icon: 'medical-outline', color: '#FFEAA7' },
      { categoryId: 'entertainment', categoryName: 'Sosyal Etkinlik / Torunlar', percentage: 12, suggestedAmount: 2000, icon: 'people-outline', color: '#96CEB4' },
      { categoryId: 'savings', categoryName: 'Tasarruf', percentage: 6, suggestedAmount: 1000, icon: 'wallet-outline', color: '#66BB6A' },
      { categoryId: 'other', categoryName: 'Beklenmeyen Giderler', percentage: 17, suggestedAmount: 3000, icon: 'alert-circle-outline', color: '#AED6F1' },
    ]
  },
  {
    id: 'newlywed',
    name: 'Yeni Evli Çift',
    description: 'Yeni evlenen çiftler için ortak bütçe planı',
    icon: 'heart-circle-outline',
    color: '#E91E63',
    targetAudience: 'Yeni evli çiftler, kirada veya ev sahibi',
    suggestedIncome: 35000,
    categories: [
      { categoryId: 'housing', categoryName: 'Kira / Ev Giderleri', percentage: 35, suggestedAmount: 12250, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Market / Yemek', percentage: 20, suggestedAmount: 7000, icon: 'restaurant-outline', color: '#FF6B6B' },
      { categoryId: 'bills', categoryName: 'Faturalar', percentage: 12, suggestedAmount: 4200, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 8, suggestedAmount: 2800, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'entertainment', categoryName: 'Eğlence / Çift Aktiviteleri', percentage: 10, suggestedAmount: 3500, icon: 'game-controller-outline', color: '#96CEB4' },
      { categoryId: 'savings', categoryName: 'Tasarruf / Gelecek', percentage: 10, suggestedAmount: 3500, icon: 'wallet-outline', color: '#66BB6A' },
      { categoryId: 'other', categoryName: 'Beklenmeyen / Hediye', percentage: 5, suggestedAmount: 1750, icon: 'gift-outline', color: '#AED6F1' },
    ]
  },
  {
    id: 'freelancer',
    name: 'Freelance Çalışan',
    description: 'Değişken gelirli serbest çalışanlar için esnek bütçe',
    icon: 'laptop-outline',
    color: '#FF9800',
    targetAudience: 'Freelancerlar, serbest çalışanlar',
    suggestedIncome: 20000,
    categories: [
      { categoryId: 'housing', categoryName: 'Kira / Ev', percentage: 30, suggestedAmount: 6000, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Market / Yemek', percentage: 20, suggestedAmount: 4000, icon: 'restaurant-outline', color: '#FF6B6B' },
      { categoryId: 'bills', categoryName: 'Faturalar / İnternet', percentage: 15, suggestedAmount: 3000, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'business', categoryName: 'İş Giderleri / Ekipman', percentage: 10, suggestedAmount: 2000, icon: 'briefcase-outline', color: '#9C27B0' },
      { categoryId: 'savings', categoryName: 'Acil Durum Fonu', percentage: 15, suggestedAmount: 3000, icon: 'shield-outline', color: '#66BB6A' },
      { categoryId: 'entertainment', categoryName: 'Eğlence / Sosyal', percentage: 7, suggestedAmount: 1400, icon: 'game-controller-outline', color: '#96CEB4' },
      { categoryId: 'other', categoryName: 'Diğer Giderler', percentage: 3, suggestedAmount: 600, icon: 'ellipsis-horizontal-outline', color: '#AED6F1' },
    ]
  },
  {
    id: 'minimum_wage',
    name: 'Asgari Ücretli',
    description: 'Asgari ücretle geçinen bireyler için tasarruflu bütçe',
    icon: 'cash-outline',
    color: '#607D8B',
    targetAudience: 'Asgari ücretli çalışanlar',
    suggestedIncome: 22000,
    categories: [
      { categoryId: 'housing', categoryName: 'Kira / Ev Payı', percentage: 45, suggestedAmount: 9900, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Market / Temel İhtiyaç', percentage: 30, suggestedAmount: 6600, icon: 'basket-outline', color: '#FF6B6B' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 10, suggestedAmount: 2200, icon: 'bus-outline', color: '#4ECDC4' },
      { categoryId: 'bills', categoryName: 'Faturalar / Telefon', percentage: 10, suggestedAmount: 2200, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'health', categoryName: 'Sağlık / İlaç', percentage: 3, suggestedAmount: 660, icon: 'medical-outline', color: '#FFEAA7' },
      { categoryId: 'savings', categoryName: 'Tasarruf', percentage: 2, suggestedAmount: 440, icon: 'wallet-outline', color: '#66BB6A' },
    ]
  },
  {
    id: 'debt_payer',
    name: 'Borç Ödeyen',
    description: 'Kredi ve borç ödemesi olan bireyler için disiplinli bütçe',
    icon: 'card-outline',
    color: '#F44336',
    targetAudience: 'Kredi kartı, konut kredisi borcu olanlar',
    suggestedIncome: 30000,
    categories: [
      { categoryId: 'debt', categoryName: 'Kredi / Borç Ödemeleri', percentage: 35, suggestedAmount: 10500, icon: 'card-outline', color: '#F44336' },
      { categoryId: 'housing', categoryName: 'Kira / Ev', percentage: 25, suggestedAmount: 7500, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Market / Temel Gıda', percentage: 20, suggestedAmount: 6000, icon: 'basket-outline', color: '#FF6B6B' },
      { categoryId: 'bills', categoryName: 'Faturalar', percentage: 10, suggestedAmount: 3000, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 5, suggestedAmount: 1500, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'emergency', categoryName: 'Acil Durum', percentage: 3, suggestedAmount: 900, icon: 'shield-outline', color: '#66BB6A' },
      { categoryId: 'other', categoryName: 'Diğer (Minimum)', percentage: 2, suggestedAmount: 600, icon: 'ellipsis-horizontal-outline', color: '#AED6F1' },
    ]
  },
  {
    id: 'entrepreneur',
    name: 'Girişimci',
    description: 'Kendi işini kurmak isteyen bireyler için yatırım odaklı bütçe',
    icon: 'rocket-outline',
    color: '#4CAF50',
    targetAudience: 'Girişimciler, iş kurmak isteyenler',
    suggestedIncome: 40000,
    categories: [
      { categoryId: 'business', categoryName: 'İş Yatırımı / Sermaye', percentage: 30, suggestedAmount: 12000, icon: 'trending-up-outline', color: '#4CAF50' },
      { categoryId: 'housing', categoryName: 'Kira / Ev', percentage: 25, suggestedAmount: 10000, icon: 'home-outline', color: '#795548' },
      { categoryId: 'food', categoryName: 'Market / Yemek', percentage: 15, suggestedAmount: 6000, icon: 'restaurant-outline', color: '#FF6B6B' },
      { categoryId: 'bills', categoryName: 'Faturalar', percentage: 8, suggestedAmount: 3200, icon: 'receipt-outline', color: '#FFA726' },
      { categoryId: 'education', categoryName: 'Eğitim / Kurs', percentage: 7, suggestedAmount: 2800, icon: 'book-outline', color: '#DDA0DD' },
      { categoryId: 'emergency', categoryName: 'Acil Durum Fonu', percentage: 10, suggestedAmount: 4000, icon: 'shield-outline', color: '#66BB6A' },
      { categoryId: 'transportation', categoryName: 'Ulaşım', percentage: 3, suggestedAmount: 1200, icon: 'car-outline', color: '#4ECDC4' },
      { categoryId: 'other', categoryName: 'Diğer Giderler', percentage: 2, suggestedAmount: 800, icon: 'ellipsis-horizontal-outline', color: '#AED6F1' },
    ]
  }
];

const BudgetTemplatesScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showIncomeModal, setShowIncomeModal] = useState(false);
  const [customIncome, setCustomIncome] = useState('');

  // Template seçimi
  const handleTemplateSelect = useCallback((templateId: string) => {
    setSelectedTemplate(templateId);
  }, []);

  // Gelir girişi modalını aç
  const handleCreateFromTemplate = useCallback(() => {
    if (!selectedTemplate || !user) {
      Alert.alert('Hata', 'Lütfen bir şablon seçin.');
      return;
    }

    const template = BUDGET_TEMPLATES.find(t => t.id === selectedTemplate);
    if (!template) {
      Alert.alert('Hata', 'Seçilen şablon bulunamadı.');
      return;
    }

    // Önerilen geliri varsayılan olarak ayarla
    setCustomIncome(template.suggestedIncome.toString());
    setShowIncomeModal(true);
  }, [selectedTemplate, user]);

  // Gelir girişi onaylandığında bütçe oluştur
  const handleConfirmIncome = useCallback(() => {
    const income = parseFloat(customIncome);
    if (!income || income <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir gelir miktarı girin.');
      return;
    }

    const template = BUDGET_TEMPLATES.find(t => t.id === selectedTemplate);
    if (!template) return;

    setShowIncomeModal(false);
    createBudgetFromTemplate(template, income);
  }, [customIncome, selectedTemplate]);

  // Kategori tutarlarını gelire göre hesapla
  const calculateCategoryAmounts = useCallback((template: BudgetTemplate, income: number) => {
    return template.categories.map(cat => ({
      categoryId: cat.categoryId,
      plannedAmount: Math.round(income * (cat.percentage / 100)),
      warningThreshold: 75,
      criticalThreshold: 90,
      limitThreshold: 100,
      warningEnabled: true,
      criticalEnabled: true,
      limitEnabled: true,
      dailyDigestEnabled: false,
    }));
  }, []);

  // Şablondan bütçe oluşturma işlemi
  const createBudgetFromTemplate = useCallback(async (template: BudgetTemplate, income: number) => {
    if (!user) return;

    setIsCreating(true);
    try {
      // Kategorileri gelire göre hesapla
      const categories = calculateCategoryAmounts(template, income);

      // Bütçe input'u hazırla
      const budgetInput: CreateBudgetInput = {
        name: `${template.name} - ${new Date().toLocaleDateString('tr-TR')}`,
        period: 'monthly' as BudgetPeriod,
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        totalIncomeTarget: income,
        totalExpenseLimit: income,
        savingsTarget: 0,
        currency: 'TRY',
        notes: `${template.description} - Gelir: ${income.toLocaleString('tr-TR')}₺ - Şablondan oluşturuldu`,
        templateName: template.name,
        categories
      };

      console.log('🎯 Creating budget from template:', template.name, 'Income:', income);
      const budgetId = await BudgetService.createBudget(user.id, budgetInput);

      Alert.alert(
        'Başarılı!',
        `"${template.name}" şablonu kullanılarak ${income.toLocaleString('tr-TR')}₺ gelir ile bütçeniz oluşturuldu.\n\nKategori limitlerini düzenlemek için bütçe detayına gidin.`,
        [
          {
            text: 'Bütçeyi Gör',
            onPress: () => {
              navigation.navigate('BudgetDetail' as never, { budgetId } as never);
            }
          }
        ]
      );

      console.log(`✅ Budget created from template: ${budgetId}`);
    } catch (error) {
      console.error('❌ Error creating budget from template:', error);
      Alert.alert('Hata', 'Bütçe oluşturulurken bir hata oluştu.');
    } finally {
      setIsCreating(false);
    }
  }, [user, navigation, calculateCategoryAmounts]);

  // Template kartı render
  const renderTemplateCard = useCallback((template: BudgetTemplate) => {
    const isSelected = selectedTemplate === template.id;

    return (
      <TouchableOpacity
        key={template.id}
        style={[
          styles.templateCard,
          {
            backgroundColor: theme.colors.surface,
            borderColor: isSelected ? template.color : theme.colors.border,
            borderWidth: isSelected ? 2 : 1,
          }
        ]}
        onPress={() => handleTemplateSelect(template.id)}
      >
        {/* Header */}
        <View style={styles.templateHeader}>
          <View style={[styles.templateIcon, { backgroundColor: template.color }]}>
            <Ionicons name={template.icon as any} size={32} color="white" />
          </View>
          <View style={styles.templateInfo}>
            <Text style={[styles.templateName, { color: theme.colors.text }]}>
              {template.name}
            </Text>
            <Text style={[styles.templateAudience, { color: theme.colors.textSecondary }]}>
              {template.targetAudience}
            </Text>
          </View>
          {isSelected && (
            <Ionicons name="checkmark-circle" size={24} color={template.color} />
          )}
        </View>

        {/* Description */}
        <Text style={[styles.templateDescription, { color: theme.colors.textSecondary }]}>
          {template.description}
        </Text>

        {/* Suggested Income */}
        <View style={[styles.incomeInfo, { backgroundColor: theme.colors.background }]}>
          <Ionicons name="cash-outline" size={16} color={template.color} />
          <Text style={[styles.incomeText, { color: theme.colors.text }]}>
            Önerilen Gelir: {template.suggestedIncome.toLocaleString('tr-TR')}₺
          </Text>
        </View>

        {/* Categories Preview */}
        <View style={styles.categoriesPreview}>
          <Text style={[styles.categoriesTitle, { color: theme.colors.text }]}>
            Kategoriler:
          </Text>
          <View style={styles.categoriesList}>
            {template.categories.slice(0, 3).map((cat, index) => (
              <View key={index} style={styles.categoryItem}>
                <View style={[styles.categoryIcon, { backgroundColor: cat.color }]}>
                  <Ionicons name={cat.icon as any} size={16} color="white" />
                </View>
                <View style={styles.categoryTextContainer}>
                  <Text style={[styles.categoryText, { color: theme.colors.text }]}>
                    {cat.categoryName}
                  </Text>
                  <Text style={[styles.categoryAmount, { color: theme.colors.textSecondary }]}>
                    {cat.percentage}% • {cat.suggestedAmount.toLocaleString('tr-TR')}₺
                  </Text>
                </View>
              </View>
            ))}
            {template.categories.length > 3 && (
              <Text style={[styles.moreCategories, { color: theme.colors.textSecondary }]}>
                +{template.categories.length - 3} kategori daha
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [selectedTemplate, theme, handleTemplateSelect]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Bütçe Şablonları
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            Hızlı bütçe oluşturma
          </Text>
        </View>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.infoSection}>
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="flash" size={24} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
                1 Dakikada Bütçe Oluştur
              </Text>
              <Text style={[styles.infoDescription, { color: theme.colors.textSecondary }]}>
                Hazır şablonları kullanarak hızlıca bütçe oluşturun. Kategoriler ve oranlar önceden ayarlanmış!
              </Text>
            </View>
          </View>
        </View>

        {/* Templates */}
        <View style={styles.templatesSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Şablonları Seçin
          </Text>
          {BUDGET_TEMPLATES.map(renderTemplateCard)}
        </View>

        {/* Create Button */}
        {selectedTemplate && (
          <View style={styles.createSection}>
            <TouchableOpacity
              style={[
                styles.createButton,
                { backgroundColor: theme.colors.primary },
                isCreating && styles.createButtonDisabled
              ]}
              onPress={handleCreateFromTemplate}
              disabled={isCreating}
            >
              <Ionicons 
                name={isCreating ? "hourglass-outline" : "add-circle"} 
                size={24} 
                color="white" 
              />
              <Text style={styles.createButtonText}>
                {isCreating ? 'Oluşturuluyor...' : 'Şablondan Bütçe Oluştur'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Income Input Modal */}
      <Modal
        visible={showIncomeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowIncomeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Aylık Gelirinizi Belirleyin
            </Text>

            <Text style={[styles.modalDescription, { color: theme.colors.textSecondary }]}>
              Bütçe kategorileri gelir miktarınıza göre otomatik olarak hesaplanacak.
            </Text>

            <View style={styles.incomeInputContainer}>
              <TextInput
                style={[
                  styles.incomeInput,
                  {
                    backgroundColor: theme.colors.background,
                    color: theme.colors.text,
                    borderColor: theme.colors.border,
                  }
                ]}
                value={customIncome}
                onChangeText={setCustomIncome}
                placeholder="Aylık gelir (₺)"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
                autoFocus
              />
              <Text style={[styles.currencySymbol, { color: theme.colors.text }]}>₺</Text>
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton, { borderColor: theme.colors.border }]}
                onPress={() => setShowIncomeModal(false)}
              >
                <Text style={[styles.cancelButtonText, { color: theme.colors.text }]}>
                  İptal
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleConfirmIncome}
              >
                <Text style={styles.confirmButtonText}>
                  Bütçe Oluştur
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerContent: {
    flex: 1,
    marginLeft: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    padding: 16,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  templatesSection: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  templateCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  templateIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  templateAudience: {
    fontSize: 14,
  },
  templateDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  incomeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  incomeText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  categoriesPreview: {
    marginTop: 8,
  },
  categoriesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  categoriesList: {
    gap: 6,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  categoryTextContainer: {
    flex: 1,
  },
  categoryText: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 2,
  },
  categoryAmount: {
    fontSize: 11,
  },
  moreCategories: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 4,
  },
  createSection: {
    padding: 16,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  createButtonDisabled: {
    opacity: 0.6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomSpacing: {
    height: 32,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  modalDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  incomeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  incomeInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    // backgroundColor set dynamically
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BudgetTemplatesScreen;
