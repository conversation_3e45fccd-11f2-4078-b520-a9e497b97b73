// Income Hybrid Service - Gelir-Bütçe Hibrit Sistemi

import databaseManager from '../database/DatabaseManager';

export interface BudgetIncomeCategory {
  id: string;
  budgetId: string;
  categoryId: string;
  targetAmount: number;
  actualAmount: number;
  remainingAmount: number;
  progressPercentage: number;
  status: 'pending' | 'behind' | 'on_track' | 'achieved' | 'exceeded';
  warningThreshold: number;
  targetThreshold: number;
  warningEnabled: boolean;
  targetEnabled: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBudgetIncomeCategoryInput {
  categoryId: string;
  targetAmount: number;
  warningThreshold?: number;
  targetThreshold?: number;
  warningEnabled?: boolean;
  targetEnabled?: boolean;
}

export interface IncomeBudgetSummary {
  totalIncomeTarget: number;
  totalIncomeActual: number;
  totalIncomeRemaining: number;
  incomeProgressPercentage: number;
  incomeStatus: 'pending' | 'behind' | 'on_track' | 'achieved' | 'exceeded';
  categories: BudgetIncomeCategory[];
}

class IncomeHybridService {
  /**
   * Bütçeye gelir kategorileri ekle
   */
  static async addIncomeCategoriesToBudget(
    budgetId: string,
    incomeCategories: CreateBudgetIncomeCategoryInput[]
  ): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`💰 Adding ${incomeCategories.length} income categories to budget: ${budgetId}`);

      await db.withTransactionAsync(async () => {
        for (const category of incomeCategories) {
          const id = `budget_income_cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          
          await db.runAsync(
            `INSERT INTO budget_income_categories (
              id, budget_id, category_id, target_amount, actual_amount, remaining_amount,
              progress_percentage, status, warning_threshold, target_threshold,
              warning_enabled, target_enabled, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              id,
              budgetId,
              category.categoryId,
              category.targetAmount,
              0, // actual_amount
              category.targetAmount, // remaining_amount
              0, // progress_percentage
              'pending', // status
              category.warningThreshold || 50,
              category.targetThreshold || 100,
              category.warningEnabled ? 1 : 0,
              category.targetEnabled ? 1 : 0,
              1, // is_active
              new Date().toISOString(),
              new Date().toISOString(),
            ]
          );

          console.log(`✅ Added income category: ${category.categoryId} with target: ${category.targetAmount}`);
        }

        // Ana bütçenin gelir hedefini güncelle
        const totalIncomeTarget = incomeCategories.reduce((sum, cat) => sum + cat.targetAmount, 0);
        
        await db.runAsync(
          `UPDATE budgets 
           SET total_income_target = ?, updated_at = ?
           WHERE id = ?`,
          [totalIncomeTarget, new Date().toISOString(), budgetId]
        );

        console.log(`📊 Updated budget total income target: ${totalIncomeTarget}`);
      });

      console.log('✅ Income categories added to budget successfully');
    } catch (error) {
      console.error('❌ Error adding income categories to budget:', error);
      throw error;
    }
  }

  /**
   * Bütçenin gelir kategorilerini al
   */
  static async getBudgetIncomeCategories(budgetId: string): Promise<BudgetIncomeCategory[]> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`📊 Getting income categories for budget: ${budgetId}`);

      const categories = await db.getAllAsync(
        `SELECT 
          bic.*,
          c.name as category_name,
          c.icon as category_icon,
          c.color as category_color
         FROM budget_income_categories bic
         JOIN categories c ON bic.category_id = c.id
         WHERE bic.budget_id = ? AND bic.is_active = 1
         ORDER BY bic.target_amount DESC`,
        [budgetId]
      );

      const result = categories.map((cat: any) => ({
        id: cat.id,
        budgetId: cat.budget_id,
        categoryId: cat.category_id,
        targetAmount: cat.target_amount,
        actualAmount: cat.actual_amount,
        remainingAmount: cat.remaining_amount,
        progressPercentage: cat.progress_percentage,
        status: cat.status,
        warningThreshold: cat.warning_threshold,
        targetThreshold: cat.target_threshold,
        warningEnabled: Boolean(cat.warning_enabled),
        targetEnabled: Boolean(cat.target_enabled),
        isActive: Boolean(cat.is_active),
        createdAt: cat.created_at,
        updatedAt: cat.updated_at,
        // Extra fields for UI
        categoryName: cat.category_name,
        categoryIcon: cat.category_icon,
        categoryColor: cat.category_color,
      }));

      console.log(`✅ Found ${result.length} income categories`);
      return result;
    } catch (error) {
      console.error('❌ Error getting budget income categories:', error);
      throw error;
    }
  }

  /**
   * Bütçenin gelir özetini al
   */
  static async getIncomeBudgetSummary(budgetId: string): Promise<IncomeBudgetSummary> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`📊 Getting income budget summary for: ${budgetId}`);

      // Ana bütçe bilgilerini al
      const budget = await db.getFirstAsync(
        `SELECT 
          total_income_target,
          total_income_actual,
          total_income_remaining,
          income_progress_percentage,
          income_status
         FROM budgets 
         WHERE id = ?`,
        [budgetId]
      );

      if (!budget) {
        throw new Error(`Budget not found: ${budgetId}`);
      }

      // Gelir kategorilerini al
      const categories = await this.getBudgetIncomeCategories(budgetId);

      const summary: IncomeBudgetSummary = {
        totalIncomeTarget: budget.total_income_target || 0,
        totalIncomeActual: budget.total_income_actual || 0,
        totalIncomeRemaining: budget.total_income_remaining || 0,
        incomeProgressPercentage: budget.income_progress_percentage || 0,
        incomeStatus: budget.income_status || 'pending',
        categories,
      };

      console.log(`✅ Income budget summary:`, {
        target: summary.totalIncomeTarget,
        actual: summary.totalIncomeActual,
        progress: summary.incomeProgressPercentage,
        status: summary.incomeStatus,
        categoriesCount: categories.length,
      });

      return summary;
    } catch (error) {
      console.error('❌ Error getting income budget summary:', error);
      throw error;
    }
  }

  /**
   * Gelir işlemini bütçeye otomatik bağla
   */
  static async linkIncomeTransactionToBudget(
    transactionId: string,
    userId: string,
    categoryId: string,
    amount: number,
    date: string
  ): Promise<string | null> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`🔗 Linking income transaction to budget:`, {
        transactionId,
        categoryId,
        amount,
        date,
      });

      // Uygun bütçeyi bul (tarih aralığına göre)
      const budget = await db.getFirstAsync(
        `SELECT id, name, start_date, end_date
         FROM budgets
         WHERE user_id = ?
           AND is_active = 1
           AND date(?) BETWEEN date(start_date) AND date(end_date)
         ORDER BY created_at DESC
         LIMIT 1`,
        [userId, date]
      );

      if (!budget) {
        console.log('⚠️ No active budget found for date:', date);
        return null;
      }

      // Bu bütçede bu gelir kategorisi var mı kontrol et
      const incomeCategory = await db.getFirstAsync(
        `SELECT id FROM budget_income_categories
         WHERE budget_id = ? AND category_id = ? AND is_active = 1`,
        [budget.id, categoryId]
      );

      if (!incomeCategory) {
        console.log(`⚠️ Income category ${categoryId} not found in budget ${budget.id}`);
        return null;
      }

      // Transaction'ı bütçeye bağla
      await db.runAsync(
        `UPDATE transactions 
         SET budget_id = ?, updated_at = ?
         WHERE id = ?`,
        [budget.id, new Date().toISOString(), transactionId]
      );

      console.log(`✅ Income transaction linked to budget: ${budget.name} (${budget.id})`);
      return budget.id;
    } catch (error) {
      console.error('❌ Error linking income transaction to budget:', error);
      throw error;
    }
  }

  /**
   * Gelir kategorisi hedefini güncelle
   */
  static async updateIncomeCategoryTarget(
    budgetId: string,
    categoryId: string,
    newTargetAmount: number
  ): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`💰 Updating income category target:`, {
        budgetId,
        categoryId,
        newTargetAmount,
      });

      await db.withTransactionAsync(async () => {
        // Kategori hedefini güncelle
        await db.runAsync(
          `UPDATE budget_income_categories
           SET target_amount = ?, 
               remaining_amount = target_amount - actual_amount,
               progress_percentage = CASE 
                 WHEN ? > 0 THEN (actual_amount / ?) * 100 
                 ELSE 0 
               END,
               updated_at = ?
           WHERE budget_id = ? AND category_id = ?`,
          [
            newTargetAmount,
            newTargetAmount,
            newTargetAmount,
            new Date().toISOString(),
            budgetId,
            categoryId,
          ]
        );

        // Ana bütçenin toplam gelir hedefini yeniden hesapla
        const totalTarget = await db.getFirstAsync(
          `SELECT COALESCE(SUM(target_amount), 0) as total
           FROM budget_income_categories
           WHERE budget_id = ? AND is_active = 1`,
          [budgetId]
        );

        await db.runAsync(
          `UPDATE budgets
           SET total_income_target = ?,
               total_income_remaining = ? - total_income_actual,
               income_progress_percentage = CASE 
                 WHEN ? > 0 THEN (total_income_actual / ?) * 100 
                 ELSE 0 
               END,
               updated_at = ?
           WHERE id = ?`,
          [
            totalTarget?.total || 0,
            totalTarget?.total || 0,
            totalTarget?.total || 0,
            totalTarget?.total || 0,
            new Date().toISOString(),
            budgetId,
          ]
        );
      });

      console.log('✅ Income category target updated successfully');
    } catch (error) {
      console.error('❌ Error updating income category target:', error);
      throw error;
    }
  }

  /**
   * Gelir kategorilerini manuel olarak yeniden hesapla
   */
  static async recalculateIncomeCategories(budgetId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();

      console.log(`🔄 Recalculating income categories for budget: ${budgetId}`);

      const incomeCategories = await db.getAllAsync(
        `SELECT id, category_id, target_amount
         FROM budget_income_categories
         WHERE budget_id = ? AND is_active = 1`,
        [budgetId]
      );

      for (const category of incomeCategories) {
        // Bu kategori için toplam geliri hesapla
        const actualAmount = await db.getFirstAsync(
          `SELECT COALESCE(SUM(amount), 0) as total
           FROM transactions
           WHERE budget_id = ? 
             AND category_id = ?
             AND type = 'income'
             AND is_deleted = 0`,
          [budgetId, category.category_id]
        );

        const actual = actualAmount?.total || 0;
        const remaining = category.target_amount - actual;
        const progress = category.target_amount > 0 ? (actual / category.target_amount) * 100 : 0;
        
        let status = 'pending';
        if (actual >= category.target_amount) status = 'achieved';
        else if (progress >= 80) status = 'on_track';
        else if (progress >= 50) status = 'behind';

        // Kategoriyi güncelle
        await db.runAsync(
          `UPDATE budget_income_categories
           SET actual_amount = ?,
               remaining_amount = ?,
               progress_percentage = ?,
               status = ?,
               updated_at = ?
           WHERE id = ?`,
          [
            actual,
            remaining,
            progress,
            status,
            new Date().toISOString(),
            category.id,
          ]
        );

        console.log(`✅ Updated income category ${category.category_id}: ${actual}/${category.target_amount}`);
      }

      // Ana bütçeyi güncelle
      const totalActual = await db.getFirstAsync(
        `SELECT COALESCE(SUM(actual_amount), 0) as total
         FROM budget_income_categories
         WHERE budget_id = ? AND is_active = 1`,
        [budgetId]
      );

      const totalTarget = await db.getFirstAsync(
        `SELECT COALESCE(SUM(target_amount), 0) as total
         FROM budget_income_categories
         WHERE budget_id = ? AND is_active = 1`,
        [budgetId]
      );

      const actual = totalActual?.total || 0;
      const target = totalTarget?.total || 0;
      const remaining = target - actual;
      const progress = target > 0 ? (actual / target) * 100 : 0;
      
      let status = 'pending';
      if (actual >= target) status = 'achieved';
      else if (progress >= 80) status = 'on_track';
      else if (progress >= 50) status = 'behind';

      await db.runAsync(
        `UPDATE budgets
         SET total_income_actual = ?,
             total_income_remaining = ?,
             income_progress_percentage = ?,
             income_status = ?,
             updated_at = ?
         WHERE id = ?`,
        [
          actual,
          remaining,
          progress,
          status,
          new Date().toISOString(),
          budgetId,
        ]
      );

      console.log(`✅ Income categories recalculated: ${actual}/${target} (${progress.toFixed(1)}%)`);
    } catch (error) {
      console.error('❌ Error recalculating income categories:', error);
      throw error;
    }
  }
}

export default IncomeHybridService;
