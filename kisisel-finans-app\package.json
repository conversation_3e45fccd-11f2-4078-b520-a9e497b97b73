{"name": "kisisel-finans-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "^4.5.7", "@react-native-ml-kit/text-recognition": "^1.5.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "@types/jsonwebtoken": "^9.0.9", "base32.js": "^0.1.0", "expo": "~53.0.9", "expo-auth-session": "~6.1.5", "expo-background-fetch": "^13.1.5", "expo-camera": "~16.1.6", "expo-checkbox": "~4.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "^7.1.5", "expo-local-authentication": "~16.0.4", "expo-mail-composer": "^14.1.4", "expo-media-library": "^17.1.6", "expo-notifications": "~0.31.2", "expo-screen-capture": "~7.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-sqlite": "~15.2.10", "expo-status-bar": "~2.2.3", "expo-task-manager": "^13.1.5", "jsonwebtoken": "^9.0.2", "react": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.4", "react-native-fbsdk-next": "^13.4.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.12.3", "react-native-pdf-lib": "^1.0.0", "react-native-qrcode-svg": "^6.3.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-share": "^12.0.11", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.0.3", "react-native-vision-camera": "^4.6.4", "react-redux": "^9.0.4", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3"}, "private": true}