// API Service - Backend entegrasyonu

import { Transaction, User, Category, Budget, FinancialGoal } from '../types';
import SecureHttpClient from './SecureHttpClient';
import JWTService from './JWTService';

// API Configuration
const API_BASE_URL = __DEV__
  ? 'http://localhost:3000/api'
  : 'https://your-production-api.com/api';

const API_VERSION = 'v1';
const BASE_URL = `${API_BASE_URL}/${API_VERSION}`;

// External API Configuration
const EXTERNAL_APIS = {
  YAHOO_FINANCE: 'https://query1.finance.yahoo.com/v8/finance/chart',
  ALPHA_VANTAGE: 'https://www.alphavantage.co/query',
  EXCHANGE_RATE: 'https://api.exchangerate-api.com/v4/latest',
  NEWS_API: 'https://newsapi.org/v2',
  CRYPTO_API: 'https://api.coingecko.com/api/v3',
};

// Request types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

class ApiService {
  private static token: string | null = null;
  private static httpClient = SecureHttpClient;
  private static jwtService = JWTService.getInstance();

  // Set authentication token
  static setToken(token: string) {
    this.token = token;

    // HTTP client'a auth interceptor ekle
    this.httpClient.addInterceptor({
      onRequest: async (url, options) => {
        const currentToken = await this.getCurrentToken();
        if (currentToken) {
          options.headers = {
            ...options.headers,
            'Authorization': `Bearer ${currentToken}`,
          };
        }
        return { url, options };
      },
    });
  }

  // Clear authentication token
  static clearToken() {
    this.token = null;
  }

  // Get current token (with auto-refresh)
  static async getCurrentToken(): Promise<string | null> {
    try {
      // JWT service'den token al
      let token = await this.jwtService.getAccessToken();

      if (!token) {
        return this.token; // Fallback to static token
      }

      // Token geçerli mi kontrol et
      const isValid = await this.jwtService.isTokenValid(token);
      if (!isValid) {
        // Token refresh dene
        const refreshed = await this.jwtService.refreshAccessToken();
        if (refreshed) {
          token = await this.jwtService.getAccessToken();
        } else {
          return null; // Refresh başarısız
        }
      }

      return token;
    } catch (error) {
      console.error('Error getting current token:', error);
      return this.token; // Fallback
    }
  }

  // Get current token (legacy)
  static getToken(): string | null {
    return this.token;
  }

  // Generic request method with SSL Pinning
  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${BASE_URL}${endpoint}`;

    try {
      // SecureHttpClient ile SSL pinning'li request
      const response = await this.httpClient.request<T>(url, {
        ...options,
        validateSSL: true,
        encryptPayload: false, // API'ye göre ayarlanabilir
        retryAttempts: 3,
        timeout: 30000,
      });

      if (!response.success) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // ApiResponse formatına dönüştür
      return {
        success: response.success,
        data: response.data,
        message: response.statusText,
      };
    } catch (error) {
      console.error('API Request Error:', error);

      // Hata formatını standardize et
      return {
        success: false,
        data: null as any,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Authentication endpoints
  static async login(email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  static async register(userData: {
    name: string;
    email: string;
    password: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  static async logout(): Promise<ApiResponse<null>> {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  static async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return this.request('/auth/refresh', {
      method: 'POST',
    });
  }

  static async forgotPassword(email: string): Promise<ApiResponse<null>> {
    return this.request('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // User endpoints
  static async getProfile(): Promise<ApiResponse<User>> {
    return this.request('/user/profile');
  }

  static async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  static async deleteAccount(): Promise<ApiResponse<null>> {
    return this.request('/user/account', {
      method: 'DELETE',
    });
  }

  // Transaction endpoints
  static async getTransactions(params: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    category?: string;
    type?: 'income' | 'expense';
  } = {}): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/transactions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request(endpoint);
  }

  static async createTransaction(transactionData: Omit<Transaction, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Transaction>> {
    return this.request('/transactions', {
      method: 'POST',
      body: JSON.stringify(transactionData),
    });
  }

  static async updateTransaction(id: string, transactionData: Partial<Transaction>): Promise<ApiResponse<Transaction>> {
    return this.request(`/transactions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(transactionData),
    });
  }

  static async deleteTransaction(id: string): Promise<ApiResponse<null>> {
    return this.request(`/transactions/${id}`, {
      method: 'DELETE',
    });
  }

  // Category endpoints
  static async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request('/categories');
  }

  static async createCategory(categoryData: Omit<Category, 'id' | 'userId'>): Promise<ApiResponse<Category>> {
    return this.request('/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData),
    });
  }

  static async updateCategory(id: string, categoryData: Partial<Category>): Promise<ApiResponse<Category>> {
    return this.request(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData),
    });
  }

  static async deleteCategory(id: string): Promise<ApiResponse<null>> {
    return this.request(`/categories/${id}`, {
      method: 'DELETE',
    });
  }

  // Budget endpoints
  static async getBudgets(): Promise<ApiResponse<Budget[]>> {
    return this.request('/budgets');
  }

  static async createBudget(budgetData: Omit<Budget, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Budget>> {
    return this.request('/budgets', {
      method: 'POST',
      body: JSON.stringify(budgetData),
    });
  }

  static async updateBudget(id: string, budgetData: Partial<Budget>): Promise<ApiResponse<Budget>> {
    return this.request(`/budgets/${id}`, {
      method: 'PUT',
      body: JSON.stringify(budgetData),
    });
  }

  static async deleteBudget(id: string): Promise<ApiResponse<null>> {
    return this.request(`/budgets/${id}`, {
      method: 'DELETE',
    });
  }

  // Financial Goals endpoints
  static async getGoals(): Promise<ApiResponse<FinancialGoal[]>> {
    return this.request('/goals');
  }

  static async createGoal(goalData: Omit<FinancialGoal, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<FinancialGoal>> {
    return this.request('/goals', {
      method: 'POST',
      body: JSON.stringify(goalData),
    });
  }

  static async updateGoal(id: string, goalData: Partial<FinancialGoal>): Promise<ApiResponse<FinancialGoal>> {
    return this.request(`/goals/${id}`, {
      method: 'PUT',
      body: JSON.stringify(goalData),
    });
  }

  static async deleteGoal(id: string): Promise<ApiResponse<null>> {
    return this.request(`/goals/${id}`, {
      method: 'DELETE',
    });
  }

  // Analytics endpoints
  static async getAnalytics(params: {
    startDate: string;
    endDate: string;
    groupBy?: 'day' | 'week' | 'month' | 'year';
  }): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/analytics?${queryParams.toString()}`);
  }

  // File upload endpoint
  static async uploadFile(file: FormData): Promise<ApiResponse<{ url: string }>> {
    return this.request('/upload', {
      method: 'POST',
      body: file,
      headers: {
        // Don't set Content-Type for FormData, let browser set it
      },
    });
  }

  // Sync endpoints
  static async syncData(): Promise<ApiResponse<{
    transactions: Transaction[];
    categories: Category[];
    budgets: Budget[];
    goals: FinancialGoal[];
  }>> {
    return this.request('/sync');
  }

  static async getServerStatus(): Promise<ApiResponse<{
    status: 'online' | 'offline';
    version: string;
    timestamp: string;
  }>> {
    return this.request('/status');
  }
}

export default ApiService;
