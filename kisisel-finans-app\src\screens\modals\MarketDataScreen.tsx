// Market Data Screen - <PERSON><PERSON><PERSON> verileri ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface MarketData {
  symbol: string;
  name: string;
  currentPrice: number;
  change: number;
  changePercentage: number;
  volume: number;
  lastUpdated: string;
}

const MarketDataScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // Mock market data
  const mockMarketData: MarketData[] = [
    {
      symbol: 'BIST100',
      name: 'BIST 100 Endeksi',
      currentPrice: 8245.67,
      change: 125.43,
      changePercentage: 1.54,
      volume: 45678900,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'USDTRY',
      name: 'Dolar/TL',
      currentPrice: 32.45,
      change: -0.23,
      changePercentage: -0.70,
      volume: 1234567,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'EURTRY',
      name: 'Euro/TL',
      currentPrice: 35.12,
      change: 0.45,
      changePercentage: 1.30,
      volume: 987654,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'GOLD',
      name: 'Altın (Ons)',
      currentPrice: 2045.67,
      change: 12.34,
      changePercentage: 0.61,
      volume: 567890,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'GARAN',
      name: 'Garanti BBVA',
      currentPrice: 89.50,
      change: 2.10,
      changePercentage: 2.40,
      volume: ********,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'THYAO',
      name: 'Türk Hava Yolları',
      currentPrice: 245.75,
      change: -5.25,
      changePercentage: -2.09,
      volume: 8765432,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'AKBNK',
      name: 'Akbank',
      currentPrice: 67.80,
      change: 1.45,
      changePercentage: 2.18,
      volume: ********,
      lastUpdated: new Date().toISOString(),
    },
    {
      symbol: 'TUPRS',
      name: 'Tüpraş',
      currentPrice: 156.25,
      change: -3.75,
      changePercentage: -2.34,
      volume: 5432100,
      lastUpdated: new Date().toISOString(),
    },
  ];

  const [marketData, setMarketData] = useState<MarketData[]>(mockMarketData);

  const onRefresh = async () => {
    setRefreshing(true);

    // Simulate API call delay
    setTimeout(() => {
      // Update with slightly different values to simulate real-time data
      const updatedData = mockMarketData.map(item => ({
        ...item,
        currentPrice: item.currentPrice + (Math.random() - 0.5) * 2,
        change: (Math.random() - 0.5) * 10,
        changePercentage: (Math.random() - 0.5) * 5,
        lastUpdated: new Date().toISOString(),
      }));

      setMarketData(updatedData);
      setRefreshing(false);
    }, 1000);
  };

  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toLocaleString('tr-TR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? theme.colors.success : theme.colors.error;
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? 'trending-up' : 'trending-down';
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    refreshButton: {
      padding: 8,
    },
    summaryContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    summaryTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    summaryGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    summaryItem: {
      alignItems: 'center',
      flex: 1,
    },
    summaryLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    summaryValue: {
      fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 2,
    },
    summaryChange: {
      fontSize: 12,
      fontWeight: '500',
    },
    marketListContainer: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    marketItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    marketItemHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    marketItemInfo: {
      flex: 1,
    },
    marketSymbol: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    marketName: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    marketItemPrice: {
      alignItems: 'flex-end',
    },
    currentPrice: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    changeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    changeText: {
      fontSize: 12,
      fontWeight: '500',
    },
    marketItemFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    volumeText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    lastUpdated: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    disclaimerContainer: {
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      padding: 12,
      marginBottom: 20,
    },
    disclaimerText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 16,
    },
  });

  const renderMarketItem = (item: MarketData) => (
    <TouchableOpacity key={item.symbol} style={styles.marketItem}>
      <View style={styles.marketItemHeader}>
        <View style={styles.marketItemInfo}>
          <Text style={styles.marketSymbol}>{item.symbol}</Text>
          <Text style={styles.marketName} numberOfLines={1}>{item.name}</Text>
        </View>
        <View style={styles.marketItemPrice}>
          <Text style={styles.currentPrice}>
            {item.symbol.includes('TRY') ? '₺' : ''}{formatNumber(item.currentPrice)}
          </Text>
          <View style={styles.changeContainer}>
            <Ionicons
              name={getChangeIcon(item.change)}
              size={12}
              color={getChangeColor(item.change)}
            />
            <Text style={[styles.changeText, { color: getChangeColor(item.change) }]}>
              {item.change >= 0 ? '+' : ''}{formatNumber(item.change)}
              ({item.changePercentage >= 0 ? '+' : ''}{formatNumber(item.changePercentage, 2)}%)
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.marketItemFooter}>
        <Text style={styles.volumeText}>
          Hacim: {formatVolume(item.volume)}
        </Text>
        <Text style={styles.lastUpdated}>
          {new Date(item.lastUpdated).toLocaleTimeString('tr-TR', {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Piyasa Verileri</Text>
          <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
            <Ionicons name="refresh" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Market Summary */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Piyasa Özeti</Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>BIST 100</Text>
              <Text style={[
                styles.summaryValue,
                { color: getChangeColor(mockMarketData[0]?.change || 0) }
              ]}>
                {formatNumber(mockMarketData[0]?.currentPrice || 0)}
              </Text>
              <Text style={[
                styles.summaryChange,
                { color: getChangeColor(mockMarketData[0]?.change || 0) }
              ]}>
                {mockMarketData[0]?.changePercentage >= 0 ? '+' : ''}
                {formatNumber(mockMarketData[0]?.changePercentage || 0, 2)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>USD/TRY</Text>
              <Text style={[
                styles.summaryValue,
                { color: getChangeColor(mockMarketData[1]?.change || 0) }
              ]}>
                ₺{formatNumber(mockMarketData[1]?.currentPrice || 0)}
              </Text>
              <Text style={[
                styles.summaryChange,
                { color: getChangeColor(mockMarketData[1]?.change || 0) }
              ]}>
                {mockMarketData[1]?.changePercentage >= 0 ? '+' : ''}
                {formatNumber(mockMarketData[1]?.changePercentage || 0, 2)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Altın</Text>
              <Text style={[
                styles.summaryValue,
                { color: getChangeColor(mockMarketData[3]?.change || 0) }
              ]}>
                ${formatNumber(mockMarketData[3]?.currentPrice || 0)}
              </Text>
              <Text style={[
                styles.summaryChange,
                { color: getChangeColor(mockMarketData[3]?.change || 0) }
              ]}>
                {mockMarketData[3]?.changePercentage >= 0 ? '+' : ''}
                {formatNumber(mockMarketData[3]?.changePercentage || 0, 2)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Market Data List */}
        <View style={styles.marketListContainer}>
          <Text style={styles.sectionTitle}>Tüm Veriler</Text>
          {marketData.map(renderMarketItem)}
        </View>

        {/* Disclaimer */}
        <View style={styles.disclaimerContainer}>
          <Text style={styles.disclaimerText}>
            * Veriler 15 dakika gecikmeli olabilir. Yatırım kararlarınızı verirken güncel verileri kontrol edin.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

export default MarketDataScreen;
