// Currency Exchange Modal - Döviz kurları modal'ı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  TextInput,
  FlatList,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { Ionicons } from '@expo/vector-icons';

interface CurrencyExchangeModalProps {
  visible: boolean;
  onClose: () => void;
}

const CurrencyExchangeModal: React.FC<CurrencyExchangeModalProps> = ({
  visible,
  onClose,
}) => {
  const { theme } = useTheme();
  const {
    currencies,
    baseCurrency,
    exchangeRates,
    isLoading,
    setBaseCurrency,
    updateExchangeRates,
    convertAmount,
    formatAmount
  } = useCurrency();

  const [isUpdating, setIsUpdating] = useState(false);

  // Currency converter states
  const [converterAmount, setConverterAmount] = useState('100');
  const [fromCurrency, setFromCurrency] = useState(baseCurrency);
  const [toCurrency, setToCurrency] = useState(currencies.find(c => c.code === 'USD') || currencies[1]);

  // Currency picker modal states
  const [showCurrencyPicker, setShowCurrencyPicker] = useState(false);
  const [pickerType, setPickerType] = useState<'from' | 'to' | 'base'>('from');

  const handleUpdateRates = async () => {
    setIsUpdating(true);
    try {
      await updateExchangeRates();
      Alert.alert('Başarılı', 'Döviz kurları güncellendi!');
    } catch (error) {
      Alert.alert('Hata', 'Kurlar güncellenirken bir hata oluştu');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCurrencySelect = async (currency: any) => {
    try {
      await setBaseCurrency(currency);
      Alert.alert('Başarılı', `Ana para birimi ${currency.name} olarak değiştirildi`);
    } catch (error) {
      Alert.alert('Hata', 'Para birimi değiştirilirken bir hata oluştu');
    }
  };

  const getExchangeRate = (currencyCode: string) => {
    return exchangeRates[currencyCode] || 1;
  };

  const formatRate = (rate: number) => {
    return rate.toFixed(4);
  };

  const calculateConversion = () => {
    const amount = parseFloat(converterAmount) || 0;
    return convertAmount(amount, fromCurrency.code, toCurrency.code);
  };

  const swapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  const openCurrencyPicker = (type: 'from' | 'to' | 'base') => {
    setPickerType(type);
    setShowCurrencyPicker(true);
  };

  const handleCurrencyPickerSelect = (currency: any) => {
    if (pickerType === 'from') {
      setFromCurrency(currency);
    } else if (pickerType === 'to') {
      setToCurrency(currency);
    } else if (pickerType === 'base') {
      handleCurrencySelect(currency);
    }
    setShowCurrencyPicker(false);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Döviz Kurları
          </Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>

        {/* Current Base Currency */}
        <TouchableOpacity
          style={[styles.currentCurrency, { backgroundColor: theme.colors.surface }]}
          onPress={() => openCurrencyPicker('base')}
        >
          <Text style={[styles.currentLabel, { color: theme.colors.textSecondary }]}>
            Ana Para Birimi (Değiştirmek için dokunun)
          </Text>
          <View style={styles.currentInfo}>
            <Text style={[styles.currentFlag, { fontSize: 24 }]}>
              {baseCurrency.flag}
            </Text>
            <View style={{ flex: 1 }}>
              <Text style={[styles.currentName, { color: theme.colors.text }]}>
                {baseCurrency.name}
              </Text>
              <Text style={[styles.currentCode, { color: theme.colors.textSecondary }]}>
                {baseCurrency.code} ({baseCurrency.symbol})
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </View>
        </TouchableOpacity>

        {/* Update Button */}
        <TouchableOpacity
          style={[styles.updateButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleUpdateRates}
          disabled={isUpdating || isLoading}
        >
          {isUpdating ? (
            <ActivityIndicator color={theme.colors.surface} />
          ) : (
            <Ionicons name="refresh" size={20} color={theme.colors.surface} />
          )}
          <Text style={[styles.updateText, { color: theme.colors.surface }]}>
            {isUpdating ? 'Güncelleniyor...' : 'Kurları Güncelle'}
          </Text>
        </TouchableOpacity>

        {/* Currency Converter */}
        <View style={[styles.converterContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.converterTitle, { color: theme.colors.text }]}>
            Döviz Çevirici
          </Text>

          {/* Amount Input */}
          <View style={styles.converterRow}>
            <TextInput
              style={[styles.amountInput, {
                backgroundColor: theme.colors.background,
                color: theme.colors.text,
                borderColor: theme.colors.border
              }]}
              value={converterAmount}
              onChangeText={setConverterAmount}
              placeholder="Miktar"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          {/* From Currency */}
          <View style={styles.converterRow}>
            <Text style={[styles.converterLabel, { color: theme.colors.textSecondary }]}>
              Çevrilecek Para Birimi
            </Text>
            <TouchableOpacity
              style={[styles.currencySelector, { backgroundColor: theme.colors.background }]}
              onPress={() => openCurrencyPicker('from')}
            >
              <Text style={[styles.currencySelectorFlag, { fontSize: 20 }]}>
                {fromCurrency.flag}
              </Text>
              <Text style={[styles.currencySelectorText, { color: theme.colors.text }]}>
                {fromCurrency.code} - {fromCurrency.name}
              </Text>
              <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Swap Button */}
          <View style={styles.swapContainer}>
            <TouchableOpacity
              style={[styles.swapButton, { backgroundColor: theme.colors.primary }]}
              onPress={swapCurrencies}
            >
              <Ionicons name="swap-vertical" size={20} color={theme.colors.surface} />
            </TouchableOpacity>
          </View>

          {/* To Currency */}
          <View style={styles.converterRow}>
            <Text style={[styles.converterLabel, { color: theme.colors.textSecondary }]}>
              Hedef Para Birimi
            </Text>
            <TouchableOpacity
              style={[styles.currencySelector, { backgroundColor: theme.colors.background }]}
              onPress={() => openCurrencyPicker('to')}
            >
              <Text style={[styles.currencySelectorFlag, { fontSize: 20 }]}>
                {toCurrency.flag}
              </Text>
              <Text style={[styles.currencySelectorText, { color: theme.colors.text }]}>
                {toCurrency.code} - {toCurrency.name}
              </Text>
              <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Conversion Result */}
          <View style={[styles.conversionResult, { backgroundColor: theme.colors.background }]}>
            <Text style={[styles.conversionAmount, { color: theme.colors.text }]}>
              {formatAmount(calculateConversion(), toCurrency.code)}
            </Text>
            <Text style={[styles.conversionRate, { color: theme.colors.textSecondary }]}>
              1 {fromCurrency.code} = {formatRate(convertAmount(1, fromCurrency.code, toCurrency.code))} {toCurrency.code}
            </Text>
          </View>
        </View>

        {/* Exchange Rates List */}
        <ScrollView style={styles.ratesList}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Güncel Döviz Kurları (1 {baseCurrency.code} =)
          </Text>

          {currencies
            .filter(currency => currency.code !== baseCurrency.code)
            .map((currency) => {
              // 1 baseCurrency = X targetCurrency hesaplama
              const convertedRate = convertAmount(1, baseCurrency.code, currency.code);

              return (
                <TouchableOpacity
                  key={currency.code}
                  style={[styles.rateItem, { backgroundColor: theme.colors.surface }]}
                  onPress={() => handleCurrencySelect(currency)}
                >
                  <View style={styles.rateLeft}>
                    <Text style={styles.rateFlag}>{currency.flag}</Text>
                    <View>
                      <Text style={[styles.rateName, { color: theme.colors.text }]}>
                        {currency.name}
                      </Text>
                      <Text style={[styles.rateCode, { color: theme.colors.textSecondary }]}>
                        {currency.code}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.rateRight}>
                    <Text style={[styles.rateValue, { color: theme.colors.text }]}>
                      {formatRate(convertedRate)} {currency.symbol}
                    </Text>
                    <Ionicons
                      name="chevron-forward"
                      size={16}
                      color={theme.colors.textSecondary}
                    />
                  </View>
                </TouchableOpacity>
              );
            })}
        </ScrollView>

        {/* Info */}
        <View style={[styles.info, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="information-circle" size={16} color={theme.colors.textSecondary} />
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            Para birimine dokunarak ana para biriminizi değiştirebilirsiniz
          </Text>
        </View>
      </View>

      {/* Currency Picker Modal */}
      <Modal
        visible={showCurrencyPicker}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCurrencyPicker(false)}
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Para Birimi Seç
            </Text>
            <TouchableOpacity
              onPress={() => setShowCurrencyPicker(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Currency List */}
          <FlatList
            data={currencies}
            keyExtractor={(item) => item.code}
            style={{ flex: 1, paddingHorizontal: 20 }}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.currencyPickerItem, { backgroundColor: theme.colors.surface }]}
                onPress={() => handleCurrencyPickerSelect(item)}
              >
                <Text style={[styles.currencyPickerFlag, { fontSize: 24 }]}>
                  {item.flag}
                </Text>
                <View style={{ flex: 1 }}>
                  <Text style={[styles.currencyPickerName, { color: theme.colors.text }]}>
                    {item.name}
                  </Text>
                  <Text style={[styles.currencyPickerCode, { color: theme.colors.textSecondary }]}>
                    {item.code} ({item.symbol})
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  currentCurrency: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  currentLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  currentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentFlag: {
    marginRight: 12,
  },
  currentName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  currentCode: {
    fontSize: 14,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  updateText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  ratesList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  rateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  rateLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rateFlag: {
    fontSize: 20,
    marginRight: 12,
  },
  rateName: {
    fontSize: 16,
    fontWeight: '500',
  },
  rateCode: {
    fontSize: 14,
  },
  rateRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateValue: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  info: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    padding: 12,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  // Currency Converter Styles
  converterContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  converterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  converterRow: {
    marginBottom: 12,
  },
  converterLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  amountInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  currencySelectorFlag: {
    marginRight: 12,
  },
  currencySelectorText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  swapContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  swapButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  conversionResult: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  conversionAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  conversionRate: {
    fontSize: 14,
  },
  // Currency Picker Styles
  currencyPickerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginVertical: 4,
    borderRadius: 8,
  },
  currencyPickerFlag: {
    marginRight: 12,
  },
  currencyPickerName: {
    fontSize: 16,
    fontWeight: '500',
  },
  currencyPickerCode: {
    fontSize: 14,
  },
});

export default CurrencyExchangeModal;
