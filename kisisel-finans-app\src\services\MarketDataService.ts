// Market Data Service - <PERSON>yasa verileri yönetimi

import AsyncStorage from '@react-native-async-storage/async-storage';
import ExternalApiService, { StockData, ExchangeRateData, CryptoData, NewsData } from './ExternalApiService';

// Types
export interface MarketDataItem {
  id: string;
  symbol: string;
  name: string;
  type: 'stock' | 'crypto' | 'currency' | 'commodity';
  price: number;
  change: number;
  changePercent: number;
  volume?: number;
  marketCap?: number;
  lastUpdated: string;
  isWatchlisted?: boolean;
}

export interface MarketSummary {
  totalMarketCap: number;
  totalVolume: number;
  marketTrend: 'bullish' | 'bearish' | 'neutral';
  topGainers: MarketDataItem[];
  topLosers: MarketDataItem[];
  mostActive: MarketDataItem[];
  lastUpdated: string;
}

export interface WatchlistItem {
  symbol: string;
  name: string;
  type: 'stock' | 'crypto' | 'currency';
  addedAt: string;
  alertPrice?: number;
  alertType?: 'above' | 'below';
}

class MarketDataService {
  private static instance: MarketDataService;
  private externalApi: ExternalApiService;
  private watchlist: WatchlistItem[] = [];
  private marketData: Map<string, MarketDataItem> = new Map();

  // Storage keys
  private readonly WATCHLIST_KEY = 'market_watchlist';
  private readonly MARKET_DATA_KEY = 'market_data_cache';
  private readonly LAST_UPDATE_KEY = 'market_last_update';

  static getInstance(): MarketDataService {
    if (!MarketDataService.instance) {
      MarketDataService.instance = new MarketDataService();
    }
    return MarketDataService.instance;
  }

  constructor() {
    this.externalApi = ExternalApiService.getInstance();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    await this.loadWatchlist();
    await this.loadCachedMarketData();
  }

  /**
   * Watchlist yükleme
   */
  private async loadWatchlist(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.WATCHLIST_KEY);
      if (stored) {
        this.watchlist = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading watchlist:', error);
    }
  }

  /**
   * Watchlist kaydetme
   */
  private async saveWatchlist(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.WATCHLIST_KEY, JSON.stringify(this.watchlist));
    } catch (error) {
      console.error('Error saving watchlist:', error);
    }
  }

  /**
   * Cache'lenmiş market verilerini yükleme
   */
  private async loadCachedMarketData(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.MARKET_DATA_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.marketData = new Map(data);
      }
    } catch (error) {
      console.error('Error loading cached market data:', error);
    }
  }

  /**
   * Market verilerini cache'leme
   */
  private async saveCachedMarketData(): Promise<void> {
    try {
      const data = Array.from(this.marketData.entries());
      await AsyncStorage.setItem(this.MARKET_DATA_KEY, JSON.stringify(data));
      await AsyncStorage.setItem(this.LAST_UPDATE_KEY, new Date().toISOString());
    } catch (error) {
      console.error('Error saving cached market data:', error);
    }
  }

  /**
   * Watchlist'e sembol ekleme
   */
  async addToWatchlist(item: Omit<WatchlistItem, 'addedAt'>): Promise<void> {
    const existingIndex = this.watchlist.findIndex(w => w.symbol === item.symbol);

    if (existingIndex === -1) {
      this.watchlist.push({
        ...item,
        addedAt: new Date().toISOString(),
      });
      await this.saveWatchlist();
    }
  }

  /**
   * Watchlist'ten sembol çıkarma
   */
  async removeFromWatchlist(symbol: string): Promise<void> {
    this.watchlist = this.watchlist.filter(item => item.symbol !== symbol);
    await this.saveWatchlist();
  }

  /**
   * Watchlist getirme
   */
  getWatchlist(): WatchlistItem[] {
    return [...this.watchlist];
  }

  /**
   * Tek bir sembol için market verisi getirme
   */
  async getMarketData(symbol: string, type: 'stock' | 'crypto' = 'stock'): Promise<MarketDataItem | null> {
    try {
      let data: MarketDataItem | null = null;

      if (type === 'stock') {
        const stockData = await this.externalApi.getStockData(symbol);
        if (stockData) {
          data = {
            id: symbol,
            symbol: stockData.symbol,
            name: stockData.symbol, // API'den name gelmiyorsa symbol kullan
            type: 'stock',
            price: stockData.price,
            change: stockData.change,
            changePercent: stockData.changePercent,
            volume: stockData.volume,
            marketCap: stockData.marketCap,
            lastUpdated: stockData.lastUpdated,
            isWatchlisted: this.watchlist.some(w => w.symbol === symbol),
          };
        }
      } else if (type === 'crypto') {
        const cryptoData = await this.externalApi.getCryptoData([symbol.toLowerCase()]);
        if (cryptoData.length > 0) {
          const crypto = cryptoData[0];
          data = {
            id: crypto.id,
            symbol: crypto.symbol,
            name: crypto.name,
            type: 'crypto',
            price: crypto.current_price,
            change: crypto.price_change_24h,
            changePercent: crypto.price_change_percentage_24h,
            volume: crypto.volume_24h,
            marketCap: crypto.market_cap,
            lastUpdated: crypto.last_updated,
            isWatchlisted: this.watchlist.some(w => w.symbol === symbol),
          };
        }
      }

      if (data) {
        this.marketData.set(symbol, data);
        await this.saveCachedMarketData();
      }

      return data;
    } catch (error) {
      console.error('Error getting market data:', error);

      // Offline durumunda cache'den döndür
      return this.marketData.get(symbol) || null;
    }
  }

  /**
   * Watchlist için tüm market verilerini getirme
   */
  async getWatchlistData(): Promise<MarketDataItem[]> {
    const results: MarketDataItem[] = [];

    for (const item of this.watchlist) {
      const data = await this.getMarketData(item.symbol, item.type);
      if (data) {
        results.push(data);
      }
    }

    return results;
  }

  /**
   * Market özeti getirme
   */
  async getMarketSummary(): Promise<MarketSummary> {
    try {
      // Popüler sembolleri al
      const popularSymbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'];
      const marketItems: MarketDataItem[] = [];

      for (const symbol of popularSymbols) {
        const data = await this.getMarketData(symbol, 'stock');
        if (data) {
          marketItems.push(data);
        }
      }

      // Kripto verilerini ekle
      const cryptoData = await this.externalApi.getCryptoData(['bitcoin', 'ethereum']);
      for (const crypto of cryptoData) {
        marketItems.push({
          id: crypto.id,
          symbol: crypto.symbol,
          name: crypto.name,
          type: 'crypto',
          price: crypto.current_price,
          change: crypto.price_change_24h,
          changePercent: crypto.price_change_percentage_24h,
          volume: crypto.volume_24h,
          marketCap: crypto.market_cap,
          lastUpdated: crypto.last_updated,
        });
      }

      // Analiz yap
      const totalMarketCap = marketItems.reduce((sum, item) => sum + (item.marketCap || 0), 0);
      const totalVolume = marketItems.reduce((sum, item) => sum + (item.volume || 0), 0);

      const gainers = marketItems.filter(item => item.changePercent > 0)
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, 5);

      const losers = marketItems.filter(item => item.changePercent < 0)
        .sort((a, b) => a.changePercent - b.changePercent)
        .slice(0, 5);

      const mostActive = marketItems
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, 5);

      // Market trend hesapla
      const positiveCount = marketItems.filter(item => item.changePercent > 0).length;
      const negativeCount = marketItems.filter(item => item.changePercent < 0).length;

      let marketTrend: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (positiveCount > negativeCount * 1.5) marketTrend = 'bullish';
      else if (negativeCount > positiveCount * 1.5) marketTrend = 'bearish';

      return {
        totalMarketCap,
        totalVolume,
        marketTrend,
        topGainers: gainers,
        topLosers: losers,
        mostActive,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error getting market summary:', error);

      // Fallback summary
      return {
        totalMarketCap: 0,
        totalVolume: 0,
        marketTrend: 'neutral',
        topGainers: [],
        topLosers: [],
        mostActive: [],
        lastUpdated: new Date().toISOString(),
      };
    }
  }

  /**
   * Döviz kurları getirme
   */
  async getExchangeRates(baseCurrency: string = 'USD'): Promise<ExchangeRateData | null> {
    return await this.externalApi.getExchangeRates(baseCurrency);
  }

  /**
   * Finansal haberler getirme
   */
  async getFinancialNews(query: string = 'finance'): Promise<NewsData[]> {
    return await this.externalApi.getFinancialNews(query);
  }

  /**
   * Fiyat alarmı kontrolü
   */
  checkPriceAlerts(marketData: MarketDataItem[]): WatchlistItem[] {
    const triggeredAlerts: WatchlistItem[] = [];

    for (const data of marketData) {
      const watchlistItem = this.watchlist.find(w => w.symbol === data.symbol);
      if (watchlistItem?.alertPrice && watchlistItem.alertType) {
        const shouldTrigger =
          (watchlistItem.alertType === 'above' && data.price >= watchlistItem.alertPrice) ||
          (watchlistItem.alertType === 'below' && data.price <= watchlistItem.alertPrice);

        if (shouldTrigger) {
          triggeredAlerts.push(watchlistItem);
        }
      }
    }

    return triggeredAlerts;
  }

  /**
   * Cache temizleme
   */
  async clearCache(): Promise<void> {
    this.marketData.clear();
    this.externalApi.clearCache();
    await AsyncStorage.removeItem(this.MARKET_DATA_KEY);
    await AsyncStorage.removeItem(this.LAST_UPDATE_KEY);
  }

  /**
   * Son güncelleme zamanını getirme
   */
  async getLastUpdateTime(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.LAST_UPDATE_KEY);
    } catch (error) {
      return null;
    }
  }
}

export default MarketDataService;
