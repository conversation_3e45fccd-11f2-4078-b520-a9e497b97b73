// Market Data Screen - Piyasa verileri ekranı

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import MarketDataService, { MarketDataItem, MarketSummary, WatchlistItem } from '../services/MarketDataService';

const MarketDataScreen: React.FC = () => {
  const { theme } = useTheme();
  const [marketSummary, setMarketSummary] = useState<MarketSummary | null>(null);
  const [watchlistData, setWatchlistData] = useState<MarketDataItem[]>([]);
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'summary' | 'watchlist'>('summary');

  // Lazy initialization of MarketDataService
  const getMarketService = () => MarketDataService.getInstance();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        loadMarketSummary(),
        loadWatchlist(),
        loadWatchlistData(),
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMarketSummary = async () => {
    try {
      const summary = await getMarketService().getMarketSummary();
      setMarketSummary(summary);
    } catch (error) {
      console.error('Error loading market summary:', error);
    }
  };

  const loadWatchlist = async () => {
    try {
      const list = getMarketService().getWatchlist();
      setWatchlist(list);
    } catch (error) {
      console.error('Error loading watchlist:', error);
    }
  };

  const loadWatchlistData = async () => {
    try {
      const data = await getMarketService().getWatchlistData();
      setWatchlistData(data);
    } catch (error) {
      console.error('Error loading watchlist data:', error);
    }
  };

  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadInitialData();
    setIsRefreshing(false);
  }, []);

  const addToWatchlist = async (symbol: string, name: string, type: 'stock' | 'crypto') => {
    try {
      await getMarketService().addToWatchlist({ symbol, name, type });
      await loadWatchlist();
      await loadWatchlistData();
      Alert.alert('Başarılı', `${symbol} takip listesine eklendi`);
    } catch (error) {
      Alert.alert('Hata', 'Takip listesine eklenirken bir hata oluştu');
    }
  };

  const removeFromWatchlist = async (symbol: string) => {
    try {
      await getMarketService().removeFromWatchlist(symbol);
      await loadWatchlist();
      await loadWatchlistData();
      Alert.alert('Başarılı', `${symbol} takip listesinden çıkarıldı`);
    } catch (error) {
      Alert.alert('Hata', 'Takip listesinden çıkarılırken bir hata oluştu');
    }
  };

  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(price);
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return theme.colors.success;
    if (change < 0) return theme.colors.error;
    return theme.colors.text;
  };

  const renderMarketItem = (item: MarketDataItem) => (
    <View key={item.id} style={[styles.marketItem, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.marketItemHeader}>
        <View style={styles.marketItemInfo}>
          <Text style={[styles.marketItemSymbol, { color: theme.colors.text }]}>
            {item.symbol}
          </Text>
          <Text style={[styles.marketItemName, { color: theme.colors.textSecondary }]}>
            {item.name}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            if (item.isWatchlisted) {
              removeFromWatchlist(item.symbol);
            } else {
              addToWatchlist(item.symbol, item.name, item.type);
            }
          }}
        >
          <Ionicons
            name={item.isWatchlisted ? 'star' : 'star-outline'}
            size={20}
            color={item.isWatchlisted ? theme.colors.warning : theme.colors.textSecondary}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.marketItemData}>
        <View>
          <Text style={[styles.marketItemPrice, { color: theme.colors.text }]}>
            {formatPrice(item.price)}
          </Text>
          <Text style={[styles.marketItemChange, { color: getChangeColor(item.change) }]}>
            {formatChange(item.change, item.changePercent)}
          </Text>
        </View>

        {item.volume && (
          <View style={styles.marketItemVolume}>
            <Text style={[styles.marketItemVolumeLabel, { color: theme.colors.textSecondary }]}>
              Hacim
            </Text>
            <Text style={[styles.marketItemVolumeValue, { color: theme.colors.text }]}>
              {(item.volume / 1000000).toFixed(1)}M
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderSummaryTab = () => (
    <ScrollView style={styles.tabContent}>
      {marketSummary && (
        <>
          {/* Market Trend */}
          <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
              Piyasa Durumu
            </Text>
            <View style={styles.trendContainer}>
              <Ionicons
                name={
                  marketSummary.marketTrend === 'bullish' ? 'trending-up' :
                  marketSummary.marketTrend === 'bearish' ? 'trending-down' : 'remove'
                }
                size={24}
                color={
                  marketSummary.marketTrend === 'bullish' ? theme.colors.success :
                  marketSummary.marketTrend === 'bearish' ? theme.colors.error : theme.colors.textSecondary
                }
              />
              <Text style={[styles.trendText, { color: theme.colors.text }]}>
                {marketSummary.marketTrend === 'bullish' ? 'Yükseliş' :
                 marketSummary.marketTrend === 'bearish' ? 'Düşüş' : 'Yatay'}
              </Text>
            </View>
          </View>

          {/* Top Gainers */}
          {marketSummary.topGainers.length > 0 && (
            <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
                En Çok Yükselenler
              </Text>
              {marketSummary.topGainers.slice(0, 3).map(renderMarketItem)}
            </View>
          )}

          {/* Top Losers */}
          {marketSummary.topLosers.length > 0 && (
            <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
                En Çok Düşenler
              </Text>
              {marketSummary.topLosers.slice(0, 3).map(renderMarketItem)}
            </View>
          )}

          {/* Most Active */}
          {marketSummary.mostActive.length > 0 && (
            <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
                En Aktifler
              </Text>
              {marketSummary.mostActive.slice(0, 3).map(renderMarketItem)}
            </View>
          )}
        </>
      )}
    </ScrollView>
  );

  const renderWatchlistTab = () => (
    <ScrollView style={styles.tabContent}>
      {watchlistData.length > 0 ? (
        watchlistData.map(renderMarketItem)
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="star-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
            Takip listesi boş
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
            Piyasa özeti sekmesinden hisse senetlerini takip listesine ekleyebilirsiniz
          </Text>
        </View>
      )}
    </ScrollView>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Piyasa verileri yükleniyor...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Piyasa Verileri
        </Text>
        <TouchableOpacity onPress={onRefresh}>
          <Ionicons name="refresh" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'summary' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setSelectedTab('summary')}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'summary' ? '#FFFFFF' : theme.colors.text }
          ]}>
            Piyasa Özeti
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'watchlist' && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setSelectedTab('watchlist')}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'watchlist' ? '#FFFFFF' : theme.colors.text }
          ]}>
            Takip Listesi ({watchlist.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {selectedTab === 'summary' ? renderSummaryTab() : renderWatchlistTab()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    padding: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  marketItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  marketItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  marketItemInfo: {
    flex: 1,
  },
  marketItemSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  marketItemName: {
    fontSize: 14,
    marginTop: 2,
  },
  marketItemData: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  marketItemPrice: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  marketItemChange: {
    fontSize: 14,
    marginTop: 2,
  },
  marketItemVolume: {
    alignItems: 'flex-end',
  },
  marketItemVolumeLabel: {
    fontSize: 12,
  },
  marketItemVolumeValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
});

export default MarketDataScreen;
