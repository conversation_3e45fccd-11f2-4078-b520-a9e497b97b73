// Add Debt Screen - Borç ekleme ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import DatePicker from '../../components/DatePicker';

const AddDebtScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [formData, setFormData] = useState({
    name: '',
    creditor: '',
    totalAmount: '',
    remainingAmount: '',
    interestRate: '',
    monthlyPayment: '',
    dueDate: new Date(),
    type: 'credit_card' as 'credit_card' | 'loan' | 'mortgage' | 'personal' | 'other',
  });

  const [isLoading, setIsLoading] = useState(false);

  const debtTypes = [
    { id: 'credit_card', name: '<PERSON><PERSON><PERSON>', icon: 'card', color: theme.colors.error },
    { id: 'loan', name: '<PERSON><PERSON>i', icon: 'cash', color: theme.colors.warning },
    { id: 'mortgage', name: 'Konut Kredisi', icon: 'home', color: '#3B82F6' },
    { id: 'personal', name: 'Kişisel Borç', icon: 'person', color: '#8B5CF6' },
    { id: 'other', name: 'Diğer', icon: 'document', color: theme.colors.textSecondary },
  ];

  const handleSave = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Borç adı gerekli.');
      return;
    }

    if (!formData.creditor.trim()) {
      Alert.alert('Hata', 'Alacaklı adı gerekli.');
      return;
    }

    const totalAmount = parseFloat(formData.totalAmount.replace(',', '.'));
    if (isNaN(totalAmount) || totalAmount <= 0) {
      Alert.alert('Hata', 'Geçerli bir toplam tutar girin.');
      return;
    }

    const remainingAmount = parseFloat(formData.remainingAmount.replace(',', '.'));
    if (isNaN(remainingAmount) || remainingAmount < 0) {
      Alert.alert('Hata', 'Geçerli bir kalan tutar girin.');
      return;
    }

    if (remainingAmount > totalAmount) {
      Alert.alert('Hata', 'Kalan tutar toplam tutardan büyük olamaz.');
      return;
    }

    const interestRate = parseFloat(formData.interestRate.replace(',', '.'));
    if (isNaN(interestRate) || interestRate < 0) {
      Alert.alert('Hata', 'Geçerli bir faiz oranı girin.');
      return;
    }

    const monthlyPayment = parseFloat(formData.monthlyPayment.replace(',', '.'));
    if (isNaN(monthlyPayment) || monthlyPayment <= 0) {
      Alert.alert('Hata', 'Geçerli bir aylık ödeme tutarı girin.');
      return;
    }

    setIsLoading(true);

    try {
      // TODO: API çağrısı
      console.log('Adding debt:', {
        name: formData.name.trim(),
        creditor: formData.creditor.trim(),
        totalAmount,
        remainingAmount,
        interestRate,
        monthlyPayment,
        dueDate: formData.dueDate.toISOString(),
        type: formData.type,
      });

      Alert.alert('Başarılı', 'Borç başarıyla eklendi.', [
        { text: 'Tamam', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Hata', error || 'Borç eklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (text: string) => {
    const cleaned = text.replace(/[^0-9.,]/g, '');
    return cleaned;
  };

  const calculateProgress = () => {
    const total = parseFloat(formData.totalAmount.replace(',', '.')) || 0;
    const remaining = parseFloat(formData.remainingAmount.replace(',', '.')) || 0;
    return total > 0 ? ((total - remaining) / total) * 100 : 0;
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 50,
    },
    currencySymbol: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginRight: 8,
    },
    amountInput: {
      flex: 1,
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    typeScroll: {
      flexDirection: 'row',
    },
    typeButton: {
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      borderWidth: 1,
      marginRight: 8,
      backgroundColor: theme.colors.surface,
      minWidth: 80,
    },
    typeButtonActive: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    typeText: {
      fontSize: 12,
      color: theme.colors.text,
      marginTop: 4,
      textAlign: 'center',
    },
    previewContainer: {
      marginBottom: 20,
    },
    previewTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    previewCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    previewHeader: {
      marginBottom: 16,
    },
    previewName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    previewCreditor: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    progressContainer: {
      marginBottom: 16,
    },
    progressInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    progressAmount: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    progressPercentage: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.success,
    },
    progressBar: {
      height: 8,
      backgroundColor: theme.colors.border,
      borderRadius: 4,
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    previewStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    statItem: {
      alignItems: 'center',
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    statValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginBottom: 40,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        {/* Debt Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Borç Adı *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Örn: ABC Bank Kredi Kartı"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            autoFocus
          />
        </View>

        {/* Creditor */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Alacaklı *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Örn: ABC Bank"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.creditor}
            onChangeText={(text) => setFormData({ ...formData, creditor: text })}
          />
        </View>

        {/* Total Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Toplam Tutar *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.totalAmount}
              onChangeText={(text) => setFormData({ ...formData, totalAmount: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Remaining Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Kalan Tutar *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.remainingAmount}
              onChangeText={(text) => setFormData({ ...formData, remainingAmount: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Interest Rate */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Faiz Oranı (%) *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>%</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.interestRate}
              onChangeText={(text) => setFormData({ ...formData, interestRate: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Monthly Payment */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Aylık Ödeme *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.monthlyPayment}
              onChangeText={(text) => setFormData({ ...formData, monthlyPayment: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Due Date */}
        <DatePicker
          label="Sonraki Ödeme Tarihi *"
          value={formData.dueDate}
          onDateChange={(date) => setFormData({ ...formData, dueDate: date })}
          minimumDate={new Date()}
        />

        {/* Debt Type Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Borç Türü</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeScroll}>
            {debtTypes.map((type) => (
              <TouchableOpacity
                key={type.id}
                style={[
                  styles.typeButton,
                  formData.type === type.id && styles.typeButtonActive,
                  { borderColor: type.color }
                ]}
                onPress={() => setFormData({ ...formData, type: type.id as any })}
              >
                <Ionicons name={type.icon as any} size={20} color={type.color} />
                <Text style={[
                  styles.typeText,
                  formData.type === type.id && { color: type.color }
                ]}>
                  {type.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Debt Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Borç Özeti</Text>
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewName}>{formData.name || 'Borç Adı'}</Text>
              <Text style={styles.previewCreditor}>{formData.creditor || 'Alacaklı'}</Text>
            </View>

            <View style={styles.progressContainer}>
              <View style={styles.progressInfo}>
                <Text style={styles.progressAmount}>
                  ₺{formData.remainingAmount || '0'} / ₺{formData.totalAmount || '0'}
                </Text>
                <Text style={styles.progressPercentage}>
                  {calculateProgress().toFixed(0)}% ödendi
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min(calculateProgress(), 100)}%`,
                      backgroundColor: theme.colors.success
                    }
                  ]}
                />
              </View>
            </View>

            <View style={styles.previewStats}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Faiz Oranı</Text>
                <Text style={styles.statValue}>%{formData.interestRate || '0'}</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Aylık Ödeme</Text>
                <Text style={styles.statValue}>₺{formData.monthlyPayment || '0'}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Ekleniyor...' : 'Borç Ekle'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AddDebtScreen;
