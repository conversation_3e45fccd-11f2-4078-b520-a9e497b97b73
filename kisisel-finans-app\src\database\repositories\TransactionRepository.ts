// Transaction Repository - İşlem veritabanı operasyonları

import DatabaseManager from '../DatabaseManager';
import {
  Transaction,
  IncomeTransaction,
  ExpenseTransaction,
  TransactionFilter,
  TransactionSort,
  Pagination,
  TransactionListResponse,
  TransactionSummary,
  TransactionType,
  TransactionStatus,
  CurrencyCode,
} from '../../types/transaction';

class TransactionRepository {
  private static instance: TransactionRepository;
  private dbManager = DatabaseManager;

  static getInstance(): TransactionRepository {
    if (!TransactionRepository.instance) {
      TransactionRepository.instance = new TransactionRepository();
    }
    return TransactionRepository.instance;
  }

  /**
   * Yeni işlem oluştur
   */
  async create(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log('💾 Creating transaction:', {
        type: transaction.type,
        amount: transaction.amount,
        category: transaction.category,
        description: transaction.description
      });

      const db = this.dbManager.getDatabase();
      const id = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();

      // Önce transaction'ı kaydet
      await db.runAsync(
        `INSERT INTO transactions (
          id, user_id, type, amount, currency, category, subcategory, description,
          merchant, payment_method, date, budget_id, category_id, location_latitude, location_longitude,
          location_address, location_place_name, receipt_id, parent_transaction_id,
          recurrence_frequency, recurrence_interval, recurrence_end_date, status,
          is_business_expense, is_deductible, tax_amount, created_at, updated_at,
          is_deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          transaction.userId,
          transaction.type,
          transaction.amount,
          transaction.currency,
          transaction.category,
          (transaction as ExpenseTransaction).subcategory || null,
          transaction.description || null,
          (transaction as ExpenseTransaction).merchant || (transaction as IncomeTransaction).source || null,
          transaction.paymentMethod || null,
          transaction.date,
          transaction.budgetId || null, // hibrit sistem için budget_id
          (transaction as any).categoryId || null, // hibrit sistem için category_id
          transaction.location?.latitude || null,
          transaction.location?.longitude || null,
          transaction.location?.address || null,
          transaction.location?.placeName || null,
          null, // receipt_id - sonra update edilecek
          transaction.parentTransactionId || null,
          transaction.recurrence?.frequency || null,
          transaction.recurrence?.interval || null,
          transaction.recurrence?.endDate || null,
          transaction.status,
          (transaction as ExpenseTransaction).isBusinessExpense ? 1 : 0,
          (transaction as ExpenseTransaction).isDeductible ? 1 : 0,
          (transaction as IncomeTransaction).taxAmount || 0,
          now,
          now,
          transaction.isDeleted ? 1 : 0,
        ]
      );

      // Sonra receipt'i kaydet ve transaction'ı update et
      if (transaction.receipt) {
        const receiptId = await this.createReceipt(transaction.receipt, id);
        await db.runAsync(
          'UPDATE transactions SET receipt_id = ? WHERE id = ?',
          [receiptId, id]
        );
        console.log('📄 Receipt saved with ID:', receiptId);
      }

      // Etiketleri ekle
      if (transaction.tags && transaction.tags.length > 0) {
        await this.addTransactionTags(id, transaction.tags.map(tag => tag.id));
      }

      console.log(`✅ Transaction created: ${id}`);
      return id;
    } catch (error) {
      console.error('❌ Error creating transaction:', error);
      throw error;
    }
  }

  /**
   * İşlemi güncelle
   */
  async update(id: string, updates: Partial<Transaction>): Promise<boolean> {
    try {
      const db = this.dbManager.getDatabase();
      const now = new Date().toISOString();

      // Güncelleme alanlarını hazırla
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updates.amount !== undefined) {
        updateFields.push('amount = ?');
        updateValues.push(updates.amount);
      }
      if (updates.currency !== undefined) {
        updateFields.push('currency = ?');
        updateValues.push(updates.currency);
      }
      if (updates.category !== undefined) {
        updateFields.push('category = ?');
        updateValues.push(updates.category);
      }
      if ((updates as ExpenseTransaction).subcategory !== undefined) {
        updateFields.push('subcategory = ?');
        updateValues.push((updates as ExpenseTransaction).subcategory);
      }
      if (updates.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(updates.description);
      }
      if (updates.paymentMethod !== undefined) {
        updateFields.push('payment_method = ?');
        updateValues.push(updates.paymentMethod);
      }
      if (updates.date !== undefined) {
        updateFields.push('date = ?');
        updateValues.push(updates.date);
      }
      if (updates.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(updates.status);
      }
      if (updates.isDeleted !== undefined) {
        updateFields.push('is_deleted = ?');
        updateValues.push(updates.isDeleted ? 1 : 0);
        if (updates.isDeleted) {
          updateFields.push('deleted_at = ?');
          updateValues.push(now);
        }
      }

      if (updateFields.length === 0) {
        return false;
      }

      updateFields.push('updated_at = ?');
      updateValues.push(now);
      updateValues.push(id);

      const result = await db.runAsync(
        `UPDATE transactions SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // Etiketleri güncelle
      if (updates.tags !== undefined) {
        await this.removeTransactionTags(id);
        if (updates.tags.length > 0) {
          await this.addTransactionTags(id, updates.tags.map(tag => tag.id));
        }
      }

      console.log(`✅ Transaction updated: ${id}`);
      return result.changes > 0;
    } catch (error) {
      console.error('❌ Error updating transaction:', error);
      throw error;
    }
  }

  /**
   * İşlemi sil (soft delete)
   */
  async delete(id: string): Promise<boolean> {
    try {
      const db = this.dbManager.getDatabase();
      const now = new Date().toISOString();

      const result = await db.runAsync(
        'UPDATE transactions SET is_deleted = 1, deleted_at = ?, updated_at = ? WHERE id = ?',
        [now, now, id]
      );

      console.log(`✅ Transaction deleted: ${id}`);
      return result.changes > 0;
    } catch (error) {
      console.error('❌ Error deleting transaction:', error);
      throw error;
    }
  }

  /**
   * İşlemi geri getir
   */
  async restore(id: string): Promise<boolean> {
    try {
      const db = this.dbManager.getDatabase();
      const now = new Date().toISOString();

      const result = await db.runAsync(
        'UPDATE transactions SET is_deleted = 0, deleted_at = NULL, updated_at = ? WHERE id = ?',
        [now, id]
      );

      console.log(`✅ Transaction restored: ${id}`);
      return result.changes > 0;
    } catch (error) {
      console.error('❌ Error restoring transaction:', error);
      throw error;
    }
  }

  /**
   * İşlemi kalıcı olarak sil
   */
  async hardDelete(id: string): Promise<boolean> {
    try {
      const db = this.dbManager.getDatabase();

      // Önce etiketleri sil
      await this.removeTransactionTags(id);

      // Sonra işlemi sil
      const result = await db.runAsync('DELETE FROM transactions WHERE id = ?', [id]);

      console.log(`✅ Transaction permanently deleted: ${id}`);
      return result.changes > 0;
    } catch (error) {
      console.error('❌ Error permanently deleting transaction:', error);
      throw error;
    }
  }

  /**
   * ID ile işlem al
   */
  async getById(id: string): Promise<Transaction | null> {
    try {
      const db = this.dbManager.getDatabase();

      const row = await db.getFirstAsync<any>(
        `SELECT * FROM transactions WHERE id = ? AND is_deleted = 0`,
        [id]
      );

      if (!row) return null;

      // Etiketleri al
      const tags = await this.getTransactionTags(id);

      return await this.mapRowToTransaction(row, tags);
    } catch (error) {
      console.error('❌ Error getting transaction by ID:', error);
      throw error;
    }
  }

  /**
   * Test user migration - eski işlemleri yeni test user ID'sine taşı
   */
  async migrateTestUserTransactions(): Promise<void> {
    try {
      const db = this.dbManager.getDatabase();

      // Eski test user ID'lerini yeni sabit ID'ye migrate et
      await db.runAsync(
        `UPDATE transactions
         SET user_id = 'test_user_123'
         WHERE user_id LIKE '174862%' AND user_id != 'test_user_123'`
      );

      console.log('✅ Test user transactions migrated to test_user_123');
    } catch (error) {
      console.error('❌ Error migrating test user transactions:', error);
    }
  }

  /**
   * Kullanıcının tüm işlemlerini al (basit versiyon)
   */
  async getByUserId(userId: string): Promise<Transaction[]> {
    try {
      const db = this.dbManager.getDatabase();

      // Test user için migration yap
      if (userId === 'test_user_123') {
        await this.migrateTestUserTransactions();
      }

      const rows = await db.getAllAsync<any>(
        `SELECT * FROM transactions
         WHERE user_id = ? AND is_deleted = 0
         ORDER BY date DESC`,
        [userId]
      );

      console.log(`📊 Raw database rows: ${rows.length}`);
      console.log('📋 Sample rows:', rows.slice(0, 2).map(r => ({
        id: r.id,
        type: r.type,
        amount: r.amount,
        category: r.category,
        description: r.description
      })));

      // İşlemleri map et
      const transactions: Transaction[] = [];
      for (const row of rows) {
        const tags = await this.getTransactionTags(row.id);
        const transaction = await this.mapRowToTransaction(row, tags);
        transactions.push(transaction);
        console.log(`🔄 Mapped transaction: ${transaction.type} - ${transaction.amount} - ${transaction.description}`);
      }

      console.log(`✅ Retrieved ${transactions.length} transactions for user ${userId}`);
      return transactions;
    } catch (error) {
      console.error('❌ Error getting transactions by user ID:', error);
      throw error;
    }
  }

  /**
   * Filtrelenmiş işlem listesi al
   */
  async getList(
    userId: string,
    filter?: TransactionFilter,
    sort?: TransactionSort,
    pagination?: Pagination
  ): Promise<TransactionListResponse> {
    try {
      const db = this.dbManager.getDatabase();

      // WHERE koşullarını oluştur
      const whereConditions: string[] = ['user_id = ?', 'is_deleted = 0'];
      const whereValues: any[] = [userId];

      if (filter) {
        if (filter.type) {
          whereConditions.push('type = ?');
          whereValues.push(filter.type);
        }
        if (filter.categories && filter.categories.length > 0) {
          // Hem category (string) hem de category_id (ID) ile eşleştir
          const placeholders = filter.categories.map(() => '?').join(', ');
          whereConditions.push(`(category IN (${placeholders}) OR category_id IN (${placeholders}))`);
          whereValues.push(...filter.categories, ...filter.categories);
        }
        if (filter.paymentMethods && filter.paymentMethods.length > 0) {
          whereConditions.push(`payment_method IN (${filter.paymentMethods.map(() => '?').join(', ')})`);
          whereValues.push(...filter.paymentMethods);
        }
        if (filter.dateRange) {
          whereConditions.push('date >= ? AND date <= ?');
          whereValues.push(filter.dateRange.start, filter.dateRange.end);
        }
        if (filter.amountRange) {
          if (filter.amountRange.min !== undefined) {
            whereConditions.push('amount >= ?');
            whereValues.push(filter.amountRange.min);
          }
          if (filter.amountRange.max !== undefined) {
            whereConditions.push('amount <= ?');
            whereValues.push(filter.amountRange.max);
          }
        }
        if (filter.currencies && filter.currencies.length > 0) {
          whereConditions.push(`currency IN (${filter.currencies.map(() => '?').join(', ')})`);
          whereValues.push(...filter.currencies);
        }
        if (filter.status && filter.status.length > 0) {
          whereConditions.push(`status IN (${filter.status.map(() => '?').join(', ')})`);
          whereValues.push(...filter.status);
        }
        if (filter.hasReceipt !== undefined) {
          if (filter.hasReceipt) {
            whereConditions.push('receipt_id IS NOT NULL');
          } else {
            whereConditions.push('receipt_id IS NULL');
          }
        }
        if (filter.hasLocation !== undefined) {
          if (filter.hasLocation) {
            whereConditions.push('location_latitude IS NOT NULL AND location_longitude IS NOT NULL');
          } else {
            whereConditions.push('location_latitude IS NULL OR location_longitude IS NULL');
          }
        }
        if (filter.searchQuery) {
          whereConditions.push('(description LIKE ? OR merchant LIKE ? OR category LIKE ?)');
          const searchTerm = `%${filter.searchQuery}%`;
          whereValues.push(searchTerm, searchTerm, searchTerm);
        }
      }

      const whereClause = whereConditions.join(' AND ');

      // ORDER BY koşulunu oluştur
      let orderClause = 'ORDER BY date DESC';
      if (sort) {
        const direction = sort.direction.toUpperCase();
        switch (sort.field) {
          case 'date':
            orderClause = `ORDER BY date ${direction}`;
            break;
          case 'amount':
            orderClause = `ORDER BY amount ${direction}`;
            break;
          case 'category':
            orderClause = `ORDER BY category ${direction}`;
            break;
          case 'createdAt':
            orderClause = `ORDER BY created_at ${direction}`;
            break;
        }
      }

      // LIMIT ve OFFSET
      let limitClause = '';
      if (pagination) {
        const offset = (pagination.page - 1) * pagination.limit;
        limitClause = `LIMIT ${pagination.limit} OFFSET ${offset}`;
      }

      // Toplam sayıyı al
      const countResult = await db.getFirstAsync<{ total: number }>(
        `SELECT COUNT(*) as total FROM transactions WHERE ${whereClause}`,
        whereValues
      );
      const total = countResult?.total || 0;

      // İşlemleri al
      const rows = await db.getAllAsync<any>(
        `SELECT * FROM transactions WHERE ${whereClause} ${orderClause} ${limitClause}`,
        whereValues
      );

      // İşlemleri map et
      const transactions: Transaction[] = [];
      for (const row of rows) {
        const tags = await this.getTransactionTags(row.id);
        transactions.push(await this.mapRowToTransaction(row, tags));
      }

      // Özet hesapla
      const summary = await this.calculateSummary(userId, filter);

      return {
        transactions,
        summary,
        pagination: {
          page: pagination?.page || 1,
          limit: pagination?.limit || transactions.length,
          total,
          hasMore: pagination ? (pagination.page * pagination.limit) < total : false,
        },
      };
    } catch (error) {
      console.error('❌ Error getting transaction list:', error);
      throw error;
    }
  }

  /**
   * İşlem özetini hesapla
   */
  private async calculateSummary(userId: string, filter?: TransactionFilter): Promise<TransactionSummary> {
    try {
      const db = this.dbManager.getDatabase();

      // WHERE koşullarını oluştur (getList ile aynı)
      const whereConditions: string[] = ['user_id = ?', 'is_deleted = 0'];
      const whereValues: any[] = [userId];

      // Filter koşullarını ekle (getList'teki ile aynı logic)
      // ... (filter logic buraya eklenecek)

      const whereClause = whereConditions.join(' AND ');

      // Gelir toplamı
      const incomeResult = await db.getFirstAsync<{ total: number; count: number }>(
        `SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count
         FROM transactions WHERE ${whereClause} AND type = 'income'`,
        whereValues
      );

      // Gider toplamı
      const expenseResult = await db.getFirstAsync<{ total: number; count: number }>(
        `SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count
         FROM transactions WHERE ${whereClause} AND type = 'expense'`,
        whereValues
      );

      const totalIncome = incomeResult?.total || 0;
      const totalExpense = expenseResult?.total || 0;
      const transactionCount = (incomeResult?.count || 0) + (expenseResult?.count || 0);

      // Kategori dağılımı
      const categoryBreakdown = {
        income: {} as Record<string, number>,
        expense: {} as Record<string, number>,
      };

      const categoryResults = await db.getAllAsync<{ category: string; type: string; total: number }>(
        `SELECT category, type, SUM(amount) as total
         FROM transactions WHERE ${whereClause}
         GROUP BY category, type`,
        whereValues
      );

      for (const row of categoryResults) {
        if (row.type === 'income') {
          categoryBreakdown.income[row.category] = row.total;
        } else {
          categoryBreakdown.expense[row.category] = row.total;
        }
      }

      return {
        totalIncome,
        totalExpense,
        netAmount: totalIncome - totalExpense,
        transactionCount,
        currency: 'TRY', // Default currency, could be dynamic
        period: {
          start: filter?.dateRange?.start || '',
          end: filter?.dateRange?.end || '',
        },
        categoryBreakdown,
      };
    } catch (error) {
      console.error('❌ Error calculating summary:', error);
      throw error;
    }
  }

  /**
   * İşlem etiketlerini al
   */
  private async getTransactionTags(transactionId: string): Promise<any[]> {
    try {
      const db = this.dbManager.getDatabase();

      const tags = await db.getAllAsync<any>(
        `SELECT t.* FROM tags t
         INNER JOIN transaction_tags tt ON t.id = tt.tag_id
         WHERE tt.transaction_id = ?`,
        [transactionId]
      );

      return tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        color: tag.color,
        icon: tag.icon,
      }));
    } catch (error) {
      console.error('❌ Error getting transaction tags:', error);
      return [];
    }
  }

  /**
   * Receipt oluştur
   */
  private async createReceipt(receipt: any, transactionId: string): Promise<string> {
    try {
      const db = this.dbManager.getDatabase();

      await db.runAsync(
        `INSERT INTO receipts (
          id, transaction_id, image_uri, thumbnail_uri, file_size,
          merchant_name, total_amount, receipt_date, ocr_confidence,
          ocr_data, uploaded_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          receipt.id,
          transactionId,
          receipt.imageUri,
          receipt.thumbnailUri || null,
          receipt.fileSize || null,
          receipt.ocrData?.merchantName || null,
          receipt.ocrData?.totalAmount || null,
          receipt.ocrData?.date || null,
          receipt.ocrData?.confidence || null,
          receipt.ocrData ? JSON.stringify(receipt.ocrData) : null,
          receipt.uploadedAt,
        ]
      );

      console.log(`✅ Receipt created: ${receipt.id}`);
      return receipt.id;
    } catch (error) {
      console.error('❌ Error creating receipt:', error);
      throw error;
    }
  }

  /**
   * İşlem etiketlerini ekle
   */
  private async addTransactionTags(transactionId: string, tagIds: string[]): Promise<void> {
    try {
      const db = this.dbManager.getDatabase();

      for (const tagId of tagIds) {
        await db.runAsync(
          'INSERT OR IGNORE INTO transaction_tags (transaction_id, tag_id) VALUES (?, ?)',
          [transactionId, tagId]
        );
      }
    } catch (error) {
      console.error('❌ Error adding transaction tags:', error);
      throw error;
    }
  }

  /**
   * İşlem etiketlerini kaldır
   */
  private async removeTransactionTags(transactionId: string): Promise<void> {
    try {
      const db = this.dbManager.getDatabase();

      await db.runAsync(
        'DELETE FROM transaction_tags WHERE transaction_id = ?',
        [transactionId]
      );
    } catch (error) {
      console.error('❌ Error removing transaction tags:', error);
      throw error;
    }
  }

  /**
   * Receipt bilgisini al
   */
  private async getReceiptById(receiptId: string): Promise<any | null> {
    try {
      const db = this.dbManager.getDatabase();

      const receipt = await db.getFirstAsync<any>(
        'SELECT * FROM receipts WHERE id = ?',
        [receiptId]
      );

      if (!receipt) return null;

      return {
        id: receipt.id,
        imageUri: receipt.image_uri,
        thumbnailUri: receipt.thumbnail_uri,
        fileSize: receipt.file_size,
        uploadedAt: receipt.uploaded_at,
        ocrData: receipt.ocr_data ? JSON.parse(receipt.ocr_data) : undefined,
      };
    } catch (error) {
      console.error('❌ Error getting receipt:', error);
      return null;
    }
  }

  /**
   * Database row'unu Transaction objesine çevir
   */
  private async mapRowToTransaction(row: any, tags: any[]): Promise<Transaction> {
    // Receipt bilgisini al
    const receipt = row.receipt_id ? await this.getReceiptById(row.receipt_id) : undefined;

    const baseTransaction = {
      id: row.id,
      userId: row.user_id,
      amount: row.amount,
      currency: row.currency as CurrencyCode,
      date: row.date,
      description: row.description,
      tags,
      location: row.location_latitude && row.location_longitude ? {
        latitude: row.location_latitude,
        longitude: row.location_longitude,
        address: row.location_address,
        placeName: row.location_place_name,
      } : undefined,
      receipt,
      paymentMethod: row.payment_method,
      recurrence: row.recurrence_frequency ? {
        frequency: row.recurrence_frequency,
        interval: row.recurrence_interval,
        endDate: row.recurrence_end_date,
      } : undefined,
      parentTransactionId: row.parent_transaction_id,
      status: row.status as TransactionStatus,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      syncedAt: row.synced_at,
      isDeleted: row.is_deleted === 1,
      deletedAt: row.deleted_at,
    };

    if (row.type === 'income') {
      return {
        ...baseTransaction,
        type: 'income',
        category: row.category,
        categoryId: row.category_id,
        source: row.merchant,
        taxable: row.tax_amount > 0,
        taxAmount: row.tax_amount,
      } as IncomeTransaction;
    } else {
      return {
        ...baseTransaction,
        type: 'expense',
        category: row.category,
        categoryId: row.category_id,
        subcategory: row.subcategory,
        merchant: row.merchant,
        isBusinessExpense: row.is_business_expense === 1,
        isDeductible: row.is_deductible === 1,
      } as ExpenseTransaction;
    }
  }
}

export default TransactionRepository.getInstance();
