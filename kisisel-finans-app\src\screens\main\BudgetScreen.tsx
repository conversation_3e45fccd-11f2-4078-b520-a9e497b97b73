// RFC-003 Gelişmiş Bütçe Yönetim Ekranı

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { Ionicons } from '@expo/vector-icons';
import BudgetService from '../../services/BudgetService';

const BudgetScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();

  // State
  const [budgets, setBudgets] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Component mount debug
  console.log('🚀 BudgetScreen component mounted!');

  // Load budgets
  const loadBudgets = useCallback(async () => {
    console.log('🔥 loadBudgets function called!');
    try {
      console.log('📊 Loading budgets...');
      console.log('📊 BudgetService:', BudgetService);

      if (!user?.id) {
        console.log('❌ No user ID available');
        console.log('❌ User object is:', user);
        return;
      }

      console.log('👤 Using user ID:', user.id);
      console.log('👤 User object:', JSON.stringify(user, null, 2));
      const userBudgets = await BudgetService.getUserBudgets(user.id);
      console.log('📊 Raw userBudgets result:', userBudgets);

      // Debug: Tüm bütçeleri kontrol et
      const DatabaseManager = require('../../database/DatabaseManager').default;
      const db = DatabaseManager.getDatabase();
      const allBudgets = await db.getAllAsync('SELECT * FROM budgets');
      console.log('📊 All budgets in database:', allBudgets);

      // Debug: User ID karşılaştırması
      if (allBudgets.length > 0) {
        const dbUserId = allBudgets[0].user_id;
        console.log('🔍 DB User ID:', dbUserId);
        console.log('🔍 Current User ID:', user.id);
        console.log('🔍 IDs match:', dbUserId === user.id);

        if (dbUserId !== user.id) {
          console.log('⚠️ USER ID MISMATCH! This is why no budgets are showing.');
          console.log('🔧 Fixing user_id mismatch...');

          // Bütçenin user_id'sini güncelle
          await db.runAsync(
            'UPDATE budgets SET user_id = ? WHERE user_id = ?',
            [user.id, dbUserId]
          );

          // Budget categories'i de güncelle
          await db.runAsync(
            'UPDATE budget_categories SET user_id = ? WHERE budget_id IN (SELECT id FROM budgets WHERE user_id = ?)',
            [user.id, user.id]
          );

          console.log('✅ User ID fixed! Reloading budgets...');

          // Tekrar yükle
          const fixedBudgets = await BudgetService.getUserBudgets(user.id);
          setBudgets(fixedBudgets);
          console.log(`📊 After fix: Loaded ${fixedBudgets.length} budgets`);
          return;
        }
      }

      setBudgets(userBudgets);
      console.log(`📊 Loaded ${userBudgets.length} budgets`);

      // Debug: Her bütçenin detaylarını logla
      userBudgets.forEach((budget, index) => {
        console.log(`📊 Budget ${index + 1}:`, {
          name: budget.name,
          total_planned: budget.total_planned,
          total_spent: budget.total_spent,
          typeof_total_planned: typeof budget.total_planned,
          typeof_total_spent: typeof budget.total_spent,
          category_count: budget.category_count
        });
      });

    } catch (error) {
      console.error('❌ Error loading budgets:', error);
      console.error('❌ Error details:', error);
      Alert.alert('Hata', 'Bütçeler yüklenirken bir hata oluştu.');
    } finally {
      console.log('📊 loadBudgets finished, setting loading to false');
      setLoading(false);
    }
  }, [user]);

  // Refresh budgets
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadBudgets();
    setRefreshing(false);
  }, [loadBudgets]);

  // Load budgets on screen focus
  useFocusEffect(
    useCallback(() => {
      loadBudgets();
    }, [loadBudgets])
  );

  // Delete budget
  const handleDeleteBudget = useCallback((budgetId: string, budgetName: string) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budgetName}" bütçesini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await BudgetService.deleteBudget(budgetId);
              Alert.alert('Başarılı', 'Bütçe başarıyla silindi.');
              loadBudgets();
            } catch (error) {
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  }, [loadBudgets]);

  // Navigate to budget wizard
  const handleCreateBudget = useCallback(() => {
    navigation.navigate('BudgetWizard' as never);
  }, [navigation]);

  // Navigate to budget templates
  const handleBudgetTemplates = useCallback(() => {
    navigation.navigate('BudgetTemplates' as never);
  }, [navigation]);

  // Navigate to budget detail
  const handleBudgetPress = useCallback((budgetId: string) => {
    navigation.navigate('BudgetDetail' as never, { budgetId } as never);
  }, [navigation]);

  const formatCurrency = useCallback((amount: number | string, currency: string = 'TRY') => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount || 0;
    const symbol = currency === 'TRY' ? '₺' : currency === 'USD' ? '$' : '€';
    return `${numAmount.toLocaleString('tr-TR')} ${symbol}`;
  }, []);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  }, []);

  const getPeriodLabel = useCallback((period: string) => {
    switch (period) {
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'quarterly': return '3 Aylık';
      case 'annually': return 'Yıllık';
      default: return period;
    }
  }, []);

  const renderBudget = ({ item }: { item: any }) => {
    const totalPlanned = item.total_planned || 0;
    const totalSpent = item.total_spent || 0;
    const remaining = totalPlanned - totalSpent;
    const progressPercentage = totalPlanned > 0 ? Math.min((totalSpent / totalPlanned) * 100, 100) : 0;

    const getProgressColor = () => {
      if (progressPercentage >= 100) return theme.colors.error;
      if (progressPercentage >= 80) return theme.colors.warning;
      return theme.colors.success;
    };

    return (
      <TouchableOpacity
        style={[styles.budgetItem, { backgroundColor: theme.colors.surface }]}
        onPress={() => handleBudgetPress(item.id)}
      >
        {/* Header */}
        <View style={styles.budgetHeader}>
          <View style={styles.budgetInfo}>
            <Text style={[styles.budgetName, { color: theme.colors.text }]}>
              {item.name}
            </Text>
            <Text style={[styles.budgetDate, { color: theme.colors.textSecondary }]}>
              {formatDate(item.start_date)} - {formatDate(item.end_date)}
            </Text>
          </View>

          <View style={styles.budgetActions}>
            <Text style={[styles.budgetPeriod, {
              color: theme.colors.primary,
              backgroundColor: theme.colors.primary + '20'
            }]}>
              {getPeriodLabel(item.period)}
            </Text>

            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteBudget(item.id, item.name)}
            >
              <Ionicons name="trash-outline" size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Progress */}
        <View style={styles.budgetProgress}>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${progressPercentage}%`,
                  backgroundColor: getProgressColor()
                }
              ]}
            />
          </View>
          <Text style={[styles.progressText, { color: theme.colors.text }]}>
            {progressPercentage.toFixed(0)}%
          </Text>
        </View>

        {/* Amounts */}
        <View style={styles.budgetAmounts}>
          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
              Planlanan
            </Text>
            <Text style={[styles.amountValue, { color: theme.colors.primary }]}>
              {formatCurrency(totalPlanned, item.currency)}
            </Text>
          </View>

          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
              Harcanan
            </Text>
            <Text style={[styles.amountValue, { color: theme.colors.error }]}>
              {formatCurrency(totalSpent, item.currency)}
            </Text>
          </View>

          <View style={styles.amountItem}>
            <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
              Kalan
            </Text>
            <Text style={[
              styles.amountValue,
              { color: remaining >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {formatCurrency(remaining, item.currency)}
            </Text>
          </View>
        </View>

        {/* Categories Count */}
        <View style={styles.budgetFooter}>
          <View style={styles.categoryInfo}>
            <Ionicons name="grid-outline" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.categoryCount, { color: theme.colors.textSecondary }]}>
              {item.category_count} kategori
            </Text>
          </View>

          <Text style={[styles.budgetId, { color: theme.colors.textSecondary }]}>
            ID: {item.id.slice(-8)}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          Bütçeler yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Bütçelerim</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.templateButton, { backgroundColor: theme.colors.success }]}
            onPress={handleBudgetTemplates}
          >
            <Ionicons name="flash" size={20} color={theme.colors.surface} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleCreateBudget}
          >
            <Ionicons name="add" size={24} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Budget Summary */}
      {budgets.length > 0 && (
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.summaryHeader}>
              <Ionicons name="pie-chart" size={24} color={theme.colors.primary} />
              <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
                Toplam Bütçe Özeti
              </Text>
            </View>

            <View style={styles.summaryStats}>
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.primary }]}>
                  {formatCurrency(budgets.reduce((sum, b) => sum + (b.total_planned || 0), 0))}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Toplam Planlanan
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.text }]}>
                  {budgets.length}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Aktif Bütçe
                </Text>
              </View>

              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.success }]}>
                  {budgets.reduce((sum, b) => sum + (b.category_count || 0), 0)}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Toplam Kategori
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Budgets List */}
      <FlatList
        data={budgets}
        renderItem={renderBudget}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="wallet-outline" size={80} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
              Henüz bütçe oluşturmadınız
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
              Finansal hedeflerinizi takip etmek için ilk bütçenizi oluşturun
            </Text>
            <View style={styles.emptyActions}>
              <TouchableOpacity
                style={[styles.templateEmptyButton, { backgroundColor: theme.colors.success }]}
                onPress={handleBudgetTemplates}
              >
                <Ionicons name="flash" size={20} color={theme.colors.surface} />
                <Text style={[styles.templateEmptyButtonText, { color: theme.colors.surface }]}>
                  Şablondan Oluştur
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.createButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleCreateBudget}
              >
                <Ionicons name="add" size={20} color={theme.colors.surface} />
                <Text style={[styles.createButtonText, { color: theme.colors.surface }]}>
                  Sıfırdan Oluştur
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  templateButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  budgetItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  budgetInfo: {
    flex: 1,
  },
  budgetName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  budgetDate: {
    fontSize: 12,
  },
  budgetActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  budgetPeriod: {
    fontSize: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 4,
  },
  budgetProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  budgetAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  amountItem: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  budgetFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryCount: {
    fontSize: 12,
    marginLeft: 4,
  },
  budgetId: {
    fontSize: 10,
    fontFamily: 'monospace',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  templateEmptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
  },
  templateEmptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default BudgetScreen;
