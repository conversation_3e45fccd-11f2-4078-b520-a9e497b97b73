// RFC-003 Bütçe Planlama - Test Suite

import BudgetService from '../services/BudgetService';
import databaseManager from '../database/DatabaseManager';
import { CreateBudgetInput } from '../types/budget';

class BudgetServiceTest {
  
  /**
   * Test 1: Database Schema Migration
   */
  static async testDatabaseMigration(): Promise<void> {
    console.log('🧪 Testing Database Migration...');
    
    try {
      // Database'i başlat (migration otomatik çalışacak)
      await databaseManager.initialize();
      
      // Tabloların varlığını kontrol et
      const db = databaseManager.getDatabase();
      
      // budgets tablosu kontrolü
      const budgetsTable = await db.getFirstAsync(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='budgets'
      `);
      
      if (!budgetsTable) {
        throw new Error('❌ budgets table not found');
      }
      
      // budget_categories tablosu kontrolü
      const budgetCategoriesTable = await db.getFirstAsync(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='budget_categories'
      `);
      
      if (!budgetCategoriesTable) {
        throw new Error('❌ budget_categories table not found');
      }
      
      // Tablo yapısını kontrol et
      const budgetsColumns = await db.getAllAsync(`PRAGMA table_info(budgets)`);
      const expectedColumns = [
        'id', 'user_id', 'name', 'period', 'start_date', 'end_date',
        'total_income_target', 'total_expense_limit', 'savings_target',
        'currency', 'notes', 'template_name', 'copied_from_budget_id',
        'is_active', 'created_at', 'updated_at'
      ];
      
      for (const expectedCol of expectedColumns) {
        const found = budgetsColumns.find((col: any) => col.name === expectedCol);
        if (!found) {
          throw new Error(`❌ Column '${expectedCol}' not found in budgets table`);
        }
      }
      
      console.log('✅ Database migration test passed!');
      console.log(`📊 budgets table has ${budgetsColumns.length} columns`);
      
      // Database version kontrolü
      const versionResult = await db.getFirstAsync<{ user_version: number }>('PRAGMA user_version;');
      console.log(`📈 Database version: ${versionResult?.user_version}`);
      
    } catch (error) {
      console.error('❌ Database migration test failed:', error);
      throw error;
    }
  }

  /**
   * Test 2: Budget Creation
   */
  static async testBudgetCreation(): Promise<string> {
    console.log('🧪 Testing Budget Creation...');

    try {
      // Database'in initialize edildiğinden emin ol
      await databaseManager.initialize();

      // Test kategorilerini kontrol et ve gerekirse oluştur
      const db = databaseManager.getDatabase();

      // Yiyecek kategorisi
      let foodCategory = await db.getFirstAsync(
        'SELECT id FROM categories WHERE name = ? AND type = ?',
        ['Yiyecek', 'expense']
      );

      if (!foodCategory) {
        await db.runAsync(
          `INSERT INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            'food_test_cat',
            'test_user_1',
            'Yiyecek',
            'expense',
            'restaurant-outline',
            '#F44336',
            0,
            new Date().toISOString(),
            new Date().toISOString(),
          ]
        );
        foodCategory = { id: 'food_test_cat' };
      }

      // Ulaşım kategorisi
      let transportCategory = await db.getFirstAsync(
        'SELECT id FROM categories WHERE name = ? AND type = ?',
        ['Ulaşım', 'expense']
      );

      if (!transportCategory) {
        await db.runAsync(
          `INSERT INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            'transport_test_cat',
            'test_user_1',
            'Ulaşım',
            'expense',
            'car-outline',
            '#607D8B',
            0,
            new Date().toISOString(),
            new Date().toISOString(),
          ]
        );
        transportCategory = { id: 'transport_test_cat' };
      }

      const testBudgetInput: CreateBudgetInput = {
        name: 'Test Aylık Bütçe',
        period: 'monthly',
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        totalIncomeTarget: 10000,
        totalExpenseLimit: 8000,
        savingsTarget: 2000,
        currency: 'TRY',
        notes: 'Test bütçesi - RFC-003',
        categories: [
          {
            categoryId: foodCategory.id,
            plannedAmount: 2000,
            warningThreshold: 75,
            criticalThreshold: 90,
            limitThreshold: 100
          },
          {
            categoryId: transportCategory.id,
            plannedAmount: 1000,
            warningThreshold: 80,
            criticalThreshold: 95,
            limitThreshold: 110
          }
        ]
      };

      const budgetId = await BudgetService.createBudget('test_user_1', testBudgetInput);
      
      if (!budgetId) {
        throw new Error('❌ Budget creation returned null/undefined');
      }
      
      console.log(`✅ Budget created successfully with ID: ${budgetId}`);
      
      // Oluşturulan bütçeyi kontrol et
      const createdBudget = await BudgetService.getBudgetById(budgetId);
      if (!createdBudget) {
        throw new Error('❌ Created budget not found');
      }
      
      console.log(`📊 Budget name: ${createdBudget.name}`);
      console.log(`💰 Income target: ${createdBudget.totalIncomeTarget}`);
      console.log(`💸 Expense limit: ${createdBudget.totalExpenseLimit}`);
      console.log(`💎 Savings target: ${createdBudget.savingsTarget}`);
      
      // Kategori bütçelerini kontrol et
      const categories = await BudgetService.getBudgetCategories(budgetId);
      console.log(`📋 Created ${categories.length} budget categories`);
      
      for (const category of categories) {
        console.log(`  - Category: ${category.categoryId}, Planned: ${category.plannedAmount}`);
      }
      
      return budgetId;
    } catch (error) {
      console.error('❌ Budget creation test failed:', error);
      throw error;
    }
  }

  /**
   * Test 3: Budget Summary
   */
  static async testBudgetSummary(budgetId: string): Promise<void> {
    console.log('🧪 Testing Budget Summary...');

    try {
      await databaseManager.initialize();
      const summary = await BudgetService.getBudgetSummary(budgetId);
      
      if (!summary) {
        throw new Error('❌ Budget summary not found');
      }
      
      console.log('✅ Budget summary retrieved successfully!');
      console.log(`📊 Total planned: ${summary.totalPlanned}`);
      console.log(`💸 Total spent: ${summary.totalSpent}`);
      console.log(`💰 Total remaining: ${summary.totalRemaining}`);
      console.log(`📈 Overall progress: ${summary.overallProgress.toFixed(1)}%`);
      console.log(`📅 Days remaining: ${summary.daysRemaining}`);
      console.log(`📊 Status: ${summary.status}`);
      
      // Kategori detaylarını kontrol et
      console.log(`📋 Categories with details: ${summary.categories.length}`);
      for (const category of summary.categories) {
        console.log(`  - ${category.categoryName}: ${category.progress.toFixed(1)}% (${category.status})`);
      }
      
    } catch (error) {
      console.error('❌ Budget summary test failed:', error);
      throw error;
    }
  }

  /**
   * Test 4: Budget Update
   */
  static async testBudgetUpdate(budgetId: string): Promise<void> {
    console.log('🧪 Testing Budget Update...');
    
    try {
      await BudgetService.updateBudget(budgetId, {
        name: 'Updated Test Bütçe',
        totalIncomeTarget: 12000,
        savingsTarget: 3000,
        notes: 'Updated notes - RFC-003 test'
      });
      
      const updatedBudget = await BudgetService.getBudgetById(budgetId);
      
      if (!updatedBudget) {
        throw new Error('❌ Updated budget not found');
      }
      
      if (updatedBudget.name !== 'Updated Test Bütçe') {
        throw new Error('❌ Budget name not updated');
      }
      
      if (updatedBudget.totalIncomeTarget !== 12000) {
        throw new Error('❌ Income target not updated');
      }
      
      console.log('✅ Budget update test passed!');
      console.log(`📝 Updated name: ${updatedBudget.name}`);
      console.log(`💰 Updated income target: ${updatedBudget.totalIncomeTarget}`);
      
    } catch (error) {
      console.error('❌ Budget update test failed:', error);
      throw error;
    }
  }

  /**
   * Test 5: Budget Suggestion
   */
  static async testBudgetSuggestion(): Promise<void> {
    console.log('🧪 Testing Budget Suggestion...');
    
    try {
      const suggestion = await BudgetService.generateBudgetSuggestion(
        'test_user_1',
        'monthly',
        3 // 3 aylık geçmiş veri
      );
      
      console.log('✅ Budget suggestion generated successfully!');
      console.log(`📝 Suggested name: ${suggestion.name}`);
      console.log(`📅 Period: ${suggestion.period}`);
      console.log(`💰 Income target: ${suggestion.totalIncomeTarget}`);
      console.log(`💸 Expense limit: ${suggestion.totalExpenseLimit}`);
      console.log(`💎 Savings target: ${suggestion.savingsTarget}`);
      console.log(`📋 Categories: ${suggestion.categories.length}`);
      
    } catch (error) {
      console.error('❌ Budget suggestion test failed:', error);
      throw error;
    }
  }

  /**
   * Test 6: Budget Copy
   */
  static async testBudgetCopy(sourceBudgetId: string): Promise<void> {
    console.log('🧪 Testing Budget Copy...');
    
    try {
      const copiedBudgetId = await BudgetService.copyFromPreviousBudget(
        'test_user_1',
        sourceBudgetId,
        {
          name: 'Copied Test Budget',
          startDate: '2024-02-01',
          endDate: '2024-02-29'
        }
      );
      
      const copiedBudget = await BudgetService.getBudgetById(copiedBudgetId);
      
      if (!copiedBudget) {
        throw new Error('❌ Copied budget not found');
      }
      
      if (copiedBudget.copiedFromBudgetId !== sourceBudgetId) {
        throw new Error('❌ Copy reference not set correctly');
      }
      
      console.log('✅ Budget copy test passed!');
      console.log(`📝 Copied budget ID: ${copiedBudgetId}`);
      console.log(`🔗 Source budget ID: ${copiedBudget.copiedFromBudgetId}`);
      
    } catch (error) {
      console.error('❌ Budget copy test failed:', error);
      throw error;
    }
  }

  /**
   * Tüm testleri çalıştır
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 Starting RFC-003 Budget Service Tests...\n');
    
    try {
      // Test 1: Database Migration
      await this.testDatabaseMigration();
      console.log('');
      
      // Test 2: Budget Creation
      const budgetId = await this.testBudgetCreation();
      console.log('');
      
      // Test 3: Budget Summary
      await this.testBudgetSummary(budgetId);
      console.log('');
      
      // Test 4: Budget Update
      await this.testBudgetUpdate(budgetId);
      console.log('');
      
      // Test 5: Budget Suggestion
      await this.testBudgetSuggestion();
      console.log('');
      
      // Test 6: Budget Copy
      await this.testBudgetCopy(budgetId);
      console.log('');
      
      console.log('🎉 All RFC-003 Budget Service tests passed successfully!');
      
      // Database stats
      const stats = await databaseManager.getStats();
      console.log('\n📊 Database Stats:');
      console.log(`  - budgets: ${stats.tables.budgets || 0} records`);
      console.log(`  - budget_categories: ${stats.tables.budget_categories || 0} records`);
      console.log(`  - Database size: ${(stats.totalSize / 1024).toFixed(2)} KB`);
      
    } catch (error) {
      console.error('💥 Test suite failed:', error);
      throw error;
    }
  }
}

export default BudgetServiceTest;
