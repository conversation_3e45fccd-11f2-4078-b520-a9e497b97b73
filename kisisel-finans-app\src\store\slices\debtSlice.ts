// Debt Slice - Borç state yönetimi

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Debt } from '../../types';

interface DebtState {
  debts: Debt[];
  isLoading: boolean;
  error: string | null;
}

const initialState: DebtState = {
  debts: [],
  isLoading: false,
  error: null,
};

export const fetchDebts = createAsyncThunk(
  'debts/fetchDebts',
  async (_, { rejectWithValue }) => {
    try {
      const mockDebts: Debt[] = [
        {
          id: '1',
          userId: '1',
          name: '<PERSON><PERSON><PERSON>',
          type: 'credit_card',
          totalAmount: 5000,
          remainingAmount: 2500,
          interestRate: 2.5,
          minimumPayment: 250,
          dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          creditor: 'ABC Bank',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];
      return mockDebts;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Borçlar yüklenirken hata oluştu');
    }
  }
);

export const addDebt = createAsyncThunk(
  'debts/addDebt',
  async (debtData: Omit<Debt, 'id' | 'userId' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const newDebt: Debt = {
        ...debtData,
        id: Date.now().toString(),
        userId: '1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      return newDebt;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Borç eklenirken hata oluştu');
    }
  }
);

export const updateDebt = createAsyncThunk(
  'debts/updateDebt',
  async (params: { id: string; data: Partial<Debt> }, { rejectWithValue }) => {
    try {
      const updatedDebt: Debt = {
        ...params.data,
        id: params.id,
        updatedAt: new Date().toISOString(),
      } as Debt;
      return updatedDebt;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Borç güncellenirken hata oluştu');
    }
  }
);

export const deleteDebt = createAsyncThunk(
  'debts/deleteDebt',
  async (debtId: string, { rejectWithValue }) => {
    try {
      return debtId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Borç silinirken hata oluştu');
    }
  }
);

const debtSlice = createSlice({
  name: 'debts',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDebts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDebts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.debts = action.payload;
      })
      .addCase(fetchDebts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(addDebt.fulfilled, (state, action) => {
        state.debts.push(action.payload);
      })
      .addCase(updateDebt.fulfilled, (state, action) => {
        const index = state.debts.findIndex(d => d.id === action.payload.id);
        if (index !== -1) {
          state.debts[index] = action.payload;
        }
      })
      .addCase(deleteDebt.fulfilled, (state, action) => {
        state.debts = state.debts.filter(d => d.id !== action.payload);
      });
  },
});

export const { clearError } = debtSlice.actions;
export default debtSlice.reducer;
