// Security Service - Güvenlik özellikleri yönetimi

import * as ScreenCapture from 'expo-screen-capture';
import * as Device from 'expo-device';
import * as LocalAuthentication from 'expo-local-authentication';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';
import EncryptionService from './EncryptionService';

export interface SecuritySettings {
  screenCaptureBlocked: boolean;
  biometricEnabled: boolean;
  autoLockEnabled: boolean;
  autoLockTimeout: number; // dakika
  jailbreakDetectionEnabled: boolean;
}

export interface SecurityStatus {
  isJailbroken: boolean;
  isEmulator: boolean;
  biometricAvailable: boolean;
  biometricTypes: LocalAuthentication.AuthenticationType[];
  screenCaptureBlocked: boolean;
}

class SecurityService {
  private static instance: SecurityService;
  private securitySettings: SecuritySettings = {
    screenCaptureBlocked: false,
    biometricEnabled: false,
    autoLockEnabled: true,
    autoLockTimeout: 5, // 5 dakika
    jailbreakDetectionEnabled: true,
  };

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  constructor() {
    this.loadSecuritySettings();
    this.initializeSecurity();
  }

  /**
   * Güvenlik ayarlarını yükle
   */
  private async loadSecuritySettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('security_settings');
      if (stored) {
        this.securitySettings = { ...this.securitySettings, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.error('Security settings load error:', error);
    }
  }

  /**
   * Güvenlik ayarlarını kaydet
   */
  private async saveSecuritySettings(): Promise<void> {
    try {
      await AsyncStorage.setItem('security_settings', JSON.stringify(this.securitySettings));
    } catch (error) {
      console.error('Security settings save error:', error);
    }
  }

  /**
   * Güvenlik sistemini başlat
   */
  private async initializeSecurity(): Promise<void> {
    // Jailbreak kontrolü
    if (this.securitySettings.jailbreakDetectionEnabled) {
      await this.checkDeviceSecurity();
    }

    // Ekran görüntüsü engelleme
    if (this.securitySettings.screenCaptureBlocked) {
      await this.enableScreenCaptureProtection();
    }
  }

  /**
   * Cihaz güvenlik durumunu kontrol et
   */
  async checkDeviceSecurity(): Promise<SecurityStatus> {
    const isJailbroken = await this.detectJailbreak();
    const isEmulator = await this.detectEmulator();
    const biometricAvailable = await LocalAuthentication.hasHardwareAsync();
    const biometricTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

    const status: SecurityStatus = {
      isJailbroken,
      isEmulator,
      biometricAvailable,
      biometricTypes,
      screenCaptureBlocked: this.securitySettings.screenCaptureBlocked,
    };

    // Güvenlik uyarıları
    if (isJailbroken && this.securitySettings.jailbreakDetectionEnabled) {
      this.showSecurityWarning('Jailbreak/Root Algılandı',
        'Cihazınızda güvenlik riski tespit edildi. Uygulamanın güvenliği tehlikede olabilir.');
    }

    if (isEmulator) {
      this.showSecurityWarning('Emulator Algılandı',
        'Uygulama bir emulator üzerinde çalışıyor. Güvenlik özellikleri sınırlı olabilir.');
    }

    return status;
  }

  /**
   * Gelişmiş Jailbreak/Root algılama
   */
  private async detectJailbreak(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios') {
        return await this.detectiOSJailbreak();
      } else if (Platform.OS === 'android') {
        return await this.detectAndroidRoot();
      }
      return false;
    } catch (error) {
      console.error('Jailbreak detection error:', error);
      return false;
    }
  }

  /**
   * iOS Jailbreak algılama - Gelişmiş yöntemler
   */
  private async detectiOSJailbreak(): Promise<boolean> {
    try {
      let suspiciousCount = 0;

      // 1. Jailbreak uygulamaları ve dosyaları
      const jailbreakPaths = [
        '/Applications/Cydia.app',
        '/Applications/blackra1n.app',
        '/Applications/FakeCarrier.app',
        '/Applications/Icy.app',
        '/Applications/IntelliScreen.app',
        '/Applications/MxTube.app',
        '/Applications/RockApp.app',
        '/Applications/SBSettings.app',
        '/Applications/WinterBoard.app',
        '/Library/MobileSubstrate/MobileSubstrate.dylib',
        '/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist',
        '/Library/MobileSubstrate/DynamicLibraries/Veency.plist',
        '/private/var/lib/apt/',
        '/private/var/lib/cydia',
        '/private/var/mobile/Library/SBSettings/Themes',
        '/private/var/stash',
        '/private/var/tmp/cydia.log',
        '/System/Library/LaunchDaemons/com.ikey.bbot.plist',
        '/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist',
        '/usr/bin/sshd',
        '/usr/libexec/sftp-server',
        '/usr/sbin/sshd',
        '/etc/apt',
        '/bin/bash',
        '/bin/sh',
        '/usr/bin/ssh',
      ];

      // Dosya varlığı kontrolü (React Native'de doğrudan dosya erişimi sınırlı)
      // Bu kontroller gerçek cihazda native kod ile yapılmalı

      // 2. Cydia URL scheme kontrolü
      try {
        // Linking.canOpenURL('cydia://') kontrolü yapılabilir
        suspiciousCount += 0; // Placeholder
      } catch (error) {
        // URL scheme mevcut değil
      }

      // 3. Sistem davranış anomalileri
      const deviceInfo = {
        brand: Device.brand,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
      };

      // Şüpheli cihaz özellikleri
      if (deviceInfo.modelName?.includes('Simulator')) {
        suspiciousCount += 1;
      }

      // 4. Sandbox bypass kontrolü
      try {
        // Sandbox dışına çıkma denemesi
        // Bu kontrol native kod gerektirir
        suspiciousCount += 0; // Placeholder
      } catch (error) {
        // Sandbox korumalı - normal davranış
      }

      // 5. Dinamik kütüphane kontrolü
      // MobileSubstrate varlığı kontrolü
      // Bu kontrol native kod gerektirir

      // Development modunda false döndür
      if (__DEV__) {
        return false;
      }

      // Şüpheli aktivite eşiği
      return suspiciousCount >= 2;
    } catch (error) {
      console.error('iOS Jailbreak detection error:', error);
      return false;
    }
  }

  /**
   * Android Root algılama - Gelişmiş yöntemler
   */
  private async detectAndroidRoot(): Promise<boolean> {
    try {
      let suspiciousCount = 0;

      // 1. Su binary dosyaları
      const suPaths = [
        '/data/local/',
        '/data/local/bin/',
        '/data/local/xbin/',
        '/sbin/',
        '/su/bin/',
        '/system/bin/',
        '/system/bin/.ext/',
        '/system/bin/failsafe/',
        '/system/sd/xbin/',
        '/system/usr/we-need-root/',
        '/system/xbin/',
        '/cache/',
        '/data/',
        '/dev/',
      ];

      // 2. Root yönetim uygulamaları
      const rootApps = [
        'com.noshufou.android.su',
        'com.noshufou.android.su.elite',
        'eu.chainfire.supersu',
        'com.koushikdutta.superuser',
        'com.thirdparty.superuser',
        'com.yellowes.su',
        'com.topjohnwu.magisk',
        'com.kingroot.kinguser',
        'com.kingo.root',
        'com.smedialink.oneclickroot',
        'com.zhiqupk.root.global',
        'com.alephzain.framaroot',
      ];

      // 3. Magisk algılama
      const magiskPaths = [
        '/sbin/.magisk/',
        '/sbin/.core/',
        '/cache/.disable_magisk',
        '/dev/.magisk/',
        '/data/adb/',
      ];

      // 4. Xposed Framework algılama
      const xposedPaths = [
        '/system/framework/XposedBridge.jar',
        '/system/bin/app_process32_xposed',
        '/system/bin/app_process64_xposed',
      ];

      // 5. Build.TAGS kontrolü
      // Android'de Build.TAGS "test-keys" içeriyorsa şüpheli
      // Bu kontrol native kod gerektirir

      // 6. Sistem özellik kontrolü
      const deviceInfo = {
        brand: Device.brand,
        modelName: Device.modelName,
        manufacturer: Device.manufacturer,
      };

      // Şüpheli cihaz özellikleri
      const suspiciousManufacturers = ['unknown', 'generic', 'google_sdk'];
      if (suspiciousManufacturers.includes(deviceInfo.manufacturer?.toLowerCase() || '')) {
        suspiciousCount += 1;
      }

      // 7. Emulator kontrolü
      const suspiciousModels = [
        'sdk',
        'emulator',
        'simulator',
        'genymotion',
        'android sdk built for x86',
        'google_sdk',
      ];

      const modelName = deviceInfo.modelName?.toLowerCase() || '';
      if (suspiciousModels.some(model => modelName.includes(model))) {
        suspiciousCount += 1;
      }

      // 8. Debugging kontrolü
      // USB debugging, ADB aktif kontrolü
      // Bu kontrol native kod gerektirir

      // Development modunda false döndür
      if (__DEV__) {
        return false;
      }

      // Şüpheli aktivite eşiği
      return suspiciousCount >= 2;
    } catch (error) {
      console.error('Android Root detection error:', error);
      return false;
    }
  }

  /**
   * Gelişmiş Emulator algılama
   */
  private async detectEmulator(): Promise<boolean> {
    try {
      let suspiciousCount = 0;

      // 1. Temel cihaz kontrolü
      if (!Device.isDevice) {
        return true; // Expo Go veya simulator
      }

      // 2. Cihaz parmak izi analizi
      const deviceFingerprint = await this.generateDeviceFingerprint();

      // 3. iOS Simulator algılama
      if (Platform.OS === 'ios') {
        suspiciousCount += await this.detectiOSSimulator();
      }

      // 4. Android Emulator algılama
      if (Platform.OS === 'android') {
        suspiciousCount += await this.detectAndroidEmulator();
      }

      // 5. Genel emulator özellikleri
      suspiciousCount += this.detectGenericEmulatorFeatures(deviceFingerprint);

      // Şüpheli aktivite eşiği
      return suspiciousCount >= 3;
    } catch (error) {
      console.error('Emulator detection error:', error);
      return false;
    }
  }

  /**
   * iOS Simulator algılama
   */
  private async detectiOSSimulator(): Promise<number> {
    let suspiciousCount = 0;

    try {
      const deviceInfo = {
        brand: Device.brand,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
      };

      // Simulator özellikleri
      const simulatorIndicators = [
        'simulator',
        'x86_64',
        'i386',
      ];

      const modelName = deviceInfo.modelName?.toLowerCase() || '';
      if (simulatorIndicators.some(indicator => modelName.includes(indicator))) {
        suspiciousCount += 2;
      }

      // iOS Simulator'da Device.brand genellikle "Apple" değil "simulator" olur
      if (deviceInfo.brand?.toLowerCase().includes('simulator')) {
        suspiciousCount += 2;
      }

      // Simulator'da bazı donanım özellikleri eksik olur
      // Bu kontroller native kod gerektirir

    } catch (error) {
      console.error('iOS Simulator detection error:', error);
    }

    return suspiciousCount;
  }

  /**
   * Android Emulator algılama
   */
  private async detectAndroidEmulator(): Promise<number> {
    let suspiciousCount = 0;

    try {
      const deviceInfo = {
        brand: Device.brand,
        modelName: Device.modelName,
        manufacturer: Device.manufacturer,
      };

      // 1. Emulator markaları ve modelleri
      const emulatorBrands = [
        'generic',
        'unknown',
        'emulator',
        'android',
        'google_sdk',
        'genymotion',
      ];

      const emulatorModels = [
        'sdk',
        'emulator',
        'simulator',
        'android sdk built for x86',
        'android sdk built for x86_64',
        'google_sdk',
        'genymotion',
        'vbox86p',
        'ttvm_hdragon',
      ];

      const emulatorManufacturers = [
        'genymotion',
        'unknown',
        'generic',
        'google',
      ];

      // Marka kontrolü
      if (emulatorBrands.includes(deviceInfo.brand?.toLowerCase() || '')) {
        suspiciousCount += 2;
      }

      // Model kontrolü
      const modelName = deviceInfo.modelName?.toLowerCase() || '';
      if (emulatorModels.some(model => modelName.includes(model))) {
        suspiciousCount += 2;
      }

      // Üretici kontrolü
      if (emulatorManufacturers.includes(deviceInfo.manufacturer?.toLowerCase() || '')) {
        suspiciousCount += 1;
      }

      // 2. Telefon numarası kontrolü
      // Emulator'larda genellikle varsayılan telefon numaraları kullanılır
      const suspiciousPhoneNumbers = [
        '15555215554',
        '15555215556',
        '15555215558',
        '15555215560',
        '15555215562',
        '15555215564',
        '15555215566',
        '15555215568',
      ];

      // Bu kontrol native kod gerektirir

      // 3. IMEI kontrolü
      // Emulator'larda genellikle varsayılan IMEI'ler kullanılır
      const suspiciousIMEIs = [
        '000000000000000',
        '004999010640000',
        '012345678901234',
        '123456789012345',
      ];

      // Bu kontrol native kod gerektirir

    } catch (error) {
      console.error('Android Emulator detection error:', error);
    }

    return suspiciousCount;
  }

  /**
   * Cihaz parmak izi oluştur
   */
  private async generateDeviceFingerprint(): Promise<{
    brand: string | null;
    modelName: string | null;
    manufacturer: string | null;
    osName: string | null;
    osVersion: string | null;
    isDevice: boolean;
  }> {
    return {
      brand: Device.brand,
      modelName: Device.modelName,
      manufacturer: Device.manufacturer,
      osName: Device.osName,
      osVersion: Device.osVersion,
      isDevice: Device.isDevice,
    };
  }

  /**
   * Genel emulator özelliklerini algıla
   */
  private detectGenericEmulatorFeatures(fingerprint: any): number {
    let suspiciousCount = 0;

    // 1. Null veya undefined değerler
    const nullFields = Object.values(fingerprint).filter(value =>
      value === null || value === undefined || value === ''
    ).length;

    if (nullFields >= 2) {
      suspiciousCount += 1;
    }

    // 2. Şüpheli kombinasyonlar
    if (fingerprint.brand === 'generic' && fingerprint.manufacturer === 'unknown') {
      suspiciousCount += 2;
    }

    // 3. Test cihazı göstergeleri
    const testIndicators = ['test', 'debug', 'dev', 'sdk'];
    const allValues = Object.values(fingerprint).join(' ').toLowerCase();

    const testMatches = testIndicators.filter(indicator =>
      allValues.includes(indicator)
    ).length;

    suspiciousCount += testMatches;

    return suspiciousCount;
  }

  /**
   * Ekran görüntüsü korumasını etkinleştir
   */
  async enableScreenCaptureProtection(): Promise<boolean> {
    try {
      await ScreenCapture.preventScreenCaptureAsync();
      this.securitySettings.screenCaptureBlocked = true;
      await this.saveSecuritySettings();
      return true;
    } catch (error) {
      console.error('Screen capture protection error:', error);
      return false;
    }
  }

  /**
   * Ekran görüntüsü korumasını devre dışı bırak
   */
  async disableScreenCaptureProtection(): Promise<boolean> {
    try {
      await ScreenCapture.allowScreenCaptureAsync();
      this.securitySettings.screenCaptureBlocked = false;
      await this.saveSecuritySettings();
      return true;
    } catch (error) {
      console.error('Screen capture disable error:', error);
      return false;
    }
  }

  /**
   * Biyometrik kimlik doğrulama
   */
  async authenticateWithBiometrics(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      if (!hasHardware) {
        throw new Error('Biyometrik donanım bulunamadı');
      }

      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        throw new Error('Biyometrik veri kayıtlı değil');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        fallbackLabel: 'Şifre kullan',
      });

      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }

  /**
   * Güvenlik ayarlarını güncelle
   */
  async updateSecuritySettings(newSettings: Partial<SecuritySettings>): Promise<void> {
    this.securitySettings = { ...this.securitySettings, ...newSettings };
    await this.saveSecuritySettings();

    // Ekran görüntüsü ayarı değiştiyse uygula
    if (newSettings.screenCaptureBlocked !== undefined) {
      if (newSettings.screenCaptureBlocked) {
        await this.enableScreenCaptureProtection();
      } else {
        await this.disableScreenCaptureProtection();
      }
    }
  }

  /**
   * Mevcut güvenlik ayarlarını al
   */
  getSecuritySettings(): SecuritySettings {
    return { ...this.securitySettings };
  }

  /**
   * Güvenlik uyarısı göster
   */
  private showSecurityWarning(title: string, message: string): void {
    Alert.alert(
      title,
      message,
      [
        { text: 'Anladım', style: 'default' },
        {
          text: 'Güvenlik Ayarları',
          onPress: () => {
            // Navigation'a güvenlik ayarlarına yönlendirme eklenebilir
          }
        }
      ]
    );
  }

  /**
   * Otomatik kilit kontrolü
   */
  async checkAutoLock(): Promise<boolean> {
    if (!this.securitySettings.autoLockEnabled) {
      return true; // Auto lock kapalıysa her zaman geç
    }

    const lastActivity = await AsyncStorage.getItem('last_activity');
    if (!lastActivity) {
      return true; // İlk kullanım
    }

    const timeDiff = Date.now() - parseInt(lastActivity);
    const timeoutMs = this.securitySettings.autoLockTimeout * 60 * 1000; // dakika -> ms

    return timeDiff < timeoutMs;
  }

  /**
   * Aktivite zamanını güncelle
   */
  async updateLastActivity(): Promise<void> {
    await AsyncStorage.setItem('last_activity', Date.now().toString());
  }

  /**
   * Detaylı güvenlik raporunu al
   */
  async getSecurityReport(): Promise<{
    status: SecurityStatus;
    settings: SecuritySettings;
    recommendations: string[];
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    deviceFingerprint: any;
    securityScore: number;
  }> {
    const status = await this.checkDeviceSecurity();
    const deviceFingerprint = await this.generateDeviceFingerprint();
    const recommendations: string[] = [];
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    let securityScore = 100;

    // Risk değerlendirmesi
    if (status.isJailbroken) {
      recommendations.push('⚠️ KRITIK: Cihazınızda jailbreak/root tespit edildi');
      riskLevel = 'CRITICAL';
      securityScore -= 50;
    }

    if (status.isEmulator) {
      recommendations.push('⚠️ YÜKSEK: Uygulama emulator üzerinde çalışıyor');
      if (riskLevel !== 'CRITICAL') riskLevel = 'HIGH';
      securityScore -= 30;
    }

    if (!status.biometricAvailable) {
      recommendations.push('ℹ️ Biyometrik doğrulama bu cihazda desteklenmiyor');
      securityScore -= 10;
    } else if (!this.securitySettings.biometricEnabled) {
      recommendations.push('🔒 Biyometrik doğrulamayı etkinleştirin');
      if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
      securityScore -= 15;
    }

    if (!this.securitySettings.screenCaptureBlocked) {
      recommendations.push('📱 Ekran görüntüsü korumasını etkinleştirin');
      if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
      securityScore -= 10;
    }

    if (!this.securitySettings.autoLockEnabled) {
      recommendations.push('🔐 Otomatik kilidi etkinleştirin');
      securityScore -= 5;
    }

    if (!this.securitySettings.jailbreakDetectionEnabled) {
      recommendations.push('🛡️ Jailbreak/Root algılamayı etkinleştirin');
      securityScore -= 5;
    }

    // Cihaz güvenlik durumu
    if (deviceFingerprint.brand === 'generic' || deviceFingerprint.manufacturer === 'unknown') {
      recommendations.push('⚠️ Şüpheli cihaz özellikleri tespit edildi');
      if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
      securityScore -= 20;
    }

    // Development modu kontrolü
    if (__DEV__) {
      recommendations.push('🔧 Uygulama geliştirme modunda çalışıyor');
      securityScore -= 10;
    }

    // Güvenlik skoru sınırları
    securityScore = Math.max(0, Math.min(100, securityScore));

    // Risk seviyesi güvenlik skoruna göre ayarla
    if (securityScore >= 80 && riskLevel === 'LOW') {
      riskLevel = 'LOW';
    } else if (securityScore >= 60 && riskLevel !== 'CRITICAL' && riskLevel !== 'HIGH') {
      riskLevel = 'MEDIUM';
    } else if (securityScore >= 40 && riskLevel !== 'CRITICAL') {
      riskLevel = 'HIGH';
    } else if (securityScore < 40) {
      riskLevel = 'CRITICAL';
    }

    return {
      status,
      settings: this.securitySettings,
      recommendations,
      riskLevel,
      deviceFingerprint,
      securityScore,
    };
  }

  /**
   * Veri şifreleme
   */
  async encryptData(data: string): Promise<string> {
    try {
      const encryptedData = await EncryptionService.encrypt(data);
      return JSON.stringify(encryptedData);
    } catch (error) {
      console.error('Data encryption error:', error);
      throw new Error('Veri şifrelenirken hata oluştu');
    }
  }

  /**
   * Veri şifre çözme
   */
  async decryptData(encryptedData: string): Promise<string> {
    try {
      const parsedData = JSON.parse(encryptedData);
      return await EncryptionService.decrypt(parsedData);
    } catch (error) {
      console.error('Data decryption error:', error);
      throw new Error('Veri şifresi çözülürken hata oluştu');
    }
  }

  /**
   * Biyometrik doğrulama
   */
  async authenticateWithBiometrics(): Promise<boolean> {
    try {
      // Biyometrik donanım kontrolü
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      if (!hasHardware) {
        console.warn('Biometric hardware not available');
        return false;
      }

      // Kayıtlı biyometrik veri kontrolü
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        console.warn('No biometric data enrolled');
        return false;
      }

      // Biyometrik doğrulama
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Güvenlik doğrulaması',
        fallbackLabel: 'PIN kullan',
        cancelLabel: 'İptal',
      });

      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }
}

export default SecurityService;
