// Main Navigation - Ana uygulama ekranları

import React from 'react';
import { View } from 'react-native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import CustomDrawerContent from '../components/CustomDrawerContent';
import OfflineStatusBar from '../components/OfflineStatusBar';


// Ana ekranlar
import DashboardScreen from '../screens/main/DashboardScreen';
import TransactionsScreen from '../screens/main/TransactionsScreen';
import ReceiptsScreen from '../screens/main/ReceiptsScreen';
import RecurringTransactionsScreen from '../screens/main/RecurringTransactionsScreen';
import BudgetScreen from '../screens/main/BudgetScreen';
import GoalsScreen from '../screens/main/GoalsScreen';
import InvestmentScreen from '../screens/main/InvestmentScreen';
import ReportsScreen from '../screens/main/ReportsScreen';
import BankAccountsScreen from '../screens/main/BankAccountsScreen';

// Modal ekranlar
import AddTransactionScreen from '../screens/modals/AddTransactionScreen';
import AddBudgetScreen from '../screens/modals/AddBudgetScreen';
import AddGoalScreen from '../screens/modals/AddGoalScreen';
import AddDebtScreen from '../screens/modals/AddDebtScreen';
import RiskAssessmentScreen from '../screens/modals/RiskAssessmentScreen';
import InvestmentOptionsScreen from '../screens/modals/InvestmentOptionsScreen';
import CreatePortfolioScreen from '../screens/modals/CreatePortfolioScreen';
import InvestmentDetailScreen from '../screens/modals/InvestmentDetailScreen';
import MarketDataScreen from '../screens/MarketDataScreen';
import InvestmentRecommendationsScreen from '../screens/modals/InvestmentRecommendationsScreen';
import TransactionDetailScreen from '../screens/modals/TransactionDetailScreen';
import BudgetDetailScreen from '../screens/modals/BudgetDetailScreen';
import BudgetTemplatesScreen from '../screens/budget/BudgetTemplatesScreen';
import GoalDetailScreen from '../screens/modals/GoalDetailScreen';
import SettingsScreen from '../screens/modals/SettingsScreen';
import ProfileScreen from '../screens/modals/ProfileScreen';

import TwoFactorSetupScreen from '../screens/auth/TwoFactorSetupScreen';
import TrustedDevicesScreen from '../screens/modals/TrustedDevicesScreen';
import EncryptionTestScreen from '../screens/test/EncryptionTestScreen';
import SSLPinningTestScreen from '../screens/test/SSLPinningTestScreen';
import RBACTestScreen from '../screens/test/RBACTestScreen';
import OAuthTestScreen from '../screens/test/OAuthTestScreen';
import SecurityTestScreen from '../screens/test/SecurityTestScreen';
import DatabaseTestScreen from '../screens/test/DatabaseTestScreen';
import BudgetTestScreen from '../screens/test/BudgetTestScreen';
import DatabaseDebugScreen from '../screens/test/DatabaseDebugScreen';
import TablesScreen from '../screens/test/TablesScreen';
import BudgetWizardScreen from '../screens/budget/BudgetWizardScreen';
import BudgetEditScreen from '../screens/budget/BudgetEditScreen';
import BudgetReportsScreen from '../screens/budget/BudgetReportsScreen';
import AdminDashboardScreen from '../screens/admin/AdminDashboardScreen';

export type MainDrawerParamList = {
  Dashboard: undefined;
  Transactions: undefined;
  Receipts: undefined;
  RecurringTransactions: undefined;
  Budget: undefined;
  Goals: undefined;
  Investment: undefined;
  Reports: undefined;
  BankAccounts: undefined;
  MarketData: undefined;
  Settings: undefined;
  Profile: undefined;
  AdminDashboard: undefined;
  EncryptionTest: undefined;
  SSLPinningTest: undefined;
  RBACTest: undefined;
  OAuthTest: undefined;
  SecurityTest: undefined;
  DatabaseTest: undefined;
  BudgetTest: undefined;
  DatabaseDebug: undefined;
  Tables: undefined;
  BudgetWizard: undefined;
};

export type MainStackParamList = {
  MainDrawer: undefined;
  AddTransaction: {
    type?: 'income' | 'expense';
    editTransaction?: any; // Transaction tipini import etmek yerine any kullanıyoruz
  };
  AddBudget: undefined;
  AddGoal: undefined;
  AddDebt: undefined;
  RiskAssessment: undefined;
  InvestmentRecommendations: undefined;
  CreatePortfolio: undefined;
  InvestmentOptions: undefined;
  InvestmentDetail: { investmentId: string };
  PortfolioDetail: { portfolioId: string };
  TransactionDetail: { transactionId: string };
  BudgetDetail: { budgetId: string };
  BudgetEdit: { budgetId: string };
  BudgetReports: { budgetId: string };
  BudgetTemplates: undefined;
  GoalDetail: { goalId: string };
  TwoFactorSetup: undefined;
  TrustedDevices: undefined;
};

const Drawer = createDrawerNavigator<MainDrawerParamList>();
const Stack = createStackNavigator<MainStackParamList>();

// Main Drawer Navigator
const MainDrawerNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.colors.background,
          width: 280,
        },
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: theme.colors.textSecondary,
        drawerLabelStyle: {
          fontSize: 16,
          fontWeight: '500',
        },
      }}
    >
      <Drawer.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          drawerLabel: 'Ana Sayfa',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Transactions"
        component={TransactionsScreen}
        options={{
          drawerLabel: 'İşlemler',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="list-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Receipts"
        component={ReceiptsScreen}
        options={{
          drawerLabel: 'Makbuzlar',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="document-text-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="RecurringTransactions"
        component={RecurringTransactionsScreen}
        options={{
          drawerLabel: 'Tekrarlayan İşlemler',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="repeat-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Budget"
        component={BudgetScreen}
        options={{
          drawerLabel: 'Bütçe',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="pie-chart-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          drawerLabel: 'Hedefler',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="flag-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Investment"
        component={InvestmentScreen}
        options={{
          drawerLabel: 'Yatırım',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="trending-up-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          drawerLabel: 'Raporlar',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="bar-chart-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="BankAccounts"
        component={BankAccountsScreen}
        options={{
          drawerLabel: 'Banka Hesapları',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="card-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="MarketData"
        component={MarketDataScreen}
        options={{
          drawerLabel: 'Market Verileri',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="analytics-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          drawerLabel: 'Ayarlar',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="settings-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          drawerLabel: 'Profil',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="person-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="AdminDashboard"
        component={AdminDashboardScreen}
        options={{
          drawerLabel: '👨‍💼 Admin Panel',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="shield-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="EncryptionTest"
        component={EncryptionTestScreen}
        options={{
          drawerLabel: '🔐 Encryption Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="shield-checkmark-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="SSLPinningTest"
        component={SSLPinningTestScreen}
        options={{
          drawerLabel: '🔒 SSL Pinning Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="lock-closed-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="RBACTest"
        component={RBACTestScreen}
        options={{
          drawerLabel: '👥 RBAC Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="people-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="OAuthTest"
        component={OAuthTestScreen}
        options={{
          drawerLabel: '🔐 OAuth Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="key-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="SecurityTest"
        component={SecurityTestScreen}
        options={{
          drawerLabel: '🛡️ Security Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="shield-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="DatabaseTest"
        component={DatabaseTestScreen}
        options={{
          drawerLabel: '🗄️ Database Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="server-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="BudgetTest"
        component={BudgetTestScreen}
        options={{
          drawerLabel: '📊 Budget Test',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="pie-chart-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="DatabaseDebug"
        component={DatabaseDebugScreen}
        options={{
          drawerLabel: '🔍 DB Debug',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="bug-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="Tables"
        component={TablesScreen}
        options={{
          drawerLabel: '📊 Tablolar',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="grid-outline" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="BudgetWizard"
        component={BudgetWizardScreen}
        options={{
          drawerLabel: '🧙‍♂️ Bütçe Sihirbazı',
          drawerIcon: ({ color, size }) => (
            <Ionicons name="create-outline" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

// Main Stack Navigator
const MainNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <View style={{ flex: 1 }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: theme.colors.background },
        }}
      >
        <Stack.Screen name="MainDrawer" component={MainDrawerNavigator} />

      {/* Modal Screens */}
      <Stack.Group screenOptions={{ presentation: 'modal' }}>
        <Stack.Screen
          name="AddTransaction"
          component={AddTransactionScreen}
          options={({ route }) => ({
            headerShown: true,
            headerTitle: route.params?.editTransaction ? 'İşlem Düzenle' : 'Yeni İşlem',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          })}
        />
        <Stack.Screen
          name="AddBudget"
          component={AddBudgetScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yeni Bütçe',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="AddGoal"
          component={AddGoalScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yeni Hedef',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="AddDebt"
          component={AddDebtScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yeni Borç',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="RiskAssessment"
          component={RiskAssessmentScreen}
          options={{
            headerShown: true,
            headerTitle: 'Risk Değerlendirmesi',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="InvestmentOptions"
          component={InvestmentOptionsScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yatırım Seçenekleri',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="CreatePortfolio"
          component={CreatePortfolioScreen}
          options={{
            headerShown: true,
            headerTitle: 'Portföy Oluştur',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="InvestmentDetail"
          component={InvestmentDetailScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yatırım Detayı',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="MarketData"
          component={MarketDataScreen}
          options={{
            headerShown: true,
            headerTitle: 'Piyasa Verileri',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="InvestmentRecommendations"
          component={InvestmentRecommendationsScreen}
          options={{
            headerShown: true,
            headerTitle: 'Yatırım Önerileri',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="TransactionDetail"
          component={TransactionDetailScreen}
          options={{
            headerShown: true,
            headerTitle: 'İşlem Detayı',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="BudgetDetail"
          component={BudgetDetailScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="BudgetEdit"
          component={BudgetEditScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="BudgetReports"
          component={BudgetReportsScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="BudgetTemplates"
          component={BudgetTemplatesScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="GoalDetail"
          component={GoalDetailScreen}
          options={{
            headerShown: true,
            headerTitle: 'Hedef Detayı',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            headerShown: true,
            headerTitle: 'Ayarlar',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={{
            headerShown: true,
            headerTitle: 'Profil',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="TwoFactorSetup"
          component={TwoFactorSetupScreen}
          options={{
            headerShown: true,
            headerTitle: '2FA Kurulum',
            headerStyle: {
              backgroundColor: theme.colors.surface,
            },
            headerTintColor: theme.colors.text,
          }}
        />
        <Stack.Screen
          name="TrustedDevices"
          component={TrustedDevicesScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Group>
    </Stack.Navigator>

    {/* Offline Status Bar - RFC-002: Offline Support */}
    <OfflineStatusBar />
  </View>
  );
};

export default MainNavigator;
