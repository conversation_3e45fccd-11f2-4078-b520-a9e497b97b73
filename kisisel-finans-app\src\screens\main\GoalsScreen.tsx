// Goals Screen - Hedefler ekranı

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '../../store';
import { fetchGoals } from '../../store/slices/goalSlice';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { FinancialGoal } from '../../types';

const GoalsScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { theme } = useTheme();

  const { goals, isLoading } = useAppSelector((state) => state.goals);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(fetchGoals());
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchGoals());
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getDaysRemaining = (targetDate: string) => {
    const today = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.textSecondary;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'emergency': return 'shield';
      case 'vacation': return 'airplane';
      case 'house': return 'home';
      case 'car': return 'car';
      case 'education': return 'school';
      case 'retirement': return 'time';
      default: return 'flag';
    }
  };

  const renderGoal = ({ item }: { item: FinancialGoal }) => {
    const progressPercentage = getProgressPercentage(item.currentAmount, item.targetAmount);
    const daysRemaining = getDaysRemaining(item.targetDate);
    const priorityColor = getPriorityColor(item.priority);
    const categoryIcon = getCategoryIcon(item.category);

    return (
      <TouchableOpacity
        style={[styles.goalItem, { backgroundColor: theme.colors.surface }]}
        onPress={() => {/* navigation.navigate('GoalDetail', { goalId: item.id }) */}}
      >
        <View style={styles.goalHeader}>
          <View style={styles.goalTitleContainer}>
            <View style={[styles.categoryIcon, { backgroundColor: priorityColor }]}>
              <Ionicons name={categoryIcon as any} size={20} color={theme.colors.surface} />
            </View>
            <View style={styles.goalInfo}>
              <Text style={[styles.goalTitle, { color: theme.colors.text }]}>{item.title}</Text>
              <Text style={[styles.goalDescription, { color: theme.colors.textSecondary }]}>{item.description}</Text>
            </View>
          </View>
          <View style={[styles.priorityBadge, { backgroundColor: priorityColor }]}>
            <Text style={[styles.priorityText, { color: theme.colors.surface }]}>{item.priority}</Text>
          </View>
        </View>

        <View style={styles.goalProgress}>
          <View style={styles.progressInfo}>
            <Text style={[styles.progressAmount, { color: theme.colors.text }]}>
              {formatCurrency(item.currentAmount)} / {formatCurrency(item.targetAmount)}
            </Text>
            <Text style={[styles.progressPercentage, { color: theme.colors.primary }]}>
              {progressPercentage.toFixed(0)}%
            </Text>
          </View>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${progressPercentage}%`,
                  backgroundColor: item.isCompleted ? theme.colors.success : theme.colors.primary
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.goalFooter}>
          <View style={styles.timeRemaining}>
            <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>
              {daysRemaining > 0 ? `${daysRemaining} gün kaldı` : 'Süre doldu'}
            </Text>
          </View>
          {item.isCompleted && (
            <View style={styles.completedBadge}>
              <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              <Text style={[styles.completedText, { color: theme.colors.success }]}>Tamamlandı</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const activeGoals = goals.filter(goal => !goal.isCompleted);
  const completedGoals = goals.filter(goal => goal.isCompleted);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Hedefler</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => navigation.navigate('AddGoal' as never)}
        >
          <Ionicons name="add" size={24} color={theme.colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Goals Summary */}
      <View style={styles.summaryContainer}>
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.textSecondary }]}>Toplam Hedef Tutarı</Text>
          <Text style={[styles.summaryAmount, { color: theme.colors.text }]}>
            {formatCurrency(goals.reduce((sum, g) => sum + g.targetAmount, 0))}
          </Text>
          <View style={styles.summaryStats}>
            <Text style={[styles.summarySubtitle, { color: theme.colors.textSecondary }]}>
              {activeGoals.length} aktif • {completedGoals.length} tamamlandı
            </Text>
          </View>
        </View>
      </View>

      {/* Goals List */}
      <FlatList
        data={goals}
        renderItem={renderGoal}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="flag-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>Henüz hedef yok</Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
              İlk finansal hedefinizi oluşturmak için + butonuna dokunun
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summarySubtitle: {
    fontSize: 12,
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  goalItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  goalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  goalDescription: {
    fontSize: 12,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  goalProgress: {
    marginBottom: 12,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  goalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeRemaining: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedText: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default GoalsScreen;
