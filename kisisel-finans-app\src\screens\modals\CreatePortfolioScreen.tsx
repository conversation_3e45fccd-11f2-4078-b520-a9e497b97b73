// Create Portfolio Screen - Portföy oluşturma ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
// Mock portfolio service - replace with real API

const CreatePortfolioScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [formData, setFormData] = useState({
    name: '',
    riskLevel: 'medium' as 'low' | 'medium' | 'high',
    initialAmount: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const riskLevels = [
    { id: 'low', name: 'Düşük Risk', color: theme.colors.success, description: 'Muhafazakar yatırım' },
    { id: 'medium', name: 'Orta Risk', color: theme.colors.warning, description: '<PERSON><PERSON><PERSON> yatırım' },
    { id: 'high', name: '<PERSON><PERSON><PERSON><PERSON>', color: theme.colors.error, description: 'Agresif yatırım' },
  ];

  const handleSave = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Portföy adı gerekli.');
      return;
    }

    const initialAmount = parseFloat(formData.initialAmount.replace(',', '.')) || 0;
    console.log('Portfolio created with initial amount:', initialAmount);

    setIsLoading(true);

    try {
      // Mock portfolio creation - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert('Başarılı', 'Portföy başarıyla oluşturuldu.', [
        { text: 'Tamam', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Hata', error || 'Portföy oluşturulamadı.');
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    header: {
      marginBottom: 30,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      lineHeight: 22,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 50,
    },
    currencySymbol: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginRight: 8,
    },
    amountInput: {
      flex: 1,
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    riskLevelsContainer: {
      gap: 12,
    },
    riskLevelButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 2,
      padding: 16,
    },
    riskLevelButtonActive: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    riskLevelIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    riskLevelInfo: {
      flex: 1,
    },
    riskLevelName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    riskLevelDescription: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    radioButton: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonInner: {
      width: 10,
      height: 10,
      borderRadius: 5,
    },
    previewContainer: {
      marginBottom: 20,
    },
    previewTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    previewCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    previewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    previewName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    riskBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    riskBadgeText: {
      fontSize: 10,
      fontWeight: '600',
      color: theme.colors.surface,
    },
    previewStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: 16,
    },
    statItem: {
      alignItems: 'center',
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    statValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    nextStepsContainer: {
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: 16,
    },
    nextStepsTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    nextStepsText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
      lineHeight: 16,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginBottom: 40,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        <View style={styles.header}>
          <Text style={styles.title}>Yeni Portföy</Text>
          <Text style={styles.subtitle}>
            Yatırım portföyünüzü oluşturun ve yönetin
          </Text>
        </View>

        {/* Portfolio Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Portföy Adı *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Örn: Emeklilik Portföyüm"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            autoFocus
          />
        </View>

        {/* Initial Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Başlangıç Tutarı (İsteğe Bağlı)</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.initialAmount}
              onChangeText={(text) => setFormData({ ...formData, initialAmount: text.replace(/[^0-9.,]/g, '') })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Risk Level Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Risk Seviyesi</Text>
          <View style={styles.riskLevelsContainer}>
            {riskLevels.map((level) => (
              <TouchableOpacity
                key={level.id}
                style={[
                  styles.riskLevelButton,
                  formData.riskLevel === level.id && styles.riskLevelButtonActive,
                  { borderColor: level.color }
                ]}
                onPress={() => setFormData({ ...formData, riskLevel: level.id as any })}
              >
                <View style={[styles.riskLevelIcon, { backgroundColor: level.color }]}>
                  <Ionicons
                    name={
                      level.id === 'low' ? 'shield-checkmark' :
                      level.id === 'medium' ? 'trending-up' : 'flash'
                    }
                    size={20}
                    color={theme.colors.surface}
                  />
                </View>
                <View style={styles.riskLevelInfo}>
                  <Text style={[
                    styles.riskLevelName,
                    formData.riskLevel === level.id && { color: level.color }
                  ]}>
                    {level.name}
                  </Text>
                  <Text style={styles.riskLevelDescription}>
                    {level.description}
                  </Text>
                </View>
                <View style={[
                  styles.radioButton,
                  formData.riskLevel === level.id && { borderColor: level.color }
                ]}>
                  {formData.riskLevel === level.id && (
                    <View style={[styles.radioButtonInner, { backgroundColor: level.color }]} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Portfolio Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Portföy Önizlemesi</Text>
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewName}>{formData.name || 'Portföy Adı'}</Text>
              <View style={[
                styles.riskBadge,
                { backgroundColor: riskLevels.find(r => r.id === formData.riskLevel)?.color }
              ]}>
                <Text style={styles.riskBadgeText}>
                  {riskLevels.find(r => r.id === formData.riskLevel)?.name}
                </Text>
              </View>
            </View>

            <View style={styles.previewStats}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Başlangıç Tutarı</Text>
                <Text style={styles.statValue}>
                  ₺{formData.initialAmount || '0'}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Yatırım Sayısı</Text>
                <Text style={styles.statValue}>0</Text>
              </View>
            </View>

            <View style={styles.nextStepsContainer}>
              <Text style={styles.nextStepsTitle}>Sonraki Adımlar:</Text>
              <Text style={styles.nextStepsText}>
                • Portföy oluşturduktan sonra yatırım seçeneklerini görüntüleyebilirsiniz
              </Text>
              <Text style={styles.nextStepsText}>
                • Risk profilinize uygun öneriler alabilirsiniz
              </Text>
              <Text style={styles.nextStepsText}>
                • Portföyünüzü düzenli olarak takip edebilirsiniz
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Oluşturuluyor...' : 'Portföy Oluştur'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default CreatePortfolioScreen;
