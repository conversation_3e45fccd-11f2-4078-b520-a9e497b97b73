// Date Picker Component - <PERSON><PERSON><PERSON> seçici bileşeni

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Platform,
  Alert,
  ScrollView,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface DatePickerProps {
  value: Date;
  onDateChange: (date: Date) => void;
  placeholder?: string;
  label?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  disabled?: boolean;
  style?: any;
}

const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onDateChange,
  placeholder = 'Tarih seçin',
  label,
  minimumDate,
  maximumDate,
  disabled = false,
  style,
}) => {
  const { theme } = useTheme();
  const [showPicker, setShowPicker] = useState(false);
  const [tempDate, setTempDate] = useState(value);
  const [useSimplePicker, setUseSimplePicker] = useState(false);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    console.log('📅 DatePicker handleDateChange called:', {
      event: event?.type,
      selectedDate: selectedDate?.toLocaleDateString(),
      platform: Platform.OS
    });

    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (selectedDate && event?.type !== 'dismissed') {
      console.log('📅 Setting new date:', selectedDate.toLocaleDateString());
      setTempDate(selectedDate);
      if (Platform.OS === 'android') {
        console.log('📅 Calling onDateChange for Android');
        onDateChange(selectedDate);
      }
    } else if (event?.type === 'dismissed') {
      console.log('📅 Date picker dismissed');
      setShowPicker(false);
    }
  };

  // Basit tarih seçici için yardımcı fonksiyonlar
  const generateDateOptions = () => {
    const dates = [];
    const today = new Date();

    console.log('📅 Generating date options...');
    console.log('📅 Today:', today.toLocaleDateString());
    console.log('📅 Minimum date:', minimumDate?.toLocaleDateString());
    console.log('📅 Maximum date:', maximumDate?.toLocaleDateString());

    // Son 30 gün ve gelecek 7 gün (makul aralık)
    for (let i = -30; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      // Minimum ve maximum tarih kontrolü
      if (minimumDate && date < minimumDate) continue;
      if (maximumDate && date > maximumDate) continue;

      dates.push(date);
    }

    console.log('📅 Generated', dates.length, 'date options');
    console.log('📅 First date:', dates[0]?.toLocaleDateString());
    console.log('📅 Last date:', dates[dates.length - 1]?.toLocaleDateString());

    return dates.reverse(); // En yeni tarihler üstte
  };

  const handleConfirm = () => {
    console.log('📅 Confirm pressed - Original:', value.toLocaleDateString(), 'Selected:', tempDate.toLocaleDateString());
    console.log('📅 Always calling onDateChange to ensure update');
    onDateChange(tempDate);
    setShowPicker(false);
  };

  const handleCancel = () => {
    setTempDate(value);
    setShowPicker(false);
  };

  const renderSimplePicker = () => {
    const dateOptions = generateDateOptions();
    const selectedIndex = dateOptions.findIndex(date => date.toDateString() === tempDate.toDateString());

    // Bugünkü tarihi vurgula
    const todayIndex = dateOptions.findIndex(date => date.toDateString() === new Date().toDateString());

    return (
      <Modal
        visible={showPicker}
        transparent
        animationType="slide"
        onRequestClose={handleCancel}
      >
        <View style={modalStyles.modalOverlay}>
          <View style={[modalStyles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={[modalStyles.modalHeader, { borderBottomColor: theme.colors.border }]}>
              <TouchableOpacity onPress={handleCancel}>
                <Text style={[modalStyles.cancelButton, { color: theme.colors.textSecondary }]}>İptal</Text>
              </TouchableOpacity>
              <Text style={[modalStyles.modalTitle, { color: theme.colors.text }]}>Tarih Seç</Text>
              <TouchableOpacity onPress={handleConfirm}>
                <Text style={[modalStyles.confirmButton, { color: theme.colors.primary }]}>Tamam</Text>
              </TouchableOpacity>
            </View>
            <ScrollView style={{ maxHeight: 300 }} showsVerticalScrollIndicator={false}>
              {dateOptions.map((date, index) => {
                const isSelected = date.toDateString() === tempDate.toDateString();
                const isToday = date.toDateString() === new Date().toDateString();

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      modalStyles.dateOption,
                      {
                        backgroundColor: isSelected
                          ? theme.colors.primary
                          : isToday
                            ? theme.colors.primary + '20'
                            : 'transparent'
                      }
                    ]}
                    onPress={() => {
                      console.log('📅 Date selected:', date.toLocaleDateString());
                      setTempDate(date);
                    }}
                  >
                    <Text style={[
                      modalStyles.dateOptionText,
                      {
                        color: isSelected
                          ? theme.colors.surface
                          : isToday
                            ? theme.colors.primary
                            : theme.colors.text,
                        fontWeight: isToday ? '600' : '400'
                      }
                    ]}>
                      {formatDate(date)} {isToday ? '(Bugün)' : ''}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  const renderIOSPicker = () => {
    // Eğer DateTimePicker çalışmıyorsa basit picker kullan
    if (useSimplePicker) {
      return renderSimplePicker();
    }

    return (
      <Modal
        visible={showPicker}
        transparent
        animationType="slide"
        onRequestClose={handleCancel}
      >
        <View style={modalStyles.modalOverlay}>
          <View style={[modalStyles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={[modalStyles.modalHeader, { borderBottomColor: theme.colors.border }]}>
              <TouchableOpacity onPress={handleCancel}>
                <Text style={[modalStyles.cancelButton, { color: theme.colors.textSecondary }]}>İptal</Text>
              </TouchableOpacity>
              <Text style={[modalStyles.modalTitle, { color: theme.colors.text }]}>Tarih Seç</Text>
              <TouchableOpacity onPress={handleConfirm}>
                <Text style={[modalStyles.confirmButton, { color: theme.colors.primary }]}>Tamam</Text>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              value={tempDate}
              mode="date"
              display="spinner"
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
              locale="tr-TR"
              style={[modalStyles.picker, { backgroundColor: theme.colors.surface }]}
            />
          </View>
        </View>
      </Modal>
    );
  };



  return (
    <View style={[componentStyles.container, style]}>
      {label && <Text style={[componentStyles.label, { color: theme.colors.text }]}>{label}</Text>}
      <TouchableOpacity
        style={[
          componentStyles.dateButton,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
          disabled && { backgroundColor: theme.colors.background, opacity: 0.6 }
        ]}
        onPress={() => {
          console.log('📅 DatePicker button pressed, disabled:', disabled);
          console.log('📅 Current value:', value?.toLocaleDateString());
          if (!disabled) {
            console.log('📅 Opening simple date picker...');
            setUseSimplePicker(true);
            setShowPicker(true);
          }
        }}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <Ionicons
          name="calendar-outline"
          size={20}
          color={disabled ? theme.colors.textSecondary : theme.colors.primary}
        />
        <Text style={[
          componentStyles.dateText,
          { color: disabled ? theme.colors.textSecondary : theme.colors.text }
        ]}>
          {value ? formatDate(value) : placeholder}
        </Text>
        <Ionicons
          name="chevron-down"
          size={16}
          color={disabled ? theme.colors.textSecondary : theme.colors.textSecondary}
        />
      </TouchableOpacity>

      {showPicker && renderSimplePicker()}
    </View>
  );
};

const componentStyles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 50,
  },
  dateText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
});

const modalStyles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area bottom
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cancelButton: {
    fontSize: 16,
  },
  confirmButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  picker: {
    // backgroundColor will be set dynamically
  },
  dateOption: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginVertical: 2,
    marginHorizontal: 10,
  },
  dateOptionText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default DatePicker;
