// Add Budget Screen - Bütçe ekleme ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { EXPENSE_CATEGORIES } from '../../constants/categories';
import { Ionicons } from '@expo/vector-icons';
import DatePicker from '../../components/DatePicker';

const AddBudgetScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [formData, setFormData] = useState({
    name: '',
    categoryId: '',
    amount: '',
    period: 'monthly' as 'weekly' | 'monthly' | 'quarterly' | 'yearly',
    startDate: new Date(),
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    isActive: true,
  });

  const [isLoading, setIsLoading] = useState(false);

  const periods = [
    { id: 'weekly', name: 'Haftalık', icon: 'calendar-outline' },
    { id: 'monthly', name: 'Aylık', icon: 'calendar' },
    { id: 'quarterly', name: '3 Aylık', icon: 'calendar-sharp' },
    { id: 'yearly', name: 'Yıllık', icon: 'calendar-number' },
  ];

  const handleSave = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Bütçe adı gerekli.');
      return;
    }

    if (!formData.categoryId) {
      Alert.alert('Hata', 'Kategori seçimi gerekli.');
      return;
    }

    const amount = parseFloat(formData.amount.replace(',', '.'));
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Hata', 'Geçerli bir tutar girin.');
      return;
    }

    if (formData.startDate >= formData.endDate) {
      Alert.alert('Hata', 'Bitiş tarihi başlangıç tarihinden sonra olmalı.');
      return;
    }

    setIsLoading(true);

    try {
      // Mock budget creation - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('Budget created:', {
        name: formData.name.trim(),
        categoryId: formData.categoryId,
        amount,
        period: formData.period,
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate.toISOString(),
        isActive: formData.isActive,
      });

      Alert.alert('Başarılı', 'Bütçe başarıyla oluşturuldu.', [
        { text: 'Tamam', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Hata', error || 'Bütçe oluşturulurken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (text: string) => {
    const cleaned = text.replace(/[^0-9.,]/g, '');
    return cleaned;
  };

  const updateEndDate = (period: string, startDate: Date) => {
    const newEndDate = new Date(startDate);
    switch (period) {
      case 'weekly':
        newEndDate.setDate(startDate.getDate() + 7);
        break;
      case 'monthly':
        newEndDate.setMonth(startDate.getMonth() + 1);
        break;
      case 'quarterly':
        newEndDate.setMonth(startDate.getMonth() + 3);
        break;
      case 'yearly':
        newEndDate.setFullYear(startDate.getFullYear() + 1);
        break;
    }
    return newEndDate;
  };

  const handlePeriodChange = (period: string) => {
    const newEndDate = updateEndDate(period, formData.startDate);
    setFormData({
      ...formData,
      period: period as any,
      endDate: newEndDate,
    });
  };

  const handleStartDateChange = (date: Date) => {
    const newEndDate = updateEndDate(formData.period, date);
    setFormData({
      ...formData,
      startDate: date,
      endDate: newEndDate,
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 50,
    },
    currencySymbol: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginRight: 8,
    },
    amountInput: {
      flex: 1,
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    categoryScroll: {
      flexDirection: 'row',
    },
    categoryButton: {
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      borderWidth: 1,
      marginRight: 8,
      backgroundColor: theme.colors.surface,
      minWidth: 80,
    },
    categoryButtonActive: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    categoryText: {
      fontSize: 12,
      color: theme.colors.text,
      marginTop: 4,
      textAlign: 'center',
    },
    periodContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    periodButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flex: 1,
      minWidth: '45%',
    },
    periodButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    periodText: {
      fontSize: 14,
      color: theme.colors.text,
      marginLeft: 8,
    },
    periodTextActive: {
      color: theme.colors.surface,
    },
    dateRangeContainer: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 20,
    },
    dateInputContainer: {
      flex: 1,
    },
    dateInput: {
      marginBottom: 0,
    },
    toggleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    toggleLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    toggleText: {
      fontSize: 16,
      color: theme.colors.text,
      marginLeft: 8,
    },
    switch: {
      width: 44,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.border,
      justifyContent: 'center',
      paddingHorizontal: 2,
    },
    switchActive: {
      backgroundColor: theme.colors.primary,
    },
    switchThumb: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: theme.colors.surface,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
    switchThumbActive: {
      transform: [{ translateX: 20 }],
    },
    previewContainer: {
      marginBottom: 20,
    },
    previewTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    previewCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    previewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    previewName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    previewAmount: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    previewDetails: {
      gap: 4,
    },
    previewDetail: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginBottom: 40,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        {/* Budget Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Bütçe Adı *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Örn: Yiyecek Bütçesi"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            autoFocus
          />
        </View>

        {/* Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Bütçe Tutarı *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.amount}
              onChangeText={(text) => setFormData({ ...formData, amount: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Category Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Kategori *</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
            {EXPENSE_CATEGORIES.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  formData.categoryId === category.id && styles.categoryButtonActive,
                  { borderColor: category.color }
                ]}
                onPress={() => setFormData({ ...formData, categoryId: category.id })}
              >
                <Ionicons name={category.icon as any} size={20} color={category.color} />
                <Text style={[
                  styles.categoryText,
                  formData.categoryId === category.id && { color: category.color }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Period Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Bütçe Dönemi</Text>
          <View style={styles.periodContainer}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.periodButton,
                  formData.period === period.id && styles.periodButtonActive
                ]}
                onPress={() => handlePeriodChange(period.id)}
              >
                <Ionicons
                  name={period.icon as any}
                  size={20}
                  color={formData.period === period.id ? theme.colors.surface : theme.colors.textSecondary}
                />
                <Text style={[
                  styles.periodText,
                  formData.period === period.id && styles.periodTextActive
                ]}>
                  {period.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Date Range */}
        <View style={styles.dateRangeContainer}>
          <View style={styles.dateInputContainer}>
            <DatePicker
              label="Başlangıç Tarihi"
              value={formData.startDate}
              onDateChange={handleStartDateChange}
              style={styles.dateInput}
            />
          </View>
          <View style={styles.dateInputContainer}>
            <DatePicker
              label="Bitiş Tarihi"
              value={formData.endDate}
              onDateChange={(date) => setFormData({ ...formData, endDate: date })}
              minimumDate={formData.startDate}
              style={styles.dateInput}
            />
          </View>
        </View>

        {/* Active Toggle */}
        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.toggleContainer}
            onPress={() => setFormData({ ...formData, isActive: !formData.isActive })}
          >
            <View style={styles.toggleLeft}>
              <Ionicons name="power" size={20} color={theme.colors.textSecondary} />
              <Text style={styles.toggleText}>Bütçeyi Aktif Et</Text>
            </View>
            <View style={[styles.switch, formData.isActive && styles.switchActive]}>
              <View style={[styles.switchThumb, formData.isActive && styles.switchThumbActive]} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Budget Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Bütçe Özeti</Text>
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewName}>{formData.name || 'Bütçe Adı'}</Text>
              <Text style={styles.previewAmount}>
                ₺{formData.amount || '0'}
              </Text>
            </View>
            <View style={styles.previewDetails}>
              <Text style={styles.previewDetail}>
                Dönem: {periods.find(p => p.id === formData.period)?.name}
              </Text>
              <Text style={styles.previewDetail}>
                {formData.startDate.toLocaleDateString('tr-TR')} - {formData.endDate.toLocaleDateString('tr-TR')}
              </Text>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Oluşturuluyor...' : 'Bütçe Oluştur'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AddBudgetScreen;
