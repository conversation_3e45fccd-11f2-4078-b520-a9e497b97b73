// Mock Bank API Service - Demo/Test için sahte banka API'si
// RFC-002: Bank API Integration - Demo Implementation

import AsyncStorage from '@react-native-async-storage/async-storage';
import { BankAccount, BankTransaction, BankConnectionStatus } from './BankApiService';

// Mock Bank Data
const MOCK_BANKS = {
  DEMO_BANK: {
    name: 'Demo Bank',
    accounts: [
      {
        account_id: 'demo_acc_001',
        account_number: '**********',
        account_type: 'checking' as const,
        balance: 15750.50,
        available_balance: 15750.50,
        currency: 'TRY',
        iban: 'TR33 0006 1005 1978 6457 8413 26',
      },
      {
        account_id: 'demo_acc_002',
        account_number: '**********',
        account_type: 'savings' as const,
        balance: 45200.75,
        available_balance: 45200.75,
        currency: 'TRY',
        iban: 'TR64 0004 6007 8888 8006 6665 01',
      }
    ],
    transactions: [
      {
        transaction_id: 'demo_tx_001',
        account_id: 'demo_acc_001',
        amount: -125.50,
        currency: 'TRY',
        description: 'MIGROS MARKET ALISVERISI',
        merchant_name: 'Migros',
        category: 'groceries',
        date: '2025-05-31',
        status: 'completed',
        reference: 'REF001',
        running_balance: 15750.50,
      },
      {
        transaction_id: 'demo_tx_002',
        account_id: 'demo_acc_001',
        amount: -85.00,
        currency: 'TRY',
        description: 'SHELL BENZIN ISTASYONU',
        merchant_name: 'Shell',
        category: 'gas_stations',
        date: '2025-05-30',
        status: 'completed',
        reference: 'REF002',
        running_balance: 15876.00,
      },
      {
        transaction_id: 'demo_tx_003',
        account_id: 'demo_acc_001',
        amount: 3500.00,
        currency: 'TRY',
        description: 'MAAS ODEMESI',
        merchant_name: 'ABC SIRKET',
        category: 'transfer',
        date: '2025-05-29',
        status: 'completed',
        reference: 'REF003',
        running_balance: 15961.00,
      },
      {
        transaction_id: 'demo_tx_004',
        account_id: 'demo_acc_001',
        amount: -45.75,
        currency: 'TRY',
        description: 'STARBUCKS KAHVE',
        merchant_name: 'Starbucks',
        category: 'food_and_drink',
        date: '2025-05-28',
        status: 'completed',
        reference: 'REF004',
        running_balance: 12461.00,
      },
      {
        transaction_id: 'demo_tx_005',
        account_id: 'demo_acc_001',
        amount: -250.00,
        currency: 'TRY',
        description: 'ELEKTRIK FATURASI',
        merchant_name: 'BEDAS',
        category: 'utilities',
        date: '2025-05-27',
        status: 'completed',
        reference: 'REF005',
        running_balance: 12506.75,
      },
    ]
  },
  GARANTI_DEMO: {
    name: 'Garanti BBVA Demo',
    accounts: [
      {
        account_id: 'garanti_acc_001',
        account_number: '**********',
        account_type: 'credit_card' as const,
        balance: -2450.30,
        available_balance: 7549.70,
        currency: 'TRY',
        iban: 'TR12 0062 0004 0000 0045 6789 01',
      }
    ],
    transactions: [
      {
        transaction_id: 'garanti_tx_001',
        account_id: 'garanti_acc_001',
        amount: -150.00,
        currency: 'TRY',
        description: 'AMAZON ONLINE ALISVERIS',
        merchant_name: 'Amazon',
        category: 'shopping',
        date: '2025-05-31',
        status: 'completed',
        reference: 'GREF001',
        running_balance: -2450.30,
      },
      {
        transaction_id: 'garanti_tx_002',
        account_id: 'garanti_acc_001',
        amount: -75.50,
        currency: 'TRY',
        description: 'NETFLIX ABONELIK',
        merchant_name: 'Netflix',
        category: 'entertainment',
        date: '2025-05-30',
        status: 'completed',
        reference: 'GREF002',
        running_balance: -2300.30,
      },
    ]
  }
};

class MockBankApiService {
  private static instance: MockBankApiService;

  private constructor() {}

  static getInstance(): MockBankApiService {
    if (!MockBankApiService.instance) {
      MockBankApiService.instance = new MockBankApiService();
    }
    return MockBankApiService.instance;
  }

  /**
   * Mock OAuth flow - Demo banka bağlantısı
   */
  async initiateMockBankConnection(bankCode: string): Promise<{
    success: boolean;
    account?: BankAccount;
    error?: string;
  }> {
    try {
      console.log('🏦 Mock: Connecting to demo bank:', bankCode);

      // Simulated delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockBank = MOCK_BANKS[bankCode as keyof typeof MOCK_BANKS];
      if (!mockBank) {
        return {
          success: false,
          error: 'Demo banka bulunamadı'
        };
      }

      // İlk hesabı al
      const firstAccount = mockBank.accounts[0];
      const bankAccount: BankAccount = {
        id: `${bankCode}_${firstAccount.account_id}`,
        bankCode,
        bankName: mockBank.name,
        accountNumber: firstAccount.account_number,
        accountType: firstAccount.account_type,
        currency: firstAccount.currency,
        balance: firstAccount.balance,
        availableBalance: firstAccount.available_balance,
        iban: firstAccount.iban,
        isActive: true,
        lastSyncAt: new Date().toISOString(),
        accessToken: `mock_token_${Date.now()}`,
        refreshToken: `mock_refresh_${Date.now()}`,
        tokenExpiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 saat
      };

      // Hesabı kaydet
      await this.storeMockBankAccount(bankAccount);

      console.log('✅ Mock bank account connected:', bankAccount.id);
      return {
        success: true,
        account: bankAccount
      };

    } catch (error) {
      console.error('Mock bank connection error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Mock işlem senkronizasyonu
   */
  async syncMockTransactions(accountId: string): Promise<{
    success: boolean;
    transactions?: BankTransaction[];
    newTransactionCount?: number;
    error?: string;
  }> {
    try {
      console.log('📊 Mock: Syncing transactions for:', accountId);

      // Simulated delay
      await new Promise(resolve => setTimeout(resolve, 800));

      const [bankCode, mockAccountId] = accountId.split('_');
      const mockBank = MOCK_BANKS[bankCode as keyof typeof MOCK_BANKS];
      
      if (!mockBank) {
        return { success: false, error: 'Demo banka bulunamadı' };
      }

      // İşlemleri map et
      const bankTransactions: BankTransaction[] = mockBank.transactions
        .filter(tx => tx.account_id === mockAccountId)
        .map(tx => ({
          id: `${bankCode}_${tx.transaction_id}`,
          accountId: accountId,
          amount: Math.abs(tx.amount),
          currency: tx.currency,
          description: tx.description,
          merchant: tx.merchant_name,
          category: this.mapMockCategory(tx.category),
          date: tx.date,
          type: tx.amount > 0 ? 'credit' : 'debit',
          status: tx.status as any,
          reference: tx.reference,
          balance: tx.running_balance,
        }));

      // Yeni işlemleri belirle
      const existingTransactionIds = await this.getMockImportedTransactionIds(accountId);
      const newTransactions = bankTransactions.filter(tx => 
        !existingTransactionIds.includes(tx.id)
      );

      // Yeni işlemleri kaydet
      if (newTransactions.length > 0) {
        await this.saveMockImportedTransactions(newTransactions);
        console.log(`✅ Mock: Imported ${newTransactions.length} new transactions`);
      }

      return {
        success: true,
        transactions: bankTransactions,
        newTransactionCount: newTransactions.length
      };

    } catch (error) {
      console.error('Mock transaction sync error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Mock bakiye senkronizasyonu
   */
  async syncMockAccountBalance(accountId: string): Promise<{
    success: boolean;
    balance?: number;
    error?: string;
  }> {
    try {
      console.log('💰 Mock: Syncing balance for:', accountId);

      // Simulated delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const account = await this.getMockBankAccount(accountId);
      if (!account) {
        return { success: false, error: 'Hesap bulunamadı' };
      }

      // Rastgele küçük değişiklik (demo için)
      const balanceChange = (Math.random() - 0.5) * 100; // -50 ile +50 arası
      const newBalance = account.balance + balanceChange;

      // Hesap bilgisini güncelle
      account.balance = Math.round(newBalance * 100) / 100;
      account.lastSyncAt = new Date().toISOString();
      
      await this.updateMockBankAccount(account);

      console.log('✅ Mock: Balance synced:', account.balance);
      return {
        success: true,
        balance: account.balance
      };

    } catch (error) {
      console.error('Mock balance sync error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Mock bağlı hesapları getir
   */
  async getMockConnectedAccounts(): Promise<BankAccount[]> {
    try {
      const accountsData = await AsyncStorage.getItem('mock_bank_accounts');
      if (!accountsData) return [];

      const accounts: BankAccount[] = JSON.parse(accountsData);
      return accounts.filter(account => account.isActive);
    } catch (error) {
      console.error('Error getting mock connected accounts:', error);
      return [];
    }
  }

  /**
   * Mock hesap bağlantısını kes
   */
  async disconnectMockAccount(accountId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('🔌 Mock: Disconnecting account:', accountId);

      const account = await this.getMockBankAccount(accountId);
      if (!account) {
        return { success: false, error: 'Hesap bulunamadı' };
      }

      // Hesabı deaktif et
      account.isActive = false;
      await this.updateMockBankAccount(account);

      console.log('✅ Mock: Account disconnected');
      return { success: true };

    } catch (error) {
      console.error('Mock account disconnection error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  // Helper methods
  private async storeMockBankAccount(account: BankAccount): Promise<void> {
    const accounts = await this.getMockConnectedAccounts();
    const existingIndex = accounts.findIndex(a => a.id === account.id);
    
    if (existingIndex >= 0) {
      accounts[existingIndex] = account;
    } else {
      accounts.push(account);
    }

    await AsyncStorage.setItem('mock_bank_accounts', JSON.stringify(accounts));
  }

  private async getMockBankAccount(accountId: string): Promise<BankAccount | null> {
    const accounts = await this.getMockConnectedAccounts();
    return accounts.find(account => account.id === accountId) || null;
  }

  private async updateMockBankAccount(account: BankAccount): Promise<void> {
    await this.storeMockBankAccount(account);
  }

  private async getMockImportedTransactionIds(accountId: string): Promise<string[]> {
    try {
      const data = await AsyncStorage.getItem(`mock_imported_transactions_${accountId}`);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  private async saveMockImportedTransactions(transactions: BankTransaction[]): Promise<void> {
    for (const tx of transactions) {
      const existingIds = await this.getMockImportedTransactionIds(tx.accountId);
      existingIds.push(tx.id);
      await AsyncStorage.setItem(
        `mock_imported_transactions_${tx.accountId}`, 
        JSON.stringify(existingIds)
      );
    }
  }

  private mapMockCategory(mockCategory: string): string {
    const categoryMap: { [key: string]: string } = {
      'food_and_drink': 'dining',
      'groceries': 'groceries',
      'gas_stations': 'fuel',
      'transportation': 'transportation',
      'shopping': 'shopping',
      'healthcare': 'healthcare',
      'utilities': 'utilities',
      'entertainment': 'entertainment',
      'travel': 'travel',
      'transfer': 'other',
    };

    return categoryMap[mockCategory] || 'other';
  }
}

export default MockBankApiService;
