// Offline Service - Çevrimdışı çalışma servisi

import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import EncryptionService from './EncryptionService';
import { Transaction } from '../types';
import databaseManager from '../database/DatabaseManager';

interface PendingAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  endpoint: string;
  data: any;
  timestamp: number;
}

interface CacheItem {
  data: any;
  timestamp: number;
  expiry: number;
}

// RFC-002: Offline Support - Advanced Interfaces
export interface OfflineTransaction extends Transaction {
  isOffline: boolean;
  offlineId: string;
  syncStatus: 'pending' | 'syncing' | 'synced' | 'failed';
  createdOfflineAt: string;
  lastSyncAttempt?: string;
  syncAttempts: number;
  conflictData?: any;
}

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  conflictCount: number;
  errors: string[];
}

export interface NetworkStatus {
  isConnected: boolean;
  type: string;
  isInternetReachable: boolean;
  connectionQuality: 'poor' | 'good' | 'excellent';
}

export interface OfflineQueueItem {
  id: string;
  type: 'transaction' | 'update' | 'delete';
  data: any;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
  priority: 'low' | 'medium' | 'high';
}

export interface ConflictResolution {
  strategy: 'server_wins' | 'client_wins' | 'merge' | 'manual';
  resolvedData?: any;
  timestamp: string;
}

class OfflineService {
  private static instance: OfflineService;
  private readonly PENDING_ACTIONS_KEY = 'pending_actions';
  private readonly CACHE_PREFIX = 'cache_';
  private readonly DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 dakika
  private isOnline: boolean = true;
  private listeners: ((isOnline: boolean) => void)[] = [];

  // RFC-002: Advanced Offline Support
  private readonly OFFLINE_TRANSACTIONS_KEY = 'offline_transactions';
  private readonly SYNC_QUEUE_KEY = 'sync_queue';
  private readonly LAST_SYNC_KEY = 'last_sync_timestamp';
  private readonly CONFLICT_LOG_KEY = 'conflict_resolution_log';
  private dbManager: DatabaseManager;
  private networkStatus: NetworkStatus = {
    isConnected: false,
    type: 'unknown',
    isInternetReachable: false,
    connectionQuality: 'poor',
  };
  private syncInProgress = false;
  private networkListeners: ((status: NetworkStatus) => void)[] = [];

  static getInstance(): OfflineService {
    if (!OfflineService.instance) {
      OfflineService.instance = new OfflineService();
    }
    return OfflineService.instance;
  }

  constructor() {
    this.dbManager = databaseManager;
    this.initializeNetworkListener();
    this.initializeAdvancedNetworkMonitoring();
  }

  /**
   * Ağ durumu dinleyicisini başlatır
   */
  private initializeNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (!wasOnline && this.isOnline) {
        // Çevrimiçi olduğunda bekleyen işlemleri gönder
        this.processPendingActions();
      }
      
      // Dinleyicileri bilgilendir
      this.listeners.forEach(listener => listener(this.isOnline));
    });
  }

  /**
   * Ağ durumu dinleyicisi ekler
   */
  addNetworkListener(listener: (isOnline: boolean) => void): () => void {
    this.listeners.push(listener);
    
    // Cleanup fonksiyonu döndür
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Mevcut ağ durumunu döndürür
   */
  isConnected(): boolean {
    return this.isOnline;
  }

  /**
   * Bekleyen işlemleri alır
   */
  private async getPendingActions(): Promise<PendingAction[]> {
    try {
      const actions = await EncryptionService.secureRetrieve(this.PENDING_ACTIONS_KEY);
      return actions || [];
    } catch (error) {
      console.error('Error getting pending actions:', error);
      return [];
    }
  }

  /**
   * Bekleyen işlemleri kaydeder
   */
  private async savePendingActions(actions: PendingAction[]): Promise<void> {
    try {
      await EncryptionService.secureStore(this.PENDING_ACTIONS_KEY, actions);
    } catch (error) {
      console.error('Error saving pending actions:', error);
    }
  }

  /**
   * Çevrimdışı işlem ekler
   */
  async addPendingAction(
    type: 'CREATE' | 'UPDATE' | 'DELETE',
    endpoint: string,
    data: any
  ): Promise<void> {
    const action: PendingAction = {
      id: Date.now().toString(),
      type,
      endpoint,
      data,
      timestamp: Date.now(),
    };

    const actions = await this.getPendingActions();
    actions.push(action);
    await this.savePendingActions(actions);
  }

  /**
   * Bekleyen işlemleri işler
   */
  async processPendingActions(): Promise<void> {
    if (!this.isOnline) {
      return;
    }

    const actions = await this.getPendingActions();
    const processedActions: string[] = [];

    for (const action of actions) {
      try {
        // Burada gerçek API çağrısı yapılacak
        console.log(`Processing action: ${action.type} ${action.endpoint}`, action.data);
        
        // Simüle edilmiş API çağrısı
        await this.simulateApiCall(action);
        
        processedActions.push(action.id);
      } catch (error) {
        console.error(`Error processing action ${action.id}:`, error);
        // Hata durumunda işlemi beklemede bırak
      }
    }

    // İşlenen işlemleri kaldır
    if (processedActions.length > 0) {
      const remainingActions = actions.filter(
        action => !processedActions.includes(action.id)
      );
      await this.savePendingActions(remainingActions);
    }
  }

  /**
   * API çağrısını simüle eder
   */
  private async simulateApiCall(action: PendingAction): Promise<void> {
    // Gerçek uygulamada burada HTTP isteği yapılacak
    return new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
  }

  /**
   * Veriyi cache'e kaydeder
   */
  async cacheData(
    key: string,
    data: any,
    duration: number = this.DEFAULT_CACHE_DURATION
  ): Promise<void> {
    try {
      const cacheItem: CacheItem = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + duration,
      };

      await EncryptionService.secureStore(`${this.CACHE_PREFIX}${key}`, cacheItem);
    } catch (error) {
      console.error('Error caching data:', error);
    }
  }

  /**
   * Cache'den veri alır
   */
  async getCachedData(key: string): Promise<any> {
    try {
      const cacheItem: CacheItem = await EncryptionService.secureRetrieve(
        `${this.CACHE_PREFIX}${key}`
      );

      if (!cacheItem) {
        return null;
      }

      // Süre kontrolü
      if (Date.now() > cacheItem.expiry) {
        await this.removeCachedData(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('Error getting cached data:', error);
      return null;
    }
  }

  /**
   * Cache'den veri siler
   */
  async removeCachedData(key: string): Promise<void> {
    try {
      await EncryptionService.secureRemove(`${this.CACHE_PREFIX}${key}`);
    } catch (error) {
      console.error('Error removing cached data:', error);
    }
  }

  /**
   * Tüm cache'i temizler
   */
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(`secure_${this.CACHE_PREFIX}`));
      
      await Promise.all(
        cacheKeys.map(key => AsyncStorage.removeItem(key))
      );
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Cache boyutunu alır
   */
  async getCacheSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(`secure_${this.CACHE_PREFIX}`));
      
      let totalSize = 0;
      for (const key of cacheKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }
      
      return totalSize;
    } catch (error) {
      console.error('Error getting cache size:', error);
      return 0;
    }
  }

  /**
   * Eski cache verilerini temizler
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(`secure_${this.CACHE_PREFIX}`));
      
      for (const key of cacheKeys) {
        const cacheKey = key.replace('secure_', '').replace(this.CACHE_PREFIX, '');
        const cachedData = await this.getCachedData(cacheKey);
        
        // getCachedData zaten süresi dolmuş verileri siler
        if (!cachedData) {
          continue;
        }
      }
    } catch (error) {
      console.error('Error cleaning up expired cache:', error);
    }
  }

  /**
   * Bekleyen işlem sayısını döndürür
   */
  async getPendingActionCount(): Promise<number> {
    const actions = await this.getPendingActions();
    return actions.length;
  }

  /**
   * Tüm offline verilerini temizler
   */
  async clearOfflineData(): Promise<void> {
    try {
      await EncryptionService.secureRemove(this.PENDING_ACTIONS_KEY);
      await this.clearCache();
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }

  // ============================================================================
  // RFC-002: Advanced Offline Support Methods
  // ============================================================================

  /**
   * Gelişmiş ağ durumu izleme
   */
  private async initializeAdvancedNetworkMonitoring(): Promise<void> {
    NetInfo.addEventListener(state => {
      const wasOnline = this.networkStatus.isConnected;

      this.networkStatus = {
        isConnected: state.isConnected ?? false,
        type: state.type || 'unknown',
        isInternetReachable: state.isInternetReachable ?? false,
        connectionQuality: this.determineConnectionQuality(state),
      };

      // Çevrimiçi olduğunda otomatik senkronizasyon
      if (!wasOnline && this.networkStatus.isConnected) {
        console.log('🌐 Network restored, starting sync...');
        this.startBackgroundSync();
      }

      // Network listeners'ı bilgilendir
      this.networkListeners.forEach(listener => listener(this.networkStatus));
    });
  }

  /**
   * Bağlantı kalitesini belirle
   */
  private determineConnectionQuality(state: any): 'poor' | 'good' | 'excellent' {
    if (!state.isConnected) return 'poor';

    if (state.type === 'wifi') return 'excellent';
    if (state.type === 'cellular') {
      // Cellular bağlantı kalitesi (gerçek uygulamada daha detaylı olabilir)
      return state.details?.cellularGeneration === '4g' || state.details?.cellularGeneration === '5g'
        ? 'good' : 'poor';
    }

    return 'good';
  }

  /**
   * Network status listener ekle
   */
  addNetworkStatusListener(listener: (status: NetworkStatus) => void): () => void {
    this.networkListeners.push(listener);

    return () => {
      const index = this.networkListeners.indexOf(listener);
      if (index > -1) {
        this.networkListeners.splice(index, 1);
      }
    };
  }

  /**
   * Mevcut network durumunu al
   */
  getNetworkStatus(): NetworkStatus {
    return this.networkStatus;
  }

  /**
   * Offline transaction kaydet
   */
  async saveOfflineTransaction(transaction: Omit<Transaction, 'id'>): Promise<OfflineTransaction> {
    try {
      const offlineTransaction: OfflineTransaction = {
        ...transaction,
        id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        isOffline: true,
        offlineId: `offline_${Date.now()}`,
        syncStatus: 'pending',
        createdOfflineAt: new Date().toISOString(),
        syncAttempts: 0,
      };

      // Local database'e kaydet
      await this.dbManager.addTransaction(offlineTransaction);

      // Offline transactions listesine ekle
      const offlineTransactions = await this.getOfflineTransactions();
      offlineTransactions.push(offlineTransaction);
      await this.saveOfflineTransactions(offlineTransactions);

      console.log('💾 Offline transaction saved:', offlineTransaction.id);
      return offlineTransaction;
    } catch (error) {
      console.error('Error saving offline transaction:', error);
      throw new Error('Offline işlem kaydedilemedi');
    }
  }

  /**
   * Offline transactions listesini al
   */
  private async getOfflineTransactions(): Promise<OfflineTransaction[]> {
    try {
      const transactions = await EncryptionService.secureRetrieve(this.OFFLINE_TRANSACTIONS_KEY);
      return transactions || [];
    } catch (error) {
      console.error('Error getting offline transactions:', error);
      return [];
    }
  }

  /**
   * Offline transactions listesini kaydet
   */
  private async saveOfflineTransactions(transactions: OfflineTransaction[]): Promise<void> {
    try {
      await EncryptionService.secureStore(this.OFFLINE_TRANSACTIONS_KEY, transactions);
    } catch (error) {
      console.error('Error saving offline transactions:', error);
    }
  }

  /**
   * Arka plan senkronizasyonu başlat
   */
  async startBackgroundSync(): Promise<SyncResult> {
    if (this.syncInProgress || !this.networkStatus.isConnected) {
      return {
        success: false,
        syncedCount: 0,
        failedCount: 0,
        conflictCount: 0,
        errors: ['Sync already in progress or no network connection'],
      };
    }

    this.syncInProgress = true;
    console.log('🔄 Starting background sync...');

    try {
      const result = await this.syncOfflineTransactions();
      await this.updateLastSyncTimestamp();

      console.log('✅ Background sync completed:', result);
      return result;
    } catch (error) {
      console.error('Background sync error:', error);
      return {
        success: false,
        syncedCount: 0,
        failedCount: 0,
        conflictCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown sync error'],
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Offline transactions'ları senkronize et
   */
  private async syncOfflineTransactions(): Promise<SyncResult> {
    const offlineTransactions = await this.getOfflineTransactions();
    const pendingTransactions = offlineTransactions.filter(tx => tx.syncStatus === 'pending');

    let syncedCount = 0;
    let failedCount = 0;
    let conflictCount = 0;
    const errors: string[] = [];

    for (const transaction of pendingTransactions) {
      try {
        // Sync status'u güncelle
        transaction.syncStatus = 'syncing';
        transaction.lastSyncAttempt = new Date().toISOString();
        transaction.syncAttempts += 1;

        // Simulated server sync (gerçek uygulamada API çağrısı)
        const syncResult = await this.syncTransactionToServer(transaction);

        if (syncResult.success) {
          transaction.syncStatus = 'synced';
          syncedCount++;
          console.log('✅ Transaction synced:', transaction.id);
        } else if (syncResult.conflict) {
          transaction.syncStatus = 'failed';
          transaction.conflictData = syncResult.conflictData;
          conflictCount++;
          console.warn('⚠️ Sync conflict:', transaction.id);
        } else {
          transaction.syncStatus = 'failed';
          failedCount++;
          errors.push(`Transaction ${transaction.id}: ${syncResult.error}`);
          console.error('❌ Sync failed:', transaction.id, syncResult.error);
        }
      } catch (error) {
        transaction.syncStatus = 'failed';
        failedCount++;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Transaction ${transaction.id}: ${errorMsg}`);
        console.error('❌ Sync error:', transaction.id, error);
      }
    }

    // Güncellenmiş transactions'ları kaydet
    await this.saveOfflineTransactions(offlineTransactions);

    return {
      success: errors.length === 0,
      syncedCount,
      failedCount,
      conflictCount,
      errors,
    };
  }

  /**
   * Transaction'ı server'a senkronize et (simulated)
   */
  private async syncTransactionToServer(transaction: OfflineTransaction): Promise<{
    success: boolean;
    conflict?: boolean;
    conflictData?: any;
    error?: string;
  }> {
    // Simulated API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulated success (90% success rate)
    if (Math.random() > 0.1) {
      return { success: true };
    }

    // Simulated conflict (5% conflict rate)
    if (Math.random() > 0.5) {
      return {
        success: false,
        conflict: true,
        conflictData: {
          serverVersion: { ...transaction, amount: transaction.amount + 10 },
          clientVersion: transaction,
        },
      };
    }

    // Simulated error
    return {
      success: false,
      error: 'Server temporarily unavailable',
    };
  }

  /**
   * Son senkronizasyon zamanını güncelle
   */
  private async updateLastSyncTimestamp(): Promise<void> {
    try {
      await EncryptionService.secureStore(this.LAST_SYNC_KEY, new Date().toISOString());
    } catch (error) {
      console.error('Error updating last sync timestamp:', error);
    }
  }

  /**
   * Son senkronizasyon zamanını al
   */
  async getLastSyncTimestamp(): Promise<string | null> {
    try {
      return await EncryptionService.secureRetrieve(this.LAST_SYNC_KEY);
    } catch (error) {
      console.error('Error getting last sync timestamp:', error);
      return null;
    }
  }

  /**
   * Bekleyen offline transaction sayısını al
   */
  async getPendingOfflineTransactionCount(): Promise<number> {
    const offlineTransactions = await this.getOfflineTransactions();
    return offlineTransactions.filter(tx => tx.syncStatus === 'pending').length;
  }

  /**
   * Conflict resolution uygula
   */
  async resolveConflict(
    transactionId: string,
    resolution: ConflictResolution
  ): Promise<boolean> {
    try {
      const offlineTransactions = await this.getOfflineTransactions();
      const transaction = offlineTransactions.find(tx => tx.id === transactionId);

      if (!transaction || !transaction.conflictData) {
        return false;
      }

      let resolvedTransaction: OfflineTransaction;

      switch (resolution.strategy) {
        case 'server_wins':
          resolvedTransaction = { ...transaction.conflictData.serverVersion };
          break;
        case 'client_wins':
          resolvedTransaction = { ...transaction };
          break;
        case 'merge':
          resolvedTransaction = this.mergeTransactions(
            transaction,
            transaction.conflictData.serverVersion
          );
          break;
        case 'manual':
          if (!resolution.resolvedData) {
            return false;
          }
          resolvedTransaction = { ...transaction, ...resolution.resolvedData };
          break;
        default:
          return false;
      }

      // Conflict'i çözülmüş olarak işaretle
      resolvedTransaction.syncStatus = 'pending';
      resolvedTransaction.conflictData = undefined;

      // Transaction'ı güncelle
      const index = offlineTransactions.findIndex(tx => tx.id === transactionId);
      offlineTransactions[index] = resolvedTransaction;
      await this.saveOfflineTransactions(offlineTransactions);

      // Conflict log'una kaydet
      await this.logConflictResolution(transactionId, resolution);

      console.log('✅ Conflict resolved:', transactionId, resolution.strategy);
      return true;
    } catch (error) {
      console.error('Error resolving conflict:', error);
      return false;
    }
  }

  /**
   * İki transaction'ı merge et
   */
  private mergeTransactions(
    clientTransaction: OfflineTransaction,
    serverTransaction: any
  ): OfflineTransaction {
    // Basit merge stratejisi - daha karmaşık logic eklenebilir
    return {
      ...clientTransaction,
      amount: serverTransaction.amount, // Server amount'ı kullan
      description: clientTransaction.description, // Client description'ı kullan
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * Conflict resolution'ı logla
   */
  private async logConflictResolution(
    transactionId: string,
    resolution: ConflictResolution
  ): Promise<void> {
    try {
      const logs = await EncryptionService.secureRetrieve(this.CONFLICT_LOG_KEY) || [];
      logs.push({
        transactionId,
        resolution,
        timestamp: new Date().toISOString(),
      });
      await EncryptionService.secureStore(this.CONFLICT_LOG_KEY, logs);
    } catch (error) {
      console.error('Error logging conflict resolution:', error);
    }
  }

  /**
   * Sync durumunu al
   */
  async getSyncStatus(): Promise<{
    isOnline: boolean;
    syncInProgress: boolean;
    pendingCount: number;
    lastSync: string | null;
    networkQuality: string;
  }> {
    const pendingCount = await this.getPendingOfflineTransactionCount();
    const lastSync = await this.getLastSyncTimestamp();

    return {
      isOnline: this.networkStatus.isConnected,
      syncInProgress: this.syncInProgress,
      pendingCount,
      lastSync,
      networkQuality: this.networkStatus.connectionQuality,
    };
  }

  /**
   * Manuel senkronizasyon tetikle
   */
  async triggerManualSync(): Promise<SyncResult> {
    if (!this.networkStatus.isConnected) {
      return {
        success: false,
        syncedCount: 0,
        failedCount: 0,
        conflictCount: 0,
        errors: ['No network connection available'],
      };
    }

    return await this.startBackgroundSync();
  }
}

export default OfflineService.getInstance();
