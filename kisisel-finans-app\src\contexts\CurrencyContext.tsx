// Currency Context - Çoklu para birimi desteği

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  rate: number; // USD bazında kur
  flag: string;
}

export interface ExchangeRates {
  [key: string]: number;
}

interface CurrencyContextType {
  currencies: Currency[];
  baseCurrency: Currency;
  exchangeRates: ExchangeRates;
  isLoading: boolean;
  setBaseCurrency: (currency: Currency) => Promise<void>;
  convertAmount: (amount: number, fromCurrency: string, toCurrency: string) => number;
  formatAmount: (amount: number, currencyCode?: string) => string;
  updateExchangeRates: () => Promise<void>;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

// Desteklenen para birimleri
const SUPPORTED_CURRENCIES: Currency[] = [
  { code: 'TRY', name: 'Türk Lirası', symbol: '₺', rate: 1, flag: '🇹🇷' },
  { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1, flag: '🇺🇸' },
  { code: 'EUR', name: 'Euro', symbol: '€', rate: 1, flag: '🇪🇺' },
  { code: 'GBP', name: 'British Pound', symbol: '£', rate: 1, flag: '🇬🇧' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', rate: 1, flag: '🇯🇵' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', rate: 1, flag: '🇨🇭' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', rate: 1, flag: '🇨🇦' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', rate: 1, flag: '🇦🇺' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', rate: 1, flag: '🇨🇳' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', rate: 1, flag: '🇮🇳' },
];

interface CurrencyProviderProps {
  children: ReactNode;
}

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [currencies] = useState<Currency[]>(SUPPORTED_CURRENCIES);
  const [baseCurrency, setBaseCurrencyState] = useState<Currency>(SUPPORTED_CURRENCIES[0]); // TRY default
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSavedCurrency();
    // Exchange rates'i async yap - uygulama açılışını bloklamasın
    setTimeout(() => {
      updateExchangeRates();
    }, 2000); // 2 saniye sonra yükle
  }, []);

  const loadSavedCurrency = async () => {
    try {
      const savedCurrencyCode = await AsyncStorage.getItem('base_currency');
      if (savedCurrencyCode) {
        const savedCurrency = currencies.find(c => c.code === savedCurrencyCode);
        if (savedCurrency) {
          setBaseCurrencyState(savedCurrency);
        }
      }
    } catch (error) {
      console.error('Error loading saved currency:', error);
    }
  };

  const setBaseCurrency = async (currency: Currency) => {
    try {
      await AsyncStorage.setItem('base_currency', currency.code);
      setBaseCurrencyState(currency);
    } catch (error) {
      console.error('Error saving currency:', error);
    }
  };

  const updateExchangeRates = async () => {
    try {
      setIsLoading(true);

      // Cache kontrolü - 1 saat cache
      const cacheKey = 'exchange_rates_cache';
      const cacheTimeKey = 'exchange_rates_cache_time';
      const cacheTime = await AsyncStorage.getItem(cacheTimeKey);
      const now = Date.now();
      const oneHour = 60 * 60 * 1000; // 1 saat

      if (cacheTime && (now - parseInt(cacheTime)) < oneHour) {
        const cachedRates = await AsyncStorage.getItem(cacheKey);
        if (cachedRates) {
          setExchangeRates(JSON.parse(cachedRates));
          console.log('📱 Using cached exchange rates');
          setIsLoading(false);
          return;
        }
      }

      console.log('🔄 Fetching real exchange rates...');

      // Ücretsiz Exchange Rate API kullan
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data && data.rates) {
        // USD bazlı kurları al
        const rates: ExchangeRates = {
          'USD': 1,
          'EUR': data.rates.EUR || 0.85,
          'GBP': data.rates.GBP || 0.73,
          'TRY': data.rates.TRY || 30.50,
          'JPY': data.rates.JPY || 110.25,
          'CHF': data.rates.CHF || 0.92,
          'CAD': data.rates.CAD || 1.25,
          'AUD': data.rates.AUD || 1.35,
          'CNY': data.rates.CNY || 6.45,
          'INR': data.rates.INR || 74.50,
        };

        setExchangeRates(rates);

        // Cache'e kaydet
        await AsyncStorage.setItem(cacheKey, JSON.stringify(rates));
        await AsyncStorage.setItem(cacheTimeKey, now.toString());

        console.log('✅ Real exchange rates updated successfully');
        console.log('📊 Current rates:', rates);
      } else {
        throw new Error('Invalid API response format');
      }

    } catch (error) {
      console.error('❌ Error fetching real exchange rates:', error);

      // Fallback rates - güncel yaklaşık değerler
      const fallbackRates: ExchangeRates = {
        'USD': 1,
        'EUR': 0.85,
        'GBP': 0.73,
        'TRY': 30.50,
        'JPY': 110.25,
        'CHF': 0.92,
        'CAD': 1.25,
        'AUD': 1.35,
        'CNY': 6.45,
        'INR': 74.50,
      };
      setExchangeRates(fallbackRates);
      console.log('📱 Using fallback exchange rates due to API error');
    } finally {
      setIsLoading(false);
    }
  };

  const convertAmount = (amount: number, fromCurrency: string, toCurrency: string): number => {
    if (fromCurrency === toCurrency) return amount;

    const fromRate = exchangeRates[fromCurrency] || 1;
    const toRate = exchangeRates[toCurrency] || 1;

    // Convert to USD first, then to target currency
    const usdAmount = amount / fromRate;
    const convertedAmount = usdAmount * toRate;

    return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places
  };

  const formatAmount = (amount: number, currencyCode?: string): string => {
    const currency = currencyCode
      ? currencies.find(c => c.code === currencyCode) || baseCurrency
      : baseCurrency;

    const formattedNumber = new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Math.abs(amount));

    const sign = amount < 0 ? '-' : '';

    // Different formatting based on currency
    switch (currency.code) {
      case 'USD':
      case 'CAD':
      case 'AUD':
        return `${sign}${currency.symbol}${formattedNumber}`;
      case 'EUR':
        return `${sign}${formattedNumber} ${currency.symbol}`;
      case 'GBP':
        return `${sign}${currency.symbol}${formattedNumber}`;
      case 'JPY':
      case 'CNY':
      case 'INR':
        return `${sign}${currency.symbol}${formattedNumber}`;
      case 'TRY':
        return `${sign}${formattedNumber} ${currency.symbol}`;
      case 'CHF':
        return `${sign}${formattedNumber} ${currency.symbol}`;
      default:
        return `${sign}${formattedNumber} ${currency.symbol}`;
    }
  };

  const value: CurrencyContextType = {
    currencies,
    baseCurrency,
    exchangeRates,
    isLoading,
    setBaseCurrency,
    convertAmount,
    formatAmount,
    updateExchangeRates,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};
