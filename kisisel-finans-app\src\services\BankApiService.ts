// Bank API Service - Açık Bankacılık API Entegrasyonu
// RFC-002: Bank API Integration

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction, PaymentMethod } from '../types';
import SecureHttpClient from './SecureHttpClient';
import SecurityService from './SecurityService';

// Bank API Configuration
const BANK_API_CONFIG = {
  // Türkiye Açık Bankacılık API'leri
  AKBANK: {
    baseUrl: 'https://api.akbank.com/v1',
    clientId: process.env.AKBANK_CLIENT_ID || 'demo_client_id',
    scope: 'accounts transactions',
  },
  GARANTI: {
    baseUrl: 'https://api.garantibbva.com.tr/v1',
    clientId: process.env.GARANTI_CLIENT_ID || 'demo_client_id',
    scope: 'accounts transactions',
  },
  ISBANK: {
    baseUrl: 'https://api.isbank.com.tr/v1',
    clientId: process.env.ISBANK_CLIENT_ID || 'demo_client_id',
    scope: 'accounts transactions',
  },
  // Uluslararası
  PLAID: {
    baseUrl: 'https://production.plaid.com',
    clientId: process.env.PLAID_CLIENT_ID || 'demo_client_id',
    secret: process.env.PLAID_SECRET || 'demo_secret',
  },
  YODLEE: {
    baseUrl: 'https://api.yodlee.com/ysl',
    clientId: process.env.YODLEE_CLIENT_ID || 'demo_client_id',
  },
};

// Bank Account Interface
export interface BankAccount {
  id: string;
  bankCode: string;
  bankName: string;
  accountNumber: string;
  accountType: 'checking' | 'savings' | 'credit_card' | 'investment';
  currency: string;
  balance: number;
  availableBalance?: number;
  iban?: string;
  isActive: boolean;
  lastSyncAt: string;
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: string;
}

// Bank Transaction Interface
export interface BankTransaction {
  id: string;
  accountId: string;
  amount: number;
  currency: string;
  description: string;
  merchant?: string;
  category?: string;
  date: string;
  type: 'debit' | 'credit';
  status: 'pending' | 'completed' | 'failed';
  reference?: string;
  balance?: number;
}

// Bank Connection Status
export interface BankConnectionStatus {
  bankCode: string;
  isConnected: boolean;
  lastSyncAt: string | null;
  accountCount: number;
  status: 'active' | 'expired' | 'error' | 'disconnected';
  errorMessage?: string;
}

class BankApiService {
  private static instance: BankApiService;
  private httpClient: any;
  private securityService: SecurityService;

  private constructor() {
    this.httpClient = SecureHttpClient.getInstance();
    this.securityService = SecurityService.getInstance();
  }

  static getInstance(): BankApiService {
    if (!BankApiService.instance) {
      BankApiService.instance = new BankApiService();
    }
    return BankApiService.instance;
  }

  /**
   * Banka hesabı bağlama - OAuth 2.0 flow başlatma
   */
  async initiateBankConnection(bankCode: string): Promise<{
    success: boolean;
    authUrl?: string;
    error?: string;
  }> {
    try {
      console.log('🏦 Initiating bank connection for:', bankCode);

      // Demo bankalar için biyometrik doğrulama bypass
      if (bankCode !== 'DEMO_BANK' && bankCode !== 'GARANTI_DEMO') {
        // Gerçek bankalar için biyometrik doğrulama gerekli
        const biometricAuth = await this.securityService.authenticateWithBiometrics();
        if (!biometricAuth) {
          return {
            success: false,
            error: 'Biyometrik doğrulama başarısız'
          };
        }
      }

      const bankConfig = BANK_API_CONFIG[bankCode as keyof typeof BANK_API_CONFIG];
      if (!bankConfig) {
        return {
          success: false,
          error: 'Desteklenmeyen banka'
        };
      }

      // OAuth 2.0 authorization URL oluştur
      const state = this.generateSecureState();
      const redirectUri = 'com.kisiselfinans://bank-callback';
      
      const authUrl = `${bankConfig.baseUrl}/oauth/authorize?` +
        `client_id=${bankConfig.clientId}&` +
        `response_type=code&` +
        `scope=${bankConfig.scope}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `state=${state}`;

      // State'i güvenli şekilde sakla
      await this.storeSecureData(`bank_oauth_state_${bankCode}`, state);

      return {
        success: true,
        authUrl
      };

    } catch (error) {
      console.error('Bank connection initiation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * OAuth callback işleme - Authorization code'u token'a çevirme
   */
  async handleOAuthCallback(
    bankCode: string,
    authorizationCode: string,
    state: string
  ): Promise<{
    success: boolean;
    account?: BankAccount;
    error?: string;
  }> {
    try {
      console.log('🔐 Processing OAuth callback for:', bankCode);

      // State doğrulama
      const storedState = await this.getSecureData(`bank_oauth_state_${bankCode}`);
      if (storedState !== state) {
        return {
          success: false,
          error: 'Güvenlik doğrulaması başarısız'
        };
      }

      const bankConfig = BANK_API_CONFIG[bankCode as keyof typeof BANK_API_CONFIG];
      const redirectUri = 'com.kisiselfinans://bank-callback';

      // Authorization code'u access token'a çevir
      const tokenResponse = await this.httpClient.request(`${bankConfig.baseUrl}/oauth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          client_id: bankConfig.clientId,
          code: authorizationCode,
          redirect_uri: redirectUri,
        }).toString(),
      });

      if (!tokenResponse.success) {
        return {
          success: false,
          error: 'Token alınamadı'
        };
      }

      const { access_token, refresh_token, expires_in } = tokenResponse.data;

      // Hesap bilgilerini çek
      const accountsResponse = await this.httpClient.request(`${bankConfig.baseUrl}/accounts`, {
        headers: {
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!accountsResponse.success || !accountsResponse.data.accounts?.length) {
        return {
          success: false,
          error: 'Hesap bilgileri alınamadı'
        };
      }

      // İlk hesabı kaydet (çoklu hesap desteği sonra eklenebilir)
      const firstAccount = accountsResponse.data.accounts[0];
      const bankAccount: BankAccount = {
        id: `${bankCode}_${firstAccount.account_id}`,
        bankCode,
        bankName: this.getBankName(bankCode),
        accountNumber: firstAccount.account_number,
        accountType: firstAccount.account_type,
        currency: firstAccount.currency || 'TRY',
        balance: firstAccount.balance || 0,
        availableBalance: firstAccount.available_balance,
        iban: firstAccount.iban,
        isActive: true,
        lastSyncAt: new Date().toISOString(),
        accessToken: access_token,
        refreshToken: refresh_token,
        tokenExpiresAt: new Date(Date.now() + expires_in * 1000).toISOString(),
      };

      // Hesabı güvenli şekilde kaydet
      await this.storeBankAccount(bankAccount);

      // State'i temizle
      await AsyncStorage.removeItem(`bank_oauth_state_${bankCode}`);

      console.log('✅ Bank account connected successfully:', bankAccount.id);
      return {
        success: true,
        account: bankAccount
      };

    } catch (error) {
      console.error('OAuth callback processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Banka hesaplarını listele
   */
  async getConnectedAccounts(): Promise<BankAccount[]> {
    try {
      const accountsData = await this.getSecureData('bank_accounts');
      if (!accountsData) return [];

      const accounts: BankAccount[] = JSON.parse(accountsData);
      return accounts.filter(account => account.isActive);
    } catch (error) {
      console.error('Error getting connected accounts:', error);
      return [];
    }
  }

  /**
   * Hesap bakiyesini güncelle
   */
  async syncAccountBalance(accountId: string): Promise<{
    success: boolean;
    balance?: number;
    error?: string;
  }> {
    try {
      console.log('💰 Syncing balance for account:', accountId);

      const account = await this.getBankAccount(accountId);
      if (!account) {
        return { success: false, error: 'Hesap bulunamadı' };
      }

      // Token geçerliliğini kontrol et
      const validToken = await this.ensureValidToken(account);
      if (!validToken) {
        return { success: false, error: 'Token yenilenemedi' };
      }

      const bankConfig = BANK_API_CONFIG[account.bankCode as keyof typeof BANK_API_CONFIG];
      
      // Bakiye bilgisini çek
      const balanceResponse = await this.httpClient.request(
        `${bankConfig.baseUrl}/accounts/${account.accountNumber}/balance`,
        {
          headers: {
            'Authorization': `Bearer ${account.accessToken}`,
          },
        }
      );

      if (!balanceResponse.success) {
        return { success: false, error: 'Bakiye bilgisi alınamadı' };
      }

      const newBalance = balanceResponse.data.balance;
      
      // Hesap bilgisini güncelle
      account.balance = newBalance;
      account.availableBalance = balanceResponse.data.available_balance;
      account.lastSyncAt = new Date().toISOString();
      
      await this.updateBankAccount(account);

      console.log('✅ Balance synced successfully:', newBalance);
      return {
        success: true,
        balance: newBalance
      };

    } catch (error) {
      console.error('Balance sync error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  // Helper methods
  private generateSecureState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  private getBankName(bankCode: string): string {
    const bankNames: { [key: string]: string } = {
      AKBANK: 'Akbank',
      GARANTI: 'Garanti BBVA',
      ISBANK: 'İş Bankası',
      PLAID: 'Plaid',
      YODLEE: 'Yodlee',
    };
    return bankNames[bankCode] || bankCode;
  }

  private async storeSecureData(key: string, value: string): Promise<void> {
    const encryptedValue = await this.securityService.encryptData(value);
    await AsyncStorage.setItem(key, encryptedValue);
  }

  private async getSecureData(key: string): Promise<string | null> {
    const encryptedValue = await AsyncStorage.getItem(key);
    if (!encryptedValue) return null;
    return await this.securityService.decryptData(encryptedValue);
  }

  private async storeBankAccount(account: BankAccount): Promise<void> {
    const accounts = await this.getConnectedAccounts();
    const existingIndex = accounts.findIndex(a => a.id === account.id);
    
    if (existingIndex >= 0) {
      accounts[existingIndex] = account;
    } else {
      accounts.push(account);
    }

    await this.storeSecureData('bank_accounts', JSON.stringify(accounts));
  }

  private async getBankAccount(accountId: string): Promise<BankAccount | null> {
    const accounts = await this.getConnectedAccounts();
    return accounts.find(account => account.id === accountId) || null;
  }

  private async updateBankAccount(account: BankAccount): Promise<void> {
    await this.storeBankAccount(account);
  }

  /**
   * Banka işlemlerini senkronize et
   */
  async syncTransactions(
    accountId: string,
    fromDate?: string,
    toDate?: string
  ): Promise<{
    success: boolean;
    transactions?: BankTransaction[];
    newTransactionCount?: number;
    error?: string;
  }> {
    try {
      console.log('📊 Syncing transactions for account:', accountId);

      const account = await this.getBankAccount(accountId);
      if (!account) {
        return { success: false, error: 'Hesap bulunamadı' };
      }

      // Token geçerliliğini kontrol et
      const validToken = await this.ensureValidToken(account);
      if (!validToken) {
        return { success: false, error: 'Token yenilenemedi' };
      }

      const bankConfig = BANK_API_CONFIG[account.bankCode as keyof typeof BANK_API_CONFIG];

      // Tarih aralığı belirle (varsayılan: son 30 gün)
      const endDate = toDate || new Date().toISOString().split('T')[0];
      const startDate = fromDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // İşlemleri çek
      const transactionsResponse = await this.httpClient.request(
        `${bankConfig.baseUrl}/accounts/${account.accountNumber}/transactions?` +
        `from_date=${startDate}&to_date=${endDate}&limit=100`,
        {
          headers: {
            'Authorization': `Bearer ${account.accessToken}`,
          },
        }
      );

      if (!transactionsResponse.success) {
        return { success: false, error: 'İşlemler alınamadı' };
      }

      const bankTransactions: BankTransaction[] = transactionsResponse.data.transactions.map((tx: any) => ({
        id: `${account.bankCode}_${tx.transaction_id}`,
        accountId: account.id,
        amount: Math.abs(tx.amount),
        currency: tx.currency || account.currency,
        description: tx.description || tx.merchant_name || 'Banka İşlemi',
        merchant: tx.merchant_name,
        category: this.mapBankCategory(tx.category),
        date: tx.date,
        type: tx.amount > 0 ? 'credit' : 'debit',
        status: tx.status || 'completed',
        reference: tx.reference,
        balance: tx.running_balance,
      }));

      // Yeni işlemleri belirle (daha önce import edilmemiş)
      const existingTransactionIds = await this.getImportedTransactionIds(accountId);
      const newTransactions = bankTransactions.filter(tx =>
        !existingTransactionIds.includes(tx.id)
      );

      // Yeni işlemleri kaydet
      if (newTransactions.length > 0) {
        await this.saveImportedTransactions(newTransactions);
        console.log(`✅ Imported ${newTransactions.length} new transactions`);
      }

      // Hesap sync zamanını güncelle
      account.lastSyncAt = new Date().toISOString();
      await this.updateBankAccount(account);

      return {
        success: true,
        transactions: bankTransactions,
        newTransactionCount: newTransactions.length
      };

    } catch (error) {
      console.error('Transaction sync error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Tüm bağlı hesapları senkronize et
   */
  async syncAllAccounts(): Promise<{
    success: boolean;
    results?: Array<{
      accountId: string;
      success: boolean;
      newTransactionCount?: number;
      error?: string;
    }>;
    totalNewTransactions?: number;
  }> {
    try {
      console.log('🔄 Syncing all connected accounts...');

      const accounts = await this.getConnectedAccounts();
      if (accounts.length === 0) {
        return {
          success: true,
          results: [],
          totalNewTransactions: 0
        };
      }

      const results = [];
      let totalNewTransactions = 0;

      for (const account of accounts) {
        // Bakiye sync
        const balanceResult = await this.syncAccountBalance(account.id);

        // İşlem sync
        const transactionResult = await this.syncTransactions(account.id);

        const accountResult = {
          accountId: account.id,
          success: balanceResult.success && transactionResult.success,
          newTransactionCount: transactionResult.newTransactionCount || 0,
          error: balanceResult.error || transactionResult.error
        };

        results.push(accountResult);

        if (accountResult.success && accountResult.newTransactionCount) {
          totalNewTransactions += accountResult.newTransactionCount;
        }
      }

      console.log(`✅ Sync completed. Total new transactions: ${totalNewTransactions}`);

      return {
        success: true,
        results,
        totalNewTransactions
      };

    } catch (error) {
      console.error('Sync all accounts error:', error);
      return {
        success: false,
        results: []
      };
    }
  }

  /**
   * Banka hesabı bağlantısını kes
   */
  async disconnectAccount(accountId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      console.log('🔌 Disconnecting account:', accountId);

      const account = await this.getBankAccount(accountId);
      if (!account) {
        return { success: false, error: 'Hesap bulunamadı' };
      }

      // Biyometrik doğrulama gerekli
      const biometricAuth = await this.securityService.authenticateWithBiometrics();
      if (!biometricAuth) {
        return {
          success: false,
          error: 'Biyometrik doğrulama başarısız'
        };
      }

      // Hesabı deaktif et
      account.isActive = false;
      await this.updateBankAccount(account);

      // Token'ları temizle (güvenlik için)
      account.accessToken = '';
      account.refreshToken = '';
      await this.updateBankAccount(account);

      console.log('✅ Account disconnected successfully');
      return { success: true };

    } catch (error) {
      console.error('Account disconnection error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bilinmeyen hata'
      };
    }
  }

  /**
   * Bağlantı durumlarını kontrol et
   */
  async getConnectionStatuses(): Promise<BankConnectionStatus[]> {
    try {
      const accounts = await this.getConnectedAccounts();
      const statuses: BankConnectionStatus[] = [];

      // Banka kodlarına göre grupla
      const bankGroups = accounts.reduce((groups, account) => {
        if (!groups[account.bankCode]) {
          groups[account.bankCode] = [];
        }
        groups[account.bankCode].push(account);
        return groups;
      }, {} as { [bankCode: string]: BankAccount[] });

      for (const [bankCode, bankAccounts] of Object.entries(bankGroups)) {
        const activeAccounts = bankAccounts.filter(acc => acc.isActive);
        const hasExpiredTokens = activeAccounts.some(acc =>
          new Date(acc.tokenExpiresAt) <= new Date()
        );

        statuses.push({
          bankCode,
          isConnected: activeAccounts.length > 0,
          lastSyncAt: activeAccounts.length > 0
            ? Math.max(...activeAccounts.map(acc => new Date(acc.lastSyncAt).getTime()))
            : null,
          accountCount: activeAccounts.length,
          status: hasExpiredTokens ? 'expired' :
                  activeAccounts.length > 0 ? 'active' : 'disconnected',
          errorMessage: hasExpiredTokens ? 'Token süresi dolmuş' : undefined
        });
      }

      return statuses;
    } catch (error) {
      console.error('Error getting connection statuses:', error);
      return [];
    }
  }

  // Helper methods for transaction management
  private async getImportedTransactionIds(accountId: string): Promise<string[]> {
    try {
      const data = await this.getSecureData(`imported_transactions_${accountId}`);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  private async saveImportedTransactions(transactions: BankTransaction[]): Promise<void> {
    // Bu method'da işlemleri ana transaction store'a kaydedeceğiz
    // Şimdilik sadece ID'leri saklayalım
    for (const tx of transactions) {
      const existingIds = await this.getImportedTransactionIds(tx.accountId);
      existingIds.push(tx.id);
      await this.storeSecureData(
        `imported_transactions_${tx.accountId}`,
        JSON.stringify(existingIds)
      );
    }
  }

  private mapBankCategory(bankCategory?: string): string {
    // Banka kategorilerini uygulama kategorilerine map et
    const categoryMap: { [key: string]: string } = {
      'food_and_drink': 'dining',
      'groceries': 'groceries',
      'gas_stations': 'fuel',
      'transportation': 'transportation',
      'shopping': 'shopping',
      'healthcare': 'healthcare',
      'utilities': 'utilities',
      'entertainment': 'entertainment',
      'travel': 'travel',
      'transfer': 'other',
    };

    return categoryMap[bankCategory?.toLowerCase() || ''] || 'other';
  }

  private async ensureValidToken(account: BankAccount): Promise<boolean> {
    // Token geçerlilik kontrolü ve yenileme
    const expiresAt = new Date(account.tokenExpiresAt);
    const now = new Date();

    if (expiresAt > now) {
      return true; // Token hala geçerli
    }

    // Token yenileme işlemi (sonraki adımda implement edilecek)
    console.log('🔄 Token expired, refresh needed for:', account.id);
    return await this.refreshAccessToken(account);
  }

  private async refreshAccessToken(account: BankAccount): Promise<boolean> {
    try {
      console.log('🔄 Refreshing access token for:', account.id);

      const bankConfig = BANK_API_CONFIG[account.bankCode as keyof typeof BANK_API_CONFIG];

      const tokenResponse = await this.httpClient.request(`${bankConfig.baseUrl}/oauth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          client_id: bankConfig.clientId,
          refresh_token: account.refreshToken,
        }).toString(),
      });

      if (!tokenResponse.success) {
        console.error('Token refresh failed:', tokenResponse.error);
        return false;
      }

      const { access_token, refresh_token, expires_in } = tokenResponse.data;

      // Hesap bilgilerini güncelle
      account.accessToken = access_token;
      if (refresh_token) {
        account.refreshToken = refresh_token;
      }
      account.tokenExpiresAt = new Date(Date.now() + expires_in * 1000).toISOString();

      await this.updateBankAccount(account);

      console.log('✅ Access token refreshed successfully');
      return true;

    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }
}

export default BankApiService;
