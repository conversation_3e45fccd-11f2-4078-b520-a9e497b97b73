// Receipt Camera Component - Makbuz fotoğrafı bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface ReceiptCameraProps {
  onImageSelected: (uri: string) => void;
  selectedImage?: string;
  style?: any;
  onOCRRequested?: (uri: string) => void; // OCR callback
}

const ReceiptCamera: React.FC<ReceiptCameraProps> = ({
  onImageSelected,
  selectedImage,
  style,
  onOCRRequested,
}) => {
  const { theme } = useTheme();
  const showImagePickerOptions = () => {
    console.log('showImagePickerOptions called');
    Alert.alert(
      'Makbuz Fotoğrafı',
      '<PERSON>kbuz fotoğrafını nasıl eklemek istiyorsunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Galeriden Seç', onPress: pickImageFromGallery },
        { text: 'Fotoğraf Çek', onPress: openCamera },
      ]
    );
  };

  const pickImageFromGallery = async () => {
    console.log('📸 Gallery picker started');
    try {
      // İzin kontrolü
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('📱 Gallery permission status:', status);

      if (status !== 'granted') {
        Alert.alert(
          'İzin Gerekli',
          'Galeriye erişim izni gerekli. Lütfen ayarlardan izin verin.',
          [
            { text: 'İptal', style: 'cancel' },
            { text: 'Ayarlara Git', onPress: () => console.log('Open settings') }
          ]
        );
        return;
      }

      // Galeri açma - Fiş için optimize edilmiş ayarlar
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: undefined, // Serbest kırpma - fiş boyutuna göre ayarlanabilir
        quality: 0.9, // OCR için daha yüksek kalite
        allowsMultipleSelection: false,
        exif: false, // EXIF verilerini dahil etme
      });

      console.log('📷 Gallery result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('✅ Image selected from gallery:', result.assets[0].uri);
        const imageUri = await saveImageToLocal(result.assets[0].uri);
        console.log('💾 Image saved to:', imageUri);
        onImageSelected(imageUri);
        Alert.alert('Başarılı', 'Makbuz fotoğrafı eklendi!');
      } else {
        console.log('❌ Gallery selection cancelled');
      }
    } catch (error) {
      console.error('❌ Error picking image from gallery:', error);
      Alert.alert('Hata', 'Galeri açılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const openCamera = async () => {
    console.log('📸 Camera opening started');
    try {
      // İzin kontrolü
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log('📱 Camera permission status:', status);

      if (status !== 'granted') {
        Alert.alert(
          'İzin Gerekli',
          'Kamera erişim izni gerekli. Lütfen ayarlardan izin verin.',
          [
            { text: 'İptal', style: 'cancel' },
            { text: 'Ayarlara Git', onPress: () => console.log('Open settings') }
          ]
        );
        return;
      }

      // Kamera açma - Fiş için optimize edilmiş ayarlar
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: undefined, // Serbest kırpma - fiş boyutuna göre ayarlanabilir
        quality: 0.9, // OCR için daha yüksek kalite
        exif: false, // EXIF verilerini dahil etme
      });

      console.log('📷 Camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('✅ Image captured from camera:', result.assets[0].uri);
        const imageUri = await saveImageToLocal(result.assets[0].uri);
        console.log('💾 Image saved to:', imageUri);
        onImageSelected(imageUri);
        Alert.alert('Başarılı', 'Makbuz fotoğrafı çekildi!');
      } else {
        console.log('❌ Camera capture cancelled');
      }
    } catch (error) {
      console.error('❌ Error opening camera:', error);
      Alert.alert('Hata', 'Kamera açılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const saveImageToLocal = async (uri: string): Promise<string> => {
    try {
      console.log('💾 Saving image to local storage...');
      const fileName = `receipt_${Date.now()}.jpg`;
      const documentsDir = FileSystem.documentDirectory;

      if (!documentsDir) {
        console.log('❌ Documents directory not available');
        return uri; // Fallback to original URI
      }

      const receiptsDir = `${documentsDir}receipts/`;

      // Receipts klasörünü oluştur
      const dirInfo = await FileSystem.getInfoAsync(receiptsDir);
      if (!dirInfo.exists) {
        console.log('📁 Creating receipts directory...');
        await FileSystem.makeDirectoryAsync(receiptsDir, { intermediates: true });
      }

      const newPath = `${receiptsDir}${fileName}`;
      console.log('📋 Copying image from:', uri);
      console.log('📋 Copying image to:', newPath);

      await FileSystem.copyAsync({
        from: uri,
        to: newPath,
      });

      console.log('✅ Image saved successfully to:', newPath);
      return newPath;
    } catch (error) {
      console.error('❌ Error saving image:', error);
      console.log('🔄 Using original URI as fallback');
      return uri; // Fallback to original URI
    }
  };

  const removeImage = () => {
    Alert.alert(
      'Fotoğrafı Kaldır',
      'Makbuz fotoğrafını kaldırmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Kaldır', style: 'destructive', onPress: () => onImageSelected('') },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      marginBottom: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    addButton: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: 'dashed',
      paddingVertical: 32,
      paddingHorizontal: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addButtonText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      marginTop: 8,
    },
    addButtonSubtext: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 4,
    },
    imageContainer: {
      position: 'relative',
      borderRadius: 12,
      overflow: 'hidden',
    },
    selectedImage: {
      width: '100%',
      height: 200,
      borderRadius: 12,
    },
    imageOverlay: {
      position: 'absolute',
      top: 8,
      right: 8,
      flexDirection: 'row',
      gap: 8,
    },
    imageAction: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>Makbuz Fotoğrafı</Text>

      {selectedImage ? (
        <View style={styles.imageContainer}>
          <Image source={{ uri: selectedImage }} style={styles.selectedImage} />
          <View style={styles.imageOverlay}>
            {onOCRRequested && (
              <TouchableOpacity
                style={[styles.imageAction, { backgroundColor: 'rgba(76, 175, 80, 0.8)' }]}
                onPress={() => onOCRRequested(selectedImage)}
              >
                <Ionicons name="scan" size={20} color={theme.colors.surface} />
              </TouchableOpacity>
            )}
            <TouchableOpacity style={styles.imageAction} onPress={showImagePickerOptions}>
              <Ionicons name="camera" size={20} color={theme.colors.surface} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageAction} onPress={removeImage}>
              <Ionicons name="trash" size={20} color={theme.colors.surface} />
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            console.log('Button pressed!');
            showImagePickerOptions();
          }}
        >
          <Ionicons name="camera-outline" size={32} color={theme.colors.textSecondary} />
          <Text style={styles.addButtonText}>Makbuz Fotoğrafı Ekle</Text>
          <Text style={styles.addButtonSubtext}>Fotoğraf çek veya galeriden seç</Text>
        </TouchableOpacity>
      )}


    </View>
  );
};

export default ReceiptCamera;
