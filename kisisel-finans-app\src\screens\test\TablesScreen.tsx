import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import DatabaseManager from '../../database/DatabaseManager';

const { width } = Dimensions.get('window');

interface TableData {
  name: string;
  columns: string[];
  rows: any[];
  count: number;
}

const TablesScreen: React.FC = () => {
  const { theme } = useTheme();
  const [tables, setTables] = useState<TableData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const tableQueries = [
    {
      name: 'Categories',
      query: 'SELECT id, name, type, user_id, icon, color, is_active FROM categories LIMIT 10',
      countQuery: 'SELECT COUNT(*) as count FROM categories'
    },
    {
      name: 'Budget_Categories', 
      query: 'SELECT id, budget_id, category_id, planned_amount, spent_amount, remaining_amount FROM budget_categories LIMIT 10',
      countQuery: 'SELECT COUNT(*) as count FROM budget_categories'
    },
    {
      name: 'Transactions',
      query: 'SELECT id, type, amount, category, category_id, description, date FROM transactions LIMIT 10',
      countQuery: 'SELECT COUNT(*) as count FROM transactions'
    },
    {
      name: 'Budgets',
      query: 'SELECT id, name, user_id, total_income_target, total_expense_limit, start_date, end_date FROM budgets LIMIT 10',
      countQuery: 'SELECT COUNT(*) as count FROM budgets'
    },
    {
      name: 'Users',
      query: 'SELECT id, email, name, created_at FROM users LIMIT 10',
      countQuery: 'SELECT COUNT(*) as count FROM users'
    }
  ];

  const loadTables = async () => {
    try {
      console.log('📊 Loading all tables...');
      const db = DatabaseManager.getDatabase();
      const tablesData: TableData[] = [];

      for (const tableConfig of tableQueries) {
        try {
          // Get count
          const countResult = await db.getFirstAsync(tableConfig.countQuery);
          const count = countResult?.count || 0;

          // Get data
          const rows = await db.getAllAsync(tableConfig.query);
          
          // Get columns from first row
          const columns = rows.length > 0 ? Object.keys(rows[0]) : [];

          tablesData.push({
            name: tableConfig.name,
            columns,
            rows,
            count
          });

          console.log(`✅ Loaded ${tableConfig.name}: ${count} total, ${rows.length} shown`);
        } catch (error) {
          console.error(`❌ Error loading ${tableConfig.name}:`, error);
          tablesData.push({
            name: tableConfig.name,
            columns: [],
            rows: [],
            count: 0
          });
        }
      }

      setTables(tablesData);
    } catch (error) {
      console.error('❌ Error loading tables:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadTables();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadTables();
  };

  const renderTable = (table: TableData) => (
    <View key={table.name} style={[styles.tableContainer, { backgroundColor: theme.cardBackground }]}>
      {/* Table Header */}
      <View style={styles.tableHeader}>
        <Text style={[styles.tableName, { color: theme.text }]}>
          📊 {table.name}
        </Text>
        <Text style={[styles.tableCount, { color: theme.textSecondary }]}>
          {table.count} kayıt
        </Text>
      </View>

      {/* Columns */}
      {table.columns.length > 0 && (
        <View style={styles.tableWrapper}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={true}
            style={styles.horizontalScroll}
          >
            <View style={styles.tableContent}>
              {/* Column Headers */}
              <View style={styles.columnHeaders}>
                {table.columns.map((column, index) => (
                  <Text
                    key={index}
                    style={[styles.columnHeader, { color: theme.primary }]}
                  >
                    {column}
                  </Text>
                ))}
              </View>

              {/* Rows Container with Vertical Scroll */}
              <ScrollView
                style={styles.rowsContainer}
                showsVerticalScrollIndicator={true}
                nestedScrollEnabled={true}
              >
                {table.rows.map((row, rowIndex) => (
                  <View key={rowIndex} style={[styles.row, rowIndex % 2 === 0 && styles.evenRow]}>
                    {table.columns.map((column, colIndex) => (
                      <View key={colIndex} style={styles.cellContainer}>
                        <Text
                          style={[styles.cell, { color: theme.text }]}
                          numberOfLines={2}
                          ellipsizeMode="tail"
                        >
                          {row[column]?.toString() || 'NULL'}
                        </Text>
                      </View>
                    ))}
                  </View>
                ))}
              </ScrollView>
            </View>
          </ScrollView>
        </View>
      )}

      {/* Empty State */}
      {table.rows.length === 0 && (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: theme.textSecondary }]}>
            Tablo boş
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
        <Text style={[styles.title, { color: theme.text }]}>📊 Tablolar</Text>
        <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
          <Ionicons name="refresh" size={24} color={theme.primary} />
        </TouchableOpacity>
      </View>

      {/* Tables */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {tables.map(renderTable)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  refreshButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  tableContainer: {
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tableName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  tableCount: {
    fontSize: 14,
  },
  tableWrapper: {
    maxHeight: 400, // Maksimum yükseklik
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  horizontalScroll: {
    flex: 1,
  },
  tableContent: {
    minWidth: '100%',
  },
  columnHeaders: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderBottomWidth: 2,
    borderBottomColor: '#E0E0E0',
    paddingVertical: 12,
  },
  columnHeader: {
    minWidth: 150, // Minimum genişlik artırıldı
    maxWidth: 200, // Maksimum genişlik
    fontSize: 13,
    fontWeight: 'bold',
    paddingHorizontal: 12,
    textAlign: 'center',
  },
  rowsContainer: {
    maxHeight: 320, // Satırlar için maksimum yükseklik
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    minHeight: 40,
  },
  evenRow: {
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  cellContainer: {
    minWidth: 150, // Column header ile aynı
    maxWidth: 200,
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRightWidth: 1,
    borderRightColor: '#F0F0F0',
  },
  cell: {
    fontSize: 12,
    textAlign: 'center',
    flexWrap: 'wrap',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default TablesScreen;
