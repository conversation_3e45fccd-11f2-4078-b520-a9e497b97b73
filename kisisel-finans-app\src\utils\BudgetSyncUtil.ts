// Budget Sync Utility - Mevcut işlemleri bütçelerle senkronize et

import DatabaseManager from '../database/DatabaseManager';

export class BudgetSyncUtil {
  /**
   * Tüm bütçe kategorilerinin spent_amount değerlerini yeniden hesapla
   */
  static async syncAllBudgetCategories(): Promise<void> {
    try {
      console.log('🔄 Starting budget categories sync...');
      const db = DatabaseManager.getDatabase();

      // Tüm aktif bütçe kategorilerini al
      const budgetCategories = await db.getAllAsync(`
        SELECT 
          bc.id,
          bc.budget_id,
          bc.category_id,
          b.user_id,
          b.start_date,
          b.end_date
        FROM budget_categories bc
        JOIN budgets b ON bc.budget_id = b.id
        WHERE bc.is_active = 1 AND b.is_active = 1
      `);

      console.log(`📊 Found ${budgetCategories.length} budget categories to sync`);

      // Her bütçe kategorisi için spent_amount'u hesapla
      for (const category of budgetCategories) {
        const spentAmount = await this.calculateSpentAmount(
          category.user_id,
          category.category_id,
          category.start_date,
          category.end_date
        );

        // spent_amount'u güncelle
        await db.runAsync(
          'UPDATE budget_categories SET spent_amount = ? WHERE id = ?',
          [spentAmount, category.id]
        );

        console.log(`✅ Updated category ${category.category_id}: spent_amount = ${spentAmount}`);
      }

      console.log('🎉 Budget categories sync completed!');
    } catch (error) {
      console.error('❌ Error syncing budget categories:', error);
      throw error;
    }
  }

  /**
   * Belirli bir kategori için harcanan tutarı hesapla
   */
  private static async calculateSpentAmount(
    userId: string,
    categoryId: string,
    startDate: string,
    endDate: string
  ): Promise<number> {
    try {
      const db = DatabaseManager.getDatabase();

      // Kategori ID'sinden kategori adını al
      const categoryInfo = await db.getFirstAsync<{ name: string }>(`
        SELECT name FROM categories WHERE id = ?
      `, [categoryId]);

      if (!categoryInfo) {
        console.log(`⚠️ Category not found for ID: ${categoryId}`);
        return 0;
      }

      const categoryName = categoryInfo.name;
      console.log(`🔍 Looking for transactions with category: ${categoryName} for user: ${userId}`);
      console.log(`📅 Date range: ${startDate} to ${endDate}`);

      // Önce bu kategoride kaç işlem var kontrol et
      const countResult = await db.getFirstAsync<{ count: number }>(`
        SELECT COUNT(*) as count
        FROM transactions
        WHERE user_id = ?
          AND category = ?
          AND type = 'expense'
          AND is_deleted = 0
      `, [userId, categoryName]);

      console.log(`📊 Found ${countResult?.count || 0} transactions for category: ${categoryName}`);

      const result = await db.getFirstAsync<{ total: number }>(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM transactions
        WHERE user_id = ?
          AND category = ?
          AND type = 'expense'
          AND is_deleted = 0
          AND date BETWEEN ? AND ?
      `, [userId, categoryName, startDate, endDate]);

      console.log(`💰 Found total amount: ${result?.total || 0} for category: ${categoryName}`);
      return result?.total || 0;
    } catch (error) {
      console.error('❌ Error calculating spent amount:', error);
      return 0;
    }
  }

  /**
   * Bütçe durumunu kontrol et ve rapor ver
   */
  static async getBudgetSyncReport(): Promise<{
    totalBudgets: number;
    totalCategories: number;
    totalTransactions: number;
    syncedCategories: number;
    unsyncedCategories: number;
  }> {
    try {
      const db = DatabaseManager.getDatabase();

      // Toplam bütçe sayısı
      const budgetCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM budgets WHERE is_active = 1'
      );

      // Toplam bütçe kategorisi sayısı
      const categoryCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM budget_categories WHERE is_active = 1'
      );

      // Toplam işlem sayısı
      const transactionCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM transactions WHERE is_deleted = 0'
      );

      // Senkronize olan kategoriler (spent_amount > 0)
      const syncedCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM budget_categories WHERE is_active = 1 AND spent_amount > 0'
      );

      // Senkronize olmayan kategoriler (spent_amount = 0)
      const unsyncedCount = await db.getFirstAsync<{ count: number }>(
        'SELECT COUNT(*) as count FROM budget_categories WHERE is_active = 1 AND spent_amount = 0'
      );

      return {
        totalBudgets: budgetCount?.count || 0,
        totalCategories: categoryCount?.count || 0,
        totalTransactions: transactionCount?.count || 0,
        syncedCategories: syncedCount?.count || 0,
        unsyncedCategories: unsyncedCount?.count || 0,
      };
    } catch (error) {
      console.error('❌ Error getting sync report:', error);
      throw error;
    }
  }

  /**
   * Test user için özel senkronizasyon
   */
  static async syncTestUserBudgets(): Promise<void> {
    try {
      console.log('🧪 Syncing test user budgets...');
      const db = DatabaseManager.getDatabase();
      console.log('✅ Database instance obtained for test sync');

      // Test user'ın bütçe kategorilerini al
      const testCategories = await db.getAllAsync(`
        SELECT 
          bc.id,
          bc.budget_id,
          bc.category_id,
          b.start_date,
          b.end_date
        FROM budget_categories bc
        JOIN budgets b ON bc.budget_id = b.id
        WHERE b.user_id = 'test_user_1'
          AND bc.is_active = 1 
          AND b.is_active = 1
      `);

      console.log(`📊 Found ${testCategories.length} test user budget categories`);

      for (const category of testCategories) {
        const spentAmount = await this.calculateSpentAmount(
          'test_user_1',
          category.category_id,
          category.start_date,
          category.end_date
        );

        await db.runAsync(
          'UPDATE budget_categories SET spent_amount = ? WHERE id = ?',
          [spentAmount, category.id]
        );

        console.log(`✅ Test user category ${category.category_id}: spent_amount = ${spentAmount}`);
      }

      console.log('🎉 Test user budget sync completed!');
    } catch (error) {
      console.error('❌ Error syncing test user budgets:', error);
      throw error;
    }
  }
}

export default BudgetSyncUtil;
