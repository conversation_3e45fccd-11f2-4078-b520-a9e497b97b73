// Enhanced Transactions Screen - Gelişmiş işlemler ekranı

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  TextInput,
  ActivityIndicator,
  Alert,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import { Ionicons } from '@expo/vector-icons';

// Database imports
import TransactionRepository from '../../database/repositories/TransactionRepository';
import {
  Transaction,
  IncomeTransaction,
  ExpenseTransaction,
  IncomeCategory,
  ExpenseCategory,
  PaymentMethod,
} from '../../types/transaction';
import {
  INCOME_CATEGORY_LABELS,
  EXPENSE_CATEGORY_LABELS,
  PAYMENT_METHOD_LABELS,
} from '../../types/transaction';

// Components
import AdvancedFilterModal, { AdvancedFilters } from '../../components/AdvancedFilterModal';
import ExportModal from '../../components/ExportModal';

type TransactionsNavigationProp = StackNavigationProp<MainStackParamList>;

const TransactionsScreen: React.FC = () => {
  const navigation = useNavigation<TransactionsNavigationProp>();
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const { formatAmount, convertAmount, baseCurrency } = useCurrency();

  // State management
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState({
    searchQuery: '',
    type: 'all' as 'all' | 'income' | 'expense',
    dateRange: 'all' as 'all' | 'today' | 'week' | 'month' | 'year',
    category: 'all' as string,
    sortBy: 'date' as 'date' | 'amount' | 'category',
    sortOrder: 'desc' as 'asc' | 'desc',
  });

  // Advanced filters state
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilters>({
    includeCategories: [],
    excludeCategories: [],
    paymentMethods: [],
    includeTags: [],
    excludeTags: [],
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);

  // Summary calculations
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpense: 0,
    netAmount: 0,
    transactionCount: 0,
  });

  // Load transactions from database
  const loadTransactions = useCallback(async () => {
    if (!user) return;

    try {
      console.log('📊 Loading transactions from database...');
      setLoading(true);

      const allTransactions = await TransactionRepository.getByUserId(user.id);
      console.log(`✅ Loaded ${allTransactions.length} transactions`);
      console.log('📋 Sample transactions:', allTransactions.slice(0, 2));

      setTransactions(allTransactions);
      // Don't calculate summary here - it will be calculated by useEffect with filtered data
    } catch (error) {
      console.error('❌ Error loading transactions:', error);
      Alert.alert('Hata', 'İşlemler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Calculate summary statistics with multi-currency support
  const calculateSummary = useCallback((transactionList: Transaction[]) => {
    const income = transactionList
      .filter(t => t.type === 'income')
      .reduce((sum, t) => {
        // Convert to base currency
        const convertedAmount = convertAmount(t.amount, t.currency, baseCurrency.code);
        return sum + convertedAmount;
      }, 0);

    const expense = transactionList
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => {
        // Convert to base currency
        const convertedAmount = convertAmount(t.amount, t.currency, baseCurrency.code);
        return sum + convertedAmount;
      }, 0);

    setSummary({
      totalIncome: income,
      totalExpense: expense,
      netAmount: income - expense,
      transactionCount: transactionList.length,
    });
  }, [convertAmount, baseCurrency.code]);

  // Load transactions on screen focus
  useFocusEffect(
    useCallback(() => {
      loadTransactions();
    }, [loadTransactions])
  );

  // Refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
  }, [loadTransactions]);

  // Helper functions
  const getCategoryLabel = (category: string, type: 'income' | 'expense'): string => {
    if (type === 'income') {
      return INCOME_CATEGORY_LABELS[category as IncomeCategory] || category;
    } else {
      return EXPENSE_CATEGORY_LABELS[category as ExpenseCategory] || category;
    }
  };

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    return PAYMENT_METHOD_LABELS[method] || method;
  };

  // Filter and search handlers
  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
  };

  const handleFilterChange = (type: 'all' | 'income' | 'expense') => {
    setFilters(prev => ({ ...prev, type }));
  };

  const handleDateRangeChange = (dateRange: 'all' | 'today' | 'week' | 'month' | 'year') => {
    setFilters(prev => ({ ...prev, dateRange }));
  };

  const handleSortChange = (sortBy: 'date' | 'amount' | 'category') => {
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: prev.sortBy === sortBy && prev.sortOrder === 'desc' ? 'asc' : 'desc'
    }));
  };

  // Advanced filters handler
  const handleAdvancedFilters = (newFilters: AdvancedFilters) => {
    setAdvancedFilters(newFilters);
  };

  // Apply advanced filters to a transaction
  const applyAdvancedFilters = (transaction: Transaction): boolean => {
    console.log('🔍 Applying advanced filters to transaction:', {
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      category: transaction.category,
      description: transaction.description
    });
    console.log('🔧 Current advanced filters:', advancedFilters);

    const incomeTransaction = transaction as IncomeTransaction;
    const expenseTransaction = transaction as ExpenseTransaction;

    // Amount range filter
    if (advancedFilters.minAmount !== undefined && transaction.amount < advancedFilters.minAmount) {
      console.log(`❌ Amount filter failed: ${transaction.amount} < ${advancedFilters.minAmount}`);
      return false;
    }
    if (advancedFilters.maxAmount !== undefined && transaction.amount > advancedFilters.maxAmount) {
      console.log(`❌ Amount filter failed: ${transaction.amount} > ${advancedFilters.maxAmount}`);
      return false;
    }

    // Currency filter
    if (advancedFilters.currency && transaction.currency !== advancedFilters.currency) {
      console.log(`❌ Currency filter failed: ${transaction.currency} !== ${advancedFilters.currency}`);
      return false;
    }

    // Category include filter
    if (advancedFilters.includeCategories.length > 0 &&
        !advancedFilters.includeCategories.includes(transaction.category)) {
      console.log(`❌ Include category filter failed: ${transaction.category} not in [${advancedFilters.includeCategories.join(', ')}]`);
      return false;
    }

    // Category exclude filter
    if (advancedFilters.excludeCategories.length > 0 &&
        advancedFilters.excludeCategories.includes(transaction.category)) {
      console.log(`❌ Exclude category filter failed: ${transaction.category} in [${advancedFilters.excludeCategories.join(', ')}]`);
      return false;
    }

    // Payment method filter
    if (advancedFilters.paymentMethods.length > 0 &&
        transaction.paymentMethod &&
        !advancedFilters.paymentMethods.includes(transaction.paymentMethod)) {
      console.log(`❌ Payment method filter failed: ${transaction.paymentMethod} not in [${advancedFilters.paymentMethods.join(', ')}]`);
      return false;
    }

    // Date range filter (custom)
    if (advancedFilters.startDate || advancedFilters.endDate) {
      const transactionDate = new Date(transaction.date);
      if (advancedFilters.startDate && transactionDate < advancedFilters.startDate) {
        console.log(`❌ Start date filter failed: ${transactionDate} < ${advancedFilters.startDate}`);
        return false;
      }
      if (advancedFilters.endDate && transactionDate > advancedFilters.endDate) {
        console.log(`❌ End date filter failed: ${transactionDate} > ${advancedFilters.endDate}`);
        return false;
      }
    }

    // Business expense filter
    if (advancedFilters.isBusinessExpense !== undefined &&
        transaction.type === 'expense' &&
        expenseTransaction.isBusinessExpense !== advancedFilters.isBusinessExpense) {
      console.log(`❌ Business expense filter failed: ${expenseTransaction.isBusinessExpense} !== ${advancedFilters.isBusinessExpense}`);
      return false;
    }

    // Tax deductible filter
    if (advancedFilters.isDeductible !== undefined &&
        transaction.type === 'expense' &&
        expenseTransaction.isDeductible !== advancedFilters.isDeductible) {
      console.log(`❌ Tax deductible filter failed: ${expenseTransaction.isDeductible} !== ${advancedFilters.isDeductible}`);
      return false;
    }

    // Taxable income filter
    if (advancedFilters.isTaxable !== undefined &&
        transaction.type === 'income' &&
        incomeTransaction.taxable !== advancedFilters.isTaxable) {
      console.log(`❌ Taxable income filter failed: ${incomeTransaction.taxable} !== ${advancedFilters.isTaxable}`);
      return false;
    }

    // Location filter
    if (advancedFilters.hasLocation !== undefined) {
      const hasLocation = !!(transaction.locationLatitude && transaction.locationLongitude);
      if (hasLocation !== advancedFilters.hasLocation) {
        console.log(`❌ Location filter failed: ${hasLocation} !== ${advancedFilters.hasLocation}`);
        return false;
      }
    }

    // Receipt filter
    if (advancedFilters.hasReceipt !== undefined) {
      const hasReceipt = !!transaction.receiptId;
      if (hasReceipt !== advancedFilters.hasReceipt) {
        console.log(`❌ Receipt filter failed: ${hasReceipt} !== ${advancedFilters.hasReceipt}`);
        return false;
      }
    }

    console.log('✅ Transaction passed all advanced filters');
    return true;
  };

  // Advanced filtering logic
  const getFilteredAndSortedTransactions = () => {
    let filtered = transactions.filter(transaction => {
      // Search filter
      const searchLower = filters.searchQuery.toLowerCase();
      const matchesSearch =
        transaction.description.toLowerCase().includes(searchLower) ||
        getCategoryLabel(transaction.category, transaction.type).toLowerCase().includes(searchLower) ||
        (transaction.type === 'expense' &&
         (transaction as ExpenseTransaction).merchant?.toLowerCase().includes(searchLower)) ||
        (transaction.type === 'income' &&
         (transaction as IncomeTransaction).source?.toLowerCase().includes(searchLower));

      // Type filter
      const matchesType = filters.type === 'all' || transaction.type === filters.type;

      // Date range filter
      const transactionDate = new Date(transaction.date);
      const now = new Date();
      let matchesDateRange = true;

      switch (filters.dateRange) {
        case 'today':
          matchesDateRange = transactionDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDateRange = transactionDate >= weekAgo;
          break;
        case 'month':
          matchesDateRange =
            transactionDate.getMonth() === now.getMonth() &&
            transactionDate.getFullYear() === now.getFullYear();
          break;
        case 'year':
          matchesDateRange = transactionDate.getFullYear() === now.getFullYear();
          break;
        default:
          matchesDateRange = true;
      }

      // Advanced filters
      const matchesAdvancedFilters = applyAdvancedFilters(transaction);

      return matchesSearch && matchesType && matchesDateRange && matchesAdvancedFilters;
    });

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (filters.sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'category':
          const aCat = getCategoryLabel(a.category, a.type);
          const bCat = getCategoryLabel(b.category, b.type);
          comparison = aCat.localeCompare(bCat);
          break;
      }

      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  };

  // Memoized filtered transactions to prevent infinite loops
  const filteredTransactions = useMemo(() => {
    return getFilteredAndSortedTransactions();
  }, [
    transactions,
    filters.searchQuery,
    filters.type,
    filters.dateRange,
    filters.sortBy,
    filters.sortOrder,
    advancedFilters
  ]);

  // Update summary when filtered transactions change
  useEffect(() => {
    console.log(`📊 Updating summary for ${filteredTransactions.length} filtered transactions`);
    calculateSummary(filteredTransactions);
  }, [filteredTransactions, convertAmount, baseCurrency.code]);

  // Swipe actions handlers
  const handleDeleteTransaction = async (transactionId: string) => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ Deleting transaction:', transactionId);

              await TransactionRepository.delete(transactionId);

              console.log('✅ Transaction deleted successfully');

              // Refresh the list
              await loadTransactions();

              Alert.alert('Başarılı', 'İşlem başarıyla silindi.');
            } catch (error) {
              console.error('❌ Error deleting transaction:', error);
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleEditTransaction = (transactionId: string) => {
    // Find the transaction to edit
    const transactionToEdit = transactions.find(t => t.id === transactionId);
    if (transactionToEdit) {
      navigation.navigate('AddTransaction', {
        editTransaction: transactionToEdit
      });
    } else {
      Alert.alert('Hata', 'Düzenlenecek işlem bulunamadı.');
    }
  };

  // Render swipe actions
  const renderRightActions = (transactionId: string) => {
    return (
      <View style={styles.swipeActionsContainer}>
        <TouchableOpacity
          style={[styles.swipeAction, styles.editAction, { backgroundColor: theme.colors.primary }]}
          onPress={() => handleEditTransaction(transactionId)}
        >
          <Ionicons name="create-outline" size={20} color={theme.colors.surface} />
          <Text style={[styles.swipeActionText, { color: theme.colors.surface }]}>Düzenle</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.swipeAction, styles.deleteAction, { backgroundColor: theme.colors.error }]}
          onPress={() => handleDeleteTransaction(transactionId)}
        >
          <Ionicons name="trash-outline" size={20} color={theme.colors.surface} />
          <Text style={[styles.swipeActionText, { color: theme.colors.surface }]}>Sil</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Enhanced transaction card renderer
  const renderTransaction = ({ item }: { item: Transaction }) => {
    const categoryLabel = getCategoryLabel(item.category, item.type);
    const isIncome = item.type === 'income';
    const incomeTransaction = item as IncomeTransaction;
    const expenseTransaction = item as ExpenseTransaction;

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(item.id)}
        rightThreshold={40}
      >
        <TouchableOpacity
          style={[styles.transactionItem, { backgroundColor: theme.colors.surface }]}
          onPress={() => navigation.navigate('TransactionDetail', { transactionId: item.id })}
          activeOpacity={0.7}
        >
        <View style={styles.transactionLeft}>
          <View style={[
            styles.transactionIcon,
            { backgroundColor: isIncome ? theme.colors.income : theme.colors.expense }
          ]}>
            <Ionicons
              name={isIncome ? 'trending-up' : 'trending-down'}
              size={20}
              color={theme.colors.surface}
            />
          </View>
          <View style={styles.transactionDetails}>
            <Text style={[styles.transactionDescription, { color: theme.colors.text }]}>
              {item.description}
            </Text>
            <Text style={[styles.transactionCategory, { color: theme.colors.textSecondary }]}>
              {categoryLabel}
            </Text>
            {/* Merchant/Source info */}
            {isIncome && incomeTransaction.source && (
              <Text style={[styles.transactionMerchant, { color: theme.colors.textSecondary }]}>
                📍 {incomeTransaction.source}
              </Text>
            )}
            {!isIncome && expenseTransaction.merchant && (
              <Text style={[styles.transactionMerchant, { color: theme.colors.textSecondary }]}>
                🏪 {expenseTransaction.merchant}
              </Text>
            )}
            <View style={styles.transactionMeta}>
              <Text style={[styles.transactionDate, { color: theme.colors.textSecondary }]}>
                {new Date(item.date).toLocaleDateString('tr-TR')}
              </Text>
              {item.paymentMethod && (
                <Text style={[styles.paymentMethod, { color: theme.colors.textSecondary }]}>
                  • {getPaymentMethodLabel(item.paymentMethod)}
                </Text>
              )}
            </View>
          </View>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[
            styles.transactionAmount,
            { color: isIncome ? theme.colors.income : theme.colors.expense }
          ]}>
            {isIncome ? '+' : '-'}{formatAmount(item.amount, item.currency)}
          </Text>
          {/* Business/Tax indicators */}
          <View style={styles.transactionTags}>
            {!isIncome && expenseTransaction.isBusinessExpense && (
              <View style={[styles.tag, { backgroundColor: theme.colors.primary + '20' }]}>
                <Text style={[styles.tagText, { color: theme.colors.primary }]}>İş</Text>
              </View>
            )}
            {!isIncome && expenseTransaction.isDeductible && (
              <View style={[styles.tag, { backgroundColor: theme.colors.success + '20' }]}>
                <Text style={[styles.tagText, { color: theme.colors.success }]}>Vergi</Text>
              </View>
            )}
            {isIncome && incomeTransaction.taxable && (
              <View style={[styles.tag, { backgroundColor: theme.colors.warning + '20' }]}>
                <Text style={[styles.tagText, { color: theme.colors.warning }]}>Vergi</Text>
              </View>
            )}
          </View>
        </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>İşlemler</Text>
        <View style={styles.headerActions}>
          {/* Clear Filters Button - Show when filters are active */}
          {(filters.searchQuery || filters.type !== 'all' || filters.dateRange !== 'all' ||
            advancedFilters.includeCategories.length > 0 || advancedFilters.excludeCategories.length > 0 ||
            advancedFilters.paymentMethods.length > 0 || advancedFilters.minAmount !== undefined ||
            advancedFilters.maxAmount !== undefined) && (
            <TouchableOpacity
              style={[styles.clearButton, { backgroundColor: theme.colors.warning }]}
              onPress={() => {
                setFilters({
                  searchQuery: '',
                  type: 'all',
                  dateRange: 'all',
                  category: 'all',
                  sortBy: 'date',
                  sortOrder: 'desc',
                });
                setAdvancedFilters({
                  includeCategories: [],
                  excludeCategories: [],
                  paymentMethods: [],
                  includeTags: [],
                  excludeTags: [],
                });
              }}
            >
              <Ionicons name="refresh" size={16} color={theme.colors.surface} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.sortButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => handleSortChange('date')}
          >
            <Ionicons
              name={filters.sortBy === 'date' ? 'calendar' : 'calendar-outline'}
              size={20}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setShowAdvancedFilters(true)}
          >
            <Ionicons
              name="options-outline"
              size={20}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.exportButton, { backgroundColor: theme.colors.success }]}
            onPress={() => setShowExportModal(true)}
          >
            <Ionicons
              name="download-outline"
              size={20}
              color={theme.colors.surface}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('AddTransaction', {})}
          >
            <Ionicons name="add" size={24} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Summary Cards */}
      {!loading && transactions.length > 0 && (
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Gelir</Text>
                <Text style={[styles.summaryValue, { color: theme.colors.income }]}>
                  {formatAmount(summary.totalIncome, baseCurrency.code)}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Gider</Text>
                <Text style={[styles.summaryValue, { color: theme.colors.expense }]}>
                  {formatAmount(summary.totalExpense, baseCurrency.code)}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Net</Text>
                <Text style={[
                  styles.summaryValue,
                  { color: summary.netAmount >= 0 ? theme.colors.success : theme.colors.error }
                ]}>
                  {formatAmount(summary.netAmount, baseCurrency.code)}
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.border
        }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="İşlem ara..."
            placeholderTextColor={theme.colors.textSecondary}
            value={filters.searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      {/* Enhanced Filter Tabs */}
      <View style={styles.filterContainer}>
        {/* Type Filters */}
        <View style={styles.filterRow}>
          <TouchableOpacity
            style={[
              styles.filterTab,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.type === 'all' && { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary }
            ]}
            onPress={() => handleFilterChange('all')}
          >
            <Text style={[
              styles.filterText,
              { color: theme.colors.text },
              filters.type === 'all' && { color: theme.colors.surface }
            ]}>
              Tümü
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.type === 'income' && { backgroundColor: theme.colors.income, borderColor: theme.colors.income }
            ]}
            onPress={() => handleFilterChange('income')}
          >
            <Text style={[
              styles.filterText,
              { color: theme.colors.text },
              filters.type === 'income' && { color: theme.colors.surface }
            ]}>
              Gelir
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.type === 'expense' && { backgroundColor: theme.colors.expense, borderColor: theme.colors.expense }
            ]}
            onPress={() => handleFilterChange('expense')}
          >
            <Text style={[
              styles.filterText,
              { color: theme.colors.text },
              filters.type === 'expense' && { color: theme.colors.surface }
            ]}>
              Gider
            </Text>
          </TouchableOpacity>
        </View>

        {/* Date Range Filters */}
        <View style={styles.filterRow}>
          {(['all', 'today', 'week', 'month'] as const).map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.dateFilterTab,
                { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
                filters.dateRange === range && { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary }
              ]}
              onPress={() => handleDateRangeChange(range)}
            >
              <Text style={[
                styles.dateFilterText,
                { color: theme.colors.textSecondary },
                filters.dateRange === range && { color: theme.colors.surface }
              ]}>
                {range === 'all' ? 'Tümü' :
                 range === 'today' ? 'Bugün' :
                 range === 'week' ? 'Hafta' : 'Ay'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Loading State */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            İşlemler yükleniyor...
          </Text>
        </View>
      ) : (
        /* Enhanced Transactions List */
        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListHeaderComponent={
            filteredTransactions.length > 0 ? (
              <View style={styles.listHeader}>
                <Text style={[styles.listHeaderText, { color: theme.colors.textSecondary }]}>
                  {filteredTransactions.length} işlem bulundu
                </Text>
                <TouchableOpacity
                  style={styles.sortButton}
                  onPress={() => handleSortChange('amount')}
                >
                  <Ionicons
                    name={filters.sortBy === 'amount' ? 'swap-vertical' : 'swap-vertical-outline'}
                    size={16}
                    color={theme.colors.textSecondary}
                  />
                  <Text style={[styles.sortText, { color: theme.colors.textSecondary }]}>
                    {filters.sortBy === 'amount' ? 'Tutar' : 'Sırala'}
                  </Text>
                </TouchableOpacity>
              </View>
            ) : null
          }
          ListEmptyComponent={
            !loading ? (
              <View style={styles.emptyContainer}>
                <Ionicons name="receipt-outline" size={64} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                  {filters.searchQuery || filters.type !== 'all' || filters.dateRange !== 'all'
                    ? 'Arama sonucu bulunamadı'
                    : 'Henüz işlem yok'}
                </Text>
                <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
                  {filters.searchQuery || filters.type !== 'all' || filters.dateRange !== 'all'
                    ? 'Farklı filtreler deneyebilir veya yeni işlem ekleyebilirsiniz'
                    : 'İlk işleminizi eklemek için + butonuna dokunun'}
                </Text>
                {(filters.searchQuery || filters.type !== 'all' || filters.dateRange !== 'all' ||
                  advancedFilters.includeCategories.length > 0 || advancedFilters.excludeCategories.length > 0 ||
                  advancedFilters.paymentMethods.length > 0 || advancedFilters.minAmount !== undefined ||
                  advancedFilters.maxAmount !== undefined) && (
                  <TouchableOpacity
                    style={[styles.clearFiltersButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => {
                      setFilters({
                        searchQuery: '',
                        type: 'all',
                        dateRange: 'all',
                        category: 'all',
                        sortBy: 'date',
                        sortOrder: 'desc',
                      });
                      setAdvancedFilters({
                        includeCategories: [],
                        excludeCategories: [],
                        paymentMethods: [],
                        includeTags: [],
                        excludeTags: [],
                      });
                    }}
                  >
                    <Text style={[styles.clearFiltersText, { color: theme.colors.surface }]}>
                      Tüm Filtreleri Temizle
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : null
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Advanced Filter Modal */}
      <AdvancedFilterModal
        visible={showAdvancedFilters}
        onClose={() => setShowAdvancedFilters(false)}
        onApply={handleAdvancedFilters}
        initialFilters={advancedFilters}
        transactionType={filters.type}
      />

      {/* Export Modal */}
      <ExportModal
        visible={showExportModal}
        onClose={() => setShowExportModal(false)}
        transactions={transactions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  exportButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  clearButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 44,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
  filterContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  filterRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateFilterTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 6,
    borderWidth: 1,
  },
  dateFilterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionMerchant: {
    fontSize: 11,
    marginTop: 2,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  transactionTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 4,
    marginBottom: 2,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '500',
  },
  paymentMethod: {
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 8,
  },
  listHeaderText: {
    fontSize: 14,
  },
  sortText: {
    fontSize: 12,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  clearFiltersButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Swipe Actions Styles
  swipeActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: '100%',
  },
  swipeAction: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: '100%',
    paddingHorizontal: 8,
  },
  editAction: {
    marginRight: 1,
  },
  deleteAction: {
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
  },
  swipeActionText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
  },
});

export default TransactionsScreen;
