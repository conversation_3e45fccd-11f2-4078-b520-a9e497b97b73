// Loading Screen - Uygulama yüklenirken gösterilen ekran

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

const LoadingScreen: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 80,
    },
    logoPlaceholder: {
      width: 100,
      height: 100,
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    logoText: {
      fontSize: 40,
    },
    appName: {
      fontSize: 28,
      fontWeight: 'bold',
      marginBottom: 8,
      textAlign: 'center',
    },
    appSubtitle: {
      fontSize: 16,
      textAlign: 'center',
    },
    loadingContainer: {
      alignItems: 'center',
    },
    spinner: {
      marginBottom: 16,
    },
    loadingText: {
      fontSize: 16,
      textAlign: 'center',
    },
    footer: {
      paddingBottom: 40,
      paddingHorizontal: 40,
    },
    footerText: {
      fontSize: 12,
      textAlign: 'center',
      lineHeight: 18,
    },
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <View style={[styles.logoPlaceholder, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.logoText}>💰</Text>
          </View>
          <Text style={[styles.appName, { color: theme.colors.text }]}>Kişisel Finans</Text>
          <Text style={[styles.appSubtitle, { color: theme.colors.textSecondary }]}>Finansal Özgürlüğünüz İçin</Text>
        </View>

        {/* Loading Indicator */}
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={theme.colors.primary}
            style={styles.spinner}
          />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>Yükleniyor...</Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          Finansal verileriniz güvenli şekilde yükleniyor
        </Text>
      </View>
    </View>
  );
};

export default LoadingScreen;
