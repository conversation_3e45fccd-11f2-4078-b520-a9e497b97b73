// Theme Toggle Component - <PERSON><PERSON> değiştirme butonu

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, ThemeMode } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: any;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  showLabel = true,
  size = 'medium',
  style,
}) => {
  const { theme, themeMode, setThemeMode, toggleTheme } = useTheme();

  const iconSizes = {
    small: 20,
    medium: 24,
    large: 28,
  };

  const iconSize = iconSizes[size];

  const getThemeIcon = () => {
    switch (themeMode) {
      case 'light':
        return 'sunny';
      case 'dark':
        return 'moon';
      case 'system':
        return 'phone-portrait';
      default:
        return 'sunny';
    }
  };

  const getThemeLabel = () => {
    switch (themeMode) {
      case 'light':
        return 'Açık Tema';
      case 'dark':
        return 'Koyu Tema';
      case 'system':
        return 'Sistem Teması';
      default:
        return 'Açık Tema';
    }
  };

  const showThemeOptions = () => {
    Alert.alert(
      'Tema Seçin',
      'Hangi temayı kullanmak istiyorsunuz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Açık Tema',
          onPress: () => setThemeMode('light'),
        },
        {
          text: 'Koyu Tema',
          onPress: () => setThemeMode('dark'),
        },
        {
          text: 'Sistem Teması',
          onPress: () => setThemeMode('system'),
        },
      ]
    );
  };

  const handlePress = () => {
    if (size === 'small') {
      // Küçük buton için sadece toggle
      toggleTheme();
    } else {
      // Büyük buton için seçenekler menüsü
      showThemeOptions();
    }
  };

  const handleLongPress = () => {
    showThemeOptions();
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name={getThemeIcon()}
          size={iconSize}
          color={theme.colors.text}
        />
      </View>
      
      {showLabel && (
        <Text style={[styles.label, { color: theme.colors.textSecondary }]}>
          {getThemeLabel()}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  iconContainer: {
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ThemeToggle;
