// Biometric Service - Biyometrik kimlik doğrulama servisi

import * as LocalAuthentication from 'expo-local-authentication';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface BiometricInfo {
  isAvailable: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  isEnrolled: boolean;
}

class BiometricService {
  private static instance: BiometricService;
  private readonly BIOMETRIC_ENABLED_KEY = 'biometric_enabled';

  static getInstance(): BiometricService {
    if (!BiometricService.instance) {
      BiometricService.instance = new BiometricService();
    }
    return BiometricService.instance;
  }

  /**
   * Cihazın biyometrik kimlik doğrulamayı destekleyip desteklemediğini kontrol eder
   */
  async getBiometricInfo(): Promise<BiometricInfo> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      return {
        isAvailable,
        supportedTypes,
        isEnrolled,
      };
    } catch (error) {
      console.error('Error getting biometric info:', error);
      return {
        isAvailable: false,
        supportedTypes: [],
        isEnrolled: false,
      };
    }
  }

  /**
   * Biyometrik kimlik doğrulamayı başlatır
   */
  async authenticate(reason?: string): Promise<LocalAuthentication.LocalAuthenticationResult> {
    try {
      const biometricInfo = await this.getBiometricInfo();
      
      if (!biometricInfo.isAvailable) {
        return {
          success: false,
          error: 'Biyometrik kimlik doğrulama bu cihazda desteklenmiyor.',
        };
      }

      if (!biometricInfo.isEnrolled) {
        return {
          success: false,
          error: 'Biyometrik kimlik doğrulama ayarlanmamış. Lütfen cihaz ayarlarından biyometrik kimlik doğrulamayı etkinleştirin.',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason || 'Kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        fallbackLabel: 'Şifre kullan',
        disableDeviceFallback: false,
      });

      return result;
    } catch (error) {
      console.error('Error during biometric authentication:', error);
      return {
        success: false,
        error: 'Kimlik doğrulama sırasında bir hata oluştu.',
      };
    }
  }

  /**
   * Biyometrik kimlik doğrulamanın etkin olup olmadığını kontrol eder
   */
  async isBiometricEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem(this.BIOMETRIC_ENABLED_KEY);
      return enabled === 'true';
    } catch (error) {
      console.error('Error checking biometric enabled status:', error);
      return false;
    }
  }

  /**
   * Biyometrik kimlik doğrulamayı etkinleştirir/devre dışı bırakır
   */
  async setBiometricEnabled(enabled: boolean): Promise<boolean> {
    try {
      if (enabled) {
        // Önce biyometrik kimlik doğrulamayı test et
        const result = await this.authenticate('Biyometrik kimlik doğrulamayı etkinleştirmek için kimliğinizi doğrulayın');
        
        if (!result.success) {
          return false;
        }
      }

      await AsyncStorage.setItem(this.BIOMETRIC_ENABLED_KEY, enabled.toString());
      return true;
    } catch (error) {
      console.error('Error setting biometric enabled status:', error);
      return false;
    }
  }

  /**
   * Desteklenen biyometrik türlerin açıklamalarını döndürür
   */
  getBiometricTypeDescription(types: LocalAuthentication.AuthenticationType[]): string {
    const descriptions: string[] = [];

    types.forEach(type => {
      switch (type) {
        case LocalAuthentication.AuthenticationType.FINGERPRINT:
          descriptions.push('Parmak izi');
          break;
        case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
          descriptions.push('Yüz tanıma');
          break;
        case LocalAuthentication.AuthenticationType.IRIS:
          descriptions.push('İris tanıma');
          break;
      }
    });

    return descriptions.join(', ') || 'Bilinmeyen';
  }

  /**
   * Biyometrik kimlik doğrulama hatalarını kullanıcı dostu mesajlara çevirir
   */
  getErrorMessage(error: string): string {
    switch (error) {
      case 'UserCancel':
        return 'Kimlik doğrulama iptal edildi.';
      case 'UserFallback':
        return 'Kullanıcı alternatif kimlik doğrulama yöntemini seçti.';
      case 'SystemCancel':
        return 'Sistem kimlik doğrulamayı iptal etti.';
      case 'PasscodeNotSet':
        return 'Cihazda şifre ayarlanmamış.';
      case 'BiometricNotAvailable':
        return 'Biyometrik kimlik doğrulama kullanılamıyor.';
      case 'BiometricNotEnrolled':
        return 'Biyometrik kimlik doğrulama ayarlanmamış.';
      case 'BiometricLockout':
        return 'Çok fazla başarısız deneme. Lütfen daha sonra tekrar deneyin.';
      default:
        return 'Kimlik doğrulama başarısız oldu.';
    }
  }
}

export default BiometricService.getInstance();
