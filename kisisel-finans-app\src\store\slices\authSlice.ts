// Auth Slice - Kimlik doğrulama state yönetimi

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../../types';
import * as SecureStore from 'expo-secure-store';
import ApiService from '../../services/ApiService';

// Auth state interface
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  biometricEnabled: boolean;
  rememberMe: boolean;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  biometricEnabled: false,
  rememberMe: false,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      // Gerçek API çağrısı
      const response = await ApiService.login(credentials.email, credentials.password);

      if (response.success && response.data) {
        const { user, token } = response.data;

        // Token'ı güvenli olarak sakla
        await SecureStore.setItemAsync('authToken', token);
        await SecureStore.setItemAsync('userEmail', user.email);

        // API Service'e token'ı set et
        ApiService.setToken(token);

        return user;
      } else {
        throw new Error(response.error || 'Giriş başarısız');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Giriş yapılırken hata oluştu');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (userData: { email: string; password: string; name: string }, { rejectWithValue }) => {
    try {
      // Gerçek API çağrısı
      const response = await ApiService.register(userData);

      if (response.success && response.data) {
        const { user, token } = response.data;

        // Token'ı güvenli olarak sakla
        await SecureStore.setItemAsync('authToken', token);
        await SecureStore.setItemAsync('userEmail', user.email);

        // API Service'e token'ı set et
        ApiService.setToken(token);

        return user;
      } else {
        throw new Error(response.error || 'Kayıt başarısız');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Kayıt olurken hata oluştu');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      // API'ye logout isteği gönder
      await ApiService.logout();

      // Token'ları temizle
      await SecureStore.deleteItemAsync('authToken');
      await SecureStore.deleteItemAsync('userEmail');

      // API Service'den token'ı temizle
      ApiService.clearToken();

      return null;
    } catch (error: any) {
      // Logout hatası olsa bile local token'ları temizle
      await SecureStore.deleteItemAsync('authToken');
      await SecureStore.deleteItemAsync('userEmail');
      ApiService.clearToken();

      return rejectWithValue(error.message || 'Çıkış yapılırken hata oluştu');
    }
  }
);

export const checkAuthStatus = createAsyncThunk(
  'auth/checkAuthStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      const email = await SecureStore.getItemAsync('userEmail');

      if (token && email) {
        // TODO: Token'ı doğrula ve kullanıcı bilgilerini al
        const mockUser: User = {
          id: '1',
          email: email,
          name: 'Test Kullanıcı',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        return mockUser;
      }

      return null;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Auth durumu kontrol edilirken hata oluştu');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (userData: Partial<User>, { rejectWithValue, getState }) => {
    try {
      // TODO: API çağrısı yapılacak
      const state = getState() as { auth: AuthState };
      const currentUser = state.auth.user;

      if (!currentUser) {
        throw new Error('Kullanıcı bulunamadı');
      }

      const updatedUser: User = {
        ...currentUser,
        ...userData,
        updatedAt: new Date().toISOString(),
      };

      return updatedUser;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Profil güncellenirken hata oluştu');
    }
  }
);

export const enableBiometric = createAsyncThunk(
  'auth/enableBiometric',
  async (enabled: boolean, { rejectWithValue }) => {
    try {
      await SecureStore.setItemAsync('biometricEnabled', enabled.toString());
      return enabled;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Biyometrik ayar kaydedilirken hata oluştu');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setRememberMe: (state, action: PayloadAction<boolean>) => {
      state.rememberMe = action.payload;
    },
    resetAuthState: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Register
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Logout
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Check auth status
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Update profile
    builder
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Enable biometric
    builder
      .addCase(enableBiometric.fulfilled, (state, action) => {
        state.biometricEnabled = action.payload;
      });
  },
});

export const { clearError, setRememberMe, resetAuthState } = authSlice.actions;
export default authSlice.reducer;
