// Reports Screen - Detaylı finansal raporlar ve analizler

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
  Modal,
} from 'react-native';
import { useAppSelector } from '../../store';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useCurrency } from '../../contexts/CurrencyContext';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from '../../components';
import DatePicker from '../../components/DatePicker';
import ExportService, { ExportOptions } from '../../services/ExportService';
import { Transaction } from '../../types/transaction';

const { width } = Dimensions.get('window');

const ReportsScreen: React.FC = () => {
  const { transactions } = useAppSelector((state) => state.transactions);
  const { theme } = useTheme();
  const { formatAmount } = useCurrency();

  // State
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [showExportModal, setShowExportModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<'excel' | 'csv' | 'pdf'>('excel');

  // Bugünün tarihini al ve 30 gün öncesini hesapla
  const today = new Date();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(today.getDate() - 30);

  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(today);
  const [includeSummary, setIncludeSummary] = useState(true);

  // Debug log'ları
  console.log('📅 ReportsScreen - Initial dates:', {
    startDate: startDate.toLocaleDateString(),
    endDate: endDate.toLocaleDateString(),
    thirtyDaysAgo: thirtyDaysAgo.toLocaleDateString(),
    today: today.toLocaleDateString()
  });

  // Mock data - gerçek uygulamada Redux'tan gelecek
  const mockTransactions = [
    {
      id: '1',
      description: 'Market Alışverişi',
      amount: 250,
      type: 'expense' as const,
      date: new Date().toISOString(),
      category: 'food',
      currency: 'TRY',
      isRecurring: false,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      description: 'Maaş',
      amount: 8000,
      type: 'income' as const,
      date: new Date().toISOString(),
      category: 'salary',
      currency: 'TRY',
      isRecurring: true,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '3',
      description: 'Elektrik Faturası',
      amount: 180,
      type: 'expense' as const,
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      category: 'utilities',
      currency: 'TRY',
      isRecurring: false,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '4',
      description: 'Freelance Proje',
      amount: 1500,
      type: 'income' as const,
      date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      category: 'freelance',
      currency: 'TRY',
      isRecurring: false,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  // Eğer Redux'ta veri yoksa mock data kullan
  const allTransactions = transactions.length > 0 ? transactions : mockTransactions;

  // Kategori etiketleri
  const getCategoryLabel = (category: string, type: 'income' | 'expense'): string => {
    const categoryLabels: Record<string, string> = {
      // Gelir kategorileri
      salary: 'Maaş',
      freelance: 'Serbest Çalışma',
      business: 'İş Geliri',
      investment: 'Yatırım Geliri',
      rental: 'Kira Geliri',
      gift: 'Hediye',
      other_income: 'Diğer Gelir',

      // Gider kategorileri
      food: 'Yemek',
      transportation: 'Ulaşım',
      utilities: 'Faturalar',
      entertainment: 'Eğlence',
      healthcare: 'Sağlık',
      education: 'Eğitim',
      shopping: 'Alışveriş',
      travel: 'Seyahat',
      other_expense: 'Diğer Gider',
    };

    return categoryLabels[category] || category;
  };

  // Analiz verileri
  const analysisData = useMemo(() => {
    const now = new Date();
    let startPeriod: Date;
    let endPeriod: Date = now;

    switch (selectedPeriod) {
      case 'week':
        startPeriod = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startPeriod = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startPeriod = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'year':
        startPeriod = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startPeriod = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Saatleri normalize et
    startPeriod.setHours(0, 0, 0, 0);
    endPeriod.setHours(23, 59, 59, 999);

    const filteredTransactions = allTransactions.filter(t => {
      const transactionDate = new Date(t.date);
      return transactionDate >= startPeriod && transactionDate <= endPeriod;
    });

    // Toplam gelir ve gider
    const totalIncome = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpense = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    // Kategori bazlı analiz
    const categoryData: Record<string, { income: number; expense: number; count: number }> = {};

    filteredTransactions.forEach(t => {
      const categoryLabel = getCategoryLabel(t.category, t.type);
      if (!categoryData[categoryLabel]) {
        categoryData[categoryLabel] = { income: 0, expense: 0, count: 0 };
      }

      if (t.type === 'income') {
        categoryData[categoryLabel].income += t.amount;
      } else {
        categoryData[categoryLabel].expense += t.amount;
      }
      categoryData[categoryLabel].count += 1;
    });

    return {
      totalIncome,
      totalExpense,
      netAmount: totalIncome - totalExpense,
      transactionCount: filteredTransactions.length,
      categoryData,
      filteredTransactions,
    };
  }, [allTransactions, selectedPeriod]);

  // Chart verileri
  const chartData = useMemo(() => {
    // Pie Chart - Gider kategorileri
    const expenseCategories = Object.entries(analysisData.categoryData)
      .filter(([_, data]) => data.expense > 0)
      .map(([category, data], index) => ({
        name: category,
        amount: data.expense,
        color: [
          '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
          '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ][index % 10],
        legendFontColor: theme.colors.text,
        legendFontSize: 12,
      }));

    // Bar Chart - Gelir vs Gider karşılaştırması
    const incomeVsExpenseData = {
      labels: ['Gelir', 'Gider'],
      datasets: [{
        data: [analysisData.totalIncome, analysisData.totalExpense],
        color: (opacity = 1) => theme.colors.primary,
      }],
    };

    // Line Chart - Son 7 günlük trend (mock data)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' });
    });

    const trendData = {
      labels: last7Days,
      datasets: [{
        data: [1200, 800, 1500, 900, 1100, 1300, 950], // Mock trend data
        color: (opacity = 1) => theme.colors.primary,
        strokeWidth: 2,
      }],
    };

    return {
      expenseCategories,
      incomeVsExpenseData,
      trendData,
    };
  }, [analysisData, theme.colors]);

  // Export fonksiyonu
  const handleExport = async () => {
    if (isExporting) return;

    try {
      setIsExporting(true);

      // Tarih aralığını kontrol et
      if (startDate > endDate) {
        Alert.alert('Hata', 'Başlangıç tarihi bitiş tarihinden sonra olamaz!');
        return;
      }

      // Export seçeneklerini hazırla
      const exportOptions: ExportOptions = {
        format: selectedFormat,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
        includeSummary,
      };

      // Tarih aralığına göre işlemleri filtrele
      const filteredTransactionsForExport = allTransactions.filter(t => {
        const transactionDate = new Date(t.date);
        const start = new Date(startDate);
        const end = new Date(endDate);

        // Saatleri normalize et - başlangıç günün başı, bitiş günün sonu
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);

        return transactionDate >= start && transactionDate <= end;
      });

      console.log('🚀 Export başlatılıyor:', exportOptions);
      console.log('📊 Toplam işlem sayısı:', allTransactions.length);
      console.log('📅 Filtrelenmiş işlem sayısı:', filteredTransactionsForExport.length);
      console.log('📅 Tarih aralığı:', startDate.toLocaleDateString(), '-', endDate.toLocaleDateString());

      if (filteredTransactionsForExport.length === 0) {
        Alert.alert('Uyarı', 'Seçilen tarih aralığında hiç işlem bulunamadı!');
        return;
      }

      // Export işlemini başlat
      const result = await ExportService.exportTransactions(filteredTransactionsForExport, exportOptions);

      if (result.success) {
        Alert.alert(
          'Rapor Başarıyla Oluşturuldu! 📊',
          `${selectedFormat.toUpperCase()} raporu hazırlandı ve paylaşım menüsü açıldı.\n\n📁 Dosya: ${result.fileName}\n\n✅ ${filteredTransactionsForExport.length} işlem dışa aktarıldı\n\n💡 İpucu: Paylaşım menüsünde "Dosyalara Kaydet" seçeneğini kullanarak telefona kaydedebilir veya WhatsApp, Email gibi uygulamalara paylaşabilirsiniz.`,
          [
            { text: 'Anladım', style: 'default' }
          ]
        );
        setShowExportModal(false);
      } else {
        Alert.alert('Hata', result.error || 'Rapor oluşturulurken bir hata oluştu.');
      }
    } catch (error: any) {
      console.error('❌ Export error:', error);
      Alert.alert('Hata', 'Rapor oluşturulurken bir hata oluştu: ' + error.message);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={[styles.title, { color: theme.colors.text }]}>Finansal Raporlar</Text>
            <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
              Detaylı analiz ve raporlama
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.headerExportButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => setShowExportModal(true)}
          >
            <Ionicons name="download-outline" size={18} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>

      {/* Period Selection */}
      <View style={styles.periodContainer}>
        <View style={styles.periodSelector}>
          {(['week', 'month', 'quarter', 'year'] as const).map((period, index) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodTab,
                {
                  backgroundColor: selectedPeriod === period ? theme.colors.primary : 'transparent',
                },
                index === 0 && styles.periodTabFirst,
                index === 3 && styles.periodTabLast,
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text style={[
                styles.periodTabText,
                {
                  color: selectedPeriod === period ? theme.colors.surface : theme.colors.text,
                  fontWeight: selectedPeriod === period ? '600' : '500',
                }
              ]}>
                {period === 'week' ? 'Hafta' :
                 period === 'month' ? 'Ay' :
                 period === 'quarter' ? 'Çeyrek' : 'Yıl'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Financial Summary */}
      <View style={[styles.summarySection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Finansal Özet</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Toplam Gelir</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.income }]}>
              {formatAmount(analysisData.totalIncome)}
            </Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Toplam Gider</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.expense }]}>
              {formatAmount(analysisData.totalExpense)}
            </Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Net Tutar</Text>
            <Text style={[
              styles.summaryValue,
              { color: analysisData.netAmount >= 0 ? theme.colors.income : theme.colors.expense }
            ]}>
              {formatAmount(analysisData.netAmount)}
            </Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>İşlem Sayısı</Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
              {analysisData.transactionCount}
            </Text>
          </View>
        </View>
      </View>

      {/* Charts Section */}
      <View style={[styles.chartsSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Grafikler</Text>

        {/* Expense Categories Pie Chart */}
        {chartData.expenseCategories.length > 0 && (
          <View style={styles.chartContainer}>
            <PieChart
              data={chartData.expenseCategories}
              title="Gider Kategorileri"
              showLegend={true}
              size={width - 80}
            />
          </View>
        )}

        {/* Income vs Expense Bar Chart */}
        <View style={styles.chartContainer}>
          <BarChart
            data={chartData.incomeVsExpenseData}
            title="Gelir vs Gider"
            yAxisSuffix=" ₺"
            showValues={true}
            size={width - 80}
          />
        </View>

        {/* Trend Line Chart */}
        <View style={styles.chartContainer}>
          <LineChart
            data={chartData.trendData}
            title="Son 7 Günlük Trend"
            yAxisSuffix=" ₺"
            showDots={true}
            size={width - 80}
          />
        </View>
      </View>

      {/* Financial Health Report */}
      <View style={[styles.healthSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Finansal Sağlık Raporu</Text>

        {/* Health Score */}
        <View style={styles.healthScoreContainer}>
          <View style={styles.healthScoreCircle}>
            <Text style={[styles.healthScoreText, { color: theme.colors.primary }]}>
              {Math.round((analysisData.netAmount > 0 ? 85 : 45))}
            </Text>
            <Text style={[styles.healthScoreLabel, { color: theme.colors.textSecondary }]}>Puan</Text>
          </View>
          <View style={styles.healthScoreInfo}>
            <Text style={[styles.healthScoreTitle, { color: theme.colors.text }]}>
              {analysisData.netAmount > 0 ? 'İyi Durumda' : 'Dikkat Gerekli'}
            </Text>
            <Text style={[styles.healthScoreDesc, { color: theme.colors.textSecondary }]}>
              {analysisData.netAmount > 0
                ? 'Finansal durumunuz olumlu görünüyor'
                : 'Harcamalarınızı gözden geçirmeniz önerilir'
              }
            </Text>
          </View>
        </View>

        {/* Health Metrics */}
        <View style={styles.healthMetrics}>
          <View style={styles.healthMetricRow}>
            <View style={styles.healthMetric}>
              <Ionicons
                name="trending-up"
                size={20}
                color={analysisData.totalIncome > analysisData.totalExpense ? theme.colors.income : theme.colors.expense}
              />
              <Text style={[styles.healthMetricLabel, { color: theme.colors.textSecondary }]}>Tasarruf Oranı</Text>
              <Text style={[styles.healthMetricValue, {
                color: analysisData.totalIncome > analysisData.totalExpense ? theme.colors.income : theme.colors.expense
              }]}>
                {analysisData.totalIncome > 0
                  ? `%${Math.round(((analysisData.totalIncome - analysisData.totalExpense) / analysisData.totalIncome) * 100)}`
                  : '%0'
                }
              </Text>
            </View>
            <View style={styles.healthMetric}>
              <Ionicons name="wallet" size={20} color={theme.colors.primary} />
              <Text style={[styles.healthMetricLabel, { color: theme.colors.textSecondary }]}>Harcama/Gelir</Text>
              <Text style={[styles.healthMetricValue, { color: theme.colors.text }]}>
                {analysisData.totalIncome > 0
                  ? `%${Math.round((analysisData.totalExpense / analysisData.totalIncome) * 100)}`
                  : '%0'
                }
              </Text>
            </View>
          </View>
        </View>

        {/* Recommendations */}
        <View style={styles.recommendations}>
          <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>Öneriler</Text>
          {analysisData.netAmount > 0 ? (
            <>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Mevcut tasarruf oranınızı korumaya devam edin
              </Text>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Yatırım seçeneklerini değerlendirin
              </Text>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Acil durum fonu oluşturmayı düşünün
              </Text>
            </>
          ) : (
            <>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Gereksiz harcamaları azaltmaya odaklanın
              </Text>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Bütçe planı oluşturun ve takip edin
              </Text>
              <Text style={[styles.recommendationText, { color: theme.colors.textSecondary }]}>
                • Ek gelir kaynakları araştırın
              </Text>
            </>
          )}
        </View>
      </View>

      {/* Category Analysis */}
      <View style={[styles.categorySection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Kategori Analizi</Text>
        {Object.entries(analysisData.categoryData).map(([category, data]) => (
          <View key={category} style={styles.categoryRow}>
            <View style={styles.categoryInfo}>
              <Text style={[styles.categoryName, { color: theme.colors.text }]}>{category}</Text>
              <Text style={[styles.categoryCount, { color: theme.colors.textSecondary }]}>
                {data.count} işlem
              </Text>
            </View>
            <View style={styles.categoryAmounts}>
              {data.income > 0 && (
                <Text style={[styles.categoryAmount, { color: theme.colors.income }]}>
                  +{formatAmount(data.income)}
                </Text>
              )}
              {data.expense > 0 && (
                <Text style={[styles.categoryAmount, { color: theme.colors.expense }]}>
                  -{formatAmount(data.expense)}
                </Text>
              )}
            </View>
          </View>
        ))}
      </View>

      </ScrollView>

      {/* Export Modal */}
      <Modal
        visible={showExportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowExportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Rapor Dışa Aktar</Text>
              <TouchableOpacity onPress={() => setShowExportModal(false)}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            {/* Format Selection */}
            <View style={styles.formatContainer}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Format Seçin:</Text>
              <View style={styles.formatButtons}>
                {(['excel', 'csv', 'pdf'] as const).map((format) => (
                  <TouchableOpacity
                    key={format}
                    style={[
                      styles.formatButton,
                      {
                        backgroundColor: selectedFormat === format ? theme.colors.primary : theme.colors.background,
                        borderColor: theme.colors.border
                      }
                    ]}
                    onPress={() => setSelectedFormat(format)}
                  >
                    <Text style={[
                      styles.formatButtonText,
                      {
                        color: selectedFormat === format ? theme.colors.surface : theme.colors.text
                      }
                    ]}>
                      {format.toUpperCase()}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Date Range */}
            <View style={styles.dateRangeContainer}>
              <Text style={[styles.label, { color: theme.colors.text }]}>Tarih Aralığı:</Text>
              <View style={styles.dateInputs}>
                <View style={styles.dateInputWrapper}>
                  <DatePicker
                    label="Başlangıç"
                    value={startDate}
                    onDateChange={(date) => {
                      console.log('📅 Start date changed from:', startDate.toLocaleDateString(), 'to:', date.toLocaleDateString());
                      setStartDate(date);
                    }}
                    maximumDate={endDate}
                  />
                </View>
                <View style={styles.dateInputWrapper}>
                  <DatePicker
                    label="Bitiş"
                    value={endDate}
                    onDateChange={(date) => {
                      console.log('📅 End date changed from:', endDate.toLocaleDateString(), 'to:', date.toLocaleDateString());
                      setEndDate(date);
                    }}
                    minimumDate={startDate}
                    maximumDate={new Date()}
                  />
                </View>
              </View>
            </View>

            {/* Options */}
            <View style={styles.optionsContainer}>
              <TouchableOpacity
                style={styles.optionRow}
                onPress={() => setIncludeSummary(!includeSummary)}
              >
                <Text style={[styles.optionText, { color: theme.colors.text }]}>Özet bilgileri dahil et</Text>
                <Ionicons
                  name={includeSummary ? "checkbox" : "square-outline"}
                  size={24}
                  color={theme.colors.primary}
                />
              </TouchableOpacity>
            </View>

            {/* Export Button */}
            <TouchableOpacity
              style={[
                styles.modalExportButton,
                {
                  backgroundColor: isExporting ? theme.colors.textSecondary : theme.colors.primary,
                  opacity: isExporting ? 0.7 : 1
                }
              ]}
              onPress={handleExport}
              disabled={isExporting}
            >
              {isExporting ? (
                <ActivityIndicator color={theme.colors.surface} size="small" />
              ) : (
                <Ionicons name="download-outline" size={20} color={theme.colors.surface} />
              )}
              <Text style={[styles.exportButtonText, { color: theme.colors.surface }]}>
                {isExporting ? 'Rapor Oluşturuluyor...' : 'Rapor Oluştur'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  headerExportButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  periodSection: {
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  periodContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  periodTab: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  periodTabFirst: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  periodTabLast: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  periodTabText: {
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 16,
  },
  summarySection: {
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
  },
  summaryCard: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    paddingVertical: 15,
  },
  summaryLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  chartsSection: {
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartContainer: {
    marginBottom: 30,
    alignItems: 'center',
  },
  healthSection: {
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  healthScoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#4ECDC4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 20,
  },
  healthScoreText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  healthScoreLabel: {
    fontSize: 12,
  },
  healthScoreInfo: {
    flex: 1,
  },
  healthScoreTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  healthScoreDesc: {
    fontSize: 14,
    lineHeight: 20,
  },
  healthMetrics: {
    marginBottom: 20,
  },
  healthMetricRow: {
    flexDirection: 'row',
    gap: 15,
  },
  healthMetric: {
    flex: 1,
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'rgba(78, 205, 196, 0.1)',
    borderRadius: 12,
  },
  healthMetricLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  healthMetricValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recommendations: {
    marginTop: 10,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 6,
  },
  categorySection: {
    margin: 20,
    marginTop: 0,
    borderRadius: 12,
    padding: 20,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
  },
  categoryCount: {
    fontSize: 12,
    marginTop: 2,
  },
  categoryAmounts: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 24,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
  },
  formatContainer: {
    marginBottom: 20,
  },
  formatButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  formatButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  formatButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateRangeContainer: {
    marginBottom: 20,
  },
  dateInputs: {
    flexDirection: 'row',
    gap: 10,
  },
  dateInputWrapper: {
    flex: 1,
  },
  optionsContainer: {
    marginBottom: 20,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionText: {
    fontSize: 16,
  },
  modalExportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
});

export default ReportsScreen;
