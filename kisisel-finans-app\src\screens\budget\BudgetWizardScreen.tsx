// RFC-003 Gelişmiş Bütçe Oluşturma Sihirbazı

import React, { useState, useCallback, memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { BudgetWizardProvider, useBudgetWizard } from '../../contexts/BudgetWizardContext';
import BudgetService from '../../services/BudgetService';

// Wizard Steps
import Step1BasicInfo from './wizard/Step1BasicInfo';
import Step2IncomeTargets from './wizard/Step2IncomeTargets';
import Step3CategorySelection from './wizard/Step3CategorySelection';
import Step4ThresholdSettings from './wizard/Step4ThresholdSettings';
import Step5Summary from './wizard/Step5Summary';

// Progress Indicator Component
const ProgressIndicator: React.FC = memo(() => {
  const { theme } = useTheme();
  const { state } = useBudgetWizard();

  return (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            {
              backgroundColor: theme.colors.primary,
              width: `${(state.currentStep / state.totalSteps) * 100}%`
            }
          ]}
        />
      </View>
      <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
        Adım {state.currentStep} / {state.totalSteps}
      </Text>
    </View>
  );
});

// Step Navigation Component
const StepNavigation: React.FC<{ onSave: () => void; isSaving: boolean }> = memo(({ onSave, isSaving }) => {
  const { theme } = useTheme();
  const { state, prevStep, nextStep, canGoNext, canGoPrev } = useBudgetWizard();

  // Debug: Navigation state'i kontrol et
  console.log('🔍 StepNavigation - State:', {
    currentStep: state.currentStep,
    totalSteps: state.totalSteps,
    canGoNext: canGoNext(),
    canGoPrev: canGoPrev(),
    errors: state.errors,
    isSaving,
    isLastStep: state.currentStep === state.totalSteps,
  });

  return (
    <View style={[styles.navigationContainer, { backgroundColor: theme.colors.surface }]}>
      <TouchableOpacity
        style={[
          styles.navButton,
          styles.prevButton,
          { backgroundColor: theme.colors.surfaceSecondary },
          !canGoPrev() && styles.disabledButton
        ]}
        onPress={prevStep}
        disabled={!canGoPrev()}
      >
        <Ionicons name="chevron-back" size={20} color={theme.colors.text} />
        <Text style={[styles.navButtonText, { color: theme.colors.text }]}>
          Geri
        </Text>
      </TouchableOpacity>

      {state.currentStep === state.totalSteps ? (
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.saveButton,
            { backgroundColor: theme.colors.success },
            isSaving && styles.disabledButton
          ]}
          onPress={() => {
            console.log('🔥 Save button pressed!');
            console.log('🔥 isSaving:', isSaving);
            console.log('🔥 disabled:', isSaving);
            onSave();
          }}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color={theme.colors.surface} />
          ) : (
            <Ionicons name="checkmark" size={20} color={theme.colors.surface} />
          )}
          <Text style={[styles.navButtonText, { color: theme.colors.surface }]}>
            {isSaving ? 'Kaydediliyor...' : 'Bütçeyi Oluştur'}
          </Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.nextButton,
            { backgroundColor: theme.colors.primary },
            !canGoNext() && styles.disabledButton
          ]}
          onPress={nextStep}
          disabled={!canGoNext()}
        >
          <Text style={[styles.navButtonText, { color: theme.colors.surface }]}>
            İleri
          </Text>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.surface} />
        </TouchableOpacity>
      )}
    </View>
  );
});

// Main Wizard Content Component
const BudgetWizardContent: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const { state, getBudgetInput, resetWizard } = useBudgetWizard();
  const [isSaving, setIsSaving] = useState(false);

  // Auth context'ten user bilgisini al
  const { user } = useSimpleAuth();

  const renderCurrentStep = () => {
    switch (state.currentStep) {
      case 1:
        return <Step1BasicInfo />;
      case 2:
        return <Step2IncomeTargets />;
      case 3:
        return <Step3CategorySelection />;
      case 4:
        return <Step4ThresholdSettings />;
      case 5:
        return <Step5Summary />;
      default:
        return <Step1BasicInfo />;
    }
  };

  const handleSave = useCallback(async () => {
    console.log('🔥 handleSave called!');
    console.log('🔥 Current state errors:', state.errors);
    console.log('🔥 Current step:', state.currentStep);
    console.log('🔥 Total steps:', state.totalSteps);

    console.log('🔥 About to enter try block...');

    try {
      console.log('🔥 Inside try block!');
      setIsSaving(true);
      console.log('🔥 Setting isSaving to true');

      console.log('🔥 About to call getBudgetInput...');
      const budgetInput = getBudgetInput();
      console.log('🔥 getBudgetInput completed');
      console.log('🧪 Creating budget with input:', JSON.stringify(budgetInput, null, 2));

      // User kontrolü
      if (!user?.id) {
        Alert.alert('Hata', 'Kullanıcı bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
        return;
      }

      console.log('🔥 Calling BudgetService.createBudget with user ID:', user.id);
      const budgetId = await BudgetService.createBudget(user.id, budgetInput);
      console.log('🔥 BudgetService.createBudget returned:', budgetId);

      Alert.alert(
        'Başarılı!',
        `Bütçeniz başarıyla oluşturuldu.\n\n"${state.name}" bütçesi aktif hale getirildi.`,
        [
          {
            text: 'Bütçeleri Gör',
            onPress: () => {
              console.log('🔥 Resetting wizard and navigating to Budget screen...');
              resetWizard();
              navigation.navigate('Budget' as never);
            }
          }
        ]
      );

      console.log(`✅ Budget created successfully with ID: ${budgetId}`);
      
    } catch (error: any) {
      console.error('❌ Error creating budget:', error);
      console.error('❌ Error stack:', error.stack);
      console.error('❌ Error message:', error.message);
      Alert.alert(
        'Hata',
        `Bütçe oluşturulurken bir hata oluştu: ${error.message}`,
        [{ text: 'Tamam' }]
      );
    } finally {
      console.log('🔥 Finally block - setting isSaving to false');
      setIsSaving(false);
    }
  }, [getBudgetInput, resetWizard, navigation, state, user]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => {
            Alert.alert(
              'Çıkış',
              'Bütçe oluşturma işlemini iptal etmek istediğinizden emin misiniz?',
              [
                { text: 'Hayır', style: 'cancel' },
                { 
                  text: 'Evet', 
                  style: 'destructive',
                  onPress: () => {
                    resetWizard();
                    navigation.goBack();
                  }
                }
              ]
            );
          }}
        >
          <Ionicons name="close" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Yeni Bütçe Oluştur
          </Text>
          <ProgressIndicator />
        </View>
      </View>

      {/* Step Content */}
      <View style={styles.stepContainer}>
        {renderCurrentStep()}
      </View>

      {/* Navigation */}
      <StepNavigation onSave={handleSave} isSaving={isSaving} />
    </SafeAreaView>
  );
};

// Main Screen Component with Provider
const BudgetWizardScreen: React.FC = () => {
  return (
    <BudgetWizardProvider>
      <BudgetWizardContent />
    </BudgetWizardProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  closeButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
  },
  stepContainer: {
    flex: 1,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    justifyContent: 'center',
  },
  prevButton: {
    // Previous button styles
  },
  nextButton: {
    // Next button styles
  },
  saveButton: {
    // Save button styles
  },
  disabledButton: {
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 4,
  },
});

export default BudgetWizardScreen;
