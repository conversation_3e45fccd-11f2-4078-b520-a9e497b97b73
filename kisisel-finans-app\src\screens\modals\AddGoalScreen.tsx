// Add Goal Screen - Hedef ekleme ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
// Mock goal service - replace with real API
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import DatePicker from '../../components/DatePicker';

const AddGoalScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    targetAmount: '',
    currentAmount: '0',
    targetDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    category: 'other' as 'emergency' | 'vacation' | 'house' | 'car' | 'education' | 'retirement' | 'other',
    priority: 'medium' as 'low' | 'medium' | 'high',
  });

  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    { id: 'emergency', name: 'Acil Durum', icon: 'shield', color: theme.colors.error },
    { id: 'vacation', name: 'Tatil', icon: 'airplane', color: '#3B82F6' },
    { id: 'house', name: 'Ev', icon: 'home', color: '#10B981' },
    { id: 'car', name: 'Araç', icon: 'car', color: '#F59E0B' },
    { id: 'education', name: 'Eğitim', icon: 'school', color: '#8B5CF6' },
    { id: 'retirement', name: 'Emeklilik', icon: 'time', color: '#14B8A6' },
    { id: 'other', name: 'Diğer', icon: 'flag', color: theme.colors.textSecondary },
  ];

  const priorities = [
    { id: 'low', name: 'Düşük', color: theme.colors.success },
    { id: 'medium', name: 'Orta', color: theme.colors.warning },
    { id: 'high', name: 'Yüksek', color: theme.colors.error },
  ];

  const handleSave = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Hata', 'Hedef başlığı gerekli.');
      return;
    }

    if (!formData.description.trim()) {
      Alert.alert('Hata', 'Hedef açıklaması gerekli.');
      return;
    }

    const targetAmount = parseFloat(formData.targetAmount.replace(',', '.'));
    if (isNaN(targetAmount) || targetAmount <= 0) {
      Alert.alert('Hata', 'Geçerli bir hedef tutar girin.');
      return;
    }

    const currentAmount = parseFloat(formData.currentAmount.replace(',', '.'));
    if (isNaN(currentAmount) || currentAmount < 0) {
      Alert.alert('Hata', 'Geçerli bir mevcut tutar girin.');
      return;
    }

    if (currentAmount > targetAmount) {
      Alert.alert('Hata', 'Mevcut tutar hedef tutardan büyük olamaz.');
      return;
    }

    if (formData.targetDate <= new Date()) {
      Alert.alert('Hata', 'Hedef tarihi gelecekte olmalı.');
      return;
    }

    setIsLoading(true);

    try {
      // Mock goal creation - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('Goal created:', {
        title: formData.title.trim(),
        description: formData.description.trim(),
        targetAmount,
        currentAmount,
        targetDate: formData.targetDate.toISOString(),
        category: formData.category,
        priority: formData.priority,
        isCompleted: false,
      });

      Alert.alert('Başarılı', 'Hedef başarıyla oluşturuldu.', [
        { text: 'Tamam', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Hata', error || 'Hedef oluşturulurken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (text: string) => {
    const cleaned = text.replace(/[^0-9.,]/g, '');
    return cleaned;
  };

  const calculateProgress = () => {
    const target = parseFloat(formData.targetAmount.replace(',', '.')) || 0;
    const current = parseFloat(formData.currentAmount.replace(',', '.')) || 0;
    return target > 0 ? (current / target) * 100 : 0;
  };

  const calculateDaysRemaining = () => {
    const today = new Date();
    const target = new Date(formData.targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const calculateDailyTarget = () => {
    const target = parseFloat(formData.targetAmount.replace(',', '.')) || 0;
    const current = parseFloat(formData.currentAmount.replace(',', '.')) || 0;
    const remaining = target - current;
    const daysRemaining = calculateDaysRemaining();
    return daysRemaining > 0 ? remaining / daysRemaining : 0;
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    textArea: {
      height: 80,
      textAlignVertical: 'top',
    },
    amountInputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      height: 50,
    },
    currencySymbol: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginRight: 8,
    },
    amountInput: {
      flex: 1,
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    categoryScroll: {
      flexDirection: 'row',
    },
    categoryButton: {
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      borderWidth: 1,
      marginRight: 8,
      backgroundColor: theme.colors.surface,
      minWidth: 80,
    },
    categoryButtonActive: {
      backgroundColor: theme.colors.surfaceSecondary,
    },
    categoryText: {
      fontSize: 12,
      color: theme.colors.text,
      marginTop: 4,
      textAlign: 'center',
    },
    priorityContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    priorityButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      alignItems: 'center',
    },
    priorityButtonActive: {
      borderColor: 'transparent',
    },
    priorityText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    priorityTextActive: {
      color: theme.colors.surface,
    },
    previewContainer: {
      marginBottom: 20,
    },
    previewTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    previewCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    previewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    previewName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
    },
    priorityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 8,
    },
    priorityBadgeText: {
      fontSize: 10,
      fontWeight: '600',
      color: theme.colors.surface,
      textTransform: 'uppercase',
    },
    previewDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 16,
      lineHeight: 20,
    },
    progressContainer: {
      marginBottom: 16,
    },
    progressInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    progressAmount: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    progressPercentage: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    progressBar: {
      height: 8,
      backgroundColor: theme.colors.border,
      borderRadius: 4,
    },
    progressFill: {
      height: 8,
      borderRadius: 4,
    },
    previewStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    statItem: {
      alignItems: 'center',
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    statValue: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      marginBottom: 40,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
        {/* Goal Title */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Hedef Başlığı *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Örn: Yeni Araba"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
            autoFocus
          />
        </View>

        {/* Goal Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Açıklama *</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            placeholder="Hedefinizi detaylı olarak açıklayın..."
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.description}
            onChangeText={(text) => setFormData({ ...formData, description: text })}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Target Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Hedef Tutar *</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.targetAmount}
              onChangeText={(text) => setFormData({ ...formData, targetAmount: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Current Amount */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Mevcut Tutar</Text>
          <View style={styles.amountInputWrapper}>
            <Text style={styles.currencySymbol}>₺</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0,00"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.currentAmount}
              onChangeText={(text) => setFormData({ ...formData, currentAmount: formatAmount(text) })}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* Target Date */}
        <DatePicker
          label="Hedef Tarihi *"
          value={formData.targetDate}
          onDateChange={(date) => setFormData({ ...formData, targetDate: date })}
          minimumDate={new Date()}
        />

        {/* Category Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Kategori</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  formData.category === category.id && styles.categoryButtonActive,
                  { borderColor: category.color }
                ]}
                onPress={() => setFormData({ ...formData, category: category.id as any })}
              >
                <Ionicons name={category.icon as any} size={20} color={category.color} />
                <Text style={[
                  styles.categoryText,
                  formData.category === category.id && { color: category.color }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Priority Selection */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Öncelik</Text>
          <View style={styles.priorityContainer}>
            {priorities.map((priority) => (
              <TouchableOpacity
                key={priority.id}
                style={[
                  styles.priorityButton,
                  formData.priority === priority.id && styles.priorityButtonActive,
                  formData.priority === priority.id && { backgroundColor: priority.color }
                ]}
                onPress={() => setFormData({ ...formData, priority: priority.id as any })}
              >
                <Text style={[
                  styles.priorityText,
                  formData.priority === priority.id && styles.priorityTextActive
                ]}>
                  {priority.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Goal Preview */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Hedef Özeti</Text>
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewName}>{formData.title || 'Hedef Başlığı'}</Text>
              <View style={[
                styles.priorityBadge,
                { backgroundColor: priorities.find(p => p.id === formData.priority)?.color }
              ]}>
                <Text style={styles.priorityBadgeText}>{formData.priority}</Text>
              </View>
            </View>

            <Text style={styles.previewDescription}>
              {formData.description || 'Hedef açıklaması'}
            </Text>

            <View style={styles.progressContainer}>
              <View style={styles.progressInfo}>
                <Text style={styles.progressAmount}>
                  ₺{formData.currentAmount || '0'} / ₺{formData.targetAmount || '0'}
                </Text>
                <Text style={styles.progressPercentage}>
                  {calculateProgress().toFixed(0)}%
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min(calculateProgress(), 100)}%`,
                      backgroundColor: theme.colors.primary
                    }
                  ]}
                />
              </View>
            </View>

            <View style={styles.previewStats}>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Kalan Gün</Text>
                <Text style={styles.statValue}>{calculateDaysRemaining()}</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statLabel}>Günlük Hedef</Text>
                <Text style={styles.statValue}>₺{calculateDailyTarget().toFixed(0)}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Oluşturuluyor...' : 'Hedef Oluştur'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default AddGoalScreen;
