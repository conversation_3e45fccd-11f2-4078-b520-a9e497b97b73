// RFC-003 Bütçe Test Ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import BudgetServiceTest from '../../tests/BudgetServiceTest';

const BudgetTestScreen: React.FC = () => {
  const { theme } = useTheme();
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentTest('Testler başlatılıyor...');

    try {
      // Console.log'ları yakalayalım
      const originalLog = console.log;
      const originalError = console.error;

      console.log = (message: string) => {
        addTestResult(message);
        originalLog(message);
      };

      console.error = (message: string, error?: any) => {
        addTestResult(`❌ ${message}`);
        originalError(message, error);
      };

      await BudgetServiceTest.runAllTests();

      // Console'u geri yükle
      console.log = originalLog;
      console.error = originalError;

      setCurrentTest('✅ Tüm testler başarıyla tamamlandı!');
      Alert.alert('Başarılı', 'RFC-003 Bütçe testleri başarıyla tamamlandı!');

    } catch (error: any) {
      setCurrentTest(`❌ Test hatası: ${error.message}`);
      Alert.alert('Test Hatası', error.message || 'Testler sırasında bir hata oluştu');
    } finally {
      setIsRunning(false);
    }
  };

  const runIndividualTest = async (testName: string, testFunction: () => Promise<any>) => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentTest(`${testName} çalıştırılıyor...`);

    try {
      const originalLog = console.log;
      const originalError = console.error;

      console.log = (message: string) => {
        addTestResult(message);
        originalLog(message);
      };

      console.error = (message: string, error?: any) => {
        addTestResult(`❌ ${message}`);
        originalError(message, error);
      };

      await testFunction();

      console.log = originalLog;
      console.error = originalError;

      setCurrentTest(`✅ ${testName} başarıyla tamamlandı!`);
      Alert.alert('Başarılı', `${testName} başarıyla tamamlandı!`);

    } catch (error: any) {
      setCurrentTest(`❌ ${testName} hatası: ${error.message}`);
      Alert.alert('Test Hatası', error.message || `${testName} sırasında bir hata oluştu`);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setCurrentTest('');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          RFC-003 Bütçe Testleri
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          Database Schema & Service Layer Test
        </Text>
      </View>

      {/* Test Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.testButton,
            styles.primaryButton,
            { backgroundColor: theme.colors.primary },
            isRunning && styles.disabledButton
          ]}
          onPress={runAllTests}
          disabled={isRunning}
        >
          <Ionicons name="play-circle" size={24} color={theme.colors.surface} />
          <Text style={[styles.buttonText, { color: theme.colors.surface }]}>
            Tüm Testleri Çalıştır
          </Text>
        </TouchableOpacity>

        <View style={styles.individualTests}>
          <TouchableOpacity
            style={[
              styles.testButton,
              styles.secondaryButton,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              isRunning && styles.disabledButton
            ]}
            onPress={() => runIndividualTest(
              'Database Migration',
              BudgetServiceTest.testDatabaseMigration
            )}
            disabled={isRunning}
          >
            <Ionicons name="server" size={20} color={theme.colors.primary} />
            <Text style={[styles.buttonText, { color: theme.colors.primary }]}>
              Database Migration
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.testButton,
              styles.secondaryButton,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              isRunning && styles.disabledButton
            ]}
            onPress={() => runIndividualTest(
              'Budget Creation',
              async () => await BudgetServiceTest.testBudgetCreation()
            )}
            disabled={isRunning}
          >
            <Ionicons name="add-circle" size={20} color={theme.colors.primary} />
            <Text style={[styles.buttonText, { color: theme.colors.primary }]}>
              Budget Creation
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[
            styles.testButton,
            styles.clearButton,
            { backgroundColor: theme.colors.surfaceSecondary },
            isRunning && styles.disabledButton
          ]}
          onPress={clearResults}
          disabled={isRunning}
        >
          <Ionicons name="refresh" size={20} color={theme.colors.textSecondary} />
          <Text style={[styles.buttonText, { color: theme.colors.textSecondary }]}>
            Sonuçları Temizle
          </Text>
        </TouchableOpacity>
      </View>

      {/* Current Test Status */}
      {currentTest && (
        <View style={[styles.statusContainer, { backgroundColor: theme.colors.surface }]}>
          {isRunning && <ActivityIndicator size="small" color={theme.colors.primary} />}
          <Text style={[styles.statusText, { color: theme.colors.text }]}>
            {currentTest}
          </Text>
        </View>
      )}

      {/* Test Results */}
      <ScrollView 
        style={styles.resultsContainer}
        contentContainerStyle={styles.resultsContent}
      >
        {testResults.map((result, index) => (
          <View key={index} style={[styles.resultItem, { backgroundColor: theme.colors.surface }]}>
            <Text style={[
              styles.resultText, 
              { 
                color: result.includes('❌') 
                  ? theme.colors.error 
                  : result.includes('✅') 
                    ? theme.colors.success 
                    : theme.colors.text 
              }
            ]}>
              {result}
            </Text>
          </View>
        ))}
        
        {testResults.length === 0 && !isRunning && (
          <View style={styles.emptyContainer}>
            <Ionicons name="flask-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              Test sonuçları burada görünecek
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  buttonContainer: {
    marginBottom: 16,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  primaryButton: {
    // Primary button styles
  },
  secondaryButton: {
    borderWidth: 1,
  },
  clearButton: {
    // Clear button styles
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  individualTests: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  resultsContainer: {
    flex: 1,
  },
  resultsContent: {
    paddingBottom: 20,
  },
  resultItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  resultText: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

export default BudgetTestScreen;
