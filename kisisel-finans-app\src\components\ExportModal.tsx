// Export Modal - <PERSON><PERSON> dışa aktarma modal'ı
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { Transaction } from '../types/transaction';
import ExportService, { ExportOptions, ExportResult } from '../services/ExportService';
import ShareService from '../services/ShareService';
import DatePicker from './DatePicker';

interface ExportModalProps {
  visible: boolean;
  onClose: () => void;
  transactions: Transaction[];
}

const ExportModal: React.FC<ExportModalProps> = ({
  visible,
  onClose,
  transactions,
}) => {
  const { theme } = useTheme();

  // State management
  const [exportOptions, setExportOptions] = useState<ExportOptions>(() => {
    const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const endDate = new Date();

    console.log('📅 ExportModal - Initial date range:', {
      start: startDate.toLocaleDateString(),
      end: endDate.toLocaleDateString(),
      startISO: startDate.toISOString(),
      endISO: endDate.toISOString()
    });

    return {
      format: 'excel',
      dateRange: {
        start: startDate.toISOString(), // 7 gün önce
        end: endDate.toISOString(), // Bugün
      },
      includeSummary: true,
      includeCharts: false,
    };
  });

  const [isExporting, setIsExporting] = useState(false);

  // Format seçimi
  const handleFormatChange = (format: 'pdf' | 'excel' | 'csv') => {
    setExportOptions(prev => ({ ...prev, format }));
  };

  // Tarih değişikliği
  const handleStartDateChange = (date: Date) => {
    console.log('📅 ExportModal - Start date changed to:', date.toLocaleDateString());
    setExportOptions(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        start: date.toISOString(),
      },
    }));
  };

  const handleEndDateChange = (date: Date) => {
    console.log('📅 ExportModal - End date changed to:', date.toLocaleDateString());
    setExportOptions(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        end: date.toISOString(),
      },
    }));
  };

  // Export işlemi
  const handleExport = async () => {
    try {
      setIsExporting(true);

      console.log('📅 ExportModal - Export başlatılıyor...');
      console.log('📅 ExportModal - Tarih aralığı:', {
        start: exportOptions.dateRange.start,
        end: exportOptions.dateRange.end,
        startFormatted: new Date(exportOptions.dateRange.start).toLocaleDateString(),
        endFormatted: new Date(exportOptions.dateRange.end).toLocaleDateString()
      });
      console.log('📅 ExportModal - Toplam işlem sayısı:', transactions.length);

      // Filtrelenmiş işlem sayısını kontrol et
      const filteredCount = transactions.filter(t => {
        const transactionDate = new Date(t.date);
        const startDate = new Date(exportOptions.dateRange.start);
        const endDate = new Date(exportOptions.dateRange.end);

        console.log('📅 ExportModal - İşlem kontrol ediliyor:', {
          description: t.description,
          date: t.date,
          transactionDate: transactionDate.toLocaleDateString(),
          startDate: startDate.toLocaleDateString(),
          endDate: endDate.toLocaleDateString(),
          passes: transactionDate >= startDate && transactionDate <= endDate
        });

        return transactionDate >= startDate && transactionDate <= endDate;
      }).length;

      console.log('📅 ExportModal - Filtrelenmiş işlem sayısı:', filteredCount);

      if (filteredCount === 0) {
        Alert.alert(
          'Uyarı',
          'Seçilen tarih aralığında hiç işlem bulunamadı.',
          [{ text: 'Tamam' }]
        );
        return;
      }

      // Export işlemini başlat
      const result: ExportResult = await ExportService.exportTransactions(
        transactions,
        exportOptions
      );

      if (result.success && result.filePath && result.fileName) {
        // Başarılı export - gelişmiş paylaşım seçeneği sun
        Alert.alert(
          'Başarılı!',
          `Rapor başarıyla oluşturuldu.\n${filteredCount} işlem dışa aktarıldı.`,
          [
            { text: 'Tamam', style: 'cancel' },
            {
              text: 'Paylaş',
              onPress: () => showShareOptions(result.filePath!, result.fileName!),
            },
          ]
        );
      } else {
        Alert.alert(
          'Hata',
          result.error || 'Rapor oluşturulurken bir hata oluştu.',
          [{ text: 'Tamam' }]
        );
      }
    } catch (error: any) {
      console.error('Export error:', error);
      Alert.alert(
        'Hata',
        'Rapor oluşturulurken beklenmeyen bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsExporting(false);
    }
  };

  // Gelişmiş paylaşım seçenekleri menüsü
  const showShareOptions = (filePath: string, fileName: string) => {
    Alert.alert(
      'Paylaş',
      `${fileName} dosyasını nasıl paylaşmak istiyorsunuz?`,
      [
        {
          text: '📧 Email',
          onPress: () => handleEmailShare(filePath, fileName),
        },
        {
          text: '💬 WhatsApp',
          onPress: () => handleWhatsAppShare(filePath, fileName),
        },
        {
          text: '📱 Diğer Uygulamalar',
          onPress: () => handleGeneralShare(filePath, fileName),
        },
        {
          text: 'İptal',
          style: 'cancel',
        },
      ]
    );
  };

  // Email paylaşımı
  const handleEmailShare = async (filePath: string, fileName: string) => {
    try {
      // Dosya boyutunu kontrol et
      const sizeCheck = await ShareService.checkFileSize(filePath, 25);
      if (!sizeCheck.valid) {
        Alert.alert(
          'Dosya Çok Büyük',
          `Dosya boyutu ${sizeCheck.sizeMB}MB. Email için maksimum 25MB olmalıdır.`,
          [{ text: 'Tamam' }]
        );
        return;
      }

      // Email içeriği hazırla
      const filteredCount = getFilteredTransactionCount();
      const startDate = new Date(exportOptions.dateRange.start).toLocaleDateString('tr-TR');
      const endDate = new Date(exportOptions.dateRange.end).toLocaleDateString('tr-TR');

      const emailBody = `
Merhaba,

Kişisel finans raporunuz ektedir.

📊 Rapor Detayları:
• Tarih Aralığı: ${startDate} - ${endDate}
• İşlem Sayısı: ${filteredCount}
• Format: ${exportOptions.format.toUpperCase()}
• Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}

Bu rapor Kişisel Finans Uygulaması ile oluşturulmuştur.

İyi günler!
      `.trim();

      const result = await ShareService.sendEmail({
        subject: `Finans Raporu - ${startDate} / ${endDate}`,
        body: emailBody,
        attachments: [filePath],
        isHtml: false,
      });

      if (result.success) {
        onClose(); // Modal'ı kapat
      } else {
        Alert.alert('Hata', result.error || 'Email gönderilemedi');
      }
    } catch (error) {
      console.error('Email share error:', error);
      Alert.alert('Hata', 'Email paylaşımı sırasında bir hata oluştu.');
    }
  };

  // WhatsApp paylaşımı
  const handleWhatsAppShare = async (filePath: string, fileName: string) => {
    try {
      const filteredCount = getFilteredTransactionCount();
      const startDate = new Date(exportOptions.dateRange.start).toLocaleDateString('tr-TR');
      const endDate = new Date(exportOptions.dateRange.end).toLocaleDateString('tr-TR');

      const message = `📊 Finans Raporum\n\n📅 ${startDate} - ${endDate}\n💼 ${filteredCount} işlem\n\nKişisel Finans Uygulaması ile oluşturuldu.`;

      const result = await ShareService.shareToWhatsApp(message, filePath);

      if (result.success) {
        onClose(); // Modal'ı kapat
      } else {
        Alert.alert('Hata', result.error || 'WhatsApp paylaşımı başarısız');
      }
    } catch (error) {
      console.error('WhatsApp share error:', error);
      Alert.alert('Hata', 'WhatsApp paylaşımı sırasında bir hata oluştu.');
    }
  };

  // Genel paylaşım (sistem paylaşım menüsü)
  const handleGeneralShare = async (filePath: string, fileName: string) => {
    try {
      const result = await ShareService.shareGeneral({
        title: 'Finans Raporu Paylaş',
        url: filePath,
      });

      if (result.success) {
        onClose(); // Modal'ı kapat
      } else {
        Alert.alert('Hata', result.error || 'Paylaşım başarısız');
      }
    } catch (error) {
      console.error('General share error:', error);
      Alert.alert('Hata', 'Paylaşım sırasında bir hata oluştu.');
    }
  };

  // Format butonları
  const renderFormatButtons = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Dosya Formatı
      </Text>
      <View style={styles.formatContainer}>
        {[
          { key: 'excel', label: 'Excel (.xlsx)', icon: 'document-text' },
          { key: 'csv', label: 'CSV (.csv)', icon: 'list' },
          { key: 'pdf', label: 'PDF (.html)', icon: 'document' },
        ].map((format) => (
          <TouchableOpacity
            key={format.key}
            style={[
              styles.formatButton,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
              },
              exportOptions.format === format.key && {
                backgroundColor: theme.colors.primary,
                borderColor: theme.colors.primary,
              },
            ]}
            onPress={() => handleFormatChange(format.key as any)}
          >
            <Ionicons
              name={format.icon as any}
              size={24}
              color={
                exportOptions.format === format.key
                  ? theme.colors.surface
                  : theme.colors.text
              }
            />
            <Text
              style={[
                styles.formatText,
                { color: theme.colors.text },
                exportOptions.format === format.key && {
                  color: theme.colors.surface,
                },
              ]}
            >
              {format.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Tarih aralığı seçimi
  const renderDateRange = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Tarih Aralığı
      </Text>
      <View style={styles.dateContainer}>
        <DatePicker
          label="Başlangıç"
          value={new Date(exportOptions.dateRange.start)}
          onDateChange={handleStartDateChange}
          style={{ flex: 1 }}
        />

        <Text style={[styles.dateRangeText, { color: theme.colors.textSecondary }]}>
          -
        </Text>

        <DatePicker
          label="Bitiş"
          value={new Date(exportOptions.dateRange.end)}
          onDateChange={handleEndDateChange}
          style={{ flex: 1 }}
        />
      </View>
    </View>
  );

  // Seçenekler
  const renderOptions = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Rapor Seçenekleri
      </Text>

      <View style={styles.optionRow}>
        <View style={styles.optionInfo}>
          <Text style={[styles.optionTitle, { color: theme.colors.text }]}>
            Özet Bilgileri Dahil Et
          </Text>
          <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
            Toplam gelir, gider ve net tutar bilgilerini ekle
          </Text>
        </View>
        <Switch
          value={exportOptions.includeSummary}
          onValueChange={(value) =>
            setExportOptions(prev => ({ ...prev, includeSummary: value }))
          }
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.surface}
        />
      </View>

      <View style={styles.optionRow}>
        <View style={styles.optionInfo}>
          <Text style={[styles.optionTitle, { color: theme.colors.text }]}>
            Grafikleri Dahil Et
          </Text>
          <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
            Kategori dağılımı ve trend grafikleri (sadece PDF)
          </Text>
        </View>
        <Switch
          value={exportOptions.includeCharts}
          onValueChange={(value) =>
            setExportOptions(prev => ({ ...prev, includeCharts: value }))
          }
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.surface}
          disabled={exportOptions.format !== 'pdf'}
        />
      </View>
    </View>
  );

  // İşlem sayısı bilgisi
  const getFilteredTransactionCount = () => {
    const count = transactions.filter(t => {
      const transactionDate = new Date(t.date);
      const startDate = new Date(exportOptions.dateRange.start);
      const endDate = new Date(exportOptions.dateRange.end);
      return transactionDate >= startDate && transactionDate <= endDate;
    }).length;

    console.log('📅 ExportModal - getFilteredTransactionCount:', {
      totalTransactions: transactions.length,
      filteredCount: count,
      dateRange: {
        start: new Date(exportOptions.dateRange.start).toLocaleDateString(),
        end: new Date(exportOptions.dateRange.end).toLocaleDateString()
      }
    });

    return count;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Rapor Dışa Aktar
          </Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderFormatButtons()}
          {renderDateRange()}
          {renderOptions()}

          {/* İşlem sayısı bilgisi */}
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="information-circle" size={20} color={theme.colors.primary} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>
              Seçilen tarih aralığında {getFilteredTransactionCount()} işlem dışa aktarılacak.
            </Text>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[
              styles.exportButton,
              { backgroundColor: theme.colors.primary },
              isExporting && { opacity: 0.7 },
            ]}
            onPress={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <ActivityIndicator size="small" color={theme.colors.surface} />
            ) : (
              <Ionicons name="download" size={20} color={theme.colors.surface} />
            )}
            <Text style={[styles.exportButtonText, { color: theme.colors.surface }]}>
              {isExporting ? 'Oluşturuluyor...' : 'Raporu Oluştur'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  formatContainer: {
    gap: 12,
  },
  formatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  formatText: {
    fontSize: 16,
    fontWeight: '500',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  dateButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  dateText: {
    fontSize: 16,
  },
  dateRangeText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionInfo: {
    flex: 1,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
    marginTop: 8,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  exportButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ExportModal;
