// Deep Link Service - <PERSON><PERSON> ba<PERSON> servisi

import * as Linking from 'expo-linking';
import { NavigationContainerRef } from '@react-navigation/native';

interface DeepLinkRoute {
  screen: string;
  params?: any;
}

interface DeepLinkHandler {
  pattern: RegExp;
  handler: (matches: RegExpMatchArray, url: string) => DeepLinkRoute | null;
}

class DeepLinkService {
  private static instance: DeepLinkService;
  private navigationRef: NavigationContainerRef<any> | null = null;
  private handlers: DeepLinkHandler[] = [];
  private pendingUrl: string | null = null;

  static getInstance(): DeepLinkService {
    if (!DeepLinkService.instance) {
      DeepLinkService.instance = new DeepLinkService();
    }
    return DeepLinkService.instance;
  }

  /**
   * Navigation referansını ayarlar
   */
  setNavigationRef(ref: NavigationContainerRef<any>): void {
    this.navigationRef = ref;
    
    // Bekleyen URL varsa işle
    if (this.pendingUrl) {
      this.handleDeepLink(this.pendingUrl);
      this.pendingUrl = null;
    }
  }

  /**
   * Deep link dinleyicilerini başlatır
   */
  initialize(): () => void {
    // URL scheme'leri tanımla
    const prefix = Linking.createURL('/');
    
    // İlk açılış URL'ini kontrol et
    Linking.getInitialURL().then(url => {
      if (url) {
        this.handleDeepLink(url);
      }
    });

    // URL değişikliklerini dinle
    const subscription = Linking.addEventListener('url', ({ url }) => {
      this.handleDeepLink(url);
    });

    // Cleanup fonksiyonu döndür
    return () => {
      subscription.remove();
    };
  }

  /**
   * Deep link handler'ları kaydet
   */
  private registerHandlers(): void {
    // Transaction detail: finans://transaction/123
    this.addHandler(
      /^.*\/transaction\/(\d+)$/,
      (matches) => ({
        screen: 'TransactionDetail',
        params: { transactionId: matches[1] },
      })
    );

    // Budget detail: finans://budget/456
    this.addHandler(
      /^.*\/budget\/(\d+)$/,
      (matches) => ({
        screen: 'BudgetDetail',
        params: { budgetId: matches[1] },
      })
    );

    // Goal detail: finans://goal/789
    this.addHandler(
      /^.*\/goal\/(\d+)$/,
      (matches) => ({
        screen: 'GoalDetail',
        params: { goalId: matches[1] },
      })
    );

    // Add transaction: finans://add-transaction?type=expense
    this.addHandler(
      /^.*\/add-transaction(\?.*)?$/,
      (matches, url) => {
        const urlObj = new URL(url);
        const type = urlObj.searchParams.get('type') || 'expense';
        return {
          screen: 'AddTransaction',
          params: { type },
        };
      }
    );

    // Reports with filter: finans://reports?period=month
    this.addHandler(
      /^.*\/reports(\?.*)?$/,
      (matches, url) => {
        const urlObj = new URL(url);
        const period = urlObj.searchParams.get('period') || 'month';
        return {
          screen: 'Reports',
          params: { period },
        };
      }
    );

    // Settings: finans://settings
    this.addHandler(
      /^.*\/settings$/,
      () => ({
        screen: 'Settings',
      })
    );

    // Profile: finans://profile
    this.addHandler(
      /^.*\/profile$/,
      () => ({
        screen: 'Profile',
      })
    );

    // Category manager: finans://categories
    this.addHandler(
      /^.*\/categories$/,
      () => ({
        screen: 'CategoryManager',
      })
    );
  }

  /**
   * Handler ekler
   */
  private addHandler(pattern: RegExp, handler: (matches: RegExpMatchArray, url: string) => DeepLinkRoute | null): void {
    this.handlers.push({ pattern, handler });
  }

  /**
   * Deep link'i işler
   */
  private handleDeepLink(url: string): void {
    if (!this.navigationRef) {
      // Navigation henüz hazır değilse beklet
      this.pendingUrl = url;
      return;
    }

    // Handler'ları ilk kez kaydet
    if (this.handlers.length === 0) {
      this.registerHandlers();
    }

    console.log('Handling deep link:', url);

    // URL'yi işle
    for (const { pattern, handler } of this.handlers) {
      const matches = url.match(pattern);
      if (matches) {
        try {
          const route = handler(matches, url);
          if (route) {
            this.navigateToRoute(route);
            return;
          }
        } catch (error) {
          console.error('Error handling deep link:', error);
        }
      }
    }

    // Eşleşme bulunamazsa ana sayfaya yönlendir
    this.navigateToHome();
  }

  /**
   * Belirtilen route'a yönlendirir
   */
  private navigateToRoute(route: DeepLinkRoute): void {
    if (!this.navigationRef) {
      return;
    }

    try {
      this.navigationRef.navigate(route.screen as never, route.params as never);
    } catch (error) {
      console.error('Error navigating to route:', error);
      this.navigateToHome();
    }
  }

  /**
   * Ana sayfaya yönlendirir
   */
  private navigateToHome(): void {
    if (!this.navigationRef) {
      return;
    }

    try {
      this.navigationRef.navigate('Home' as never);
    } catch (error) {
      console.error('Error navigating to home:', error);
    }
  }

  /**
   * Deep link URL'i oluşturur
   */
  createDeepLink(path: string, params?: Record<string, string>): string {
    const baseUrl = Linking.createURL(path);
    
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params);
      return `${baseUrl}?${searchParams.toString()}`;
    }
    
    return baseUrl;
  }

  /**
   * Transaction detail deep link'i oluşturur
   */
  createTransactionLink(transactionId: string): string {
    return this.createDeepLink(`/transaction/${transactionId}`);
  }

  /**
   * Budget detail deep link'i oluşturur
   */
  createBudgetLink(budgetId: string): string {
    return this.createDeepLink(`/budget/${budgetId}`);
  }

  /**
   * Goal detail deep link'i oluşturur
   */
  createGoalLink(goalId: string): string {
    return this.createDeepLink(`/goal/${goalId}`);
  }

  /**
   * Add transaction deep link'i oluşturur
   */
  createAddTransactionLink(type: 'income' | 'expense' = 'expense'): string {
    return this.createDeepLink('/add-transaction', { type });
  }

  /**
   * Reports deep link'i oluşturur
   */
  createReportsLink(period: string = 'month'): string {
    return this.createDeepLink('/reports', { period });
  }

  /**
   * Settings deep link'i oluşturur
   */
  createSettingsLink(): string {
    return this.createDeepLink('/settings');
  }

  /**
   * Profile deep link'i oluşturur
   */
  createProfileLink(): string {
    return this.createDeepLink('/profile');
  }

  /**
   * Category manager deep link'i oluşturur
   */
  createCategoriesLink(): string {
    return this.createDeepLink('/categories');
  }

  /**
   * Deep link'i paylaşır
   */
  async shareDeepLink(url: string, message?: string): Promise<void> {
    try {
      const shareMessage = message ? `${message}\n\n${url}` : url;
      
      // Expo'da Share API kullanılabilir
      // import { Share } from 'react-native';
      // await Share.share({ message: shareMessage });
      
      console.log('Sharing deep link:', shareMessage);
    } catch (error) {
      console.error('Error sharing deep link:', error);
    }
  }

  /**
   * URL'nin geçerli bir deep link olup olmadığını kontrol eder
   */
  isValidDeepLink(url: string): boolean {
    if (this.handlers.length === 0) {
      this.registerHandlers();
    }

    return this.handlers.some(({ pattern }) => pattern.test(url));
  }

  /**
   * URL'den route bilgisini çıkarır
   */
  parseDeepLink(url: string): DeepLinkRoute | null {
    if (this.handlers.length === 0) {
      this.registerHandlers();
    }

    for (const { pattern, handler } of this.handlers) {
      const matches = url.match(pattern);
      if (matches) {
        try {
          return handler(matches, url);
        } catch (error) {
          console.error('Error parsing deep link:', error);
        }
      }
    }

    return null;
  }
}

export default DeepLinkService.getInstance();
