// Investment Screen - Ya<PERSON><PERSON><PERSON><PERSON><PERSON> ana ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const InvestmentScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();

  // Mock data - gerçek uygulamada Redux store'dan gelecek
  const [riskProfile] = useState(null);
  const [investmentOptions] = useState([
    {
      id: '1',
      name: 'BIST 30 ETF',
      type: 'etf',
      riskLevel: 'medium',
      minimumInvestment: 100,
      performance: { oneYear: 15.2 }
    },
    {
      id: '2',
      name: 'Altın ETF',
      type: 'etf',
      riskLevel: 'low',
      minimumInvestment: 50,
      performance: { oneYear: 8.5 }
    },
    {
      id: '3',
      name: '<PERSON>k<PERSON><PERSON><PERSON>onu',
      type: 'fund',
      riskLevel: 'high',
      minimumInvestment: 250,
      performance: { oneYear: 22.1 }
    }
  ]);
  const [portfolios] = useState<any[]>([]);

  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // Mock refresh - replace with real API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleCreateRiskProfile = () => {
    navigation.navigate('RiskAssessment' as never);
  };

  const handleViewRecommendations = () => {
    if (riskProfile) {
      navigation.navigate('InvestmentRecommendations' as never);
    } else {
      Alert.alert(
        'Risk Profili Gerekli',
        'Yatırım önerileri için önce risk profilinizi oluşturmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Risk Profili Oluştur', onPress: handleCreateRiskProfile }
        ]
      );
    }
  };

  const handleCreatePortfolio = () => {
    navigation.navigate('CreatePortfolio' as never);
  };

  const calculateTotalPortfolioValue = () => {
    return portfolios.reduce((total, portfolio) => total + portfolio.totalValue, 0);
  };

  const calculateTotalReturn = () => {
    return portfolios.reduce((total, portfolio) => total + portfolio.totalReturn, 0);
  };

  const getTotalReturnPercentage = () => {
    const totalInvested = portfolios.reduce((total, portfolio) => total + portfolio.totalInvested, 0);
    const totalReturn = calculateTotalReturn();
    return totalInvested > 0 ? (totalReturn / totalInvested) * 100 : 0;
  };



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>Yatırımlarım</Text>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('InvestmentOptions' as never)}
          >
            <Ionicons name="add" size={24} color={theme.colors.surface} />
          </TouchableOpacity>
        </View>

        {/* Portfolio Summary */}
        <View style={[styles.summaryCard, {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.border
        }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>Portföy Özeti</Text>
          <View style={styles.summaryContent}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Toplam Değer</Text>
              <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
                ₺{calculateTotalPortfolioValue().toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Toplam Getiri</Text>
              <Text style={[
                styles.summaryValue,
                { color: calculateTotalReturn() >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {calculateTotalReturn() >= 0 ? '+' : ''}₺{calculateTotalReturn().toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Getiri Oranı</Text>
              <Text style={[
                styles.summaryValue,
                { color: getTotalReturnPercentage() >= 0 ? theme.colors.success : theme.colors.error }
              ]}>
                {getTotalReturnPercentage() >= 0 ? '+' : ''}{getTotalReturnPercentage().toFixed(2)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Hızlı İşlemler</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity
              style={[styles.actionCard, {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }]}
              onPress={handleCreateRiskProfile}
            >
              <View style={[styles.actionIcon, { backgroundColor: theme.colors.primary }]}>
                <Ionicons name="analytics" size={24} color={theme.colors.surface} />
              </View>
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Risk Profili</Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>
                {riskProfile ? 'Güncelle' : 'Oluştur'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionCard, {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }]}
              onPress={handleViewRecommendations}
            >
              <View style={[styles.actionIcon, { backgroundColor: theme.colors.success }]}>
                <Ionicons name="bulb" size={24} color={theme.colors.surface} />
              </View>
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Öneriler</Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>AI Destekli</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionCard, {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }]}
              onPress={handleCreatePortfolio}
            >
              <View style={[styles.actionIcon, { backgroundColor: theme.colors.info }]}>
                <Ionicons name="briefcase" size={24} color={theme.colors.surface} />
              </View>
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Portföy</Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>Oluştur</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionCard, {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }]}
              onPress={() => navigation.navigate('MarketData' as never)}
            >
              <View style={[styles.actionIcon, { backgroundColor: theme.colors.warning }]}>
                <Ionicons name="trending-up" size={24} color={theme.colors.surface} />
              </View>
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>Piyasa</Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>Veriler</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* My Portfolios */}
        <View style={styles.portfoliosContainer}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Portföylerim</Text>
            <TouchableOpacity onPress={handleCreatePortfolio}>
              <Text style={[styles.seeAllText, { color: theme.colors.primary }]}>Yeni Oluştur</Text>
            </TouchableOpacity>
          </View>

          {portfolios.length === 0 ? (
            <View style={[styles.emptyState, {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border
            }]}>
              <Ionicons name="briefcase-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>Henüz portföyünüz yok</Text>
              <Text style={[styles.emptyStateSubtitle, { color: theme.colors.textSecondary }]}>
                İlk portföyünüzü oluşturmak için yukarıdaki butona tıklayın
              </Text>
              <TouchableOpacity
                style={[styles.createPortfolioButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleCreatePortfolio}
              >
                <Text style={[styles.createPortfolioButtonText, { color: theme.colors.surface }]}>Portföy Oluştur</Text>
              </TouchableOpacity>
            </View>
          ) : (
            portfolios.map((portfolio) => (
              <TouchableOpacity
                key={portfolio.id}
                style={[styles.portfolioCard, {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border
                }]}
                onPress={() => {
                  // Portfolio detay sayfasına git - şimdilik alert göster
                  Alert.alert('Portföy Detayı', `${portfolio.name} portföyü detayları yakında eklenecek.`);
                }}
              >
                <View style={styles.portfolioHeader}>
                  <Text style={[styles.portfolioName, { color: theme.colors.text }]}>{portfolio.name}</Text>
                  <Text style={[
                    styles.portfolioReturn,
                    { color: portfolio.totalReturnPercentage >= 0 ? theme.colors.success : theme.colors.error }
                  ]}>
                    {portfolio.totalReturnPercentage >= 0 ? '+' : ''}{portfolio.totalReturnPercentage.toFixed(2)}%
                  </Text>
                </View>
                <View style={styles.portfolioContent}>
                  <Text style={[styles.portfolioValue, { color: theme.colors.text }]}>
                    ₺{portfolio.totalValue.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
                  </Text>
                  <Text style={[styles.portfolioRisk, { color: theme.colors.textSecondary }]}>
                    Risk: {portfolio.riskLevel === 'low' ? 'Düşük' :
                           portfolio.riskLevel === 'medium' ? 'Orta' : 'Yüksek'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>

        {/* Investment Options Preview */}
        <View style={styles.optionsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Yatırım Seçenekleri</Text>
            <TouchableOpacity onPress={() => navigation.navigate('InvestmentOptions' as never)}>
              <Text style={[styles.seeAllText, { color: theme.colors.primary }]}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>

          {investmentOptions.slice(0, 3).map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[styles.optionCard, {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }]}
              onPress={() => {
                // Yatırım detay sayfasına git - şimdilik alert göster
                Alert.alert('Yatırım Detayı', `${option.name} yatırım detayları yakında eklenecek.`);
              }}
            >
              <View style={styles.optionHeader}>
                <View>
                  <Text style={[styles.optionName, { color: theme.colors.text }]}>{option.name}</Text>
                  <Text style={[styles.optionType, { color: theme.colors.textSecondary }]}>
                    {option.type === 'stock' ? 'Hisse Senedi' :
                     option.type === 'bond' ? 'Tahvil' :
                     option.type === 'fund' ? 'Fon' :
                     option.type === 'etf' ? 'ETF' : option.type}
                  </Text>
                </View>
                <View style={styles.optionPerformance}>
                  <Text style={[
                    styles.optionReturn,
                    { color: option.performance.oneYear >= 0 ? theme.colors.success : theme.colors.error }
                  ]}>
                    {option.performance.oneYear >= 0 ? '+' : ''}{option.performance.oneYear.toFixed(1)}%
                  </Text>
                  <Text style={[styles.optionPeriod, { color: theme.colors.textSecondary }]}>1 Yıl</Text>
                </View>
              </View>
              <View style={styles.optionFooter}>
                <Text style={[styles.optionRisk, { color: theme.colors.textSecondary }]}>
                  Risk: {option.riskLevel === 'low' ? 'Düşük' :
                         option.riskLevel === 'medium' ? 'Orta' : 'Yüksek'}
                </Text>
                <Text style={[styles.optionMinInvestment, { color: theme.colors.textSecondary }]}>
                  Min: ₺{option.minimumInvestment}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  addButton: {
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  summaryContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionsContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    borderRadius: 12,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  portfoliosContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    borderWidth: 1,
  },
  emptyStateTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  createPortfolioButton: {
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  createPortfolioButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  portfolioCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  portfolioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  portfolioName: {
    fontSize: 16,
    fontWeight: '600',
  },
  portfolioReturn: {
    fontSize: 14,
    fontWeight: '600',
  },
  portfolioContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  portfolioValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  portfolioRisk: {
    fontSize: 12,
  },
  optionsContainer: {
    marginBottom: 20,
  },
  optionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  optionName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionType: {
    fontSize: 12,
  },
  optionPerformance: {
    alignItems: 'flex-end',
  },
  optionReturn: {
    fontSize: 16,
    fontWeight: '600',
  },
  optionPeriod: {
    fontSize: 12,
  },
  optionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionRisk: {
    fontSize: 12,
  },
  optionMinInvestment: {
    fontSize: 12,
  },
});

export default InvestmentScreen;
