// OAuth Helper Functions - OAuth yardımcı fonksiyonları

import { TokenResponse, SocialAuthUser } from './SocialAuthService';
import SecureStorageWrapper from './SecureStorageWrapper';

/**
 * Authorization code'u token'larl<PERSON>
 */
export async function exchangeCodeForTokens(
  code: string,
  codeVerifier: string,
  clientId: string,
  redirectUri: string
): Promise<TokenResponse | null> {
  try {
    const tokenEndpoint = 'https://oauth2.googleapis.com/token';
    
    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: clientId,
      code: code,
      redirect_uri: redirectUri,
      code_verifier: codeVerifier,
    });

    const response = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: body.toString(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Token exchange error:', errorData);
      return null;
    }

    const tokenData = await response.json();
    
    return {
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      idToken: tokenData.id_token,
      tokenType: tokenData.token_type || 'Bearer',
      expiresIn: tokenData.expires_in || 3600,
      scope: tokenData.scope,
    };
  } catch (error) {
    console.error('Error exchanging code for tokens:', error);
    return null;
  }
}

/**
 * Google kullanıcı bilgilerini al
 */
export async function getGoogleUserInfo(accessToken: string): Promise<SocialAuthUser | null> {
  try {
    const userInfoEndpoint = 'https://www.googleapis.com/oauth2/v2/userinfo';
    
    const response = await fetch(userInfoEndpoint, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('User info request failed:', response.status);
      return null;
    }

    const userData = await response.json();
    
    return {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      avatar: userData.picture,
      provider: 'google',
      emailVerified: userData.verified_email,
      locale: userData.locale,
      givenName: userData.given_name,
      familyName: userData.family_name,
    };
  } catch (error) {
    console.error('Error getting Google user info:', error);
    return null;
  }
}

/**
 * Token'ları güvenli şekilde sakla
 */
export async function storeTokens(
  provider: string,
  tokens: TokenResponse
): Promise<void> {
  try {
    const tokenData = {
      ...tokens,
      provider,
      storedAt: Date.now(),
    };

    await SecureStorageWrapper.setItem(
      `oauth_tokens_${provider}`,
      tokenData,
      {
        encrypt: true,
        useSecureStore: true,
      }
    );
  } catch (error) {
    console.error('Error storing tokens:', error);
    throw new Error('Token storage failed');
  }
}

/**
 * Saklanan token'ları al
 */
export async function getStoredTokens(provider: string): Promise<TokenResponse | null> {
  try {
    const tokenData = await SecureStorageWrapper.getItem(
      `oauth_tokens_${provider}`,
      { encrypt: true }
    );

    if (!tokenData) {
      return null;
    }

    // Token expiry kontrolü
    const expiryTime = tokenData.storedAt + (tokenData.expiresIn * 1000);
    if (Date.now() >= expiryTime) {
      // Token expired, refresh dene
      if (tokenData.refreshToken) {
        return await refreshAccessToken(provider, tokenData.refreshToken);
      } else {
        // Refresh token yok, token'ı sil
        await SecureStorageWrapper.removeItem(`oauth_tokens_${provider}`);
        return null;
      }
    }

    return tokenData;
  } catch (error) {
    console.error('Error getting stored tokens:', error);
    return null;
  }
}

/**
 * Access token'ı yenile
 */
export async function refreshAccessToken(
  provider: string,
  refreshToken: string
): Promise<TokenResponse | null> {
  try {
    if (provider !== 'google') {
      console.error('Refresh token only supported for Google');
      return null;
    }

    const tokenEndpoint = 'https://oauth2.googleapis.com/token';
    
    const body = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    });

    const response = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: body.toString(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Token refresh error:', errorData);
      
      // Refresh token invalid, token'ları sil
      await SecureStorageWrapper.removeItem(`oauth_tokens_${provider}`);
      return null;
    }

    const tokenData = await response.json();
    
    const newTokens: TokenResponse = {
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || refreshToken, // Keep old refresh token if new one not provided
      idToken: tokenData.id_token,
      tokenType: tokenData.token_type || 'Bearer',
      expiresIn: tokenData.expires_in || 3600,
      scope: tokenData.scope,
    };

    // Yeni token'ları sakla
    await storeTokens(provider, newTokens);
    
    return newTokens;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    return null;
  }
}

/**
 * Token'ları revoke et
 */
export async function revokeTokens(provider: string): Promise<boolean> {
  try {
    const tokens = await getStoredTokens(provider);
    if (!tokens) {
      return true; // Already revoked or not found
    }

    if (provider === 'google') {
      // Google token revoke
      const revokeEndpoint = 'https://oauth2.googleapis.com/revoke';
      
      const body = new URLSearchParams({
        token: tokens.accessToken,
      });

      const response = await fetch(revokeEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body.toString(),
      });

      if (!response.ok) {
        console.warn('Token revoke failed, but continuing with local cleanup');
      }
    }

    // Local token'ları sil
    await SecureStorageWrapper.removeItem(`oauth_tokens_${provider}`);
    
    return true;
  } catch (error) {
    console.error('Error revoking tokens:', error);
    return false;
  }
}

/**
 * ID Token'ı doğrula (JWT signature verification)
 */
export async function validateIdToken(idToken: string, clientId: string): Promise<boolean> {
  try {
    // JWT header ve payload'ı decode et
    const parts = idToken.split('.');
    if (parts.length !== 3) {
      return false;
    }

    const header = JSON.parse(atob(parts[0]));
    const payload = JSON.parse(atob(parts[1]));

    // Basic validations
    if (payload.aud !== clientId) {
      console.error('ID token audience mismatch');
      return false;
    }

    if (payload.exp * 1000 < Date.now()) {
      console.error('ID token expired');
      return false;
    }

    if (payload.iss !== 'https://accounts.google.com') {
      console.error('ID token issuer invalid');
      return false;
    }

    // Note: Gerçek uygulamada JWT signature verification yapılmalı
    // Bu mock implementation'da sadece basic validation yapıyoruz
    
    return true;
  } catch (error) {
    console.error('Error validating ID token:', error);
    return false;
  }
}

/**
 * OAuth scope'larını kontrol et
 */
export function validateScopes(requestedScopes: string[], grantedScopes: string): boolean {
  const granted = grantedScopes.split(' ');
  return requestedScopes.every(scope => granted.includes(scope));
}
