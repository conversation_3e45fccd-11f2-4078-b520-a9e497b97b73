// Goal Detail Screen - Hedef detay ekranı

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useRoute } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

type GoalDetailRouteProp = RouteProp<MainStackParamList, 'GoalDetail'>;

const GoalDetailScreen: React.FC = () => {
  const route = useRoute<GoalDetailRouteProp>();
  const { theme } = useTheme();
  const { goalId } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      alignItems: 'center',
      paddingVertical: 30,
      paddingHorizontal: 20,
      backgroundColor: theme.colors.surface,
    },
    categoryIcon: {
      width: 80,
      height: 80,
      borderRadius: 40,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    goalTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    goalDescription: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 16,
      lineHeight: 22,
    },
    priorityBadge: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 16,
    },
    priorityText: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.surface,
      textTransform: 'uppercase',
    },
    progressContainer: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      marginTop: 1,
    },
    progressInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    progressLabel: {
      fontSize: 16,
      color: theme.colors.text,
      fontWeight: '500',
    },
    progressPercentage: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    progressBar: {
      height: 12,
      backgroundColor: theme.colors.border,
      borderRadius: 6,
      marginBottom: 12,
    },
    progressFill: {
      height: '100%',
      borderRadius: 6,
    },
    progressAmounts: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    currentAmount: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.success,
    },
    targetAmount: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    statsContainer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    statCard: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
      textAlign: 'center',
    },
    statValue: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
    },
    detailsContainer: {
      padding: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
    },
    detailItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailLabel: {
      fontSize: 16,
      color: theme.colors.text,
    },
    detailValue: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    actionsContainer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      flexWrap: 'wrap',
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 12,
      paddingVertical: 16,
      minWidth: 120,
    },
    completedButton: {
      borderColor: theme.colors.success,
      backgroundColor: theme.colors.surface,
    },
    actionButtonText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginLeft: 8,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginTop: 16,
    },
  });

  // Mock goal data - replace with real API call
  const goal = {
    id: goalId,
    title: 'Acil Durum Fonu',
    description: 'Beklenmedik durumlar için 6 aylık gider tutarı',
    targetAmount: 50000,
    currentAmount: 32000,
    targetDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(),
    category: 'emergency',
    priority: 'high',
    isCompleted: false,
    createdAt: new Date().toISOString(),
  };

  if (!goal) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.errorTitle}>Hedef Bulunamadı</Text>
        </View>
      </View>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const progressPercentage = Math.min((goal.currentAmount / goal.targetAmount) * 100, 100);
  const remaining = goal.targetAmount - goal.currentAmount;

  const getDaysRemaining = () => {
    const today = new Date();
    const target = new Date(goal.targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = getDaysRemaining();

  const getPriorityColor = () => {
    switch (goal.priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.textSecondary;
    }
  };

  const getCategoryIcon = () => {
    switch (goal.category) {
      case 'emergency': return 'shield';
      case 'vacation': return 'airplane';
      case 'house': return 'home';
      case 'car': return 'car';
      case 'education': return 'school';
      case 'retirement': return 'time';
      default: return 'flag';
    }
  };



  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.categoryIcon, { backgroundColor: getPriorityColor() }]}>
          <Ionicons name={getCategoryIcon() as any} size={32} color={theme.colors.surface} />
        </View>
        <Text style={styles.goalTitle}>{goal.title}</Text>
        <Text style={styles.goalDescription}>{goal.description}</Text>
        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor() }]}>
          <Text style={styles.priorityText}>
            {goal.priority === 'high' && 'Yüksek Öncelik'}
            {goal.priority === 'medium' && 'Orta Öncelik'}
            {goal.priority === 'low' && 'Düşük Öncelik'}
          </Text>
        </View>
      </View>

      {/* Progress */}
      <View style={styles.progressContainer}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressLabel}>İlerleme</Text>
          <Text style={styles.progressPercentage}>
            {progressPercentage.toFixed(0)}%
          </Text>
        </View>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${progressPercentage}%`,
                backgroundColor: goal.isCompleted ? theme.colors.success : theme.colors.primary
              }
            ]}
          />
        </View>
        <View style={styles.progressAmounts}>
          <Text style={styles.currentAmount}>
            {formatCurrency(goal.currentAmount)}
          </Text>
          <Text style={styles.targetAmount}>
            {formatCurrency(goal.targetAmount)}
          </Text>
        </View>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statLabel}>Kalan Tutar</Text>
          <Text style={[
            styles.statValue,
            { color: remaining <= 0 ? theme.colors.success : theme.colors.text }
          ]}>
            {formatCurrency(remaining)}
          </Text>
        </View>

        <View style={styles.statCard}>
          <Text style={styles.statLabel}>Kalan Süre</Text>
          <Text style={[
            styles.statValue,
            { color: daysRemaining < 30 ? theme.colors.warning : theme.colors.text }
          ]}>
            {daysRemaining > 0 ? `${daysRemaining} gün` : 'Süre doldu'}
          </Text>
        </View>

        <View style={styles.statCard}>
          <Text style={styles.statLabel}>Günlük Hedef</Text>
          <Text style={styles.statValue}>
            {daysRemaining > 0
              ? formatCurrency(remaining / daysRemaining)
              : formatCurrency(0)
            }
          </Text>
        </View>
      </View>

      {/* Details */}
      <View style={styles.detailsContainer}>
        <Text style={styles.sectionTitle}>Detaylar</Text>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Kategori</Text>
          <Text style={styles.detailValue}>
            {goal.category === 'emergency' && 'Acil Durum'}
            {goal.category === 'vacation' && 'Tatil'}
            {goal.category === 'house' && 'Ev'}
            {goal.category === 'car' && 'Araç'}
            {goal.category === 'education' && 'Eğitim'}
            {goal.category === 'retirement' && 'Emeklilik'}
            {goal.category === 'other' && 'Diğer'}
          </Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Hedef Tarihi</Text>
          <Text style={styles.detailValue}>
            {new Date(goal.targetDate).toLocaleDateString('tr-TR')}
          </Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Durum</Text>
          <Text style={[
            styles.detailValue,
            { color: goal.isCompleted ? theme.colors.success : theme.colors.textSecondary }
          ]}>
            {goal.isCompleted ? 'Tamamlandı' : 'Devam Ediyor'}
          </Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Oluşturulma</Text>
          <Text style={styles.detailValue}>
            {new Date(goal.createdAt).toLocaleDateString('tr-TR')}
          </Text>
        </View>
      </View>

      {/* Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="add-circle-outline" size={20} color={theme.colors.success} />
          <Text style={styles.actionButtonText}>Para Ekle</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Düzenle</Text>
        </TouchableOpacity>

        {goal.isCompleted && (
          <TouchableOpacity style={[styles.actionButton, styles.completedButton]}>
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
            <Text style={[styles.actionButtonText, { color: theme.colors.success }]}>
              Tamamlandı
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
};

export default GoalDetailScreen;
