// Backup Service - Cloud backup ve restore işlemleri

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Transaction, User, Budget, FinancialGoal } from '../types';
import ApiService from './ApiService';

export interface BackupData {
  version: string;
  timestamp: string;
  user: User;
  transactions: Transaction[];
  budgets: Budget[];
  goals: FinancialGoal[];
  settings: any;
  metadata: {
    deviceInfo: string;
    appVersion: string;
    totalTransactions: number;
    totalBudgets: number;
    totalGoals: number;
  };
}

export interface BackupInfo {
  id: string;
  timestamp: string;
  size: number;
  deviceInfo: string;
  version: string;
}

class BackupService {
  private static instance: BackupService;
  private readonly BACKUP_KEY = 'local_backup';
  private readonly BACKUP_HISTORY_KEY = 'backup_history';

  static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  /**
   * Tüm verileri yedekler
   */
  async createBackup(): Promise<BackupData> {
    try {
      // Mevcut verileri topla
      const transactions = await this.getStoredTransactions();
      const budgets = await this.getStoredBudgets();
      const goals = await this.getStoredGoals();
      const settings = await this.getStoredSettings();
      const user = await this.getStoredUser();

      const backupData: BackupData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        user,
        transactions,
        budgets,
        goals,
        settings,
        metadata: {
          deviceInfo: 'Mobile Device', // Platform.OS + Device.modelName
          appVersion: '1.0.0',
          totalTransactions: transactions.length,
          totalBudgets: budgets.length,
          totalGoals: goals.length,
        }
      };

      // Local backup kaydet
      await this.saveLocalBackup(backupData);
      
      // Backup geçmişine ekle
      await this.addToBackupHistory({
        id: Date.now().toString(),
        timestamp: backupData.timestamp,
        size: JSON.stringify(backupData).length,
        deviceInfo: backupData.metadata.deviceInfo,
        version: backupData.version,
      });

      return backupData;
    } catch (error) {
      console.error('Backup creation error:', error);
      throw new Error('Yedekleme oluşturulurken hata oluştu');
    }
  }

  /**
   * Cloud'a yedek yükler
   */
  async uploadToCloud(backupData: BackupData): Promise<boolean> {
    try {
      // API üzerinden cloud'a yükle
      const response = await ApiService.request('/backup/upload', {
        method: 'POST',
        body: JSON.stringify(backupData),
      });

      if (response.success) {
        // Son cloud backup tarihini kaydet
        await AsyncStorage.setItem('last_cloud_backup', new Date().toISOString());
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Cloud upload error:', error);
      return false;
    }
  }

  /**
   * Cloud'dan yedek listesini alır
   */
  async getCloudBackups(): Promise<BackupInfo[]> {
    try {
      const response = await ApiService.request('/backup/list');
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return [];
    } catch (error) {
      console.error('Get cloud backups error:', error);
      return [];
    }
  }

  /**
   * Cloud'dan yedek indirir
   */
  async downloadFromCloud(backupId: string): Promise<BackupData | null> {
    try {
      const response = await ApiService.request(`/backup/download/${backupId}`);
      
      if (response.success && response.data) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Cloud download error:', error);
      return null;
    }
  }

  /**
   * Yedekten geri yükler
   */
  async restoreFromBackup(backupData: BackupData): Promise<boolean> {
    try {
      // Verileri geri yükle
      await this.restoreTransactions(backupData.transactions);
      await this.restoreBudgets(backupData.budgets);
      await this.restoreGoals(backupData.goals);
      await this.restoreSettings(backupData.settings);
      
      // Restore tarihini kaydet
      await AsyncStorage.setItem('last_restore', new Date().toISOString());
      
      return true;
    } catch (error) {
      console.error('Restore error:', error);
      throw new Error('Geri yükleme işlemi başarısız oldu');
    }
  }

  /**
   * Local backup kaydet
   */
  async saveLocalBackup(backupData: BackupData): Promise<void> {
    try {
      await AsyncStorage.setItem(this.BACKUP_KEY, JSON.stringify(backupData));
      await AsyncStorage.setItem('last_local_backup', backupData.timestamp);
    } catch (error) {
      console.error('Save local backup error:', error);
      throw error;
    }
  }

  /**
   * Local backup al
   */
  async getLocalBackup(): Promise<BackupData | null> {
    try {
      const backup = await AsyncStorage.getItem(this.BACKUP_KEY);
      return backup ? JSON.parse(backup) : null;
    } catch (error) {
      console.error('Get local backup error:', error);
      return null;
    }
  }

  /**
   * Backup geçmişine ekle
   */
  private async addToBackupHistory(backupInfo: BackupInfo): Promise<void> {
    try {
      const history = await this.getBackupHistory();
      history.unshift(backupInfo); // En yenisi başta
      
      // Son 10 backup'ı tut
      const limitedHistory = history.slice(0, 10);
      
      await AsyncStorage.setItem(this.BACKUP_HISTORY_KEY, JSON.stringify(limitedHistory));
    } catch (error) {
      console.error('Add to backup history error:', error);
    }
  }

  /**
   * Backup geçmişini al
   */
  async getBackupHistory(): Promise<BackupInfo[]> {
    try {
      const history = await AsyncStorage.getItem(this.BACKUP_HISTORY_KEY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Get backup history error:', error);
      return [];
    }
  }

  /**
   * Son backup tarihlerini al
   */
  async getLastBackupDates(): Promise<{
    local: string | null;
    cloud: string | null;
    restore: string | null;
  }> {
    try {
      const [local, cloud, restore] = await Promise.all([
        AsyncStorage.getItem('last_local_backup'),
        AsyncStorage.getItem('last_cloud_backup'),
        AsyncStorage.getItem('last_restore'),
      ]);

      return { local, cloud, restore };
    } catch (error) {
      console.error('Get last backup dates error:', error);
      return { local: null, cloud: null, restore: null };
    }
  }

  /**
   * Otomatik backup ayarla
   */
  async scheduleAutoBackup(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem('auto_backup_enabled', enabled.toString());
      
      if (enabled) {
        // Her gün otomatik backup (gerçek uygulamada background task kullanılır)
        console.log('Auto backup enabled');
      }
    } catch (error) {
      console.error('Schedule auto backup error:', error);
    }
  }

  // Private helper methods

  private async getStoredTransactions(): Promise<Transaction[]> {
    try {
      const stored = await AsyncStorage.getItem('transactions');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async getStoredBudgets(): Promise<Budget[]> {
    try {
      const stored = await AsyncStorage.getItem('budgets');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async getStoredGoals(): Promise<FinancialGoal[]> {
    try {
      const stored = await AsyncStorage.getItem('goals');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async getStoredSettings(): Promise<any> {
    try {
      const stored = await AsyncStorage.getItem('app_settings');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      return {};
    }
  }

  private async getStoredUser(): Promise<User> {
    try {
      const stored = await AsyncStorage.getItem('user_data');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      return null;
    }
  }

  private async restoreTransactions(transactions: Transaction[]): Promise<void> {
    await AsyncStorage.setItem('transactions', JSON.stringify(transactions));
  }

  private async restoreBudgets(budgets: Budget[]): Promise<void> {
    await AsyncStorage.setItem('budgets', JSON.stringify(budgets));
  }

  private async restoreGoals(goals: FinancialGoal[]): Promise<void> {
    await AsyncStorage.setItem('goals', JSON.stringify(goals));
  }

  private async restoreSettings(settings: any): Promise<void> {
    await AsyncStorage.setItem('app_settings', JSON.stringify(settings));
  }
}

export default BackupService;
