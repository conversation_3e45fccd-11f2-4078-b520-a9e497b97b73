// Error State Component - Hata durumu bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface ErrorStateProps {
  title?: string;
  message?: string;
  buttonText?: string;
  onRetry?: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  iconSize?: number;
  iconColor?: string;
  showButton?: boolean;
  style?: any;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Bir hata oluştu',
  message = 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.',
  buttonText = 'Tekrar Dene',
  onRetry,
  icon = 'alert-circle-outline',
  iconSize = 64,
  iconColor,
  showButton = true,
  style,
}) => {
  const { theme } = useTheme();
  const defaultIconColor = iconColor || theme.colors.error;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    content: {
      alignItems: 'center',
      maxWidth: 300,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
      textAlign: 'center',
    },
    message: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: 24,
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    retryButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.surface,
      marginLeft: 8,
    },
  });

  return (
    <View style={[styles.container, style]}>
      <View style={styles.content}>
        <Ionicons name={icon} size={iconSize} color={defaultIconColor} />
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>

        {showButton && onRetry && (
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Ionicons name="refresh" size={20} color={theme.colors.surface} />
            <Text style={styles.retryButtonText}>{buttonText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// Network Error Component - Ağ hatası bileşeni
export const NetworkError: React.FC<{
  onRetry?: () => void;
  style?: any;
}> = ({ onRetry, style }) => {
  const { theme } = useTheme();
  return (
    <ErrorState
      title="Bağlantı Hatası"
      message="İnternet bağlantınızı kontrol edin ve tekrar deneyin."
      icon="wifi-outline"
      iconColor={theme.colors.warning}
      onRetry={onRetry}
      style={style}
    />
  );
};

// Not Found Error Component - Bulunamadı hatası bileşeni
export const NotFoundError: React.FC<{
  title?: string;
  message?: string;
  onGoBack?: () => void;
  style?: any;
}> = ({
  title = 'Sayfa Bulunamadı',
  message = 'Aradığınız sayfa bulunamadı veya kaldırılmış olabilir.',
  onGoBack,
  style,
}) => {
  const { theme } = useTheme();
  return (
    <ErrorState
      title={title}
      message={message}
      buttonText="Geri Dön"
      icon="search-outline"
      iconColor={theme.colors.textSecondary}
      onRetry={onGoBack}
      style={style}
    />
  );
};

// Empty State Component - Boş durum bileşeni
export const EmptyState: React.FC<{
  title?: string;
  message?: string;
  buttonText?: string;
  onAction?: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  showButton?: boolean;
  style?: any;
}> = ({
  title = 'Henüz veri yok',
  message = 'Burası şu anda boş görünüyor.',
  buttonText = 'Ekle',
  onAction,
  icon = 'document-outline',
  iconColor,
  showButton = false,
  style,
}) => {
  const { theme } = useTheme();
  const defaultIconColor = iconColor || theme.colors.textSecondary;

  return (
    <ErrorState
      title={title}
      message={message}
      buttonText={buttonText}
      onRetry={onAction}
      icon={icon}
      iconColor={defaultIconColor}
      showButton={showButton}
      style={style}
    />
  );
};

// Permission Error Component - İzin hatası bileşeni
export const PermissionError: React.FC<{
  title?: string;
  message?: string;
  onOpenSettings?: () => void;
  style?: any;
}> = ({
  title = 'İzin Gerekli',
  message = 'Bu özelliği kullanmak için gerekli izinleri vermeniz gerekiyor.',
  onOpenSettings,
  style,
}) => {
  const { theme } = useTheme();
  return (
    <ErrorState
      title={title}
      message={message}
      buttonText="Ayarları Aç"
      icon="lock-closed-outline"
      iconColor={theme.colors.warning}
      onRetry={onOpenSettings}
      style={style}
    />
  );
};

// Maintenance Error Component - Bakım hatası bileşeni
export const MaintenanceError: React.FC<{
  style?: any;
}> = ({ style }) => {
  const { theme } = useTheme();
  return (
    <ErrorState
      title="Bakım Modu"
      message="Uygulama şu anda bakım modunda. Lütfen daha sonra tekrar deneyin."
      icon="construct-outline"
      iconColor={theme.colors.warning}
      showButton={false}
      style={style}
    />
  );
};

// Inline Error Component - Satır içi hata bileşeni
export const InlineError: React.FC<{
  message: string;
  onDismiss?: () => void;
  style?: any;
}> = ({ message, onDismiss, style }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    inlineError: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.error + '10',
      borderWidth: 1,
      borderColor: theme.colors.error + '30',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      marginVertical: 4,
    },
    inlineErrorContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    inlineErrorText: {
      fontSize: 14,
      color: theme.colors.error,
      marginLeft: 8,
      flex: 1,
    },
    dismissButton: {
      padding: 4,
      marginLeft: 8,
    },
  });

  return (
    <View style={[styles.inlineError, style]}>
      <View style={styles.inlineErrorContent}>
        <Ionicons name="alert-circle" size={16} color={theme.colors.error} />
        <Text style={styles.inlineErrorText}>{message}</Text>
      </View>
      {onDismiss && (
        <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
          <Ionicons name="close" size={16} color={theme.colors.error} />
        </TouchableOpacity>
      )}
    </View>
  );
};



export default ErrorState;
