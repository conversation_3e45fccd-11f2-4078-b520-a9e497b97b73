import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, ActivityIndicator } from 'react-native';
import { store } from './src/store';
import { ThemeProvider, useTheme } from './src/contexts/ThemeContext';
import { SimpleAuthProvider } from './src/contexts/SimpleAuthContext';
import { CurrencyProvider } from './src/contexts/CurrencyContext';
import AppNavigator from './src/navigation/AppNavigator';
import DatabaseManager from './src/database/DatabaseManager';
import RecurringTransactionService from './src/services/RecurringTransactionService';
import NotificationService from './src/services/NotificationService';
import CategorySyncService from './src/services/CategorySyncService';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';

// Background task tanımı
const BACKGROUND_FETCH_TASK = 'background-fetch-recurring-transactions';

// Background task handler
TaskManager.defineTask(BACKGROUND_FETCH_TASK, async () => {
  try {
    console.log('🔄 Running background task for recurring transactions...');

    // Tekrarlayan işlemleri kontrol et ve çalıştır
    await RecurringTransactionService.processDailyRecurringTransactions();

    console.log('✅ Background task completed successfully');
    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error('❌ Background task failed:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

// Database Initialization Component
const DatabaseInitializer = ({ children }: { children: React.ReactNode }) => {
  const [isDbReady, setIsDbReady] = useState(false);
  const [dbError, setDbError] = useState<string | null>(null);
  const { theme } = useTheme();

  useEffect(() => {
    // Database'i lazy loading ile başlat
    const timer = setTimeout(() => {
      initializeDatabase();
    }, 100); // 100ms gecikme ile UI'ın render olmasını bekle

    return () => clearTimeout(timer);
  }, []);

  const setupBackgroundServices = async () => {
    try {
      console.log('🔔 Setting up background services...');

      // Notification permissions
      const hasPermission = await NotificationService.requestPermissions();
      if (hasPermission) {
        console.log('✅ Notification permissions granted');

        // Push token al
        const token = await NotificationService.getExpoPushToken();
        if (token) {
          console.log('✅ Push token obtained');
        }
      } else {
        console.warn('⚠️ Notification permissions denied');
      }

      // Background fetch setup
      const backgroundStatus = await BackgroundFetch.getStatusAsync();
      if (backgroundStatus === BackgroundFetch.BackgroundFetchStatus.Available) {
        await BackgroundFetch.registerTaskAsync(BACKGROUND_FETCH_TASK, {
          minimumInterval: 24 * 60 * 60 * 1000, // 24 saat (milisaniye)
          stopOnTerminate: false,
          startOnBoot: true,
        });
        console.log('✅ Background fetch registered');
      } else {
        console.warn('⚠️ Background fetch not available:', backgroundStatus);
      }

    } catch (error) {
      console.error('❌ Background services setup failed:', error);
    }
  };

  const initializeDatabase = async () => {
    try {
      console.log('🗄️ Initializing database...');
      await DatabaseManager.initialize();

      // Database health check
      const healthCheck = await DatabaseManager.healthCheck();
      if (!healthCheck.isHealthy) {
        console.warn('⚠️ Database health issues:', healthCheck.issues);
      }

      // Category synchronization - DEVRE DIŞI (BUDGET_CATEGORIES SİLİYOR!)
      try {
        console.log('🔄 Category synchronization DISABLED - prevents budget_categories deletion');
        // await CategorySyncService.syncCategories(); // DEVRE DIŞI
        console.log('✅ Category synchronization skipped');
      } catch (syncError) {
        console.error('❌ Category sync failed:', syncError);
        // Category sync hatası app'i durdurmasın, sadece log'la
      }

      // Background services'i geçici olarak kapat (performance için)
      // await setupBackgroundServices();

      setIsDbReady(true);
      console.log('✅ Database ready!');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      setDbError(error instanceof Error ? error.message : 'Database initialization failed');
    }
  };

  if (dbError) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
        padding: 20,
      }}>
        <Text style={{
          color: theme.colors.error,
          fontSize: 18,
          fontWeight: 'bold',
          marginBottom: 10,
          textAlign: 'center',
        }}>
          Database Error
        </Text>
        <Text style={{
          color: theme.colors.text,
          fontSize: 14,
          textAlign: 'center',
          marginBottom: 20,
        }}>
          {dbError}
        </Text>
        <Text style={{
          color: theme.colors.textSecondary,
          fontSize: 12,
          textAlign: 'center',
        }}>
          Please restart the app
        </Text>
      </View>
    );
  }

  if (!isDbReady) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
      }}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={{
          color: theme.colors.text,
          fontSize: 16,
          marginTop: 20,
          textAlign: 'center',
        }}>
          Kişisel Finans Uygulaması
        </Text>
        <Text style={{
          color: theme.colors.textSecondary,
          fontSize: 12,
          marginTop: 10,
          textAlign: 'center',
        }}>
          Yükleniyor... (Performance optimize ediliyor)
        </Text>
      </View>
    );
  }

  return <>{children}</>;
};

// App Content Component with Theme
const AppContent = () => {
  const { theme } = useTheme();

  return (
    <DatabaseInitializer>
      <StatusBar style={theme.isDark ? "light" : "dark"} />
      <AppNavigator />
    </DatabaseInitializer>
  );
};

export default function App() {
  return (
    <Provider store={store}>
      <SimpleAuthProvider>
        <CurrencyProvider>
          <ThemeProvider>
            <SafeAreaProvider>
              <AppContent />
            </SafeAreaProvider>
          </ThemeProvider>
        </CurrencyProvider>
      </SimpleAuthProvider>
    </Provider>
  );
}
