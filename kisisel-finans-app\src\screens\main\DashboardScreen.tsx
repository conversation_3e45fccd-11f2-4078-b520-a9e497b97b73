// Dashboard Screen - Ana sayfa

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useNavigation, DrawerActions } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/MainNavigator';
import { useTheme } from '../../contexts/ThemeContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import { Ionicons } from '@expo/vector-icons';

import { Transaction, Budget, FinancialGoal } from '../../types';

const { width } = Dimensions.get('window');

type DashboardNavigationProp = StackNavigationProp<MainStackParamList>;

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation<DashboardNavigationProp>();
  const { theme } = useTheme();
  const { formatAmount, baseCurrency, updateExchangeRates } = useCurrency();
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - gerçek uygulamada context'lerden gelecek
  const user = { name: 'Kullanıcı' };

  const [transactions] = useState<Transaction[]>([
    {
      id: '1',
      description: 'Market Alışverişi',
      amount: 250,
      type: 'expense',
      date: new Date().toISOString(),
      category: 'food',
      currency: 'TRY',
      isRecurring: false,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      description: 'Maaş',
      amount: 8000,
      type: 'income',
      date: new Date().toISOString(),
      category: 'salary',
      currency: 'TRY',
      isRecurring: true,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]);

  const [budgets] = useState<Budget[]>([
    {
      id: '1',
      name: 'Market',
      amount: 2000,
      spent: 1200,
      period: 'monthly',
      isActive: true,
      categoryId: 'food',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      userId: 'user1',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
  ]);

  const [goals] = useState<FinancialGoal[]>([
    {
      id: '1',
      userId: 'user1',
      title: 'Tatil',
      description: 'Yaz tatili için birikim',
      targetAmount: 10000,
      currentAmount: 3000,
      targetDate: '2024-12-31',
      category: 'vacation',
      priority: 'medium',
      isCompleted: false,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
  ]);

  const onRefresh = async () => {
    setRefreshing(true);
    // Mock refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Finansal özet hesaplamaları
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  const monthlyTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear;
  });

  const monthlyIncome = monthlyTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const monthlyExpense = monthlyTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const netAmount = monthlyIncome - monthlyExpense;

  // Aktif bütçeler
  const activeBudgets = budgets.filter(b => b.isActive);
  const totalBudgetAmount = activeBudgets.reduce((sum, b) => sum + b.amount, 0);
  const totalBudgetSpent = activeBudgets.reduce((sum, b) => sum + b.spent, 0);

  // Aktif hedefler
  const activeGoals = goals.filter(g => !g.isCompleted);
  const totalGoalTarget = activeGoals.reduce((sum, g) => sum + g.targetAmount, 0);
  const totalGoalCurrent = activeGoals.reduce((sum, g) => sum + g.currentAmount, 0);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Günaydın';
    if (hour < 18) return 'İyi günler';
    return 'İyi akşamlar';
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.background }]}>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        >
          <Ionicons name="menu" size={28} color={theme.colors.text} />
        </TouchableOpacity>

        <View style={styles.greetingContainer}>
          <Text style={[styles.greeting, { color: theme.colors.textSecondary }]}>
            {getGreeting()}
          </Text>
          <Text style={[styles.userName, { color: theme.colors.text }]}>
            {user?.name || 'Kullanıcı'}
          </Text>
        </View>

        <View style={[styles.profileImage, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="person" size={24} color={theme.colors.primary} />
        </View>
      </View>

      {/* Finansal Özet Kartları */}
      <View style={styles.summaryContainer}>
        {/* Aylık Gelir */}
        <View style={[styles.summaryCard, styles.incomeCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.summaryHeader}>
            <Ionicons name="trending-up" size={24} color={theme.colors.income} />
            <Text style={[styles.summaryTitle, { color: theme.colors.textSecondary }]}>Bu Ay Gelir</Text>
          </View>
          <Text style={[styles.summaryAmount, { color: theme.colors.income }]}>
            {formatAmount(monthlyIncome)}
          </Text>
        </View>

        {/* Aylık Gider */}
        <View style={[styles.summaryCard, styles.expenseCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.summaryHeader}>
            <Ionicons name="trending-down" size={24} color={theme.colors.expense} />
            <Text style={[styles.summaryTitle, { color: theme.colors.textSecondary }]}>Bu Ay Gider</Text>
          </View>
          <Text style={[styles.summaryAmount, { color: theme.colors.expense }]}>
            {formatAmount(monthlyExpense)}
          </Text>
        </View>

        {/* Net Tutar */}
        <View style={[styles.summaryCard, styles.netCard, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.summaryHeader}>
            <Ionicons
              name={netAmount >= 0 ? "checkmark-circle" : "alert-circle"}
              size={24}
              color={netAmount >= 0 ? theme.colors.success : theme.colors.warning}
            />
            <Text style={[styles.summaryTitle, { color: theme.colors.textSecondary }]}>Net Tutar</Text>
          </View>
          <Text style={[
            styles.summaryAmount,
            { color: netAmount >= 0 ? theme.colors.success : theme.colors.warning }
          ]}>
            {formatAmount(netAmount)}
          </Text>
        </View>
      </View>

      {/* Hızlı İşlemler */}
      <View style={styles.quickActionsContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Hızlı İşlemler</Text>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('AddTransaction', { type: 'income' })}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: theme.colors.income }]}>
              <Ionicons name="add" size={24} color={theme.colors.surface} />
            </View>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>Gelir Ekle</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('AddTransaction', { type: 'expense' })}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: theme.colors.expense }]}>
              <Ionicons name="remove" size={24} color={theme.colors.surface} />
            </View>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>Gider Ekle</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('BankAccounts' as never)}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: theme.colors.primary }]}>
              <Ionicons name="card" size={24} color={theme.colors.surface} />
            </View>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>Banka Hesapları</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Bütçe Durumu */}
      {activeBudgets.length > 0 && (
        <View style={styles.budgetContainer}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Bütçe Durumu</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Budget' as never)}>
              <Text style={[styles.seeAllText, { color: theme.colors.primary }]}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>
          <View style={[styles.budgetSummary, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.budgetProgress}>
              <View style={[styles.budgetProgressBar, { backgroundColor: theme.colors.border }]}>
                <View
                  style={[
                    styles.budgetProgressFill,
                    {
                      width: `${Math.min((totalBudgetSpent / totalBudgetAmount) * 100, 100)}%`,
                      backgroundColor: totalBudgetSpent > totalBudgetAmount ? theme.colors.error : theme.colors.primary
                    }
                  ]}
                />
              </View>
              <Text style={[styles.budgetProgressText, { color: theme.colors.text }]}>
                {formatAmount(totalBudgetSpent)} / {formatAmount(totalBudgetAmount)}
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Hedefler */}
      {activeGoals.length > 0 && (
        <View style={styles.goalsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Finansal Hedefler</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Goals' as never)}>
              <Text style={[styles.seeAllText, { color: theme.colors.primary }]}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>
          <View style={[styles.goalsSummary, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.goalsText, { color: theme.colors.textSecondary }]}>
              {activeGoals.length} aktif hedef
            </Text>
            <Text style={[styles.goalsProgress, { color: theme.colors.text }]}>
              {formatAmount(totalGoalCurrent)} / {formatAmount(totalGoalTarget)}
            </Text>
          </View>
        </View>
      )}

      {/* Son İşlemler */}
      <View style={styles.recentTransactionsContainer}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Son İşlemler</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Transactions' as never)}>
            <Text style={[styles.seeAllText, { color: theme.colors.primary }]}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>
        {transactions.slice(0, 3).map((transaction) => (
          <TouchableOpacity
            key={transaction.id}
            style={[styles.transactionItem, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('TransactionDetail', { transactionId: transaction.id })}
          >
            <View style={styles.transactionLeft}>
              <View style={[
                styles.transactionIcon,
                { backgroundColor: transaction.type === 'income' ? theme.colors.income : theme.colors.expense }
              ]}>
                <Ionicons
                  name={transaction.type === 'income' ? 'trending-up' : 'trending-down'}
                  size={16}
                  color={theme.colors.surface}
                />
              </View>
              <View style={styles.transactionDetails}>
                <Text style={[styles.transactionDescription, { color: theme.colors.text }]}>{transaction.description}</Text>
                <Text style={[styles.transactionDate, { color: theme.colors.textSecondary }]}>
                  {new Date(transaction.date).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            </View>
            <Text style={[
              styles.transactionAmount,
              { color: transaction.type === 'income' ? theme.colors.income : theme.colors.expense }
            ]}>
              {transaction.type === 'income' ? '+' : '-'}{formatAmount(transaction.amount)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('AddTransaction')}
        activeOpacity={0.8}
      >
        <Ionicons name="add" size={28} color={theme.colors.surface} />
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  menuButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  greetingContainer: {
    flex: 1,
    marginLeft: 16,
  },
  greeting: {
    fontSize: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 4,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  incomeCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#10B981',
  },
  expenseCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  netCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 14,
    marginLeft: 8,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    textAlign: 'center',
  },
  budgetContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
  },
  budgetSummary: {
    borderRadius: 12,
    padding: 16,
  },
  budgetProgress: {
    alignItems: 'center',
  },
  budgetProgressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  budgetProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  budgetProgressText: {
    fontSize: 14,
    fontWeight: '500',
  },
  goalsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  goalsSummary: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalsText: {
    fontSize: 14,
  },
  goalsProgress: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recentTransactionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
  },
  transactionDate: {
    fontSize: 12,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default DashboardScreen;
