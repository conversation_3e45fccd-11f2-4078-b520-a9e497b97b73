// Budget Debug Utility - Bütçe hesaplama sorunlarını debug etmek için

import databaseManager from '../database/DatabaseManager';

class BudgetDebugUtil {
  /**
   * Bütçe hesaplama sorunlarını debug et
   */
  static async debugBudgetCalculation(budgetId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      
      console.log('🔍 ===== BUDGET DEBUG START =====');
      console.log(`🎯 Budget ID: ${budgetId}`);

      // 1. Ana bütçe bilgilerini kontrol et
      const budget = await db.getFirstAsync(
        'SELECT * FROM budgets WHERE id = ?',
        [budgetId]
      );
      console.log('📊 Budget Info:', budget);

      // 2. Bütçe kategorilerini kontrol et
      const budgetCategories = await db.getAllAsync(
        'SELECT * FROM budget_categories WHERE budget_id = ? AND is_active = 1',
        [budgetId]
      );
      console.log(`📋 Budget Categories (${budgetCategories.length}):`, budgetCategories);

      // 3. Bu bütçeye bağlı transaction'ları kontrol et
      const transactions = await db.getAllAsync(
        'SELECT * FROM transactions WHERE budget_id = ? AND is_deleted = 0',
        [budgetId]
      );
      console.log(`💳 Transactions linked to budget (${transactions.length}):`, transactions);

      // 4. Her kategori için harcama hesapla
      for (const category of budgetCategories) {
        console.log(`\n🔍 Category: ${category.category_id}`);
        console.log(`📊 Planned: ${category.planned_amount}, Spent (DB): ${category.spent_amount}`);

        // Manuel hesaplama - category_id ile
        const spentByCategoryId = await db.getFirstAsync(
          `SELECT COALESCE(SUM(amount), 0) as total
           FROM transactions
           WHERE budget_id = ? AND category_id = ? AND type = 'expense' AND is_deleted = 0`,
          [budgetId, category.category_id]
        );

        // Manuel hesaplama - category string ile
        const categoryName = await db.getFirstAsync(
          'SELECT name FROM categories WHERE id = ?',
          [category.category_id]
        );

        const spentByCategoryName = await db.getFirstAsync(
          `SELECT COALESCE(SUM(amount), 0) as total
           FROM transactions
           WHERE budget_id = ? AND category = ? AND type = 'expense' AND is_deleted = 0`,
          [budgetId, categoryName?.name]
        );

        console.log(`💰 Manual calculation (category_id): ${spentByCategoryId?.total || 0}`);
        console.log(`💰 Manual calculation (category name): ${spentByCategoryName?.total || 0}`);
        console.log(`📝 Category name: ${categoryName?.name}`);

        // Bu kategoriye ait transaction'ları listele
        const categoryTransactions = await db.getAllAsync(
          `SELECT id, amount, category, category_id, description, date
           FROM transactions
           WHERE budget_id = ? AND (category_id = ? OR category = ?) AND type = 'expense' AND is_deleted = 0`,
          [budgetId, category.category_id, categoryName?.name]
        );
        console.log(`📋 Category transactions (${categoryTransactions.length}):`, categoryTransactions);
      }

      // 5. Trigger'ları kontrol et
      const triggers = await db.getAllAsync(
        "SELECT name, sql FROM sqlite_master WHERE type = 'trigger' AND name LIKE '%budget%'"
      );
      console.log(`⚡ Budget-related triggers (${triggers.length}):`, triggers.map(t => t.name));

      console.log('🔍 ===== BUDGET DEBUG END =====\n');
    } catch (error) {
      console.error('❌ Budget debug error:', error);
    }
  }

  /**
   * Bütçe kategori harcamalarını manuel olarak yeniden hesapla
   */
  static async recalculateBudgetSpending(budgetId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      
      console.log('🔄 Recalculating budget spending for:', budgetId);

      // Bütçe kategorilerini al
      const budgetCategories = await db.getAllAsync(
        'SELECT * FROM budget_categories WHERE budget_id = ? AND is_active = 1',
        [budgetId]
      );

      for (const category of budgetCategories) {
        // Bu kategori için toplam harcamayı hesapla
        const spentAmount = await this.calculateCategorySpending(budgetId, category.category_id);
        
        // spent_amount'u güncelle
        await db.runAsync(
          'UPDATE budget_categories SET spent_amount = ?, updated_at = ? WHERE id = ?',
          [spentAmount, new Date().toISOString(), category.id]
        );

        console.log(`✅ Updated category ${category.category_id}: ${category.spent_amount} → ${spentAmount}`);
      }

      console.log('🎉 Budget spending recalculation completed!');
    } catch (error) {
      console.error('❌ Error recalculating budget spending:', error);
      throw error;
    }
  }

  /**
   * Belirli bir kategori için harcama hesapla
   */
  private static async calculateCategorySpending(budgetId: string, categoryId: string): Promise<number> {
    try {
      const db = databaseManager.getDatabase();

      // Kategori adını al
      const category = await db.getFirstAsync(
        'SELECT name FROM categories WHERE id = ?',
        [categoryId]
      );

      // Hem category_id hem de category name ile eşleşen transaction'ları topla
      const result = await db.getFirstAsync(
        `SELECT COALESCE(SUM(amount), 0) as total
         FROM transactions
         WHERE budget_id = ?
           AND (category_id = ? OR category = ?)
           AND type = 'expense'
           AND is_deleted = 0`,
        [budgetId, categoryId, category?.name]
      );

      return result?.total || 0;
    } catch (error) {
      console.error('❌ Error calculating category spending:', error);
      return 0;
    }
  }

  /**
   * Tüm aktif bütçelerin harcamalarını yeniden hesapla
   */
  static async recalculateAllBudgets(userId: string): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      
      console.log('🔄 Recalculating all budgets for user:', userId);

      // Kullanıcının aktif bütçelerini al
      const budgets = await db.getAllAsync(
        'SELECT id FROM budgets WHERE user_id = ? AND is_active = 1',
        [userId]
      );

      console.log(`📊 Found ${budgets.length} active budgets`);

      for (const budget of budgets) {
        await this.recalculateBudgetSpending(budget.id);
      }

      console.log('🎉 All budgets recalculated successfully!');
    } catch (error) {
      console.error('❌ Error recalculating all budgets:', error);
      throw error;
    }
  }

  /**
   * Trigger'ları temizle ve yeniden oluştur
   */
  static async recreateTriggers(): Promise<void> {
    try {
      const db = databaseManager.getDatabase();
      
      console.log('🔄 Recreating budget triggers...');

      // Eski trigger'ları sil
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_category_spent_insert;');
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_category_spent_update;');
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_spent_insert;');
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_spent_update;');

      // Basit ve temiz trigger'ları oluştur
      await db.execAsync(`
        CREATE TRIGGER update_budget_category_spent_insert
        AFTER INSERT ON transactions
        WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL
        BEGIN
          UPDATE budget_categories
          SET spent_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = NEW.budget_id
              AND category_id = budget_categories.category_id
              AND type = 'expense'
              AND is_deleted = 0
          )
          WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id;
        END;
      `);

      await db.execAsync(`
        CREATE TRIGGER update_budget_category_spent_update
        AFTER UPDATE ON transactions
        WHEN (NEW.type = 'expense' OR OLD.type = 'expense')
             AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount
                  OR NEW.category_id != OLD.category_id OR NEW.budget_id != OLD.budget_id)
        BEGIN
          -- Update old budget category if changed
          UPDATE budget_categories
          SET spent_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = OLD.budget_id
              AND category_id = budget_categories.category_id
              AND type = 'expense'
              AND is_deleted = 0
          )
          WHERE budget_id = OLD.budget_id AND category_id = OLD.category_id
                AND OLD.budget_id IS NOT NULL AND OLD.category_id IS NOT NULL;

          -- Update new budget category
          UPDATE budget_categories
          SET spent_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = NEW.budget_id
              AND category_id = budget_categories.category_id
              AND type = 'expense'
              AND is_deleted = 0
          )
          WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id
                AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL;
        END;
      `);

      console.log('✅ Budget triggers recreated successfully!');
    } catch (error) {
      console.error('❌ Error recreating triggers:', error);
      throw error;
    }
  }
}

export default BudgetDebugUtil;
