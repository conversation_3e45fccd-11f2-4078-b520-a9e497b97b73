// External API Service - Dış API entegrasyonları

import AsyncStorage from '@react-native-async-storage/async-storage';

// API Keys - Production'da environment variables'dan gel<PERSON><PERSON>
const API_KEYS = {
  ALPHA_VANTAGE: __DEV__ ? 'demo' : process.env.ALPHA_VANTAGE_API_KEY,
  NEWS_API: __DEV__ ? 'demo' : process.env.NEWS_API_KEY,
};

// External API URLs
const EXTERNAL_APIS = {
  YAHOO_FINANCE: 'https://query1.finance.yahoo.com/v8/finance/chart',
  ALPHA_VANTAGE: 'https://www.alphavantage.co/query',
  EXCHANGE_RATE: 'https://api.exchangerate-api.com/v4/latest',
  NEWS_API: 'https://newsapi.org/v2',
  CRYPTO_API: 'https://api.coingecko.com/api/v3',
};

// Types
export interface StockData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high52Week: number;
  low52Week: number;
  marketCap?: number;
  lastUpdated: string;
}

export interface ExchangeRateData {
  base: string;
  rates: { [key: string]: number };
  lastUpdated: string;
}

export interface CryptoData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap: number;
  volume_24h: number;
  last_updated: string;
}

export interface NewsData {
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
}

class ExternalApiService {
  private static instance: ExternalApiService;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  
  static getInstance(): ExternalApiService {
    if (!ExternalApiService.instance) {
      ExternalApiService.instance = new ExternalApiService();
    }
    return ExternalApiService.instance;
  }

  // Generic request method with caching
  private async request<T>(
    url: string,
    cacheKey: string,
    ttl: number = 300000 // 5 minutes default
  ): Promise<T> {
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        ttl,
      });
      
      return data;
    } catch (error) {
      console.error('External API Request Error:', error);
      
      // Return cached data if available, even if expired
      if (cached) {
        console.log('Returning expired cached data due to API error');
        return cached.data;
      }
      
      throw error;
    }
  }

  /**
   * Yahoo Finance - Hisse senedi verileri
   */
  async getStockData(symbol: string): Promise<StockData | null> {
    try {
      const url = `${EXTERNAL_APIS.YAHOO_FINANCE}/${symbol}`;
      const cacheKey = `stock_${symbol}`;
      
      const response = await this.request<any>(url, cacheKey, 60000); // 1 minute cache
      
      if (!response.chart?.result?.[0]) {
        return null;
      }

      const result = response.chart.result[0];
      const meta = result.meta;
      const quote = result.indicators?.quote?.[0];
      
      if (!meta || !quote) {
        return null;
      }

      return {
        symbol: meta.symbol,
        price: meta.regularMarketPrice || 0,
        change: meta.regularMarketPrice - meta.previousClose || 0,
        changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose * 100) || 0,
        volume: meta.regularMarketVolume || 0,
        high52Week: meta.fiftyTwoWeekHigh || 0,
        low52Week: meta.fiftyTwoWeekLow || 0,
        marketCap: meta.marketCap,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching stock data:', error);
      return null;
    }
  }

  /**
   * Alpha Vantage - Detaylı finansal veriler
   */
  async getDetailedStockData(symbol: string): Promise<any> {
    try {
      const url = `${EXTERNAL_APIS.ALPHA_VANTAGE}?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${API_KEYS.ALPHA_VANTAGE}`;
      const cacheKey = `alpha_${symbol}`;
      
      return await this.request(url, cacheKey, 300000); // 5 minutes cache
    } catch (error) {
      console.error('Error fetching Alpha Vantage data:', error);
      return null;
    }
  }

  /**
   * Exchange Rate API - Döviz kurları
   */
  async getExchangeRates(baseCurrency: string = 'USD'): Promise<ExchangeRateData | null> {
    try {
      const url = `${EXTERNAL_APIS.EXCHANGE_RATE}/${baseCurrency}`;
      const cacheKey = `exchange_${baseCurrency}`;
      
      const response = await this.request<any>(url, cacheKey, 3600000); // 1 hour cache
      
      return {
        base: response.base,
        rates: response.rates,
        lastUpdated: response.date,
      };
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
      return null;
    }
  }

  /**
   * CoinGecko - Kripto para verileri
   */
  async getCryptoData(ids: string[] = ['bitcoin', 'ethereum']): Promise<CryptoData[]> {
    try {
      const idsString = ids.join(',');
      const url = `${EXTERNAL_APIS.CRYPTO_API}/simple/price?ids=${idsString}&vs_currencies=usd&include_24hr_change=true&include_market_cap=true&include_24hr_vol=true&include_last_updated_at=true`;
      const cacheKey = `crypto_${idsString}`;
      
      const response = await this.request<any>(url, cacheKey, 120000); // 2 minutes cache
      
      return Object.entries(response).map(([id, data]: [string, any]) => ({
        id,
        symbol: id.toUpperCase(),
        name: id.charAt(0).toUpperCase() + id.slice(1),
        current_price: data.usd,
        price_change_24h: data.usd_24h_change || 0,
        price_change_percentage_24h: data.usd_24h_change || 0,
        market_cap: data.usd_market_cap || 0,
        volume_24h: data.usd_24h_vol || 0,
        last_updated: new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Error fetching crypto data:', error);
      return [];
    }
  }

  /**
   * News API - Finansal haberler
   */
  async getFinancialNews(query: string = 'finance', pageSize: number = 10): Promise<NewsData[]> {
    try {
      const url = `${EXTERNAL_APIS.NEWS_API}/everything?q=${encodeURIComponent(query)}&sortBy=publishedAt&pageSize=${pageSize}&apiKey=${API_KEYS.NEWS_API}`;
      const cacheKey = `news_${query}_${pageSize}`;
      
      const response = await this.request<any>(url, cacheKey, 1800000); // 30 minutes cache
      
      if (!response.articles) {
        return [];
      }

      return response.articles.map((article: any) => ({
        title: article.title,
        description: article.description,
        url: article.url,
        source: article.source?.name || 'Unknown',
        publishedAt: article.publishedAt,
        sentiment: this.analyzeSentiment(article.title + ' ' + article.description),
      }));
    } catch (error) {
      console.error('Error fetching news:', error);
      return [];
    }
  }

  /**
   * Basit sentiment analizi
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['up', 'rise', 'gain', 'profit', 'growth', 'increase', 'bull', 'strong'];
    const negativeWords = ['down', 'fall', 'loss', 'decline', 'decrease', 'bear', 'weak', 'crash'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Cache temizleme
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Belirli bir cache key'i temizleme
   */
  clearCacheKey(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Cache durumu
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export default ExternalApiService;
