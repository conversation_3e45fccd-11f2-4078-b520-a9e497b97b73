// RBAC Types - Rol tabanlı erişim kontrolü tip tanımlamaları

export type Permission = 
  // Transaction permissions
  | 'transaction:create'
  | 'transaction:read'
  | 'transaction:update'
  | 'transaction:delete'
  | 'transaction:export'
  
  // Budget permissions
  | 'budget:create'
  | 'budget:read'
  | 'budget:update'
  | 'budget:delete'
  | 'budget:share'
  
  // Goal permissions
  | 'goal:create'
  | 'goal:read'
  | 'goal:update'
  | 'goal:delete'
  | 'goal:share'
  
  // Investment permissions
  | 'investment:create'
  | 'investment:read'
  | 'investment:update'
  | 'investment:delete'
  | 'investment:trade'
  
  // Report permissions
  | 'report:view'
  | 'report:export'
  | 'report:advanced'
  | 'report:analytics'
  
  // User management permissions
  | 'user:read'
  | 'user:update'
  | 'user:delete'
  | 'user:manage_roles'
  
  // Family permissions
  | 'family:create'
  | 'family:manage'
  | 'family:view_members'
  | 'family:add_members'
  | 'family:remove_members'
  
  // Admin permissions
  | 'admin:users'
  | 'admin:system'
  | 'admin:analytics'
  | 'admin:settings'
  
  // Premium features
  | 'premium:advanced_analytics'
  | 'premium:unlimited_goals'
  | 'premium:priority_support'
  | 'premium:custom_categories';

export type RoleType = 
  | 'guest'           // Misafir kullanıcı
  | 'basic'           // Temel kullanıcı
  | 'premium'         // Premium kullanıcı
  | 'family_member'   // Aile üyesi
  | 'family_admin'    // Aile yöneticisi
  | 'moderator'       // Moderatör
  | 'admin'           // Sistem yöneticisi
  | 'super_admin';    // Süper yönetici

export interface Role {
  id: string;
  name: RoleType;
  displayName: string;
  description: string;
  permissions: Permission[];
  isDefault: boolean;
  isSystemRole: boolean;
  priority: number; // Yüksek sayı = yüksek öncelik
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  userId: string;
  roleId: string;
  roleName: RoleType;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  context?: {
    familyId?: string;
    organizationId?: string;
    scope?: string;
  };
}

export interface PermissionCheck {
  permission: Permission;
  context?: {
    resourceId?: string;
    familyId?: string;
    organizationId?: string;
  };
}

export interface AccessControlResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: RoleType;
  requiredPermission?: Permission;
  currentRoles: RoleType[];
  currentPermissions: Permission[];
}

export interface RoleHierarchy {
  role: RoleType;
  inheritsFrom: RoleType[];
  canAssign: RoleType[];
  canManage: RoleType[];
}

export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  category: 'core' | 'premium' | 'family' | 'admin';
}

export interface RoleAssignment {
  id: string;
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: string;
  expiresAt?: string;
  isActive: boolean;
  metadata?: {
    reason?: string;
    autoAssigned?: boolean;
    source?: 'manual' | 'subscription' | 'family' | 'system';
  };
}

export interface AccessLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  permission: Permission;
  allowed: boolean;
  reason?: string;
  timestamp: string;
  context?: {
    ip?: string;
    userAgent?: string;
    location?: string;
  };
}

export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  baseRole: RoleType;
  additionalPermissions: Permission[];
  removedPermissions: Permission[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
}

// Predefined role configurations
export const DEFAULT_ROLES: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'guest',
    displayName: 'Misafir',
    description: 'Sınırlı erişim hakları olan misafir kullanıcı',
    permissions: ['transaction:read', 'budget:read', 'goal:read'],
    isDefault: false,
    isSystemRole: true,
    priority: 0,
  },
  {
    name: 'basic',
    displayName: 'Temel Kullanıcı',
    description: 'Standart finans yönetimi özellikleri',
    permissions: [
      'transaction:create', 'transaction:read', 'transaction:update', 'transaction:delete',
      'budget:create', 'budget:read', 'budget:update', 'budget:delete',
      'goal:create', 'goal:read', 'goal:update', 'goal:delete',
      'investment:read',
      'report:view',
      'user:read', 'user:update',
    ],
    isDefault: true,
    isSystemRole: true,
    priority: 10,
  },
  {
    name: 'premium',
    displayName: 'Premium Kullanıcı',
    description: 'Gelişmiş özellikler ve sınırsız erişim',
    permissions: [
      'transaction:create', 'transaction:read', 'transaction:update', 'transaction:delete', 'transaction:export',
      'budget:create', 'budget:read', 'budget:update', 'budget:delete', 'budget:share',
      'goal:create', 'goal:read', 'goal:update', 'goal:delete', 'goal:share',
      'investment:create', 'investment:read', 'investment:update', 'investment:delete', 'investment:trade',
      'report:view', 'report:export', 'report:advanced', 'report:analytics',
      'user:read', 'user:update',
      'premium:advanced_analytics', 'premium:unlimited_goals', 'premium:priority_support', 'premium:custom_categories',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 20,
  },
  {
    name: 'family_member',
    displayName: 'Aile Üyesi',
    description: 'Aile bütçesine katkıda bulunabilen üye',
    permissions: [
      'transaction:create', 'transaction:read', 'transaction:update',
      'budget:read',
      'goal:read',
      'family:view_members',
      'user:read', 'user:update',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 15,
  },
  {
    name: 'family_admin',
    displayName: 'Aile Yöneticisi',
    description: 'Aile hesabını yöneten kullanıcı',
    permissions: [
      'transaction:create', 'transaction:read', 'transaction:update', 'transaction:delete', 'transaction:export',
      'budget:create', 'budget:read', 'budget:update', 'budget:delete', 'budget:share',
      'goal:create', 'goal:read', 'goal:update', 'goal:delete', 'goal:share',
      'investment:read', 'investment:update',
      'report:view', 'report:export', 'report:advanced',
      'user:read', 'user:update',
      'family:create', 'family:manage', 'family:view_members', 'family:add_members', 'family:remove_members',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 25,
  },
  {
    name: 'moderator',
    displayName: 'Moderatör',
    description: 'Kullanıcı desteği ve içerik moderasyonu',
    permissions: [
      'user:read', 'user:update',
      'admin:analytics',
      'premium:priority_support',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 50,
  },
  {
    name: 'admin',
    displayName: 'Yönetici',
    description: 'Sistem yönetimi ve kullanıcı yönetimi',
    permissions: [
      'user:read', 'user:update', 'user:delete', 'user:manage_roles',
      'admin:users', 'admin:system', 'admin:analytics', 'admin:settings',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 80,
  },
  {
    name: 'super_admin',
    displayName: 'Süper Yönetici',
    description: 'Tam sistem erişimi',
    permissions: [
      // Tüm izinler
      'transaction:create', 'transaction:read', 'transaction:update', 'transaction:delete', 'transaction:export',
      'budget:create', 'budget:read', 'budget:update', 'budget:delete', 'budget:share',
      'goal:create', 'goal:read', 'goal:update', 'goal:delete', 'goal:share',
      'investment:create', 'investment:read', 'investment:update', 'investment:delete', 'investment:trade',
      'report:view', 'report:export', 'report:advanced', 'report:analytics',
      'user:read', 'user:update', 'user:delete', 'user:manage_roles',
      'family:create', 'family:manage', 'family:view_members', 'family:add_members', 'family:remove_members',
      'admin:users', 'admin:system', 'admin:analytics', 'admin:settings',
      'premium:advanced_analytics', 'premium:unlimited_goals', 'premium:priority_support', 'premium:custom_categories',
    ],
    isDefault: false,
    isSystemRole: true,
    priority: 100,
  },
];

// Permission groups for easier management
export const PERMISSION_GROUPS: PermissionGroup[] = [
  {
    id: 'transactions',
    name: 'İşlemler',
    description: 'Gelir ve gider işlemleri yönetimi',
    category: 'core',
    permissions: ['transaction:create', 'transaction:read', 'transaction:update', 'transaction:delete', 'transaction:export'],
  },
  {
    id: 'budgets',
    name: 'Bütçeler',
    description: 'Bütçe oluşturma ve yönetimi',
    category: 'core',
    permissions: ['budget:create', 'budget:read', 'budget:update', 'budget:delete', 'budget:share'],
  },
  {
    id: 'goals',
    name: 'Hedefler',
    description: 'Finansal hedef belirleme ve takibi',
    category: 'core',
    permissions: ['goal:create', 'goal:read', 'goal:update', 'goal:delete', 'goal:share'],
  },
  {
    id: 'investments',
    name: 'Yatırımlar',
    description: 'Yatırım portföyü yönetimi',
    category: 'premium',
    permissions: ['investment:create', 'investment:read', 'investment:update', 'investment:delete', 'investment:trade'],
  },
  {
    id: 'reports',
    name: 'Raporlar',
    description: 'Finansal raporlar ve analizler',
    category: 'premium',
    permissions: ['report:view', 'report:export', 'report:advanced', 'report:analytics'],
  },
  {
    id: 'family',
    name: 'Aile Hesabı',
    description: 'Aile üyeleri ve paylaşımlı bütçe yönetimi',
    category: 'family',
    permissions: ['family:create', 'family:manage', 'family:view_members', 'family:add_members', 'family:remove_members'],
  },
  {
    id: 'premium_features',
    name: 'Premium Özellikler',
    description: 'Premium abonelik özellikleri',
    category: 'premium',
    permissions: ['premium:advanced_analytics', 'premium:unlimited_goals', 'premium:priority_support', 'premium:custom_categories'],
  },
  {
    id: 'administration',
    name: 'Yönetim',
    description: 'Sistem ve kullanıcı yönetimi',
    category: 'admin',
    permissions: ['admin:users', 'admin:system', 'admin:analytics', 'admin:settings', 'user:manage_roles'],
  },
];

// Role hierarchy definitions
export const ROLE_HIERARCHY: RoleHierarchy[] = [
  {
    role: 'guest',
    inheritsFrom: [],
    canAssign: [],
    canManage: [],
  },
  {
    role: 'basic',
    inheritsFrom: ['guest'],
    canAssign: [],
    canManage: [],
  },
  {
    role: 'premium',
    inheritsFrom: ['basic'],
    canAssign: [],
    canManage: [],
  },
  {
    role: 'family_member',
    inheritsFrom: ['basic'],
    canAssign: [],
    canManage: [],
  },
  {
    role: 'family_admin',
    inheritsFrom: ['premium'],
    canAssign: ['family_member'],
    canManage: ['family_member'],
  },
  {
    role: 'moderator',
    inheritsFrom: ['premium'],
    canAssign: ['basic', 'premium'],
    canManage: ['guest', 'basic', 'premium', 'family_member'],
  },
  {
    role: 'admin',
    inheritsFrom: ['moderator'],
    canAssign: ['guest', 'basic', 'premium', 'family_member', 'family_admin', 'moderator'],
    canManage: ['guest', 'basic', 'premium', 'family_member', 'family_admin', 'moderator'],
  },
  {
    role: 'super_admin',
    inheritsFrom: ['admin'],
    canAssign: ['guest', 'basic', 'premium', 'family_member', 'family_admin', 'moderator', 'admin'],
    canManage: ['guest', 'basic', 'premium', 'family_member', 'family_admin', 'moderator', 'admin'],
  },
];
