// Ana tip tanımlamaları

export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: string;
  userId: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  category: string;
  subcategory?: string;
  description: string;
  date: string;
  paymentMethod?: 'cash' | 'card' | 'bank_transfer';
  location?: string;
  receiptImage?: string;
  tags?: string[];
  isRecurring: boolean;
  recurringType?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  parentId?: string;
  isDefault: boolean;
  userId: string;
}

export interface Budget {
  id: string;
  userId: string;
  name: string;
  categoryId: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  startDate: string;
  endDate: string;
  spent: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FinancialGoal {
  id: string;
  userId: string;
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'emergency' | 'vacation' | 'house' | 'car' | 'education' | 'retirement' | 'other';
  priority: 'low' | 'medium' | 'high';
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Debt {
  id: string;
  userId: string;
  name: string;
  type: 'credit_card' | 'personal_loan' | 'mortgage' | 'student_loan' | 'other';
  totalAmount: number;
  remainingAmount: number;
  interestRate: number;
  minimumPayment: number;
  dueDate: string;
  creditor: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Investment {
  id: string;
  userId: string;
  name: string;
  type: 'stock' | 'bond' | 'mutual_fund' | 'crypto' | 'real_estate' | 'other';
  symbol?: string;
  quantity: number;
  purchasePrice: number;
  currentPrice: number;
  purchaseDate: string;
  platform: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Redux State Types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface TransactionState {
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  filters: {
    dateRange: {
      start: string;
      end: string;
    };
    categories: string[];
    type: 'all' | 'income' | 'expense';
  };
}

export interface BudgetState {
  budgets: Budget[];
  isLoading: boolean;
  error: string | null;
}

export interface GoalState {
  goals: FinancialGoal[];
  isLoading: boolean;
  error: string | null;
}

export interface DebtState {
  debts: Debt[];
  isLoading: boolean;
  error: string | null;
}

export interface InvestmentState {
  investments: Investment[];
  isLoading: boolean;
  error: string | null;
}

export interface RootState {
  auth: AuthState;
  transactions: TransactionState;
  budgets: BudgetState;
  goals: GoalState;
  debts: DebtState;
  investments: InvestmentState;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  TransactionDetail: { transactionId: string };
  AddTransaction: { type?: 'income' | 'expense' };
  EditTransaction: { transactionId: string };
  BudgetDetail: { budgetId: string };
  GoalDetail: { goalId: string };
  DebtDetail: { debtId: string };
  Settings: undefined;
  Profile: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Transactions: undefined;
  Budget: undefined;
  Goals: undefined;
  Reports: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Form Types
export interface TransactionFormData {
  type: 'income' | 'expense';
  amount: string;
  category: string;
  subcategory?: string;
  description: string;
  date: Date;
  paymentMethod?: string;
  isRecurring: boolean;
  recurringType?: string;
  receiptImage?: string;
}

export interface BudgetFormData {
  name: string;
  categoryId: string;
  amount: string;
  period: string;
  startDate: Date;
  endDate: Date;
}

export interface GoalFormData {
  title: string;
  description: string;
  targetAmount: string;
  targetDate: Date;
  category: string;
  priority: string;
}

// Chart Data Types
export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    colors?: string[];
  }[];
}

export interface PieChartData {
  name: string;
  amount: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

// Notification Types
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'budget_alert' | 'goal_reminder' | 'debt_reminder' | 'transaction_reminder';
  data?: any;
  isRead: boolean;
  createdAt: string;
}

// Theme Types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: object;
    h2: object;
    h3: object;
    body: object;
    caption: object;
  };
}
