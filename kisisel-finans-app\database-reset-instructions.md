# Database Reset Instructions

## 🔄 OPTION 1: App Data Reset (Recommended)

### Android:
1. Settings → Apps → Kişisel Finans App
2. Storage → Clear Data
3. Restart app

### iOS:
1. Delete app completely
2. Reinstall from development

## 🔄 OPTION 2: Manual Database File Delete

### Expo Development:
1. Stop the app
2. Clear Expo cache: `npx expo start --clear`
3. Restart app

### Physical Device:
Database file location varies by platform - app data reset is easier.

## ✅ After Reset:
- Fresh database will be created
- CREATE_TABLES will run (without code column)
- Migration V11 will add code column
- Migration V13 will populate data
- No more "duplicate column name" error

## 🎯 Expected Result:
```
✅ Database created
✅ Migration V11: code column added
✅ Migration V13: categories populated
✅ 20+ categories with codes
```
