// Bank Accounts Screen - Banka Hesapları Yönetimi
// RFC-002: Bank API Integration

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import BankApiService, { BankAccount, BankConnectionStatus } from '../../services/BankApiService';
import MockBankApiService from '../../services/MockBankApiService';

const BankAccountsScreen: React.FC = () => {
  const { theme } = useTheme();
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [connectionStatuses, setConnectionStatuses] = useState<BankConnectionStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [syncing, setSyncing] = useState(false);

  const bankApiService = BankApiService.getInstance();
  const mockBankApiService = MockBankApiService.getInstance();

  useEffect(() => {
    loadBankData();
  }, []);

  const loadBankData = async () => {
    try {
      setLoading(true);

      // Mock ve gerçek hesapları birleştir
      const [realAccounts, mockAccounts] = await Promise.all([
        bankApiService.getConnectedAccounts(),
        mockBankApiService.getMockConnectedAccounts(),
      ]);

      const allAccounts = [...realAccounts, ...mockAccounts];
      setAccounts(allAccounts);

      // Connection statuses (şimdilik sadece gerçek API'den)
      const statusesData = await bankApiService.getConnectionStatuses();
      setConnectionStatuses(statusesData);

    } catch (error) {
      console.error('Error loading bank data:', error);
      Alert.alert('Hata', 'Banka verileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBankData();
    setRefreshing(false);
  };

  const handleConnectBank = async (bankCode: string) => {
    try {
      console.log('🏦 Connecting to bank:', bankCode);

      // Demo bankalar için mock API kullan
      if (bankCode === 'DEMO_BANK' || bankCode === 'GARANTI_DEMO') {
        const result = await mockBankApiService.initiateMockBankConnection(bankCode);

        if (result.success) {
          Alert.alert(
            'Demo Banka Bağlantısı Başarılı',
            `${result.account?.bankName} hesabınız başarıyla bağlandı!`,
            [
              {
                text: 'Tamam',
                onPress: () => loadBankData()
              }
            ]
          );
        } else {
          Alert.alert('Hata', result.error || 'Demo banka bağlantısı başarısız');
        }
        return;
      }

      // Gerçek bankalar için normal OAuth flow
      const result = await bankApiService.initiateBankConnection(bankCode);

      if (result.success && result.authUrl) {
        // OAuth URL'ini tarayıcıda aç
        const supported = await Linking.canOpenURL(result.authUrl);
        if (supported) {
          await Linking.openURL(result.authUrl);
          Alert.alert(
            'Banka Bağlantısı',
            'Tarayıcıda açılan sayfada banka hesabınıza giriş yapın. İşlem tamamlandıktan sonra uygulamaya geri dönün.',
            [
              {
                text: 'Tamam',
                onPress: () => {
                  // Kullanıcı geri döndüğünde verileri yenile
                  setTimeout(() => {
                    loadBankData();
                  }, 2000);
                }
              }
            ]
          );
        } else {
          Alert.alert('Hata', 'Tarayıcı açılamadı');
        }
      } else {
        Alert.alert('Hata', result.error || 'Banka bağlantısı başlatılamadı');
      }
    } catch (error) {
      console.error('Bank connection error:', error);
      Alert.alert('Hata', 'Banka bağlantısı sırasında hata oluştu');
    }
  };

  const handleSyncAccount = async (accountId: string) => {
    try {
      setSyncing(true);

      // Mock hesap kontrolü
      const isMockAccount = accountId.startsWith('DEMO_BANK') || accountId.startsWith('GARANTI_DEMO');

      let balanceResult, transactionResult;

      if (isMockAccount) {
        // Mock API kullan
        balanceResult = await mockBankApiService.syncMockAccountBalance(accountId);
        transactionResult = await mockBankApiService.syncMockTransactions(accountId);
      } else {
        // Gerçek API kullan
        balanceResult = await bankApiService.syncAccountBalance(accountId);
        transactionResult = await bankApiService.syncTransactions(accountId);
      }

      if (balanceResult.success && transactionResult.success) {
        Alert.alert(
          'Senkronizasyon Başarılı',
          `Bakiye güncellendi. ${transactionResult.newTransactionCount || 0} yeni işlem eklendi.`
        );
        await loadBankData();
      } else {
        Alert.alert(
          'Senkronizasyon Hatası',
          balanceResult.error || transactionResult.error || 'Bilinmeyen hata'
        );
      }
    } catch (error) {
      console.error('Sync error:', error);
      Alert.alert('Hata', 'Senkronizasyon sırasında hata oluştu');
    } finally {
      setSyncing(false);
    }
  };

  const handleSyncAllAccounts = async () => {
    try {
      setSyncing(true);
      
      const result = await bankApiService.syncAllAccounts();
      
      if (result.success) {
        Alert.alert(
          'Toplu Senkronizasyon Başarılı',
          `Toplam ${result.totalNewTransactions || 0} yeni işlem eklendi.`
        );
        await loadBankData();
      } else {
        Alert.alert('Hata', 'Toplu senkronizasyon başarısız oldu');
      }
    } catch (error) {
      console.error('Sync all error:', error);
      Alert.alert('Hata', 'Toplu senkronizasyon sırasında hata oluştu');
    } finally {
      setSyncing(false);
    }
  };

  const handleDisconnectAccount = async (accountId: string, bankName: string) => {
    Alert.alert(
      'Hesap Bağlantısını Kes',
      `${bankName} hesabının bağlantısını kesmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Kes',
          style: 'destructive',
          onPress: async () => {
            try {
              // Mock hesap kontrolü
              const isMockAccount = accountId.startsWith('DEMO_BANK') || accountId.startsWith('GARANTI_DEMO');

              let result;
              if (isMockAccount) {
                // Mock API kullan
                result = await mockBankApiService.disconnectMockAccount(accountId);
              } else {
                // Gerçek API kullan
                result = await bankApiService.disconnectAccount(accountId);
              }

              if (result.success) {
                Alert.alert('Başarılı', 'Hesap bağlantısı kesildi');
                await loadBankData();
              } else {
                Alert.alert('Hata', result.error || 'Bağlantı kesilemedi');
              }
            } catch (error) {
              console.error('Disconnect error:', error);
              Alert.alert('Hata', 'Bağlantı kesme sırasında hata oluştu');
            }
          }
        }
      ]
    );
  };

  const formatCurrency = (amount: number, currency: string = 'TRY') => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'expired': return theme.colors.warning;
      case 'error': return theme.colors.error;
      case 'disconnected': return theme.colors.textSecondary;
      default: return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'expired': return 'Token Süresi Dolmuş';
      case 'error': return 'Hata';
      case 'disconnected': return 'Bağlantı Kesilmiş';
      default: return 'Bilinmiyor';
    }
  };

  const supportedBanks = [
    { code: 'DEMO_BANK', name: 'Demo Bank (Test)', icon: '🏦', isDemo: true },
    { code: 'GARANTI_DEMO', name: 'Garanti BBVA (Demo)', icon: '🏦', isDemo: true },
    { code: 'AKBANK', name: 'Akbank', icon: '🏦', isDemo: false },
    { code: 'GARANTI', name: 'Garanti BBVA', icon: '🏦', isDemo: false },
    { code: 'ISBANK', name: 'İş Bankası', icon: '🏦', isDemo: false },
    { code: 'PLAID', name: 'Plaid (International)', icon: '🌐', isDemo: false },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    headerSubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    accountCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    accountHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    accountInfo: {
      flex: 1,
    },
    bankName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    accountNumber: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    accountActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      padding: 8,
      borderRadius: 8,
      backgroundColor: theme.colors.primary + '20',
    },
    balanceSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    balance: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    lastSync: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontSize: 12,
      fontWeight: '500',
      color: theme.colors.surface,
    },
    bankButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    bankIcon: {
      fontSize: 24,
      marginRight: 12,
    },
    bankInfo: {
      flex: 1,
    },
    bankButtonName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
    },
    bankButtonSubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    syncAllButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      marginBottom: 20,
    },
    syncAllButtonText: {
      color: theme.colors.surface,
      fontSize: 16,
      fontWeight: '600',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
    },
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.emptyText, { marginTop: 16 }]}>
          Banka hesapları yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Banka Hesapları</Text>
        <Text style={styles.headerSubtitle}>
          Banka hesaplarınızı bağlayın ve otomatik senkronizasyon yapın
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Bağlı Hesaplar */}
        {accounts.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Bağlı Hesaplar</Text>
            
            <TouchableOpacity
              style={styles.syncAllButton}
              onPress={handleSyncAllAccounts}
              disabled={syncing}
            >
              {syncing ? (
                <ActivityIndicator color={theme.colors.surface} />
              ) : (
                <Text style={styles.syncAllButtonText}>
                  Tüm Hesapları Senkronize Et
                </Text>
              )}
            </TouchableOpacity>

            {accounts.map((account) => (
              <View key={account.id} style={styles.accountCard}>
                <View style={styles.accountHeader}>
                  <View style={styles.accountInfo}>
                    <Text style={styles.bankName}>{account.bankName}</Text>
                    <Text style={styles.accountNumber}>
                      {account.accountNumber} • {account.accountType}
                    </Text>
                  </View>
                  <View style={styles.accountActions}>
                    <TouchableOpacity
                      style={styles.actionButton}
                      onPress={() => handleSyncAccount(account.id)}
                      disabled={syncing}
                    >
                      <Ionicons
                        name="refresh"
                        size={20}
                        color={theme.colors.primary}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.actionButton}
                      onPress={() => handleDisconnectAccount(account.id, account.bankName)}
                    >
                      <Ionicons
                        name="unlink"
                        size={20}
                        color={theme.colors.error}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.balanceSection}>
                  <Text style={styles.balance}>
                    {formatCurrency(account.balance, account.currency)}
                  </Text>
                  <Text style={styles.lastSync}>
                    Son sync: {new Date(account.lastSyncAt).toLocaleDateString('tr-TR')}
                  </Text>
                </View>

                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor('active') }
                  ]}
                >
                  <Text style={styles.statusText}>Aktif</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Yeni Banka Bağla */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Yeni Banka Hesabı Bağla</Text>
          
          {supportedBanks.map((bank) => (
            <TouchableOpacity
              key={bank.code}
              style={styles.bankButton}
              onPress={() => handleConnectBank(bank.code)}
            >
              <Text style={styles.bankIcon}>{bank.icon}</Text>
              <View style={styles.bankInfo}>
                <Text style={styles.bankButtonName}>{bank.name}</Text>
                <Text style={styles.bankButtonSubtitle}>
                  {bank.isDemo ? 'Demo hesap - Anında bağlan' : 'Güvenli OAuth 2.0 bağlantısı'}
                </Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          ))}
        </View>

        {accounts.length === 0 && (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="card-outline"
              size={64}
              color={theme.colors.textSecondary}
            />
            <Text style={styles.emptyText}>
              Henüz banka hesabı bağlamadınız.{'\n'}
              Yukarıdaki bankalardan birini seçerek başlayın.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default BankAccountsScreen;
