# RFC 002: <PERSON><PERSON><PERSON> ve Gider Takibi

## Özet
Bu RFC, Kişisel Finans Yönetimi uygulamasının gelir ve gider takibi özelliklerinin teknik tasarımını tanımlamaktadır.

## Motivasyon
Kullanıcıların finansal durumlarını etkili bir şekilde yönetebilmeleri için gelir ve giderlerini kolayca takip edebilmeleri gerekmektedir. Bu özellik, uygulamanın temel işlevselliğini oluşturmakta ve diğer tüm özelliklere temel teşkil etmektedir.

## Tasarım Detayları

### G<PERSON><PERSON> Kaydı
- **Veri Modeli**
  - Gelir ID (unique identifier)
  - Kullanıcı ID (foreign key)
  - Miktar
  - Para birimi
  - Kategori (enum: maaş, yatırım geliri, yan gelir, vb.)
  - Tarih
  - Tekrar bilgis<PERSON> (enum: tek sefer<PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON>, haft<PERSON><PERSON><PERSON>, ayl<PERSON><PERSON>, yı<PERSON><PERSON>k)
  - Açıklama
  - <PERSON><PERSON><PERSON><PERSON> (tag array)
  - Durum (enum: alındı, bekleniyor)
  
- **İşlevsellik**
  - Yeni gelir kaydı oluşturma
  - Gelir kaydı düzenleme/silme
  - Tekrarlayan gelirler için otomatik kayıt oluşturma
  - Gelir kaynağına göre filtreleme
  - Tarih aralığına göre filtreleme

### Gider Kaydı
- **Veri Modeli**
  - Gider ID (unique identifier)
  - Kullanıcı ID (foreign key)
  - Miktar
  - Para birimi
  - Ana kategori (enum: yiyecek, ulaşım, faturalar, vb.)
  - Alt kategori
  - Tarih
  - Ödeme yöntemi (enum: nakit, banka kartı, kredi kartı)
  - Konum (opsiyonel)
  - Makbuz/fatura görseli (opsiyonel)
  - Tekrar bilgisi (enum: tek seferlik, günlük, haftalık, aylık, yıllık)
  - Açıklama
  - Etiketler (tag array)
  
- **İşlevsellik**
  - Yeni gider kaydı oluşturma
  - Gider kaydı düzenleme/silme
  - Kamera ile fatura/makbuz fotoğrafı çekme
  - Galeriden fatura/makbuz yükleme
  - Tekrarlayan giderler için otomatik kayıt oluşturma
  - Kategori/alt kategoriye göre filtreleme
  - Tarih aralığına göre filtreleme
  - Konum bazlı gider haritası

### Otomatik Sınıflandırma
- **Veri Kaynağı Entegrasyonu**
  - Banka API entegrasyonu
  - Kredi kartı API entegrasyonu
  - Açık bankacılık standartları uyumu
  
- **Sınıflandırma Mekanizması**
  - Makine öğrenmesi modeli
  - İşyeri adı/açıklama tabanlı sınıflandırma
  - Benzer işlemler bazında eşleştirme
  - Kullanıcı geri bildirimine dayalı model iyileştirme
  
- **Eşleştirme ve Duplikasyon Önleme**
  - İşlem tarihi ve tutarı bazında eşleştirme
  - Makbuz verileri ile banka işlemlerini eşleştirme
  - Manuel olarak eklenen ve otomatik alınan işlemlerin birleştirilmesi

### Kullanıcı Arayüzü Bileşenleri
- Gelir ekleme formu
- Gider ekleme formu
- İşlem listeleme ekranı (sonsuz kaydırma)
- Filtreleme ve arama ara yüzü
- Grafik ve özet göstergeleri
- Kategori yönetim ekranı

## Uygulama
- **Veri Akışı**
  - Yerel veritabanı (SQLite veya PostgrSQL/Realm)
  - Çevrimdışı kullanım desteği
  - Bulut senkronizasyon
  - Batch işlem yükleme

- **Performans Optimizasyonu**
  - Sayfalı veri çekme
  - Önbelleğe alma stratejileri
  - İşlem toplu gönderimi

## Alternatifler
- OCR teknolojisi ile fatura bilgilerini otomatik çıkarma
- Ses tanıma ile gider ekleme
- GPS bazlı otomatik kategorizasyon önerisi

## Açık Sorular
- Ne kadar geçmiş veri tutulacak?
- Çoklu para birimi desteği nasıl sağlanacak?
- Silinen işlemlerin geri getirilebilmesi için çöp kutusu özelliği gerekli mi?

## Referanslar
- Açık Bankacılık API standartları
- Plaid/Yodlee gibi finansal veri toplayıcı servisler
- Mobil veritabanı optimizasyon teknikleri
