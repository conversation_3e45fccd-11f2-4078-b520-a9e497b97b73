// G<PERSON>r Hibrit Sistemi Migration - Income-Budget Integration

import { SQLiteDatabase } from 'expo-sqlite';

export class IncomeHybridMigration {
  /**
   * <PERSON><PERSON>r hibrit sistemi için database yapısını oluştur
   */
  static async migrate(db: SQLiteDatabase): Promise<void> {
    try {
      console.log('🚀 Starting Income Hybrid System migration...');

      // 1. Budget Income Categories tablosu oluştur
      await this.createBudgetIncomeCategoriesTable(db);

      // 2. Mevcut budgets tablosuna gelir tracking field'ları ekle
      await this.addIncomeTrackingFields(db);

      // 3. <PERSON><PERSON><PERSON> <PERSON> g<PERSON> (income type flag)
      await this.updateIncomeCategories(db);

      // 4. Gelir hibrit trigger'ları oluştur
      await this.createIncomeHybridTriggers(db);

      console.log('✅ Income Hybrid System migration completed successfully!');
    } catch (error) {
      console.error('❌ Income Hybrid System migration failed:', error);
      throw error;
    }
  }

  /**
   * Budget Income Categories tablosu oluştur
   */
  private static async createBudgetIncomeCategoriesTable(db: SQLiteDatabase): Promise<void> {
    console.log('📊 Creating budget_income_categories table...');

    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS budget_income_categories (
        id TEXT PRIMARY KEY,
        budget_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        target_amount REAL NOT NULL DEFAULT 0,
        actual_amount REAL NOT NULL DEFAULT 0,
        remaining_amount REAL NOT NULL DEFAULT 0,
        
        -- Progress tracking
        progress_percentage REAL NOT NULL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'pending', -- pending, on_track, achieved, exceeded
        
        -- Notification settings
        warning_threshold REAL NOT NULL DEFAULT 50,
        target_threshold REAL NOT NULL DEFAULT 100,
        warning_enabled INTEGER NOT NULL DEFAULT 1,
        target_enabled INTEGER NOT NULL DEFAULT 1,
        
        -- Metadata
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        
        -- Foreign keys
        FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
        
        -- Unique constraint
        UNIQUE(budget_id, category_id)
      );
    `);

    // Index'ler oluştur
    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_budget_income_categories_budget_id 
      ON budget_income_categories(budget_id);
    `);

    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_budget_income_categories_category_id 
      ON budget_income_categories(category_id);
    `);

    await db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_budget_income_categories_active 
      ON budget_income_categories(is_active);
    `);

    console.log('✅ budget_income_categories table created successfully');
  }

  /**
   * Budgets tablosuna gelir tracking field'ları ekle
   */
  private static async addIncomeTrackingFields(db: SQLiteDatabase): Promise<void> {
    console.log('💰 Adding income tracking fields to budgets table...');

    // Önce column'ların var olup olmadığını kontrol et
    const tableInfo = await db.getAllAsync(`PRAGMA table_info(budgets)`);
    const existingColumns = tableInfo.map((col: any) => col.name);

    // Gelir tracking field'ları ekle
    const fieldsToAdd = [
      { name: 'total_income_actual', type: 'REAL', default: '0' },
      { name: 'total_income_remaining', type: 'REAL', default: '0' },
      { name: 'income_progress_percentage', type: 'REAL', default: '0' },
      { name: 'income_status', type: 'TEXT', default: "'pending'" },
    ];

    for (const field of fieldsToAdd) {
      if (!existingColumns.includes(field.name)) {
        await db.execAsync(`
          ALTER TABLE budgets 
          ADD COLUMN ${field.name} ${field.type} NOT NULL DEFAULT ${field.default};
        `);
        console.log(`✅ Added column: ${field.name}`);
      } else {
        console.log(`⚠️ Column already exists: ${field.name}`);
      }
    }

    console.log('✅ Income tracking fields added to budgets table');
  }

  /**
   * Gelir kategorilerini güncelle
   */
  private static async updateIncomeCategories(db: SQLiteDatabase): Promise<void> {
    console.log('📋 Updating income categories...');

    // Gelir kategorilerini income type olarak işaretle
    const incomeCategories = [
      'salary',           // Maaş
      'freelance',        // Freelance
      'side_income',      // Yan Gelir
      'investment_income', // Yatırım Geliri
      'bonus',            // Prim/Bonus
      'gift_income',      // Hediye/Bağış
      'rental_income',    // Kira Geliri
      'sales_income',     // Satış Geliri
      'government_support', // Devlet Desteği
      'interest_income',  // Faiz Geliri
      'crypto_income',    // Kripto Geliri
      'stock_dividend',   // Hisse Senedi/Tahvil
      'refund',          // İade
      'other_income'     // Diğer
    ];

    for (const categoryCode of incomeCategories) {
      await db.execAsync(`
        UPDATE categories 
        SET type = 'income' 
        WHERE code = ? AND type != 'income'
      `, [categoryCode]);
    }

    console.log('✅ Income categories updated');
  }

  /**
   * Gelir hibrit trigger'ları oluştur
   */
  private static async createIncomeHybridTriggers(db: SQLiteDatabase): Promise<void> {
    console.log('⚡ Creating income hybrid triggers...');

    // Eski trigger'ları sil
    await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_insert;');
    await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_update;');
    await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_delete;');

    // Gelir işlemi eklendiğinde budget income category güncelle
    await db.execAsync(`
      CREATE TRIGGER update_budget_income_category_actual_insert
      AFTER INSERT ON transactions
      WHEN NEW.type = 'income' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL
      BEGIN
        -- Budget income category'yi güncelle
        UPDATE budget_income_categories
        SET 
          actual_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = NEW.budget_id
              AND category_id = budget_income_categories.category_id
              AND type = 'income'
              AND is_deleted = 0
          ),
          updated_at = datetime('now')
        WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id;

        -- Remaining amount ve progress hesapla
        UPDATE budget_income_categories
        SET 
          remaining_amount = target_amount - actual_amount,
          progress_percentage = CASE 
            WHEN target_amount > 0 THEN (actual_amount / target_amount) * 100 
            ELSE 0 
          END,
          status = CASE
            WHEN actual_amount >= target_amount THEN 'achieved'
            WHEN (actual_amount / NULLIF(target_amount, 0)) >= 0.8 THEN 'on_track'
            WHEN (actual_amount / NULLIF(target_amount, 0)) >= 0.5 THEN 'behind'
            ELSE 'pending'
          END,
          updated_at = datetime('now')
        WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id;

        -- Ana bütçenin gelir toplamını güncelle
        UPDATE budgets
        SET 
          total_income_actual = (
            SELECT COALESCE(SUM(actual_amount), 0)
            FROM budget_income_categories
            WHERE budget_id = NEW.budget_id AND is_active = 1
          ),
          updated_at = datetime('now')
        WHERE id = NEW.budget_id;

        -- Ana bütçenin gelir progress'ini güncelle
        UPDATE budgets
        SET 
          total_income_remaining = total_income_target - total_income_actual,
          income_progress_percentage = CASE 
            WHEN total_income_target > 0 THEN (total_income_actual / total_income_target) * 100 
            ELSE 0 
          END,
          income_status = CASE
            WHEN total_income_actual >= total_income_target THEN 'achieved'
            WHEN (total_income_actual / NULLIF(total_income_target, 0)) >= 0.8 THEN 'on_track'
            WHEN (total_income_actual / NULLIF(total_income_target, 0)) >= 0.5 THEN 'behind'
            ELSE 'pending'
          END,
          updated_at = datetime('now')
        WHERE id = NEW.budget_id;
      END;
    `);

    // Gelir işlemi güncellendiğinde
    await db.execAsync(`
      CREATE TRIGGER update_budget_income_category_actual_update
      AFTER UPDATE ON transactions
      WHEN (NEW.type = 'income' OR OLD.type = 'income')
           AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount
                OR NEW.category_id != OLD.category_id OR NEW.budget_id != OLD.budget_id)
      BEGIN
        -- Eski budget income category'yi güncelle (eğer değiştiyse)
        UPDATE budget_income_categories
        SET 
          actual_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = OLD.budget_id
              AND category_id = budget_income_categories.category_id
              AND type = 'income'
              AND is_deleted = 0
          ),
          updated_at = datetime('now')
        WHERE budget_id = OLD.budget_id AND category_id = OLD.category_id
              AND OLD.budget_id IS NOT NULL AND OLD.category_id IS NOT NULL;

        -- Yeni budget income category'yi güncelle
        UPDATE budget_income_categories
        SET 
          actual_amount = (
            SELECT COALESCE(SUM(amount), 0)
            FROM transactions
            WHERE budget_id = NEW.budget_id
              AND category_id = budget_income_categories.category_id
              AND type = 'income'
              AND is_deleted = 0
          ),
          updated_at = datetime('now')
        WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id
              AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL;

        -- Progress hesaplamalarını güncelle (her iki budget için)
        UPDATE budget_income_categories
        SET 
          remaining_amount = target_amount - actual_amount,
          progress_percentage = CASE 
            WHEN target_amount > 0 THEN (actual_amount / target_amount) * 100 
            ELSE 0 
          END,
          status = CASE
            WHEN actual_amount >= target_amount THEN 'achieved'
            WHEN (actual_amount / NULLIF(target_amount, 0)) >= 0.8 THEN 'on_track'
            WHEN (actual_amount / NULLIF(target_amount, 0)) >= 0.5 THEN 'behind'
            ELSE 'pending'
          END,
          updated_at = datetime('now')
        WHERE (budget_id = OLD.budget_id OR budget_id = NEW.budget_id)
              AND (OLD.budget_id IS NOT NULL OR NEW.budget_id IS NOT NULL);

        -- Ana bütçeleri güncelle
        UPDATE budgets
        SET 
          total_income_actual = (
            SELECT COALESCE(SUM(actual_amount), 0)
            FROM budget_income_categories
            WHERE budget_id = budgets.id AND is_active = 1
          ),
          total_income_remaining = total_income_target - total_income_actual,
          income_progress_percentage = CASE 
            WHEN total_income_target > 0 THEN (total_income_actual / total_income_target) * 100 
            ELSE 0 
          END,
          income_status = CASE
            WHEN total_income_actual >= total_income_target THEN 'achieved'
            WHEN (total_income_actual / NULLIF(total_income_target, 0)) >= 0.8 THEN 'on_track'
            WHEN (total_income_actual / NULLIF(total_income_target, 0)) >= 0.5 THEN 'behind'
            ELSE 'pending'
          END,
          updated_at = datetime('now')
        WHERE id = OLD.budget_id OR id = NEW.budget_id;
      END;
    `);

    console.log('✅ Income hybrid triggers created successfully');
  }

  /**
   * Migration'ı geri al
   */
  static async rollback(db: SQLiteDatabase): Promise<void> {
    try {
      console.log('🔄 Rolling back Income Hybrid System migration...');

      // Trigger'ları sil
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_insert;');
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_update;');
      await db.execAsync('DROP TRIGGER IF EXISTS update_budget_income_category_actual_delete;');

      // Tabloyu sil
      await db.execAsync('DROP TABLE IF EXISTS budget_income_categories;');

      // Budgets tablosundan field'ları kaldır (SQLite'da ALTER TABLE DROP COLUMN desteklenmiyor)
      // Bu yüzden sadece trigger'ları ve tabloyu siliyoruz

      console.log('✅ Income Hybrid System migration rolled back successfully');
    } catch (error) {
      console.error('❌ Income Hybrid System rollback failed:', error);
      throw error;
    }
  }
}

export default IncomeHybridMigration;
