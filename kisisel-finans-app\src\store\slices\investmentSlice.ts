// Investment Slice - Yatırım Redux slice'ı

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  RiskProfile,
  InvestmentOption,
  Portfolio,
  InvestmentRecommendation,
  InvestmentTransaction,
  MarketData,
  AutoInvestmentPlan,
} from '../../types/investment';

interface InvestmentState {
  riskProfile: RiskProfile | null;
  investmentOptions: InvestmentOption[];
  portfolios: Portfolio[];
  recommendations: InvestmentRecommendation[];
  transactions: InvestmentTransaction[];
  marketData: MarketData[];
  autoInvestmentPlans: AutoInvestmentPlan[];
  loading: {
    riskProfile: boolean;
    investmentOptions: boolean;
    portfolios: boolean;
    recommendations: boolean;
    transactions: boolean;
    marketData: boolean;
  };
  error: string | null;
}

const initialState: InvestmentState = {
  riskProfile: null,
  investmentOptions: [],
  portfolios: [],
  recommendations: [],
  transactions: [],
  marketData: [],
  autoInvestmentPlans: [],
  loading: {
    riskProfile: false,
    investmentOptions: false,
    portfolios: false,
    recommendations: false,
    transactions: false,
    marketData: false,
  },
  error: null,
};

// Async Thunks
export const createRiskProfile = createAsyncThunk(
  'investment/createRiskProfile',
  async (riskProfileData: Partial<RiskProfile>) => {
    // TODO: API çağrısı
    const response = await new Promise<RiskProfile>((resolve) => {
      setTimeout(() => {
        resolve({
          id: Date.now().toString(),
          userId: 'user1',
          ...riskProfileData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        } as RiskProfile);
      }, 1000);
    });
    return response;
  }
);

export const fetchInvestmentOptions = createAsyncThunk(
  'investment/fetchInvestmentOptions',
  async () => {
    // TODO: API çağrısı - şimdilik mock data
    const mockOptions: InvestmentOption[] = [
      {
        id: '1',
        name: 'BIST 30 Endeks Fonu',
        type: 'etf',
        symbol: 'BIST30',
        description: 'Borsa İstanbul\'da işlem gören en büyük 30 şirketin endeks fonu',
        riskLevel: 'medium',
        expectedReturn: 12.5,
        minimumInvestment: 100,
        fees: {
          managementFee: 0.5,
          transactionFee: 5,
        },
        performance: {
          oneMonth: 2.3,
          threeMonths: 5.7,
          sixMonths: 8.9,
          oneYear: 15.2,
          threeYears: 11.8,
        },
        currency: 'TRY',
        isActive: true,
        tags: ['türk hisse senetleri', 'endeks fonu', 'çeşitlendirilmiş'],
        provider: 'ABC Yatırım',
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Devlet Tahvili Fonu',
        type: 'bond',
        symbol: 'GOVT',
        description: 'Türk Devlet Tahvillerinden oluşan düşük riskli fon',
        riskLevel: 'low',
        expectedReturn: 8.5,
        minimumInvestment: 50,
        fees: {
          managementFee: 0.3,
          transactionFee: 2,
        },
        performance: {
          oneMonth: 0.7,
          threeMonths: 2.1,
          sixMonths: 4.2,
          oneYear: 8.1,
          threeYears: 7.9,
        },
        currency: 'TRY',
        isActive: true,
        tags: ['devlet tahvili', 'düşük risk', 'sabit gelir'],
        provider: 'XYZ Portföy',
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '3',
        name: 'Teknoloji Hisse Senedi Fonu',
        type: 'fund',
        symbol: 'TECH',
        description: 'Teknoloji sektöründeki şirketlere yatırım yapan yüksek büyüme potansiyelli fon',
        riskLevel: 'high',
        expectedReturn: 18.0,
        minimumInvestment: 200,
        fees: {
          managementFee: 1.2,
          transactionFee: 10,
          performanceFee: 15,
        },
        performance: {
          oneMonth: 4.5,
          threeMonths: 12.3,
          sixMonths: 22.1,
          oneYear: 28.7,
          threeYears: 19.4,
        },
        currency: 'TRY',
        isActive: true,
        tags: ['teknoloji', 'büyüme', 'yüksek risk'],
        provider: 'DEF Varlık',
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '4',
        name: 'Altın ETF',
        type: 'etf',
        symbol: 'GOLD',
        description: 'Fiziki altına dayalı borsa yatırım fonu',
        riskLevel: 'medium',
        expectedReturn: 10.0,
        minimumInvestment: 100,
        fees: {
          managementFee: 0.4,
          transactionFee: 3,
        },
        performance: {
          oneMonth: 1.2,
          threeMonths: 3.8,
          sixMonths: 7.5,
          oneYear: 12.3,
          threeYears: 9.1,
        },
        currency: 'TRY',
        isActive: true,
        tags: ['altın', 'emtia', 'enflasyon korunması'],
        provider: 'GHI Yatırım',
        lastUpdated: new Date().toISOString(),
      },
    ];

    return new Promise<InvestmentOption[]>((resolve) => {
      setTimeout(() => resolve(mockOptions), 1000);
    });
  }
);

export const createPortfolio = createAsyncThunk(
  'investment/createPortfolio',
  async (portfolioData: Partial<Portfolio>) => {
    // TODO: API çağrısı
    const response = await new Promise<Portfolio>((resolve) => {
      setTimeout(() => {
        resolve({
          id: Date.now().toString(),
          userId: 'user1',
          totalValue: 0,
          totalInvested: 0,
          totalReturn: 0,
          totalReturnPercentage: 0,
          allocations: [],
          isActive: true,
          ...portfolioData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        } as Portfolio);
      }, 1000);
    });
    return response;
  }
);

export const generateRecommendations = createAsyncThunk(
  'investment/generateRecommendations',
  async (riskProfileId: string) => {
    // TODO: AI destekli öneri algoritması
    const mockRecommendation: InvestmentRecommendation = {
      id: Date.now().toString(),
      userId: 'user1',
      riskProfileId,
      recommendedAllocations: [
        {
          investmentId: '1',
          investment: {} as InvestmentOption,
          recommendedPercentage: 40,
          reasoning: 'Orta risk seviyeniz için çeşitlendirilmiş hisse senedi fonu',
          priority: 'high',
        },
        {
          investmentId: '2',
          investment: {} as InvestmentOption,
          recommendedPercentage: 30,
          reasoning: 'Portföy dengelemesi için düşük riskli tahvil fonu',
          priority: 'high',
        },
        {
          investmentId: '4',
          investment: {} as InvestmentOption,
          recommendedPercentage: 20,
          reasoning: 'Enflasyon korunması için altın yatırımı',
          priority: 'medium',
        },
        {
          investmentId: '3',
          investment: {} as InvestmentOption,
          recommendedPercentage: 10,
          reasoning: 'Büyüme potansiyeli için teknoloji fonu',
          priority: 'low',
        },
      ],
      expectedReturn: 12.8,
      riskScore: 6.5,
      reasoning: 'Risk profilinize uygun dengeli bir portföy önerisi',
      confidence: 0.85,
      createdAt: new Date().toISOString(),
      isActive: true,
    };

    return new Promise<InvestmentRecommendation>((resolve) => {
      setTimeout(() => resolve(mockRecommendation), 1500);
    });
  }
);

const investmentSlice = createSlice({
  name: 'investment',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateMarketData: (state, action: PayloadAction<MarketData[]>) => {
      state.marketData = action.payload;
    },
    addTransaction: (state, action: PayloadAction<InvestmentTransaction>) => {
      state.transactions.unshift(action.payload);
    },
  },
  extraReducers: (builder) => {
    // Risk Profile
    builder
      .addCase(createRiskProfile.pending, (state) => {
        state.loading.riskProfile = true;
        state.error = null;
      })
      .addCase(createRiskProfile.fulfilled, (state, action) => {
        state.loading.riskProfile = false;
        state.riskProfile = action.payload;
      })
      .addCase(createRiskProfile.rejected, (state, action) => {
        state.loading.riskProfile = false;
        state.error = action.error.message || 'Risk profili oluşturulamadı';
      });

    // Investment Options
    builder
      .addCase(fetchInvestmentOptions.pending, (state) => {
        state.loading.investmentOptions = true;
        state.error = null;
      })
      .addCase(fetchInvestmentOptions.fulfilled, (state, action) => {
        state.loading.investmentOptions = false;
        state.investmentOptions = action.payload;
      })
      .addCase(fetchInvestmentOptions.rejected, (state, action) => {
        state.loading.investmentOptions = false;
        state.error = action.error.message || 'Yatırım seçenekleri yüklenemedi';
      });

    // Portfolio
    builder
      .addCase(createPortfolio.pending, (state) => {
        state.loading.portfolios = true;
        state.error = null;
      })
      .addCase(createPortfolio.fulfilled, (state, action) => {
        state.loading.portfolios = false;
        state.portfolios.push(action.payload);
      })
      .addCase(createPortfolio.rejected, (state, action) => {
        state.loading.portfolios = false;
        state.error = action.error.message || 'Portföy oluşturulamadı';
      });

    // Recommendations
    builder
      .addCase(generateRecommendations.pending, (state) => {
        state.loading.recommendations = true;
        state.error = null;
      })
      .addCase(generateRecommendations.fulfilled, (state, action) => {
        state.loading.recommendations = false;
        state.recommendations.push(action.payload);
      })
      .addCase(generateRecommendations.rejected, (state, action) => {
        state.loading.recommendations = false;
        state.error = action.error.message || 'Öneriler oluşturulamadı';
      });
  },
});

export const { clearError, updateMarketData, addTransaction } = investmentSlice.actions;
export default investmentSlice.reducer;
