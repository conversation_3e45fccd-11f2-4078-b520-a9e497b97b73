// Two Factor Verification Screen - 2FA doğrulama ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  TextInput,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { Ionicons } from '@expo/vector-icons';
import TwoFactorService from '../../services/TwoFactorService';
import { AuthStackParamList } from '../../navigation/AuthNavigator';
import CheckBox from 'expo-checkbox';

type TwoFactorVerificationNavigationProp = StackNavigationProp<AuthStackParamList, 'TwoFactorVerification'>;
type TwoFactorVerificationRouteProp = RouteProp<AuthStackParamList, 'TwoFactorVerification'>;

const TwoFactorVerificationScreen: React.FC = () => {
  const navigation = useNavigation<TwoFactorVerificationNavigationProp>();
  const route = useRoute<TwoFactorVerificationRouteProp>();
  const { theme } = useTheme();
  const { login } = useSimpleAuth();

  const { email, password } = route.params;

  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [rememberDevice, setRememberDevice] = useState(false);
  const [availableMethods, setAvailableMethods] = useState<string[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<'email' | 'sms' | 'authenticator'>('email');
  const [codeSent, setCodeSent] = useState(false);

  useEffect(() => {
    loadAvailableMethods();
  }, []);

  const loadAvailableMethods = async () => {
    try {
      const twoFactorService = TwoFactorService.getInstance();
      const status = await twoFactorService.getTwoFactorStatus();
      setAvailableMethods(status.enabledMethods);

      if (status.enabledMethods.length > 0) {
        setSelectedMethod(status.enabledMethods[0] as 'email' | 'sms' | 'authenticator');

        // Email veya SMS ise otomatik kod gönder
        if (status.enabledMethods[0] === 'email') {
          await sendEmailCode();
        } else if (status.enabledMethods[0] === 'sms') {
          await sendSMSCode();
        }
      }
    } catch (error) {
      console.error('Load available methods error:', error);
      Alert.alert('Hata', '2FA yöntemleri yüklenemedi');
    }
  };

  const sendEmailCode = async () => {
    try {
      setLoading(true);
      const twoFactorService = TwoFactorService.getInstance();
      const success = await twoFactorService.sendEmailVerificationCode(email);

      if (success) {
        setCodeSent(true);
        Alert.alert('Başarılı', 'Email adresinize doğrulama kodu gönderildi');
      } else {
        Alert.alert('Hata', 'Email kodu gönderilemedi');
      }
    } catch (error) {
      console.error('Send email code error:', error);
      Alert.alert('Hata', 'Email kodu gönderilemedi');
    } finally {
      setLoading(false);
    }
  };

  const sendSMSCode = async () => {
    try {
      setLoading(true);
      const twoFactorService = TwoFactorService.getInstance();
      // Kullanıcının kayıtlı telefon numarasını al
      const phoneNumber = '+905551234567'; // Mock telefon numarası
      const success = await twoFactorService.sendSMSVerificationCode(phoneNumber);

      if (success) {
        setCodeSent(true);
        Alert.alert('Başarılı', 'Telefon numaranıza doğrulama kodu gönderildi');
      } else {
        Alert.alert('Hata', 'SMS kodu gönderilemedi');
      }
    } catch (error) {
      console.error('Send SMS code error:', error);
      Alert.alert('Hata', 'SMS kodu gönderilemedi');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Hata', 'Lütfen doğrulama kodunu girin');
      return;
    }

    try {
      setLoading(true);
      const twoFactorService = TwoFactorService.getInstance();
      let isValid = false;

      // Seçilen yönteme göre doğrulama yap
      if (selectedMethod === 'email') {
        isValid = await twoFactorService.verifyEmailCode(verificationCode);
      } else if (selectedMethod === 'sms') {
        isValid = await twoFactorService.verifySMSCode(verificationCode);
      } else if (selectedMethod === 'authenticator') {
        isValid = await twoFactorService.verifyAuthenticatorLogin(verificationCode);
      }

      if (isValid) {
        // Remember device seçiliyse cihazı güvenilir listesine ekle
        if (rememberDevice) {
          await twoFactorService.addCurrentDeviceAsTrusted();
        }

        // Giriş işlemini tamamla
        const result = await login(email, password);
        if (result.success) {
          // AuthContext navigation'ı handle edecek
        } else {
          Alert.alert('Hata', result.error || 'Giriş tamamlanamadı');
        }
      } else {
        Alert.alert('Hata', 'Geçersiz doğrulama kodu');
      }
    } catch (error: any) {
      console.error('Verify code error:', error);
      Alert.alert('Hata', error.message || 'Doğrulama başarısız');
    } finally {
      setLoading(false);
    }
  };

  const handleMethodChange = async (method: 'email' | 'sms' | 'authenticator') => {
    setSelectedMethod(method);
    setVerificationCode('');
    setCodeSent(false);

    // Yeni yöntem için kod gönder
    if (method === 'email') {
      await sendEmailCode();
    } else if (method === 'sms') {
      await sendSMSCode();
    }
  };

  const handleResendCode = async () => {
    if (selectedMethod === 'email') {
      await sendEmailCode();
    } else if (selectedMethod === 'sms') {
      await sendSMSCode();
    }
  };

  const getMethodTitle = (method: string): string => {
    switch (method) {
      case 'email': return 'Email';
      case 'sms': return 'SMS';
      case 'authenticator': return 'Authenticator';
      default: return method;
    }
  };

  const getMethodIcon = (method: string): keyof typeof Ionicons.glyphMap => {
    switch (method) {
      case 'email': return 'mail';
      case 'sms': return 'chatbubble';
      case 'authenticator': return 'shield-checkmark';
      default: return 'shield';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          İki Faktörlü Doğrulama
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info */}
        <View style={styles.infoContainer}>
          <Ionicons name="shield-checkmark" size={48} color={theme.colors.primary} />
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Güvenlik Doğrulaması
          </Text>
          <Text style={[styles.infoDescription, { color: theme.colors.textSecondary }]}>
            Hesabınızın güvenliği için iki faktörlü doğrulama gereklidir.
          </Text>
        </View>

        {/* Method Selection */}
        {availableMethods.length > 1 && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Doğrulama Yöntemi
            </Text>
            {availableMethods.map((method) => (
              <TouchableOpacity
                key={method}
                style={[
                  styles.methodItem,
                  { borderBottomColor: theme.colors.border },
                  selectedMethod === method && { backgroundColor: theme.colors.primaryLight }
                ]}
                onPress={() => handleMethodChange(method as 'email' | 'sms' | 'authenticator')}
              >
                <Ionicons
                  name={getMethodIcon(method)}
                  size={24}
                  color={selectedMethod === method ? theme.colors.primary : theme.colors.textSecondary}
                />
                <Text style={[
                  styles.methodText,
                  { color: selectedMethod === method ? theme.colors.primary : theme.colors.text }
                ]}>
                  {getMethodTitle(method)}
                </Text>
                {selectedMethod === method && (
                  <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Code Input */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Doğrulama Kodu
          </Text>

          {selectedMethod !== 'authenticator' && codeSent && (
            <Text style={[styles.codeInfo, { color: theme.colors.textSecondary }]}>
              {selectedMethod === 'email' ? 'Email adresinize' : 'Telefon numaranıza'} doğrulama kodu gönderildi
            </Text>
          )}

          <TextInput
            style={[
              styles.codeInput,
              {
                backgroundColor: theme.colors.background,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={verificationCode}
            onChangeText={setVerificationCode}
            placeholder="6 haneli kodu girin"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={6}
            autoFocus
          />

          {selectedMethod !== 'authenticator' && (
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResendCode}
              disabled={loading}
            >
              <Text style={[styles.resendText, { color: theme.colors.primary }]}>
                Kodu Tekrar Gönder
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Remember Device */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => setRememberDevice(!rememberDevice)}
          >
            <CheckBox
              value={rememberDevice}
              onValueChange={setRememberDevice}
              color={rememberDevice ? theme.colors.primary : undefined}
            />
            <View style={styles.checkboxTextContainer}>
              <Text style={[styles.checkboxTitle, { color: theme.colors.text }]}>
                Bu cihazı hatırla
              </Text>
              <Text style={[styles.checkboxDescription, { color: theme.colors.textSecondary }]}>
                30 gün boyunca bu cihazda 2FA istenmeyecek
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          style={[
            styles.verifyButton,
            { backgroundColor: theme.colors.primary },
            loading && { opacity: 0.7 }
          ]}
          onPress={handleVerifyCode}
          disabled={loading || !verificationCode.trim()}
        >
          {loading ? (
            <ActivityIndicator color={theme.colors.surface} />
          ) : (
            <Text style={[styles.verifyButtonText, { color: theme.colors.surface }]}>
              Doğrula ve Giriş Yap
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  infoContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  infoTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  infoDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  methodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  methodText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
  codeInfo: {
    fontSize: 14,
    padding: 16,
    paddingBottom: 8,
  },
  codeInput: {
    margin: 16,
    marginTop: 8,
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 18,
    textAlign: 'center',
    letterSpacing: 4,
  },
  resendButton: {
    alignSelf: 'center',
    padding: 8,
    marginBottom: 16,
  },
  resendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
  },
  checkboxTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  checkboxTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  checkboxDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  verifyButton: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TwoFactorVerificationScreen;
