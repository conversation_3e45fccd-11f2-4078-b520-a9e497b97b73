// Simple Authentication Context - <PERSON><PERSON>t kullanıcı kimlik doğrulama

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  preferences: {
    currency: string;
    language: string;
    notifications: boolean;
    biometric: boolean;
  };
}

interface SimpleAuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (name: string, email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
}

const SimpleAuthContext = createContext<SimpleAuthContextType | undefined>(undefined);

export const useSimpleAuth = () => {
  const context = useContext(SimpleAuthContext);
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
};

interface SimpleAuthProviderProps {
  children: ReactNode;
}

export const SimpleAuthProvider: React.FC<SimpleAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const savedUser = await AsyncStorage.getItem('simple_user');
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }
    } catch (error) {
      console.error('Auth state check error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);

      // Basit validation
      if (email.includes('@') && password.length >= 6) {
        const mockUser: User = {
          id: email === '<EMAIL>' ? 'test_user_123' : `test_user_${Date.now()}`,
          email,
          name: email.split('@')[0],
          createdAt: new Date().toISOString(),
          preferences: {
            currency: 'TRY',
            language: 'tr',
            notifications: true,
            biometric: false,
          }
        };

        await AsyncStorage.setItem('simple_user', JSON.stringify(mockUser));
        setUser(mockUser);

        console.log('✅ Simple login successful for:', email);
        return { success: true };
      } else {
        return { success: false, error: 'Geçersiz email veya şifre' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Giriş yapılırken bir hata oluştu' };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (name: string, email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);

      // Basit validation
      if (!name.trim() || !email.includes('@') || password.length < 6) {
        return { success: false, error: 'Lütfen tüm alanları doğru şekilde doldurun' };
      }

      const mockUser: User = {
        id: `test_user_${Date.now()}`,
        email,
        name,
        createdAt: new Date().toISOString(),
        preferences: {
          currency: 'TRY',
          language: 'tr',
          notifications: true,
          biometric: false,
        }
      };

      await AsyncStorage.setItem('simple_user', JSON.stringify(mockUser));
      setUser(mockUser);

      console.log('✅ Simple registration successful for:', email);
      return { success: true };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Kayıt olurken bir hata oluştu' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('simple_user');
      setUser(null);
      console.log('✅ Simple logout successful');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value: SimpleAuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
  };

  return (
    <SimpleAuthContext.Provider value={value}>
      {children}
    </SimpleAuthContext.Provider>
  );
};
