// Two Factor Authentication Service - 2FA yönetimi

import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import * as Crypto from 'expo-crypto';
import * as base32 from 'base32.js';
import * as Device from 'expo-device';

export interface TwoFactorSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  authenticatorEnabled: boolean;
  backupCodesEnabled: boolean;
  trustedDevices: string[];
}

export interface VerificationCode {
  code: string;
  type: 'email' | 'sms' | 'authenticator';
  expiresAt: number;
  attempts: number;
  maxAttempts: number;
}

export interface TrustedDevice {
  id: string;
  name: string;
  deviceInfo: string;
  addedAt: string;
  lastUsed: string;
  isCurrentDevice: boolean;
  expiresAt?: string;
}

export interface AuthenticatorSetup {
  secret: string;
  qrCodeUrl: string;
  manualEntryKey: string;
  backupCodes: string[];
}

class TwoFactorService {
  private static instance: TwoFactorService;
  private twoFactorSettings: TwoFactorSettings = {
    emailEnabled: false,
    smsEnabled: false,
    authenticatorEnabled: false,
    backupCodesEnabled: false,
    trustedDevices: [],
  };

  // TOTP Helper Methods
  private async generateSecret(): Promise<string> {
    // 32 byte rastgele secret oluştur
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    const secret = base32.encode(Array.from(randomBytes)).replace(/=/g, ''); // Padding kaldır
    return secret;
  }

  private async hmacSha1(key: Uint8Array, message: Uint8Array): Promise<Uint8Array> {
    // HMAC-SHA1 implementasyonu (basitleştirilmiş)
    // Gerçek uygulamada crypto kütüphanesi kullanılmalı
    const digest = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA1,
      Array.from(message).map(b => String.fromCharCode(b)).join('')
    );

    // Hex string'i Uint8Array'e çevir
    const bytes = new Uint8Array(digest.length / 2);
    for (let i = 0; i < digest.length; i += 2) {
      bytes[i / 2] = parseInt(digest.substr(i, 2), 16);
    }
    return bytes;
  }

  private async generateTOTP(secret: string, timeStep: number = 30): Promise<string> {
    try {
      // Base32 decode
      const secretBytes = new Uint8Array(base32.decode(secret));

      // Current time step
      const time = Math.floor(Date.now() / 1000 / timeStep);

      // Time'ı 8 byte big-endian olarak encode et
      const timeBytes = new Uint8Array(8);
      for (let i = 7; i >= 0; i--) {
        timeBytes[i] = time & 0xff;
        time >>> 8;
      }

      // HMAC-SHA1 hesapla
      const hmac = await this.hmacSha1(secretBytes, timeBytes);

      // Dynamic truncation
      const offset = hmac[hmac.length - 1] & 0xf;
      const code = ((hmac[offset] & 0x7f) << 24) |
                   ((hmac[offset + 1] & 0xff) << 16) |
                   ((hmac[offset + 2] & 0xff) << 8) |
                   (hmac[offset + 3] & 0xff);

      // 6 haneli kod
      const otp = (code % 1000000).toString().padStart(6, '0');
      return otp;
    } catch (error) {
      console.error('TOTP generation error:', error);
      // Fallback: basit 6 haneli kod
      return Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    }
  }

  private async verifyTOTP(secret: string, token: string, window: number = 2): Promise<boolean> {
    try {
      const currentTime = Math.floor(Date.now() / 1000 / 30);

      // Window içindeki time step'leri kontrol et
      for (let i = -window; i <= window; i++) {
        const timeStep = currentTime + i;
        const expectedToken = await this.generateTOTPForTime(secret, timeStep);

        if (expectedToken === token.trim()) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('TOTP verification error:', error);
      return false;
    }
  }

  private async generateTOTPForTime(secret: string, timeStep: number): Promise<string> {
    try {
      const secretBytes = new Uint8Array(base32.decode(secret));

      // Time'ı 8 byte big-endian olarak encode et
      const timeBytes = new Uint8Array(8);
      let time = timeStep;
      for (let i = 7; i >= 0; i--) {
        timeBytes[i] = time & 0xff;
        time = Math.floor(time / 256);
      }

      // HMAC-SHA1 hesapla
      const hmac = await this.hmacSha1(secretBytes, timeBytes);

      // Dynamic truncation
      const offset = hmac[hmac.length - 1] & 0xf;
      const code = ((hmac[offset] & 0x7f) << 24) |
                   ((hmac[offset + 1] & 0xff) << 16) |
                   ((hmac[offset + 2] & 0xff) << 8) |
                   (hmac[offset + 3] & 0xff);

      // 6 haneli kod
      const otp = (code % 1000000).toString().padStart(6, '0');
      return otp;
    } catch (error) {
      console.error('TOTP generation for time error:', error);
      return '000000';
    }
  }

  static getInstance(): TwoFactorService {
    if (!TwoFactorService.instance) {
      TwoFactorService.instance = new TwoFactorService();
    }
    return TwoFactorService.instance;
  }

  constructor() {
    this.loadTwoFactorSettings();
  }

  /**
   * 2FA ayarlarını yükle
   */
  private async loadTwoFactorSettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('two_factor_settings');
      if (stored) {
        this.twoFactorSettings = { ...this.twoFactorSettings, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.error('2FA settings load error:', error);
    }
  }

  /**
   * Remember device ayarını kontrol et
   */
  async isRememberDeviceEnabled(): Promise<boolean> {
    try {
      const setting = await AsyncStorage.getItem('rememberDevice');
      return setting !== 'false';
    } catch (error) {
      console.error('Remember device check error:', error);
      return true; // Default olarak true
    }
  }

  /**
   * Mevcut cihazın ID'sini al
   */
  async getCurrentDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem('device_id');
      if (!deviceId) {
        // Yeni device ID oluştur
        deviceId = await this.generateDeviceId();
        await AsyncStorage.setItem('device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('Get current device ID error:', error);
      return 'unknown_device';
    }
  }

  /**
   * Benzersiz device ID oluştur
   */
  private async generateDeviceId(): Promise<string> {
    try {
      const deviceInfo = {
        brand: Device.brand,
        modelName: Device.modelName,
        osName: Device.osName,
        osVersion: Device.osVersion,
        timestamp: Date.now(),
        random: Math.random().toString(36).substring(2, 15)
      };

      const deviceString = JSON.stringify(deviceInfo);
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        deviceString
      );

      return hash.substring(0, 16); // 16 karakter
    } catch (error) {
      console.error('Generate device ID error:', error);
      return `device_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }
  }

  /**
   * Mevcut cihazı güvenilir listesine ekle
   */
  async addCurrentDeviceAsTrusted(): Promise<boolean> {
    try {
      const deviceId = await this.getCurrentDeviceId();
      const deviceName = this.getDeviceName();
      const deviceInfo = this.getDeviceInfo();

      const newDevice: TrustedDevice = {
        id: deviceId,
        name: deviceName,
        deviceInfo: deviceInfo,
        addedAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        isCurrentDevice: true,
        expiresAt: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)).toISOString() // 30 gün
      };

      // Mevcut güvenilir cihazları al
      const trustedDevices = await this.getTrustedDevices();

      // Bu cihaz zaten listede var mı kontrol et
      const existingDeviceIndex = trustedDevices.findIndex(device => device.id === deviceId);

      if (existingDeviceIndex >= 0) {
        // Mevcut cihazı güncelle
        trustedDevices[existingDeviceIndex] = newDevice;
      } else {
        // Yeni cihaz ekle
        trustedDevices.push(newDevice);
      }

      // Diğer cihazları current olmayan olarak işaretle
      trustedDevices.forEach(device => {
        if (device.id !== deviceId) {
          device.isCurrentDevice = false;
        }
      });

      // Güvenilir cihazları kaydet
      await AsyncStorage.setItem('trusted_devices', JSON.stringify(trustedDevices));

      return true;
    } catch (error) {
      console.error('Add current device as trusted error:', error);
      return false;
    }
  }

  /**
   * Cihaz adını al
   */
  private getDeviceName(): string {
    const brand = Device.brand || 'Unknown';
    const modelName = Device.modelName || 'Device';
    return `${brand} ${modelName}`;
  }

  /**
   * Cihaz bilgilerini al
   */
  private getDeviceInfo(): string {
    const osName = Device.osName || 'Unknown OS';
    const osVersion = Device.osVersion || '';
    return `${osName} ${osVersion}`.trim();
  }

  /**
   * 2FA ayarlarını kaydet
   */
  private async saveTwoFactorSettings(): Promise<void> {
    try {
      await AsyncStorage.setItem('two_factor_settings', JSON.stringify(this.twoFactorSettings));
    } catch (error) {
      console.error('2FA settings save error:', error);
    }
  }

  /**
   * Email ile doğrulama kodu gönder
   */
  async sendEmailVerificationCode(email: string): Promise<boolean> {
    try {
      // 6 haneli rastgele kod oluştur
      const code = this.generateVerificationCode();

      // Kodu geçici olarak sakla (gerçek uygulamada backend'de saklanır)
      const verificationData: VerificationCode = {
        code,
        type: 'email',
        expiresAt: Date.now() + (5 * 60 * 1000), // 5 dakika
        attempts: 0,
        maxAttempts: 3,
      };

      await AsyncStorage.setItem('pending_email_verification', JSON.stringify(verificationData));

      // API üzerinden email gönder
      try {
        const response = await ApiService.request('/auth/send-email-code', {
          method: 'POST',
          body: JSON.stringify({ email, code }),
        });

        if (response.success) {
          return true;
        }
      } catch (apiError) {
        console.log('API email send failed, using mock email service');
      }

      // Mock email service (development için)
      console.log(`📧 Mock Email Sent to ${email}:`);
      console.log(`Verification Code: ${code}`);
      console.log(`Expires in 5 minutes`);

      // Gerçek uygulamada email service entegrasyonu:
      // - SendGrid, AWS SES, Mailgun, vb.
      // - HTML template ile güzel email tasarımı
      // - Rate limiting ve spam koruması

      return true;
    } catch (error) {
      console.error('Email verification send error:', error);
      return false;
    }
  }

  /**
   * Email doğrulama kodunu kontrol et
   */
  async verifyEmailCode(inputCode: string): Promise<boolean> {
    try {
      const stored = await AsyncStorage.getItem('pending_email_verification');
      if (!stored) {
        throw new Error('Doğrulama kodu bulunamadı');
      }

      const verificationData: VerificationCode = JSON.parse(stored);

      // Süre kontrolü
      if (Date.now() > verificationData.expiresAt) {
        await AsyncStorage.removeItem('pending_email_verification');
        throw new Error('Doğrulama kodu süresi dolmuş');
      }

      // Deneme sayısı kontrolü
      if (verificationData.attempts >= verificationData.maxAttempts) {
        await AsyncStorage.removeItem('pending_email_verification');
        throw new Error('Maksimum deneme sayısı aşıldı');
      }

      // Kod kontrolü
      if (inputCode.trim() !== verificationData.code) {
        verificationData.attempts++;
        await AsyncStorage.setItem('pending_email_verification', JSON.stringify(verificationData));
        throw new Error(`Yanlış kod. Kalan deneme: ${verificationData.maxAttempts - verificationData.attempts}`);
      }

      // Başarılı doğrulama
      await AsyncStorage.removeItem('pending_email_verification');
      return true;
    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  }

  /**
   * 6 haneli doğrulama kodu oluştur
   */
  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Email 2FA'yı etkinleştir
   */
  async enableEmailTwoFactor(email: string): Promise<boolean> {
    try {
      // Önce email doğrulaması yap
      const codeSent = await this.sendEmailVerificationCode(email);
      if (!codeSent) {
        throw new Error('Email doğrulama kodu gönderilemedi');
      }

      return true; // Kod gönderildi, kullanıcı doğrulayacak
    } catch (error) {
      console.error('Enable email 2FA error:', error);
      return false;
    }
  }

  /**
   * Email 2FA'yı tamamla (kod doğrulandıktan sonra)
   */
  async completeEmailTwoFactor(): Promise<boolean> {
    try {
      this.twoFactorSettings.emailEnabled = true;
      await this.saveTwoFactorSettings();

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/enable-email-2fa', {
          method: 'POST',
        });
      } catch (apiError) {
        console.log('API 2FA enable failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Complete email 2FA error:', error);
      return false;
    }
  }

  /**
   * Email 2FA'yı devre dışı bırak
   */
  async disableEmailTwoFactor(): Promise<boolean> {
    try {
      this.twoFactorSettings.emailEnabled = false;
      await this.saveTwoFactorSettings();

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/disable-email-2fa', {
          method: 'POST',
        });
      } catch (apiError) {
        console.log('API 2FA disable failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Disable email 2FA error:', error);
      return false;
    }
  }

  /**
   * SMS ile doğrulama kodu gönder
   */
  async sendSMSVerificationCode(phoneNumber: string): Promise<boolean> {
    try {
      // Telefon numarası formatını kontrol et
      const formattedPhone = this.formatPhoneNumber(phoneNumber);
      if (!this.isValidPhoneNumber(formattedPhone)) {
        throw new Error('Geçersiz telefon numarası formatı');
      }

      // 6 haneli rastgele kod oluştur
      const code = this.generateVerificationCode();

      // Kodu geçici olarak sakla
      const verificationData: VerificationCode = {
        code,
        type: 'sms',
        expiresAt: Date.now() + (5 * 60 * 1000), // 5 dakika
        attempts: 0,
        maxAttempts: 3,
      };

      await AsyncStorage.setItem('pending_sms_verification', JSON.stringify(verificationData));
      await AsyncStorage.setItem('sms_phone_number', formattedPhone);

      // API üzerinden SMS gönder
      try {
        const response = await ApiService.request('/auth/send-sms-code', {
          method: 'POST',
          body: JSON.stringify({ phoneNumber: formattedPhone, code }),
        });

        if (response.success) {
          return true;
        }
      } catch (apiError) {
        console.log('API SMS send failed, using mock SMS service');
      }

      // Mock SMS service (development için)
      console.log(`📱 Mock SMS Sent to ${formattedPhone}:`);
      console.log(`Verification Code: ${code}`);
      console.log(`Expires in 5 minutes`);

      // Gerçek uygulamada SMS service entegrasyonu:
      // - Twilio, AWS SNS, Vonage, vb.
      // - Rate limiting ve spam koruması
      // - Uluslararası numara desteği
      // - Maliyet optimizasyonu

      return true;
    } catch (error) {
      console.error('SMS verification send error:', error);
      return false;
    }
  }

  /**
   * SMS doğrulama kodunu kontrol et
   */
  async verifySMSCode(inputCode: string): Promise<boolean> {
    try {
      const stored = await AsyncStorage.getItem('pending_sms_verification');
      if (!stored) {
        throw new Error('SMS doğrulama kodu bulunamadı');
      }

      const verificationData: VerificationCode = JSON.parse(stored);

      // Süre kontrolü
      if (Date.now() > verificationData.expiresAt) {
        await AsyncStorage.removeItem('pending_sms_verification');
        await AsyncStorage.removeItem('sms_phone_number');
        throw new Error('SMS doğrulama kodu süresi dolmuş');
      }

      // Deneme sayısı kontrolü
      if (verificationData.attempts >= verificationData.maxAttempts) {
        await AsyncStorage.removeItem('pending_sms_verification');
        await AsyncStorage.removeItem('sms_phone_number');
        throw new Error('Maksimum deneme sayısı aşıldı');
      }

      // Kod kontrolü
      if (inputCode.trim() !== verificationData.code) {
        verificationData.attempts++;
        await AsyncStorage.setItem('pending_sms_verification', JSON.stringify(verificationData));
        throw new Error(`Yanlış SMS kodu. Kalan deneme: ${verificationData.maxAttempts - verificationData.attempts}`);
      }

      // Başarılı doğrulama
      await AsyncStorage.removeItem('pending_sms_verification');
      return true;
    } catch (error) {
      console.error('SMS verification error:', error);
      throw error;
    }
  }

  /**
   * SMS 2FA'yı etkinleştir
   */
  async enableSMSTwoFactor(phoneNumber: string): Promise<boolean> {
    try {
      // Önce SMS doğrulaması yap
      const codeSent = await this.sendSMSVerificationCode(phoneNumber);
      if (!codeSent) {
        throw new Error('SMS doğrulama kodu gönderilemedi');
      }

      return true; // Kod gönderildi, kullanıcı doğrulayacak
    } catch (error) {
      console.error('Enable SMS 2FA error:', error);
      return false;
    }
  }

  /**
   * SMS 2FA'yı tamamla (kod doğrulandıktan sonra)
   */
  async completeSMSTwoFactor(): Promise<boolean> {
    try {
      const phoneNumber = await AsyncStorage.getItem('sms_phone_number');
      if (!phoneNumber) {
        throw new Error('Telefon numarası bulunamadı');
      }

      this.twoFactorSettings.smsEnabled = true;
      await this.saveTwoFactorSettings();

      // Telefon numarasını kaydet
      await AsyncStorage.setItem('user_phone_number', phoneNumber);

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/enable-sms-2fa', {
          method: 'POST',
          body: JSON.stringify({ phoneNumber }),
        });
      } catch (apiError) {
        console.log('API SMS 2FA enable failed, saved locally');
      }

      // Geçici verileri temizle
      await AsyncStorage.removeItem('sms_phone_number');

      return true;
    } catch (error) {
      console.error('Complete SMS 2FA error:', error);
      return false;
    }
  }

  /**
   * SMS 2FA'yı devre dışı bırak
   */
  async disableSMSTwoFactor(): Promise<boolean> {
    try {
      this.twoFactorSettings.smsEnabled = false;
      await this.saveTwoFactorSettings();

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/disable-sms-2fa', {
          method: 'POST',
        });
      } catch (apiError) {
        console.log('API SMS 2FA disable failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Disable SMS 2FA error:', error);
      return false;
    }
  }

  /**
   * Telefon numarası formatla
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Sadece rakamları al
    const digits = phoneNumber.replace(/\D/g, '');

    // Türkiye için format kontrolü
    if (digits.startsWith('90')) {
      return `+${digits}`;
    } else if (digits.startsWith('0')) {
      return `+9${digits}`;
    } else if (digits.length === 10) {
      return `+90${digits}`;
    }

    return `+${digits}`;
  }

  /**
   * Telefon numarası geçerliliğini kontrol et
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basit format kontrolü
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Kayıtlı telefon numarasını al
   */
  async getRegisteredPhoneNumber(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('user_phone_number');
    } catch (error) {
      console.error('Get phone number error:', error);
      return null;
    }
  }

  /**
   * Authenticator kurulumu için secret ve QR kod oluştur
   */
  async setupAuthenticator(userEmail: string, appName: string = 'Kişisel Finans'): Promise<AuthenticatorSetup> {
    try {
      // TOTP secret oluştur
      const secret = await this.generateSecret();

      // QR kod URL'i oluştur (Google Authenticator formatı)
      const qrCodeUrl = `otpauth://totp/${encodeURIComponent(appName)}:${encodeURIComponent(userEmail)}?secret=${secret}&issuer=${encodeURIComponent(appName)}`;

      // Manuel giriş için formatlanmış key
      const manualEntryKey = secret.match(/.{1,4}/g)?.join(' ') || secret;

      // Backup kodları oluştur
      const backupCodes = this.generateBackupCodes();

      // Setup bilgilerini geçici olarak sakla
      const setupData: AuthenticatorSetup = {
        secret,
        qrCodeUrl,
        manualEntryKey,
        backupCodes,
      };

      await AsyncStorage.setItem('pending_authenticator_setup', JSON.stringify(setupData));

      return setupData;
    } catch (error) {
      console.error('Authenticator setup error:', error);
      throw new Error('Authenticator kurulumu başarısız oldu');
    }
  }

  /**
   * Authenticator kodunu doğrula
   */
  async verifyAuthenticatorCode(inputCode: string): Promise<boolean> {
    try {
      const setupData = await AsyncStorage.getItem('pending_authenticator_setup');
      if (!setupData) {
        throw new Error('Authenticator kurulum bilgisi bulunamadı');
      }

      const { secret }: AuthenticatorSetup = JSON.parse(setupData);

      // TOTP kodunu doğrula
      const isValid = await this.verifyTOTP(secret, inputCode);

      if (!isValid) {
        throw new Error('Geçersiz authenticator kodu');
      }

      return true;
    } catch (error) {
      console.error('Authenticator verification error:', error);
      throw error;
    }
  }

  /**
   * Authenticator 2FA'yı etkinleştir
   */
  async enableAuthenticatorTwoFactor(): Promise<boolean> {
    try {
      const setupData = await AsyncStorage.getItem('pending_authenticator_setup');
      if (!setupData) {
        throw new Error('Authenticator kurulum bilgisi bulunamadı');
      }

      const { secret, backupCodes }: AuthenticatorSetup = JSON.parse(setupData);

      // Ayarları güncelle
      this.twoFactorSettings.authenticatorEnabled = true;
      await this.saveTwoFactorSettings();

      // Secret'ı güvenli olarak sakla
      await AsyncStorage.setItem('authenticator_secret', secret);
      await AsyncStorage.setItem('backup_codes', JSON.stringify(backupCodes));

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/enable-authenticator-2fa', {
          method: 'POST',
          body: JSON.stringify({ secret }),
        });
      } catch (apiError) {
        console.log('API Authenticator 2FA enable failed, saved locally');
      }

      // Geçici kurulum verilerini temizle
      await AsyncStorage.removeItem('pending_authenticator_setup');

      return true;
    } catch (error) {
      console.error('Enable Authenticator 2FA error:', error);
      return false;
    }
  }

  /**
   * Authenticator 2FA'yı devre dışı bırak
   */
  async disableAuthenticatorTwoFactor(): Promise<boolean> {
    try {
      this.twoFactorSettings.authenticatorEnabled = false;
      await this.saveTwoFactorSettings();

      // Stored secret'ı temizle
      await AsyncStorage.removeItem('authenticator_secret');
      await AsyncStorage.removeItem('backup_codes');

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/disable-authenticator-2fa', {
          method: 'POST',
        });
      } catch (apiError) {
        console.log('API Authenticator 2FA disable failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Disable Authenticator 2FA error:', error);
      return false;
    }
  }

  /**
   * Giriş sırasında authenticator kodunu doğrula
   */
  async verifyAuthenticatorLogin(inputCode: string): Promise<boolean> {
    try {
      const secret = await AsyncStorage.getItem('authenticator_secret');
      if (!secret) {
        throw new Error('Authenticator secret bulunamadı');
      }

      // TOTP kodunu doğrula
      const isValid = await this.verifyTOTP(secret, inputCode);

      if (!isValid) {
        // Backup kod kontrolü
        const isBackupValid = await this.verifyBackupCode(inputCode);
        return isBackupValid;
      }

      return true;
    } catch (error) {
      console.error('Authenticator login verification error:', error);
      return false;
    }
  }

  /**
   * Backup kodları oluştur
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // 8 haneli backup kod oluştur
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Backup kod doğrula
   */
  async verifyBackupCode(inputCode: string): Promise<boolean> {
    try {
      const storedCodes = await AsyncStorage.getItem('backup_codes');
      if (!storedCodes) {
        return false;
      }

      const backupCodes: string[] = JSON.parse(storedCodes);
      const codeIndex = backupCodes.indexOf(inputCode.trim().toUpperCase());

      if (codeIndex === -1) {
        return false;
      }

      // Kullanılan kodu listeden çıkar
      backupCodes.splice(codeIndex, 1);
      await AsyncStorage.setItem('backup_codes', JSON.stringify(backupCodes));

      return true;
    } catch (error) {
      console.error('Backup code verification error:', error);
      return false;
    }
  }

  /**
   * Kalan backup kodlarını al
   */
  async getRemainingBackupCodes(): Promise<string[]> {
    try {
      const storedCodes = await AsyncStorage.getItem('backup_codes');
      return storedCodes ? JSON.parse(storedCodes) : [];
    } catch (error) {
      console.error('Get backup codes error:', error);
      return [];
    }
  }

  /**
   * Yeni backup kodları oluştur
   */
  async regenerateBackupCodes(): Promise<string[]> {
    try {
      const newCodes = this.generateBackupCodes();
      await AsyncStorage.setItem('backup_codes', JSON.stringify(newCodes));

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/regenerate-backup-codes', {
          method: 'POST',
          body: JSON.stringify({ backupCodes: newCodes }),
        });
      } catch (apiError) {
        console.log('API backup codes regenerate failed, saved locally');
      }

      return newCodes;
    } catch (error) {
      console.error('Regenerate backup codes error:', error);
      throw new Error('Backup kodları yenilenemedi');
    }
  }

  /**
   * Authenticator kurulum durumunu iptal et
   */
  async cancelAuthenticatorSetup(): Promise<void> {
    try {
      await AsyncStorage.removeItem('pending_authenticator_setup');
    } catch (error) {
      console.error('Cancel authenticator setup error:', error);
    }
  }

  /**
   * Cihaz parmak izi oluştur
   */
  private async generateDeviceFingerprint(): Promise<string> {
    try {
      const deviceInfo = {
        brand: Device.brand || 'unknown',
        manufacturer: Device.manufacturer || 'unknown',
        modelName: Device.modelName || 'unknown',
        osName: Device.osName || 'unknown',
        osVersion: Device.osVersion || 'unknown',
        platformApiLevel: Device.platformApiLevel || 0,
        deviceYearClass: Device.deviceYearClass || 0,
      };

      // Cihaz bilgilerini string'e çevir
      const deviceString = JSON.stringify(deviceInfo);

      // SHA-256 hash oluştur
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        deviceString
      );

      return hash;
    } catch (error) {
      console.error('Device fingerprint generation error:', error);
      // Fallback: rastgele ID
      const randomBytes = await Crypto.getRandomBytesAsync(16);
      return Array.from(randomBytes).map(b => b.toString(16).padStart(2, '0')).join('');
    }
  }

  /**
   * Mevcut cihaz bilgilerini al
   */
  private async getCurrentDeviceInfo(): Promise<Partial<TrustedDevice>> {
    try {
      const fingerprint = await this.generateDeviceFingerprint();

      return {
        id: fingerprint,
        name: `${Device.brand || 'Unknown'} ${Device.modelName || 'Device'}`,
        deviceInfo: `${Device.osName || 'Unknown'} ${Device.osVersion || ''}`,
        addedAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        isCurrentDevice: true,
      };
    } catch (error) {
      console.error('Get current device info error:', error);
      return {
        id: 'unknown-device',
        name: 'Unknown Device',
        deviceInfo: 'Unknown OS',
        addedAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        isCurrentDevice: true,
      };
    }
  }

  /**
   * Cihazın güvenilir olup olmadığını kontrol et
   */
  async isDeviceTrusted(): Promise<boolean> {
    try {
      const currentDevice = await this.getCurrentDeviceInfo();
      const trustedDevices = await this.getTrustedDevices();

      return trustedDevices.some(device => device.id === currentDevice.id);
    } catch (error) {
      console.error('Check trusted device error:', error);
      return false;
    }
  }

  /**
   * Cihazı güvenilir olarak işaretle
   */
  async trustCurrentDevice(rememberDays: number = 30): Promise<boolean> {
    try {
      const currentDevice = await this.getCurrentDeviceInfo();
      const trustedDevices = await this.getTrustedDevices();

      // Cihaz zaten güvenilir mi kontrol et
      const existingIndex = trustedDevices.findIndex(device => device.id === currentDevice.id);

      const trustedDevice: TrustedDevice = {
        ...currentDevice as TrustedDevice,
        addedAt: existingIndex >= 0 ? trustedDevices[existingIndex].addedAt : new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (rememberDays * 24 * 60 * 60 * 1000)).toISOString(),
      };

      if (existingIndex >= 0) {
        // Mevcut cihazı güncelle
        trustedDevices[existingIndex] = trustedDevice;
      } else {
        // Yeni cihaz ekle
        trustedDevices.push(trustedDevice);
      }

      await this.saveTrustedDevices(trustedDevices);

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/trust-device', {
          method: 'POST',
          body: JSON.stringify({ device: trustedDevice }),
        });
      } catch (apiError) {
        console.log('API trust device failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Trust device error:', error);
      return false;
    }
  }

  /**
   * Güvenilir cihazları al
   */
  async getTrustedDevices(): Promise<TrustedDevice[]> {
    try {
      const stored = await AsyncStorage.getItem('trusted_devices');
      if (!stored) {
        return [];
      }

      const devices: TrustedDevice[] = JSON.parse(stored);

      // Süresi dolmuş cihazları filtrele
      const validDevices = devices.filter(device => {
        if (!device.expiresAt) return true; // Eski cihazlar için
        return new Date(device.expiresAt) > new Date();
      });

      // Eğer filtreleme sonucu değişiklik olduysa kaydet
      if (validDevices.length !== devices.length) {
        await this.saveTrustedDevices(validDevices);
      }

      return validDevices;
    } catch (error) {
      console.error('Get trusted devices error:', error);
      return [];
    }
  }

  /**
   * Güvenilir cihazları kaydet
   */
  private async saveTrustedDevices(devices: TrustedDevice[]): Promise<void> {
    try {
      await AsyncStorage.setItem('trusted_devices', JSON.stringify(devices));
    } catch (error) {
      console.error('Save trusted devices error:', error);
    }
  }

  /**
   * Cihazı güvenilir listesinden çıkar
   */
  async removeTrustedDevice(deviceId: string): Promise<boolean> {
    try {
      const trustedDevices = await this.getTrustedDevices();
      const filteredDevices = trustedDevices.filter(device => device.id !== deviceId);

      await this.saveTrustedDevices(filteredDevices);

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/remove-trusted-device', {
          method: 'POST',
          body: JSON.stringify({ deviceId }),
        });
      } catch (apiError) {
        console.log('API remove trusted device failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Remove trusted device error:', error);
      return false;
    }
  }

  /**
   * Tüm güvenilir cihazları temizle
   */
  async clearAllTrustedDevices(): Promise<boolean> {
    try {
      await AsyncStorage.removeItem('trusted_devices');

      // Backend'e kaydet
      try {
        await ApiService.request('/auth/clear-trusted-devices', {
          method: 'POST',
        });
      } catch (apiError) {
        console.log('API clear trusted devices failed, saved locally');
      }

      return true;
    } catch (error) {
      console.error('Clear trusted devices error:', error);
      return false;
    }
  }

  /**
   * Cihaz güvenlik durumunu güncelle
   */
  async updateDeviceLastUsed(deviceId?: string): Promise<void> {
    try {
      const currentDevice = await this.getCurrentDeviceInfo();
      const targetDeviceId = deviceId || currentDevice.id;

      const trustedDevices = await this.getTrustedDevices();
      const deviceIndex = trustedDevices.findIndex(device => device.id === targetDeviceId);

      if (deviceIndex >= 0) {
        trustedDevices[deviceIndex].lastUsed = new Date().toISOString();
        await this.saveTrustedDevices(trustedDevices);
      }
    } catch (error) {
      console.error('Update device last used error:', error);
    }
  }

  /**
   * Giriş sırasında 2FA kontrolü
   */
  async requiresTwoFactor(email: string): Promise<boolean> {
    // Kullanıcının 2FA ayarlarını kontrol et
    return this.twoFactorSettings.emailEnabled ||
           this.twoFactorSettings.smsEnabled ||
           this.twoFactorSettings.authenticatorEnabled;
  }

  /**
   * Mevcut 2FA ayarlarını al
   */
  getTwoFactorSettings(): TwoFactorSettings {
    return { ...this.twoFactorSettings };
  }

  /**
   * 2FA durumunu kontrol et
   */
  async getTwoFactorStatus(): Promise<{
    isEnabled: boolean;
    enabledMethods: string[];
    availableMethods: string[];
  }> {
    const enabledMethods: string[] = [];
    const availableMethods = ['email', 'sms', 'authenticator'];

    if (this.twoFactorSettings.emailEnabled) enabledMethods.push('email');
    if (this.twoFactorSettings.smsEnabled) enabledMethods.push('sms');
    if (this.twoFactorSettings.authenticatorEnabled) enabledMethods.push('authenticator');

    return {
      isEnabled: enabledMethods.length > 0,
      enabledMethods,
      availableMethods,
    };
  }

  /**
   * Bekleyen doğrulama kodunu iptal et
   */
  async cancelPendingVerification(type: 'email' | 'sms'): Promise<void> {
    try {
      await AsyncStorage.removeItem(`pending_${type}_verification`);
    } catch (error) {
      console.error('Cancel verification error:', error);
    }
  }

  /**
   * Doğrulama kodu durumunu al
   */
  async getVerificationStatus(type: 'email' | 'sms'): Promise<{
    hasPending: boolean;
    expiresAt?: number;
    attemptsLeft?: number;
  }> {
    try {
      const stored = await AsyncStorage.getItem(`pending_${type}_verification`);
      if (!stored) {
        return { hasPending: false };
      }

      const verificationData: VerificationCode = JSON.parse(stored);

      // Süresi dolmuşsa temizle
      if (Date.now() > verificationData.expiresAt) {
        await AsyncStorage.removeItem(`pending_${type}_verification`);
        return { hasPending: false };
      }

      return {
        hasPending: true,
        expiresAt: verificationData.expiresAt,
        attemptsLeft: verificationData.maxAttempts - verificationData.attempts,
      };
    } catch (error) {
      console.error('Get verification status error:', error);
      return { hasPending: false };
    }
  }
}

export default TwoFactorService;
