// Database Schema - SQLite tablo yapıları

// Database version for migrations
export const DATABASE_VERSION = 18;
export const DATABASE_NAME = 'finance_app.db';

// SQL table creation statements
export const CREATE_TABLES = {
  // Users table
  users: `
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      avatar_url TEXT,
      phone TEXT,
      date_of_birth TEXT,
      gender TEXT CHECK (gender IN ('male', 'female', 'other')),
      occupation TEXT,
      monthly_income REAL,
      financial_goals TEXT, -- JSON array
      risk_tolerance TEXT CHECK (risk_tolerance IN ('low', 'medium', 'high')),
      investment_experience TEXT CHECK (investment_experience IN ('beginner', 'intermediate', 'advanced')),
      preferred_currency TEXT DEFAULT 'TRY',
      preferred_language TEXT DEFAULT 'tr',
      timezone TEXT DEFAULT 'Europe/Istanbul',
      notification_preferences TEXT, -- JSON object
      privacy_settings TEXT, -- JSON object
      security_settings TEXT, -- JSON object
      subscription_plan TEXT DEFAULT 'free',
      subscription_expires_at TEXT,
      is_active INTEGER DEFAULT 1,
      is_verified INTEGER DEFAULT 0,
      email_verified_at TEXT,
      phone_verified_at TEXT,
      last_login_at TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // Transactions table
  transactions: `
    CREATE TABLE IF NOT EXISTS transactions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      amount REAL NOT NULL CHECK (amount > 0),
      currency TEXT NOT NULL DEFAULT 'TRY',
      category TEXT NOT NULL,
      subcategory TEXT,
      description TEXT,
      merchant TEXT,
      payment_method TEXT,
      date TEXT NOT NULL,
      budget_id TEXT,
      location_latitude REAL,
      location_longitude REAL,
      location_address TEXT,
      location_place_name TEXT,
      receipt_id TEXT,
      parent_transaction_id TEXT,
      recurrence_frequency TEXT,
      recurrence_interval INTEGER,
      recurrence_end_date TEXT,
      status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('completed', 'pending', 'cancelled', 'failed')),
      is_business_expense INTEGER DEFAULT 0,
      is_deductible INTEGER DEFAULT 0,
      tax_amount REAL DEFAULT 0,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      synced_at TEXT,
      is_deleted INTEGER DEFAULT 0,
      deleted_at TEXT,
      FOREIGN KEY (receipt_id) REFERENCES receipts(id),
      FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE SET NULL
    );
  `,

  // Categories table (custom categories)
  categories: `
    CREATE TABLE IF NOT EXISTS categories (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      parent_category TEXT,
      icon TEXT NOT NULL,
      color TEXT NOT NULL,
      description TEXT,
      sort_order INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      UNIQUE(user_id, name, type)
    );
  `,

  // Budgets table - RFC-003 Ana Bütçe Yapısı
  budgets: `
    CREATE TABLE IF NOT EXISTS budgets (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'quarterly', 'annually')),
      start_date TEXT NOT NULL,
      end_date TEXT NOT NULL,
      total_income_target REAL DEFAULT 0 CHECK (total_income_target >= 0),
      total_expense_limit REAL DEFAULT 0 CHECK (total_expense_limit >= 0),
      savings_target REAL DEFAULT 0 CHECK (savings_target >= 0),
      currency TEXT NOT NULL DEFAULT 'TRY',
      notes TEXT,
      template_name TEXT, -- Şablon bütçe adı
      copied_from_budget_id TEXT, -- Kopyalandığı bütçe ID'si
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // Budget Categories table - RFC-003 Kategori Bazlı Bütçe Yapısı
  budget_categories: `
    CREATE TABLE IF NOT EXISTS budget_categories (
      id TEXT PRIMARY KEY,
      budget_id TEXT NOT NULL,
      category_id TEXT NOT NULL,
      planned_amount REAL NOT NULL CHECK (planned_amount > 0),
      spent_amount REAL DEFAULT 0,
      remaining_amount REAL GENERATED ALWAYS AS (planned_amount - spent_amount) STORED,
      warning_threshold REAL DEFAULT 50 CHECK (warning_threshold BETWEEN 0 AND 100),
      critical_threshold REAL DEFAULT 80 CHECK (critical_threshold BETWEEN 0 AND 100),
      limit_threshold REAL DEFAULT 100 CHECK (limit_threshold BETWEEN 0 AND 200),
      warning_enabled INTEGER DEFAULT 1,
      critical_enabled INTEGER DEFAULT 1,
      limit_enabled INTEGER DEFAULT 1,
      daily_digest_enabled INTEGER DEFAULT 0,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
      FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
      UNIQUE(budget_id, category_id)
    );
  `,

  // Budget Alerts table - RFC-003 Bütçe Uyarı Sistemi
  budget_alerts: `
    CREATE TABLE IF NOT EXISTS budget_alerts (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      budget_id TEXT NOT NULL,
      budget_category_id TEXT,
      type TEXT NOT NULL CHECK (type IN ('warning', 'critical', 'limit', 'income_exceeded')),
      title TEXT NOT NULL,
      message TEXT NOT NULL,
      percentage REAL NOT NULL,
      amount REAL NOT NULL,
      is_read INTEGER DEFAULT 0,
      created_at TEXT NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
      FOREIGN KEY (budget_category_id) REFERENCES budget_categories(id) ON DELETE CASCADE
    );
  `,

  // Category rules for auto-classification
  category_rules: `
    CREATE TABLE IF NOT EXISTS category_rules (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      category TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      merchant_names TEXT, -- JSON array
      description_keywords TEXT, -- JSON array
      amount_min REAL,
      amount_max REAL,
      payment_methods TEXT, -- JSON array
      priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10),
      confidence REAL DEFAULT 0.8 CHECK (confidence BETWEEN 0 AND 1),
      is_active INTEGER DEFAULT 1,
      auto_apply INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // Receipts table
  receipts: `
    CREATE TABLE IF NOT EXISTS receipts (
      id TEXT PRIMARY KEY,
      transaction_id TEXT,
      image_uri TEXT NOT NULL,
      thumbnail_uri TEXT,
      file_size INTEGER,
      merchant_name TEXT,
      total_amount REAL,
      receipt_date TEXT,
      ocr_confidence REAL,
      ocr_data TEXT, -- JSON
      uploaded_at TEXT NOT NULL
    );
  `,

  // Tags table
  tags: `
    CREATE TABLE IF NOT EXISTS tags (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      color TEXT,
      icon TEXT,
      created_at TEXT NOT NULL,
      UNIQUE(user_id, name)
    );
  `,

  // Transaction tags junction table
  transaction_tags: `
    CREATE TABLE IF NOT EXISTS transaction_tags (
      transaction_id TEXT NOT NULL,
      tag_id TEXT NOT NULL,
      PRIMARY KEY (transaction_id, tag_id),
      FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
      FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
    );
  `,

  // Goals table
  goals: `
    CREATE TABLE IF NOT EXISTS goals (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('savings', 'debt_reduction', 'income_increase', 'expense_reduction')),
      target_amount REAL NOT NULL CHECK (target_amount > 0),
      current_amount REAL DEFAULT 0,
      currency TEXT NOT NULL DEFAULT 'TRY',
      deadline TEXT NOT NULL,
      category TEXT,
      description TEXT,
      is_achieved INTEGER DEFAULT 0,
      achieved_at TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // Recurring templates table
  recurring_templates: `
    CREATE TABLE IF NOT EXISTS recurring_templates (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      name TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
      amount REAL NOT NULL CHECK (amount > 0),
      currency TEXT NOT NULL DEFAULT 'TRY',
      category TEXT NOT NULL,
      subcategory TEXT,
      description TEXT,
      payment_method TEXT,
      frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'bi_weekly', 'monthly', 'quarterly', 'semi_annually', 'annually')),
      interval_value INTEGER DEFAULT 1,
      next_execution_date TEXT NOT NULL,
      last_execution_date TEXT,
      end_date TEXT,
      is_active INTEGER DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // Exchange rates table
  exchange_rates: `
    CREATE TABLE IF NOT EXISTS exchange_rates (
      id TEXT PRIMARY KEY,
      base_currency TEXT NOT NULL,
      target_currency TEXT NOT NULL,
      rate REAL NOT NULL,
      timestamp TEXT NOT NULL,
      source TEXT NOT NULL,
      bid REAL,
      ask REAL,
      UNIQUE(base_currency, target_currency, timestamp)
    );
  `,

  // Sync records table
  sync_records: `
    CREATE TABLE IF NOT EXISTS sync_records (
      id TEXT PRIMARY KEY,
      data_type TEXT NOT NULL,
      data_id TEXT NOT NULL,
      operation TEXT NOT NULL CHECK (operation IN ('create', 'update', 'delete', 'restore')),
      local_timestamp TEXT NOT NULL,
      remote_timestamp TEXT,
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'syncing', 'success', 'error', 'conflict')),
      retry_count INTEGER DEFAULT 0,
      last_retry TEXT,
      error_message TEXT,
      conflict_data TEXT, -- JSON
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    );
  `,

  // User settings table
  user_settings: `
    CREATE TABLE IF NOT EXISTS user_settings (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      setting_key TEXT NOT NULL,
      setting_value TEXT NOT NULL,
      data_type TEXT NOT NULL DEFAULT 'string' CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      UNIQUE(user_id, setting_key)
    );
  `,
};

// Indexes for better performance
export const CREATE_INDEXES = {
  transactions: [
    'CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_budget_id ON transactions(budget_id);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, date);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_user_category ON transactions(user_id, category);',
    'CREATE INDEX IF NOT EXISTS idx_transactions_deleted ON transactions(is_deleted);',
  ],
  categories: [
    'CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);',
    'CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active);',
  ],
  budgets: [
    'CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(start_date, end_date);',
    'CREATE INDEX IF NOT EXISTS idx_budgets_active ON budgets(is_active);',
    'CREATE INDEX IF NOT EXISTS idx_budgets_template ON budgets(template_name);',
  ],
  budget_categories: [
    'CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_id ON budget_categories(budget_id);',
    'CREATE INDEX IF NOT EXISTS idx_budget_categories_category_id ON budget_categories(category_id);',
    'CREATE INDEX IF NOT EXISTS idx_budget_categories_active ON budget_categories(is_active);',
    'CREATE INDEX IF NOT EXISTS idx_budget_categories_thresholds ON budget_categories(warning_threshold, critical_threshold);',
  ],
  category_rules: [
    'CREATE INDEX IF NOT EXISTS idx_category_rules_user_id ON category_rules(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_category_rules_active ON category_rules(is_active);',
    'CREATE INDEX IF NOT EXISTS idx_category_rules_priority ON category_rules(priority);',
  ],
  receipts: [
    'CREATE INDEX IF NOT EXISTS idx_receipts_transaction_id ON receipts(transaction_id);',
    'CREATE INDEX IF NOT EXISTS idx_receipts_uploaded_at ON receipts(uploaded_at);',
  ],
  tags: [
    'CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags(user_id);',
  ],
  goals: [
    'CREATE INDEX IF NOT EXISTS idx_goals_user_id ON goals(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_goals_type ON goals(type);',
    'CREATE INDEX IF NOT EXISTS idx_goals_active ON goals(is_active);',
    'CREATE INDEX IF NOT EXISTS idx_goals_deadline ON goals(deadline);',
  ],
  recurring_templates: [
    'CREATE INDEX IF NOT EXISTS idx_recurring_templates_user_id ON recurring_templates(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_recurring_templates_active ON recurring_templates(is_active);',
    'CREATE INDEX IF NOT EXISTS idx_recurring_templates_next_execution ON recurring_templates(next_execution_date);',
  ],
  exchange_rates: [
    'CREATE INDEX IF NOT EXISTS idx_exchange_rates_currencies ON exchange_rates(base_currency, target_currency);',
    'CREATE INDEX IF NOT EXISTS idx_exchange_rates_timestamp ON exchange_rates(timestamp);',
  ],
  sync_records: [
    'CREATE INDEX IF NOT EXISTS idx_sync_records_data_type ON sync_records(data_type);',
    'CREATE INDEX IF NOT EXISTS idx_sync_records_status ON sync_records(status);',
    'CREATE INDEX IF NOT EXISTS idx_sync_records_data_id ON sync_records(data_id);',
  ],
  user_settings: [
    'CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_user_settings_key ON user_settings(setting_key);',
  ],
};

// Default data to insert
export const DEFAULT_DATA = {
  // Default categories will be inserted if not exists
  categories: [
    // Income categories
    { name: 'Maaş', type: 'income', code: 'salary', icon: 'briefcase-outline', color: '#4CAF50' },
    { name: 'Yatırım Geliri', type: 'income', code: 'investment_income', icon: 'trending-up-outline', color: '#2196F3' },
    { name: 'Yan Gelir', type: 'income', code: 'side_income', icon: 'cash-outline', color: '#FF9800' },
    { name: 'Freelance', type: 'income', code: 'freelance', icon: 'laptop-outline', color: '#9C27B0' },

    // Expense categories
    { name: 'Yiyecek & İçecek', type: 'expense', code: 'food', icon: 'restaurant-outline', color: '#F44336' },
    { name: 'Ulaşım', type: 'expense', code: 'transportation', icon: 'car-outline', color: '#607D8B' },
    { name: 'Faturalar', type: 'expense', code: 'bills', icon: 'receipt-outline', color: '#795548' },
    { name: 'Alışveriş', type: 'expense', code: 'shopping', icon: 'bag-outline', color: '#E91E63' },
    { name: 'Eğlence', type: 'expense', code: 'entertainment', icon: 'game-controller-outline', color: '#673AB7' },
    { name: 'Sağlık', type: 'expense', code: 'health', icon: 'medical-outline', color: '#009688' },
    { name: 'Eğitim', type: 'expense', code: 'education', icon: 'school-outline', color: '#3F51B5' },
    { name: 'Kira', type: 'expense', code: 'rent', icon: 'home-outline', color: '#795548' },
    { name: 'Sigorta', type: 'expense', code: 'insurance', icon: 'shield-outline', color: '#607D8B' },
    { name: 'Kişisel Bakım', type: 'expense', code: 'personal_care', icon: 'person-outline', color: '#E91E63' },
    { name: 'Hediyeler', type: 'expense', code: 'gifts', icon: 'gift-outline', color: '#FF9800' },
    { name: 'Seyahat', type: 'expense', code: 'travel', icon: 'airplane-outline', color: '#00BCD4' },
    { name: 'Bağış', type: 'expense', code: 'donation', icon: 'heart-outline', color: '#4CAF50' },
    { name: 'Vergiler', type: 'expense', code: 'tax', icon: 'document-text-outline', color: '#9E9E9E' },
    { name: 'Konut', type: 'expense', code: 'housing', icon: 'business-outline', color: '#795548' },
    { name: 'Diğer', type: 'expense', code: 'other', icon: 'ellipsis-horizontal-outline', color: '#9E9E9E' },
  ],

  // Default settings
  settings: [
    { key: 'default_currency', value: 'TRY', type: 'string' },
    { key: 'budget_alert_enabled', value: 'true', type: 'boolean' },
    { key: 'auto_categorization_enabled', value: 'true', type: 'boolean' },
    { key: 'sync_enabled', value: 'false', type: 'boolean' },
    { key: 'backup_enabled', value: 'true', type: 'boolean' },
  ],
};

// Migration scripts for future versions
export const MIGRATIONS = {
  1: {
    up: [
      // Version 1 initial schema - tables will be created by CREATE_TABLES
    ],
    down: [
      // Rollback scripts if needed
    ],
  },
  2: {
    up: [
      // Remove foreign key constraint from receipts table
      `DROP TABLE IF EXISTS receipts;`,
      `CREATE TABLE receipts (
        id TEXT PRIMARY KEY,
        transaction_id TEXT,
        image_uri TEXT NOT NULL,
        thumbnail_uri TEXT,
        file_size INTEGER,
        merchant_name TEXT,
        total_amount REAL,
        receipt_date TEXT,
        ocr_confidence REAL,
        ocr_data TEXT,
        uploaded_at TEXT NOT NULL
      );`,
      `CREATE INDEX IF NOT EXISTS idx_receipts_transaction_id ON receipts(transaction_id);`,
      `CREATE INDEX IF NOT EXISTS idx_receipts_uploaded_at ON receipts(uploaded_at);`,
    ],
    down: [
      // Rollback to version 1 if needed
    ],
  },
  3: {
    up: [
      // RFC-003 Bütçe Planlama - Database Schema Upgrade
      // Basit yaklaşım: Sadece yeni tabloları oluştur

      // Drop old budgets table (if exists)
      `DROP TABLE IF EXISTS budgets;`,
      `DROP TABLE IF EXISTS budget_categories;`,

      // Create new budgets table with RFC-003 structure
      `CREATE TABLE budgets (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'quarterly', 'annually')),
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        total_income_target REAL DEFAULT 0 CHECK (total_income_target >= 0),
        total_expense_limit REAL DEFAULT 0 CHECK (total_expense_limit >= 0),
        savings_target REAL DEFAULT 0 CHECK (savings_target >= 0),
        currency TEXT NOT NULL DEFAULT 'TRY',
        notes TEXT,
        template_name TEXT,
        copied_from_budget_id TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );`,

      // Create budget_categories table
      `CREATE TABLE budget_categories (
        id TEXT PRIMARY KEY,
        budget_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        planned_amount REAL NOT NULL CHECK (planned_amount > 0),
        spent_amount REAL DEFAULT 0,
        remaining_amount REAL GENERATED ALWAYS AS (planned_amount - spent_amount) STORED,
        warning_threshold REAL DEFAULT 50 CHECK (warning_threshold BETWEEN 0 AND 100),
        critical_threshold REAL DEFAULT 80 CHECK (critical_threshold BETWEEN 0 AND 100),
        limit_threshold REAL DEFAULT 100 CHECK (limit_threshold BETWEEN 0 AND 200),
        warning_enabled INTEGER DEFAULT 1,
        critical_enabled INTEGER DEFAULT 1,
        limit_enabled INTEGER DEFAULT 1,
        daily_digest_enabled INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
        UNIQUE(budget_id, category_id)
      );`,

      // Create indexes
      `CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);`,
      `CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(start_date, end_date);`,
      `CREATE INDEX IF NOT EXISTS idx_budgets_active ON budgets(is_active);`,
      `CREATE INDEX IF NOT EXISTS idx_budgets_template ON budgets(template_name);`,
      `CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_id ON budget_categories(budget_id);`,
      `CREATE INDEX IF NOT EXISTS idx_budget_categories_category_id ON budget_categories(category_id);`,
      `CREATE INDEX IF NOT EXISTS idx_budget_categories_active ON budget_categories(is_active);`,
      `CREATE INDEX IF NOT EXISTS idx_budget_categories_thresholds ON budget_categories(warning_threshold, critical_threshold);`,

      // Drop old budget triggers
      `DROP TRIGGER IF EXISTS update_budget_spent_amount_insert;`,
      `DROP TRIGGER IF EXISTS update_budget_spent_amount_update;`,
    ],
    down: [
      // Rollback to version 2 if needed
      `DROP TABLE IF EXISTS budget_categories;`,
      `DROP INDEX IF EXISTS idx_budget_categories_budget_id;`,
      `DROP INDEX IF EXISTS idx_budget_categories_category_id;`,
      `DROP INDEX IF EXISTS idx_budget_categories_active;`,
      `DROP INDEX IF EXISTS idx_budget_categories_thresholds;`,
    ],
  },
  4: {
    up: [
      // Hibrit Bütçe Sistemi - budget_id kolonu ekleme
      // Not: Bu migration artık gerekli değil çünkü CREATE_TABLES'da budget_id zaten var

      // budget_id için index oluştur (kolon zaten var)
      `CREATE INDEX IF NOT EXISTS idx_transactions_budget_id ON transactions(budget_id);`,

      // Eski trigger'ları sil
      `DROP TRIGGER IF EXISTS update_budget_category_spent_insert;`,
      `DROP TRIGGER IF EXISTS update_budget_category_spent_update;`,

      // Yeni trigger'ları oluştur (budget_id bazlı) - HİBRİT SİSTEM
      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_insert
       AFTER INSERT ON transactions
       WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL
       BEGIN
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id;
       END;`,

      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_update
       AFTER UPDATE ON transactions
       WHEN (NEW.type = 'expense' OR OLD.type = 'expense') AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount OR NEW.category != OLD.category OR NEW.budget_id != OLD.budget_id)
       BEGIN
         -- Update old budget if budget_id changed
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = OLD.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = OLD.budget_id AND OLD.budget_id IS NOT NULL;

         -- Update new budget
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id AND NEW.budget_id IS NOT NULL;
       END;`,
    ],
    down: [
      // Rollback to version 3 if needed
      `ALTER TABLE transactions DROP COLUMN budget_id;`,
      `DROP INDEX IF EXISTS idx_transactions_budget_id;`,
    ],
  },

  // Migration V5: Hibrit Bütçe-İşlem Sistemi - category_id ekleme
  5: {
    up: [
      // Add category_id column to transactions table for hybrid budget-transaction system
      `ALTER TABLE transactions ADD COLUMN category_id TEXT;`,

      // Create index for better performance
      `CREATE INDEX IF NOT EXISTS idx_transactions_category_id ON transactions(category_id);`,

      // Create index for hybrid budget matching (category_id + date + user_id)
      `CREATE INDEX IF NOT EXISTS idx_transactions_hybrid_match ON transactions(user_id, category_id, date);`,

      // Update existing transactions to populate category_id based on category string
      // This will be handled in application logic during migration
    ],
    down: [
      // Remove the added indexes
      `DROP INDEX IF EXISTS idx_transactions_category_id;`,
      `DROP INDEX IF EXISTS idx_transactions_hybrid_match;`,
      // Note: SQLite doesn't support DROP COLUMN, so we'd need to recreate table for full rollback
    ],
  },

  // Migration V6: Hibrit Sistem Trigger Düzeltmeleri
  6: {
    up: [
      // Drop old triggers
      `DROP TRIGGER IF EXISTS update_budget_category_spent_insert;`,
      `DROP TRIGGER IF EXISTS update_budget_category_spent_update;`,

      // Create new hybrid system triggers
      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_insert
       AFTER INSERT ON transactions
       WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL
       BEGIN
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id;
       END;`,

      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_update
       AFTER UPDATE ON transactions
       WHEN (NEW.type = 'expense' OR OLD.type = 'expense') AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount OR NEW.category != OLD.category OR NEW.budget_id != OLD.budget_id)
       BEGIN
         -- Update old budget if budget_id changed
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = OLD.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = OLD.budget_id AND OLD.budget_id IS NOT NULL;

         -- Update new budget
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id AND NEW.budget_id IS NOT NULL;
       END;`,
    ],
    down: [
      // Rollback to old triggers if needed
    ],
  },

  // Migration V7: Force Trigger Update (V6 tekrarı)
  7: {
    up: [
      // Force drop and recreate triggers to ensure they work
      `DROP TRIGGER IF EXISTS update_budget_category_spent_insert;`,
      `DROP TRIGGER IF EXISTS update_budget_category_spent_update;`,

      // Create hybrid system triggers with debug logging
      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_insert
       AFTER INSERT ON transactions
       WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL
       BEGIN
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id;
       END;`,

      `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_update
       AFTER UPDATE ON transactions
       WHEN (NEW.type = 'expense' OR OLD.type = 'expense') AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount OR NEW.category != OLD.category OR NEW.budget_id != OLD.budget_id)
       BEGIN
         -- Update old budget if budget_id changed
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = OLD.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = OLD.budget_id AND OLD.budget_id IS NOT NULL;

         -- Update new budget
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(t.amount), 0)
           FROM transactions t
           WHERE t.budget_id = NEW.budget_id
             AND (t.category_id = budget_categories.category_id OR
                  (t.category_id IS NULL AND t.category = (
                    SELECT name FROM categories WHERE id = budget_categories.category_id
                  )))
             AND t.type = 'expense'
             AND t.is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id AND NEW.budget_id IS NOT NULL;
       END;`,
    ],
    down: [
      // Rollback to old triggers if needed
    ],
  },

  // Migration V8: Force Database Update to V8
  8: {
    up: [
      // Force database to V8 to ensure all migrations run
      `PRAGMA user_version = 8;`,
    ],
    down: [
      // Rollback if needed
    ],
  },

  // Migration V9: Temiz Schema - Sadece category_id kullan
  9: {
    up: [
      // 1. Mevcut işlemlerde category_id'yi populate et
      `UPDATE transactions
       SET category_id = (
         SELECT id FROM categories
         WHERE name = transactions.category
         AND type = 'expense'
         LIMIT 1
       )
       WHERE category_id IS NULL AND type = 'expense';`,

      // 2. Drop old complex triggers
      `DROP TRIGGER IF EXISTS update_budget_category_spent_insert;`,
      `DROP TRIGGER IF EXISTS update_budget_category_spent_update;`,

      // 3. Create simple, clean triggers
      `CREATE TRIGGER IF NOT EXISTS update_budget_spent_insert
       AFTER INSERT ON transactions
       WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL
       BEGIN
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(amount), 0)
           FROM transactions
           WHERE budget_id = NEW.budget_id
             AND category_id = budget_categories.category_id
             AND type = 'expense'
             AND is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id;
       END;`,

      `CREATE TRIGGER IF NOT EXISTS update_budget_spent_update
       AFTER UPDATE ON transactions
       WHEN (NEW.type = 'expense' OR OLD.type = 'expense')
            AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount
                 OR NEW.category_id != OLD.category_id OR NEW.budget_id != OLD.budget_id)
       BEGIN
         -- Update old budget category if changed
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(amount), 0)
           FROM transactions
           WHERE budget_id = OLD.budget_id
             AND category_id = budget_categories.category_id
             AND type = 'expense'
             AND is_deleted = 0
         )
         WHERE budget_id = OLD.budget_id AND category_id = OLD.category_id
               AND OLD.budget_id IS NOT NULL AND OLD.category_id IS NOT NULL;

         -- Update new budget category
         UPDATE budget_categories
         SET spent_amount = (
           SELECT COALESCE(SUM(amount), 0)
           FROM transactions
           WHERE budget_id = NEW.budget_id
             AND category_id = budget_categories.category_id
             AND type = 'expense'
             AND is_deleted = 0
         )
         WHERE budget_id = NEW.budget_id AND category_id = NEW.category_id
               AND NEW.budget_id IS NOT NULL AND NEW.category_id IS NOT NULL;
       END;`,
    ],
    down: [
      // Rollback if needed
    ],
  },

  // Migration V10: Users tablosu ekle
  10: {
    up: [
      // Users tablosu zaten CREATE_TABLES'da tanımlı, sadece migration marker
      `SELECT 1;`, // Dummy query
    ],
    down: [
      `DROP TABLE IF EXISTS users;`,
    ],
  },

  // Migration V11: Categories Code Sütunu - RFC-003 Kategori Senkronizasyonu (DISABLED)
  11: {
    up: async (db) => {
      // V11 artık hiçbir şey yapmıyor - V13'te tüm işlemler yapılacak
      console.log('⏭️ Migration V11: Skipped - All operations moved to V13');
    },
    down: async (db) => {
      console.log('⏭️ Migration V11 down: Skipped');
    },
  },

  // Migration V12: Categories Code Sütunu Düzeltme - RFC-003 UNIQUE Constraint Fix (DISABLED)
  12: {
    up: async (db) => {
      // V12 artık hiçbir şey yapmıyor - V13'te tüm işlemler yapılacak
      console.log('⏭️ Migration V12: Skipped - All operations moved to V13');
    },
    down: async (db) => {
      console.log('⏭️ Migration V12 down: Skipped');
    },
  },

  // Migration V13: Categories Code Sütunu Final Fix - RFC-003 Modular Implementation
  13: {
    up: async (db) => {
      // Import migration utility
      const { migrationV13 } = await import('./migrationUtils');
      await migrationV13(db);
    },
    down: async (db) => {
      await db.execAsync(`DROP INDEX IF EXISTS idx_categories_code;`);
    }
  },

  // Migration V14: Income Categories Fix - RFC-003 Income Category Addition
  14: {
    up: async (db) => {
      // Import migration utility
      const { migrationV13 } = await import('./migrationUtils');
      await migrationV13(db);
    },
    down: async (db) => {
      console.log('⏭️ Migration V14 down: No rollback needed');
    }
  },

  // Migration V15: Complete Category Sync - RFC-003 Full Category Implementation (SKIPPED)
  15: {
    up: async (db) => {
      console.log('⏭️ Migration V15: SKIPPED due to UNIQUE constraint issues');
      // Skip this migration to avoid UNIQUE constraint errors
    },
    down: async (db) => {
      console.log('⏭️ Migration V15 down: No rollback needed');
    }
  },

  // Migration V16: Safe Category Addition - UNIQUE Constraint Fix
  16: {
    up: async (db) => {
      console.log('🛡️ Migration V16: Safe category addition...');
      // Import migration utility
      const { migrationV16 } = await import('./migrationUtils');
      await migrationV16(db);
    },
    down: async (db) => {
      console.log('⏭️ Migration V16 down: No rollback needed');
    }
  },

  // Migration V17: Income Hybrid System - Gelir-Bütçe Entegrasyonu
  17: {
    up: async (db) => {
      console.log('💰 Migration V17: Income Hybrid System...');
      // Import migration utility
      const { IncomeHybridMigration } = await import('./migrations/IncomeHybridMigration');
      await IncomeHybridMigration.migrate(db);
    },
    down: async (db) => {
      console.log('🔄 Migration V17 down: Rolling back Income Hybrid System...');
      const { IncomeHybridMigration } = await import('./migrations/IncomeHybridMigration');
      await IncomeHybridMigration.rollback(db);
    }
  },

  // Migration V17: Force Category Addition - Final Fix
  17: {
    up: async (db) => {
      console.log('🚀 Migration V17: Force category addition...');
      // Import migration utility
      const { migrationV16 } = await import('./migrationUtils');
      await migrationV16(db);
    },
    down: async (db) => {
      console.log('⏭️ Migration V17 down: No rollback needed');
    }
  },

  // Migration V18: Budget Alerts System
  18: {
    up: async (db) => {
      console.log('🚨 Migration V18: Adding budget alerts system...');

      // Create budget_alerts table
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS budget_alerts (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          budget_id TEXT NOT NULL,
          budget_category_id TEXT,
          type TEXT NOT NULL CHECK (type IN ('warning', 'critical', 'limit', 'income_exceeded')),
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          percentage REAL NOT NULL,
          amount REAL NOT NULL,
          is_read INTEGER DEFAULT 0,
          created_at TEXT NOT NULL,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
          FOREIGN KEY (budget_category_id) REFERENCES budget_categories(id) ON DELETE CASCADE
        );
      `);

      // Create indexes for budget_alerts
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_budget_alerts_user_id ON budget_alerts(user_id);`);
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_budget_alerts_budget_id ON budget_alerts(budget_id);`);
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_budget_alerts_type ON budget_alerts(type);`);
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_budget_alerts_read ON budget_alerts(is_read);`);
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_budget_alerts_created ON budget_alerts(created_at);`);

      console.log('✅ Migration V18: Budget alerts system added successfully');
    },
    down: async (db) => {
      console.log('🔄 Migration V18 rollback: Removing budget alerts system...');
      await db.execAsync('DROP TABLE IF EXISTS budget_alerts;');
      console.log('✅ Migration V18 rollback completed');
    },
  },
};

// Database constraints and triggers
export const CREATE_TRIGGERS = [
  // Update updated_at timestamp automatically
  `CREATE TRIGGER IF NOT EXISTS update_transactions_timestamp
   AFTER UPDATE ON transactions
   BEGIN
     UPDATE transactions SET updated_at = datetime('now') WHERE id = NEW.id;
   END;`,

  `CREATE TRIGGER IF NOT EXISTS update_categories_timestamp
   AFTER UPDATE ON categories
   BEGIN
     UPDATE categories SET updated_at = datetime('now') WHERE id = NEW.id;
   END;`,

  `CREATE TRIGGER IF NOT EXISTS update_budgets_timestamp
   AFTER UPDATE ON budgets
   BEGIN
     UPDATE budgets SET updated_at = datetime('now') WHERE id = NEW.id;
   END;`,

  `CREATE TRIGGER IF NOT EXISTS update_budget_categories_timestamp
   AFTER UPDATE ON budget_categories
   BEGIN
     UPDATE budget_categories SET updated_at = datetime('now') WHERE id = NEW.id;
   END;`,

  // Update budget category spent amount when transaction is added/updated/deleted - HİBRİT SİSTEM
  `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_insert
   AFTER INSERT ON transactions
   WHEN NEW.type = 'expense' AND NEW.is_deleted = 0 AND NEW.budget_id IS NOT NULL
   BEGIN
     UPDATE budget_categories
     SET spent_amount = (
       SELECT COALESCE(SUM(t.amount), 0)
       FROM transactions t
       WHERE t.budget_id = NEW.budget_id
         AND (t.category_id = budget_categories.category_id OR
              (t.category_id IS NULL AND t.category = (
                SELECT name FROM categories WHERE id = budget_categories.category_id
              )))
         AND t.type = 'expense'
         AND t.is_deleted = 0
     )
     WHERE budget_id = NEW.budget_id;
   END;`,

  `CREATE TRIGGER IF NOT EXISTS update_budget_category_spent_update
   AFTER UPDATE ON transactions
   WHEN (NEW.type = 'expense' OR OLD.type = 'expense') AND (NEW.is_deleted != OLD.is_deleted OR NEW.amount != OLD.amount OR NEW.category != OLD.category OR NEW.budget_id != OLD.budget_id)
   BEGIN
     -- Update old budget if budget_id changed
     UPDATE budget_categories
     SET spent_amount = (
       SELECT COALESCE(SUM(t.amount), 0)
       FROM transactions t
       WHERE t.budget_id = OLD.budget_id
         AND (t.category_id = budget_categories.category_id OR
              (t.category_id IS NULL AND t.category = (
                SELECT name FROM categories WHERE id = budget_categories.category_id
              )))
         AND t.type = 'expense'
         AND t.is_deleted = 0
     )
     WHERE budget_id = OLD.budget_id AND OLD.budget_id IS NOT NULL;

     -- Update new budget
     UPDATE budget_categories
     SET spent_amount = (
       SELECT COALESCE(SUM(t.amount), 0)
       FROM transactions t
       WHERE t.budget_id = NEW.budget_id
         AND (t.category_id = budget_categories.category_id OR
              (t.category_id IS NULL AND t.category = (
                SELECT name FROM categories WHERE id = budget_categories.category_id
              )))
         AND t.type = 'expense'
         AND t.is_deleted = 0
     )
     WHERE budget_id = NEW.budget_id AND NEW.budget_id IS NOT NULL;
   END;`,

  // Soft delete cascade for transaction tags
  `CREATE TRIGGER IF NOT EXISTS soft_delete_transaction_tags
   AFTER UPDATE ON transactions
   WHEN NEW.is_deleted = 1 AND OLD.is_deleted = 0
   BEGIN
     DELETE FROM transaction_tags WHERE transaction_id = NEW.id;
   END;`,
];

export default {
  DATABASE_VERSION,
  DATABASE_NAME,
  CREATE_TABLES,
  CREATE_INDEXES,
  CREATE_TRIGGERS,
  DEFAULT_DATA,
  MIGRATIONS,
};
