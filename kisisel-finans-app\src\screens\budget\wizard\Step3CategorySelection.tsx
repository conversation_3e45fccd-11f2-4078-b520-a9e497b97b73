// RFC-003 Bütçe Sihirbazı - Adım 3: <PERSON><PERSON><PERSON>imi

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';
import { CreateBudgetCategoryInput } from '../../../types/budget';

// Predefined categories - Veritabanı ile senkronize (18 kategori)
const PREDEFINED_CATEGORIES = [
  { id: 'food', name: 'Yiyecek & İçecek', icon: 'restaurant-outline', color: '#FF6B6B', defaultAmount: 1500 },
  { id: 'transportation', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'car-outline', color: '#4ECDC4', defaultAmount: 800 },
  { id: 'shopping', name: 'Alışveriş', icon: 'bag-outline', color: '#45B7D1', defaultAmount: 1000 },
  { id: 'entertainment', name: 'Eğlence', icon: 'game-controller-outline', color: '#96CEB4', defaultAmount: 600 },
  { id: 'health', name: 'Sağlık', icon: 'medical-outline', color: '#FFEAA7', defaultAmount: 500 },
  { id: 'education', name: 'Eğitim', icon: 'school-outline', color: '#DDA0DD', defaultAmount: 400 },
  { id: 'bills', name: 'Faturalar', icon: 'receipt-outline', color: '#FFB347', defaultAmount: 1200 },
  { id: 'rent', name: 'Kira', icon: 'home-outline', color: '#F7DC6F', defaultAmount: 2500 },
  { id: 'insurance', name: 'Sigorta', icon: 'shield-outline', color: '#BB8FCE', defaultAmount: 300 },
  { id: 'personal_care', name: 'Kişisel Bakım', icon: 'person-outline', color: '#85C1E9', defaultAmount: 400 },
  { id: 'gifts', name: 'Hediyeler', icon: 'gift-outline', color: '#F8C471', defaultAmount: 200 },
  { id: 'travel', name: 'Seyahat', icon: 'airplane-outline', color: '#00BCD4', defaultAmount: 800 },
  { id: 'donation', name: 'Bağış', icon: 'heart-outline', color: '#4CAF50', defaultAmount: 150 },
  { id: 'tax', name: 'Vergiler', icon: 'document-text-outline', color: '#9E9E9E', defaultAmount: 600 },
  { id: 'housing', name: 'Konut', icon: 'business-outline', color: '#795548', defaultAmount: 500 },
  { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal-outline', color: '#AED6F1', defaultAmount: 300 },
  // Veritabanında bulunan ek kategoriler
  { id: 'personal', name: 'Personal', icon: 'person-outline', color: '#85C1E9', defaultAmount: 200 },
  { id: 'transport', name: 'Transport', icon: 'car-outline', color: '#4ECDC4', defaultAmount: 400 },
];

const Step3CategorySelection: React.FC = () => {
  const { theme } = useTheme();
  const { state, dispatch } = useBudgetWizard();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<Set<string>>(new Set());

  // Initialize selected categories from state
  useEffect(() => {
    const ids = new Set(state.selectedCategories.map(cat => cat.categoryId));
    setSelectedCategoryIds(ids);
  }, [state.selectedCategories]);

  const filteredCategories = PREDEFINED_CATEGORIES.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCategoryToggle = useCallback((categoryId: string) => {
    const newSelectedIds = new Set(selectedCategoryIds);

    if (newSelectedIds.has(categoryId)) {
      // Remove category
      newSelectedIds.delete(categoryId);
      const updatedCategories = state.selectedCategories.filter(cat => cat.categoryId !== categoryId);
      dispatch({ type: 'SET_CATEGORIES', payload: updatedCategories });
    } else {
      // Add category
      newSelectedIds.add(categoryId);
      const category = PREDEFINED_CATEGORIES.find(cat => cat.id === categoryId);
      if (category) {
        const newCategory: CreateBudgetCategoryInput = {
          categoryId: category.id,
          plannedAmount: category.defaultAmount,
          warningThreshold: state.defaultWarningThreshold,
          criticalThreshold: state.defaultCriticalThreshold,
          limitThreshold: state.defaultLimitThreshold,
          warningEnabled: true,
          criticalEnabled: true,
          limitEnabled: true,
          dailyDigestEnabled: false,
        };
        dispatch({ type: 'ADD_CATEGORY', payload: newCategory });
      }
    }

    setSelectedCategoryIds(newSelectedIds);
  }, [selectedCategoryIds, state.selectedCategories, state.defaultWarningThreshold, state.defaultCriticalThreshold, state.defaultLimitThreshold, dispatch]);

  const handleAmountChange = useCallback((categoryId: string, amount: string) => {
    const numericAmount = parseFloat(amount.replace(/[^0-9.]/g, '')) || 0;
    const categoryIndex = state.selectedCategories.findIndex(cat => cat.categoryId === categoryId);

    if (categoryIndex !== -1) {
      const updatedCategory = {
        ...state.selectedCategories[categoryIndex],
        plannedAmount: numericAmount
      };
      dispatch({
        type: 'UPDATE_CATEGORY',
        payload: { index: categoryIndex, category: updatedCategory }
      });
    }
  }, [state.selectedCategories, dispatch]);

  const selectAllEssentials = useCallback(() => {
    const essentialIds = ['food', 'transportation', 'bills', 'rent', 'health'];
    const newSelectedIds = new Set(selectedCategoryIds);
    const newCategories = [...state.selectedCategories];

    essentialIds.forEach(categoryId => {
      if (!newSelectedIds.has(categoryId)) {
        newSelectedIds.add(categoryId);
        const category = PREDEFINED_CATEGORIES.find(cat => cat.id === categoryId);
        if (category) {
          const newCategory: CreateBudgetCategoryInput = {
            categoryId: category.id,
            plannedAmount: category.defaultAmount,
            warningThreshold: state.defaultWarningThreshold,
            criticalThreshold: state.defaultCriticalThreshold,
            limitThreshold: state.defaultLimitThreshold,
            warningEnabled: true,
            criticalEnabled: true,
            limitEnabled: true,
            dailyDigestEnabled: false,
          };
          newCategories.push(newCategory);
        }
      }
    });

    setSelectedCategoryIds(newSelectedIds);
    dispatch({ type: 'SET_CATEGORIES', payload: newCategories });
  }, [selectedCategoryIds, state.selectedCategories, state.defaultWarningThreshold, state.defaultCriticalThreshold, state.defaultLimitThreshold, dispatch]);

  const getTotalPlannedAmount = useCallback(() => {
    return state.selectedCategories.reduce((total, cat) => total + cat.plannedAmount, 0);
  }, [state.selectedCategories]);

  const formatCurrency = useCallback((amount: number): string => {
    const symbol = state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€';
    return `${amount.toLocaleString('tr-TR')} ${symbol}`;
  }, [state.currency]);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Kategori Seçimi
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Bütçenize dahil etmek istediğiniz kategorileri seçin ve limitlerini belirleyin
        </Text>
      </View>

      {/* Hızlı Seçenekler */}
      <View style={styles.section}>
        <TouchableOpacity
          style={[
            styles.quickSelectButton,
            { backgroundColor: theme.colors.primary }
          ]}
          onPress={selectAllEssentials}
        >
          <Ionicons name="flash" size={20} color={theme.colors.surface} />
          <Text style={[styles.quickSelectText, { color: theme.colors.surface }]}>
            Temel Kategorileri Seç
          </Text>
        </TouchableOpacity>
      </View>

      {/* Arama */}
      <View style={styles.section}>
        <View style={[
          styles.searchContainer,
          { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }
        ]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Kategori ara..."
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>
      </View>

      {/* Kategori Listesi */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Kategoriler ({selectedCategoryIds.size} seçili)
        </Text>

        <View style={styles.categoryGrid}>
          {filteredCategories.map((category) => {
            const isSelected = selectedCategoryIds.has(category.id);
            const selectedCategory = state.selectedCategories.find(cat => cat.categoryId === category.id);

            return (
              <View key={category.id} style={styles.categoryItem}>
                <TouchableOpacity
                  style={[
                    styles.categoryCard,
                    {
                      backgroundColor: isSelected ? category.color : theme.colors.surface,
                      borderColor: isSelected ? category.color : theme.colors.border,
                    }
                  ]}
                  onPress={() => handleCategoryToggle(category.id)}
                >
                  <Ionicons
                    name={category.icon as any}
                    size={24}
                    color={isSelected ? theme.colors.surface : category.color}
                  />
                  <Text style={[
                    styles.categoryName,
                    { color: isSelected ? theme.colors.surface : theme.colors.text }
                  ]}>
                    {category.name}
                  </Text>
                  {isSelected && (
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={theme.colors.surface}
                      style={styles.checkIcon}
                    />
                  )}
                </TouchableOpacity>

                {isSelected && (
                  <View style={[
                    styles.amountInput,
                    { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }
                  ]}>
                    <TextInput
                      style={[styles.amountText, { color: theme.colors.text }]}
                      value={selectedCategory?.plannedAmount.toString() || '0'}
                      onChangeText={(text) => handleAmountChange(category.id, text)}
                      placeholder="0"
                      placeholderTextColor={theme.colors.textSecondary}
                      keyboardType="numeric"
                    />
                    <Text style={[styles.currencySymbol, { color: theme.colors.textSecondary }]}>
                      {state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€'}
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>
      </View>

      {/* Özet */}
      {selectedCategoryIds.size > 0 && (
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
            Kategori Özeti
          </Text>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Seçili Kategoriler:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
              {selectedCategoryIds.size} kategori
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
              Toplam Planlanan:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
              {formatCurrency(getTotalPlannedAmount())}
            </Text>
          </View>

          {state.totalExpenseLimit > 0 && (
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>
                Gider Limitinden:
              </Text>
              <Text style={[
                styles.summaryValue,
                { color: getTotalPlannedAmount() <= state.totalExpenseLimit ? theme.colors.success : theme.colors.error }
              ]}>
                %{((getTotalPlannedAmount() / state.totalExpenseLimit) * 100).toFixed(1)}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Validation Error */}
      {state.errors.categories && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.categories}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  quickSelectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  quickSelectText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
  },
  categoryGrid: {
    gap: 12,
  },
  categoryItem: {
    marginBottom: 8,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    position: 'relative',
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    flex: 1,
  },
  checkIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  amountInput: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    borderWidth: 1,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  amountText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  currencySymbol: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default Step3CategorySelection;
