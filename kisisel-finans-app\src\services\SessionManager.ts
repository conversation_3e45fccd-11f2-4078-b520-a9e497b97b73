// Session Manager - Oturum yönetimi ve gü<PERSON>lik

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import JWTService from './JWTService';

export interface SessionConfig {
  maxConcurrentSessions: number;
  sessionTimeoutMinutes: number;
  autoLogoutOnInactivity: boolean;
  rememberDeviceEnabled: boolean;
}

export interface ActiveSession {
  sessionId: string;
  deviceId: string;
  deviceName: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: number;
  lastActivity: number;
  isCurrentSession: boolean;
}

export interface SessionEvent {
  type: 'login' | 'logout' | 'timeout' | 'concurrent_limit' | 'suspicious_activity';
  timestamp: number;
  deviceId: string;
  sessionId: string;
  details?: any;
}

class SessionManager {
  private static instance: SessionManager;
  private static readonly SESSIONS_KEY = 'active_sessions';
  private static readonly SESSION_EVENTS_KEY = 'session_events';
  private static readonly SESSION_CONFIG_KEY = 'session_config';
  
  private jwtService: JWTService;
  private inactivityTimer: NodeJS.Timeout | null = null;
  private sessionConfig: SessionConfig;

  private constructor() {
    this.jwtService = JWTService.getInstance();
    this.sessionConfig = {
      maxConcurrentSessions: 3,
      sessionTimeoutMinutes: 30,
      autoLogoutOnInactivity: true,
      rememberDeviceEnabled: true,
    };
    this.loadSessionConfig();
    this.startInactivityMonitoring();
  }

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Session konfigürasyonunu yükle
   */
  private async loadSessionConfig(): Promise<void> {
    try {
      const configStr = await AsyncStorage.getItem(SessionManager.SESSION_CONFIG_KEY);
      if (configStr) {
        this.sessionConfig = { ...this.sessionConfig, ...JSON.parse(configStr) };
      }
    } catch (error) {
      console.error('Load session config error:', error);
    }
  }

  /**
   * Session konfigürasyonunu kaydet
   */
  async updateSessionConfig(config: Partial<SessionConfig>): Promise<void> {
    try {
      this.sessionConfig = { ...this.sessionConfig, ...config };
      await AsyncStorage.setItem(
        SessionManager.SESSION_CONFIG_KEY,
        JSON.stringify(this.sessionConfig)
      );
    } catch (error) {
      console.error('Update session config error:', error);
    }
  }

  /**
   * Yeni session başlat
   */
  async startSession(userId: string, deviceName: string): Promise<boolean> {
    try {
      // Mevcut aktif session'ları al
      const activeSessions = await this.getActiveSessions();
      
      // Concurrent session limiti kontrolü
      if (activeSessions.length >= this.sessionConfig.maxConcurrentSessions) {
        // En eski session'ı sonlandır
        const oldestSession = activeSessions
          .filter(s => !s.isCurrentSession)
          .sort((a, b) => a.lastActivity - b.lastActivity)[0];
        
        if (oldestSession) {
          await this.endSession(oldestSession.sessionId);
          await this.logSessionEvent({
            type: 'concurrent_limit',
            timestamp: Date.now(),
            deviceId: oldestSession.deviceId,
            sessionId: oldestSession.sessionId,
            details: { reason: 'Max concurrent sessions exceeded' },
          });
        }
      }

      // Yeni session oluştur
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const deviceId = await this.getDeviceId();
      
      const newSession: ActiveSession = {
        sessionId,
        deviceId,
        deviceName,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        isCurrentSession: true,
      };

      // Diğer session'ları current olmaktan çıkar
      const updatedSessions = activeSessions.map(s => ({
        ...s,
        isCurrentSession: false,
      }));
      
      updatedSessions.push(newSession);
      await this.saveActiveSessions(updatedSessions);

      // Session event'i logla
      await this.logSessionEvent({
        type: 'login',
        timestamp: Date.now(),
        deviceId,
        sessionId,
        details: { deviceName },
      });

      return true;
    } catch (error) {
      console.error('Start session error:', error);
      return false;
    }
  }

  /**
   * Session'ı sonlandır
   */
  async endSession(sessionId?: string): Promise<void> {
    try {
      const activeSessions = await this.getActiveSessions();
      const targetSessionId = sessionId || await this.getCurrentSessionId();
      
      if (!targetSessionId) {
        return;
      }

      // Session'ı listeden çıkar
      const updatedSessions = activeSessions.filter(s => s.sessionId !== targetSessionId);
      await this.saveActiveSessions(updatedSessions);

      // Session event'i logla
      const endedSession = activeSessions.find(s => s.sessionId === targetSessionId);
      if (endedSession) {
        await this.logSessionEvent({
          type: 'logout',
          timestamp: Date.now(),
          deviceId: endedSession.deviceId,
          sessionId: targetSessionId,
        });
      }

      // Eğer current session sonlandırılıyorsa token'ları temizle
      if (endedSession?.isCurrentSession) {
        await this.jwtService.clearTokens();
        this.stopInactivityMonitoring();
      }
    } catch (error) {
      console.error('End session error:', error);
    }
  }

  /**
   * Session aktivitesini güncelle
   */
  async updateSessionActivity(): Promise<void> {
    try {
      const activeSessions = await this.getActiveSessions();
      const currentSessionId = await this.getCurrentSessionId();
      
      if (!currentSessionId) {
        return;
      }

      const updatedSessions = activeSessions.map(session => {
        if (session.sessionId === currentSessionId) {
          return {
            ...session,
            lastActivity: Date.now(),
          };
        }
        return session;
      });

      await this.saveActiveSessions(updatedSessions);
      await this.jwtService.updateSessionActivity();
      
      // Inactivity timer'ı sıfırla
      this.resetInactivityTimer();
    } catch (error) {
      console.error('Update session activity error:', error);
    }
  }

  /**
   * Aktif session'ları al
   */
  async getActiveSessions(): Promise<ActiveSession[]> {
    try {
      const sessionsStr = await AsyncStorage.getItem(SessionManager.SESSIONS_KEY);
      return sessionsStr ? JSON.parse(sessionsStr) : [];
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  }

  /**
   * Aktif session'ları kaydet
   */
  private async saveActiveSessions(sessions: ActiveSession[]): Promise<void> {
    try {
      await AsyncStorage.setItem(SessionManager.SESSIONS_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Save active sessions error:', error);
    }
  }

  /**
   * Current session ID'sini al
   */
  private async getCurrentSessionId(): Promise<string | null> {
    try {
      const activeSessions = await this.getActiveSessions();
      const currentSession = activeSessions.find(s => s.isCurrentSession);
      return currentSession?.sessionId || null;
    } catch (error) {
      console.error('Get current session ID error:', error);
      return null;
    }
  }

  /**
   * Device ID'sini al
   */
  private async getDeviceId(): Promise<string> {
    try {
      let deviceId = await SecureStore.getItemAsync('device_id');
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await SecureStore.setItemAsync('device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('Get device ID error:', error);
      return `fallback_${Date.now()}`;
    }
  }

  /**
   * Session event'i logla
   */
  private async logSessionEvent(event: SessionEvent): Promise<void> {
    try {
      const eventsStr = await AsyncStorage.getItem(SessionManager.SESSION_EVENTS_KEY);
      const events: SessionEvent[] = eventsStr ? JSON.parse(eventsStr) : [];
      
      events.push(event);
      
      // Son 100 event'i sakla
      if (events.length > 100) {
        events.splice(0, events.length - 100);
      }
      
      await AsyncStorage.setItem(SessionManager.SESSION_EVENTS_KEY, JSON.stringify(events));
    } catch (error) {
      console.error('Log session event error:', error);
    }
  }

  /**
   * Session event'lerini al
   */
  async getSessionEvents(): Promise<SessionEvent[]> {
    try {
      const eventsStr = await AsyncStorage.getItem(SessionManager.SESSION_EVENTS_KEY);
      return eventsStr ? JSON.parse(eventsStr) : [];
    } catch (error) {
      console.error('Get session events error:', error);
      return [];
    }
  }

  /**
   * Inactivity monitoring başlat
   */
  private startInactivityMonitoring(): void {
    if (!this.sessionConfig.autoLogoutOnInactivity) {
      return;
    }

    this.resetInactivityTimer();
  }

  /**
   * Inactivity timer'ı sıfırla
   */
  private resetInactivityTimer(): void {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }

    if (!this.sessionConfig.autoLogoutOnInactivity) {
      return;
    }

    const timeoutMs = this.sessionConfig.sessionTimeoutMinutes * 60 * 1000;
    
    this.inactivityTimer = setTimeout(async () => {
      await this.handleInactivityTimeout();
    }, timeoutMs);
  }

  /**
   * Inactivity timeout'u işle
   */
  private async handleInactivityTimeout(): Promise<void> {
    try {
      const currentSessionId = await this.getCurrentSessionId();
      if (currentSessionId) {
        await this.logSessionEvent({
          type: 'timeout',
          timestamp: Date.now(),
          deviceId: await this.getDeviceId(),
          sessionId: currentSessionId,
          details: { reason: 'Inactivity timeout' },
        });
        
        await this.endSession(currentSessionId);
      }
    } catch (error) {
      console.error('Handle inactivity timeout error:', error);
    }
  }

  /**
   * Inactivity monitoring'i durdur
   */
  private stopInactivityMonitoring(): void {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }
  }

  /**
   * Tüm session'ları temizle
   */
  async clearAllSessions(): Promise<void> {
    try {
      await AsyncStorage.removeItem(SessionManager.SESSIONS_KEY);
      await AsyncStorage.removeItem(SessionManager.SESSION_EVENTS_KEY);
      await this.jwtService.clearTokens();
      this.stopInactivityMonitoring();
    } catch (error) {
      console.error('Clear all sessions error:', error);
    }
  }

  /**
   * Session manager'ı temizle
   */
  async cleanup(): Promise<void> {
    this.stopInactivityMonitoring();
    await this.clearAllSessions();
  }
}

export default SessionManager;
