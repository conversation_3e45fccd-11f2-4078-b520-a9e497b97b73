// RFC-003 Bütçe Düzenleme Sayfası

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
  SafeAreaView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import BudgetService from '../../services/BudgetService';
import { Budget, BudgetCategory, UpdateBudgetInput } from '../../types/budget';
import { MainStackParamList } from '../../navigation/MainNavigator';
import databaseManager from '../../database/DatabaseManager';

type BudgetEditRouteProp = RouteProp<MainStackParamList, 'BudgetEdit'>;

const BudgetEditScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<BudgetEditRouteProp>();
  const { user } = useSimpleAuth();
  const { budgetId } = route.params;

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [budget, setBudget] = useState<Budget | null>(null);
  const [categories, setCategories] = useState<BudgetCategory[]>([]);
  
  // Form state
  const [name, setName] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [totalIncomeTarget, setTotalIncomeTarget] = useState(0);
  const [totalExpenseLimit, setTotalExpenseLimit] = useState(0);
  const [savingsTarget, setSavingsTarget] = useState(0);
  const [notes, setNotes] = useState('');
  
  // Date picker state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Category state
  const [budgetCategories, setBudgetCategories] = useState<any[]>([]);
  const [availableCategories, setAvailableCategories] = useState<any[]>([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);

  // Load budget data
  const loadBudgetData = useCallback(async () => {
    try {
      setLoading(true);
      console.log('📝 Loading budget for editing:', budgetId);

      const budgetData = await BudgetService.getBudgetById(budgetId);
      const categoriesData = await BudgetService.getBudgetCategoriesWithDetails(budgetId);

      if (!budgetData) {
        Alert.alert('Hata', 'Bütçe bulunamadı');
        navigation.goBack();
        return;
      }

      setBudget(budgetData);
      setCategories(categoriesData);

      // Form state'i doldur
      setName(budgetData.name);
      setStartDate(budgetData.startDate);
      setEndDate(budgetData.endDate);
      setTotalIncomeTarget(budgetData.totalIncomeTarget || 0);
      setTotalExpenseLimit(budgetData.totalExpenseLimit || 0);
      setSavingsTarget(budgetData.savingsTarget || 0);
      setNotes(budgetData.notes || '');

      // Kategori verilerini doldur
      setBudgetCategories(categoriesData.map(cat => ({
        id: cat.categoryId,
        name: cat.categoryName,
        icon: cat.categoryIcon,
        color: cat.categoryColor,
        plannedAmount: cat.plannedAmount || 0,
        spentAmount: cat.spentAmount || 0
      })));

      // Mevcut kategorileri yükle
      await loadAvailableCategories();

      console.log('✅ Budget data loaded for editing');
    } catch (error) {
      console.error('❌ Error loading budget data:', error);
      Alert.alert('Hata', 'Bütçe verileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [budgetId, navigation]);

  useEffect(() => {
    loadBudgetData();
  }, [loadBudgetData]);

  useEffect(() => {
    if (budgetCategories.length > 0) {
      loadAvailableCategories();
    }
  }, [budgetCategories, loadAvailableCategories]);

  // Date change handlers
  const handleDateChange = (event: any, selectedDate: Date | undefined, type: 'start' | 'end') => {
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];
      if (type === 'start') {
        setStartDate(dateString);
        setShowStartDatePicker(false);
      } else {
        setEndDate(dateString);
        setShowEndDatePicker(false);
      }
    } else {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }
  };

  // Category handlers
  const handleCategoryAmountChange = (index: number, text: string) => {
    const newCategories = [...budgetCategories];
    newCategories[index].plannedAmount = parseFloat(text) || 0;
    setBudgetCategories(newCategories);
  };

  const handleRemoveCategory = (index: number) => {
    const categoryToRemove = budgetCategories[index];

    Alert.alert(
      'Kategori Sil',
      `"${categoryToRemove.name}" kategorisini bütçeden çıkarmak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ Removing category:', categoryToRemove);

              // Veritabanından kategoriyi bul ve sil
              const existingCategory = categories.find(cat => cat.categoryId === categoryToRemove.id);
              if (existingCategory) {
                console.log('🔍 Found budget category to remove:', existingCategory.id);
                await BudgetService.removeCategoryFromBudget(existingCategory.id);
              }

              // Local state'den sil
              const newCategories = [...budgetCategories];
              newCategories.splice(index, 1);
              setBudgetCategories(newCategories);

              console.log('✅ Category removed successfully');
            } catch (error) {
              console.error('❌ Error removing category:', error);
              Alert.alert('Hata', 'Kategori silinirken hata oluştu');
            }
          }
        }
      ]
    );
  };

  // Mevcut kategorileri yükle
  const loadAvailableCategories = useCallback(async () => {
    try {
      const db = databaseManager.getDatabase();
      const allCategories = await db.getAllAsync(`
        SELECT id, name, icon, color, type
        FROM categories
        WHERE type = 'expense'
        ORDER BY name ASC
      `);

      // Zaten bütçede olan kategorileri filtrele
      const usedCategoryIds = budgetCategories.map(cat => cat.id);
      const available = allCategories.filter(cat => !usedCategoryIds.includes(cat.id));

      setAvailableCategories(available);
      console.log('📋 Available categories loaded:', available.length);
    } catch (error) {
      console.error('❌ Error loading available categories:', error);
    }
  }, [budgetCategories]);

  const handleAddCategory = () => {
    setShowCategoryModal(true);
  };

  const handleSelectCategory = async (categoryId: string) => {
    try {
      // Seçilen kategoriyi bütçeye ekle
      const selectedCategory = availableCategories.find(cat => cat.id === categoryId);
      if (!selectedCategory) return;

      // BudgetService ile kategoriyi bütçeye ekle
      await BudgetService.addCategoryToBudget(budgetId, {
        categoryId: categoryId,
        plannedAmount: 100, // Default 100 TL
        warningThreshold: 75,
        criticalThreshold: 90,
        limitThreshold: 100,
      });

      // Local state'i güncelle
      const newCategory = {
        id: categoryId,
        name: selectedCategory.name,
        icon: selectedCategory.icon,
        color: selectedCategory.color,
        plannedAmount: 100, // Default 100 TL
        spentAmount: 0
      };

      setBudgetCategories([...budgetCategories, newCategory]);
      setShowCategoryModal(false);

      // Mevcut kategorileri yeniden yükle
      await loadAvailableCategories();

      console.log('✅ Category added to budget:', categoryId);
    } catch (error) {
      console.error('❌ Error adding category to budget:', error);
      Alert.alert('Hata', 'Kategori eklenirken hata oluştu');
    }
  };

  // Save changes
  const handleSave = useCallback(async () => {
    try {
      setSaving(true);
      console.log('💾 Saving budget changes...');

      // Validation
      if (!name.trim()) {
        Alert.alert('Hata', 'Bütçe adı gerekli');
        return;
      }

      if (new Date(startDate) >= new Date(endDate)) {
        Alert.alert('Hata', 'Bitiş tarihi başlangıç tarihinden sonra olmalı');
        return;
      }

      const updateInput: UpdateBudgetInput = {
        name: name.trim(),
        totalIncomeTarget,
        totalExpenseLimit,
        savingsTarget,
        notes: notes.trim(),
      };

      await BudgetService.updateBudget(budgetId, updateInput);

      // Kategori güncellemelerini kaydet
      console.log('💾 Updating budget categories:', budgetCategories);
      for (const category of budgetCategories) {
        // Mevcut kategori verilerini bul
        const existingCategory = categories.find(cat => cat.categoryId === category.id);
        console.log('🔍 Found existing category:', existingCategory?.id, 'for', category.id);
        if (existingCategory) {
          console.log('📝 Updating category amount:', existingCategory.id, category.plannedAmount);
          await BudgetService.updateBudgetCategory(existingCategory.id, {
            plannedAmount: category.plannedAmount
          });
        }
      }

      console.log('✅ All budget changes saved successfully');

      Alert.alert(
        'Başarılı!',
        'Bütçe başarıyla güncellendi',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack()
          }
        ]
      );

      console.log('✅ Budget updated successfully');
    } catch (error) {
      console.error('❌ Error updating budget:', error);
      Alert.alert('Hata', 'Bütçe güncellenirken hata oluştu');
    } finally {
      setSaving(false);
    }
  }, [budgetId, name, startDate, endDate, totalIncomeTarget, totalExpenseLimit, savingsTarget, notes, navigation]);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Bütçe verileri yükleniyor...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Bütçe Düzenle
        </Text>
        
        <TouchableOpacity
          style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleSave}
          disabled={saving}
        >
          <Text style={[styles.saveButtonText, { color: theme.colors.onPrimary }]}>
            {saving ? 'Kaydediliyor...' : 'Kaydet'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Bütçe Adı */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Bütçe Adı
          </Text>
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={name}
            onChangeText={setName}
            placeholder="Bütçe adını girin"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        {/* Tarih Aralığı */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Tarih Aralığı
          </Text>
          
          <View style={styles.dateRow}>
            <View style={styles.dateField}>
              <Text style={[styles.dateLabel, { color: theme.colors.textSecondary }]}>
                Başlangıç
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  { 
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border
                  }
                ]}
                onPress={() => setShowStartDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.dateText, { color: theme.colors.text }]}>
                  {new Date(startDate).toLocaleDateString('tr-TR')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.dateField}>
              <Text style={[styles.dateLabel, { color: theme.colors.textSecondary }]}>
                Bitiş
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  { 
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border
                  }
                ]}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.dateText, { color: theme.colors.text }]}>
                  {new Date(endDate).toLocaleDateString('tr-TR')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Finansal Hedefler */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Finansal Hedefler
          </Text>
          
          <View style={styles.targetField}>
            <Text style={[styles.targetLabel, { color: theme.colors.textSecondary }]}>
              Gelir Hedefi
            </Text>
            <TextInput
              style={[
                styles.numberInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={totalIncomeTarget.toString()}
              onChangeText={(text) => setTotalIncomeTarget(parseFloat(text) || 0)}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <Text style={[styles.currencyText, { color: theme.colors.textSecondary }]}>
              ₺
            </Text>
          </View>

          <View style={styles.targetField}>
            <Text style={[styles.targetLabel, { color: theme.colors.textSecondary }]}>
              Gider Limiti
            </Text>
            <TextInput
              style={[
                styles.numberInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={totalExpenseLimit.toString()}
              onChangeText={(text) => setTotalExpenseLimit(parseFloat(text) || 0)}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <Text style={[styles.currencyText, { color: theme.colors.textSecondary }]}>
              ₺
            </Text>
          </View>

          <View style={styles.targetField}>
            <Text style={[styles.targetLabel, { color: theme.colors.textSecondary }]}>
              Tasarruf Hedefi
            </Text>
            <TextInput
              style={[
                styles.numberInput,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={savingsTarget.toString()}
              onChangeText={(text) => setSavingsTarget(parseFloat(text) || 0)}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <Text style={[styles.currencyText, { color: theme.colors.textSecondary }]}>
              ₺
            </Text>
          </View>
        </View>

        {/* Kategori Düzenleme */}
        <View style={styles.section}>
          <View style={styles.categoryHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Kategori Bütçeleri
            </Text>
            <TouchableOpacity
              style={[styles.addCategoryButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleAddCategory}
            >
              <Ionicons name="add" size={20} color="white" />
              <Text style={styles.addCategoryText}>Ekle</Text>
            </TouchableOpacity>
          </View>

          {budgetCategories.map((category, index) => (
            <View key={category.id || index} style={[styles.categoryItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
              <View style={styles.categoryInfo}>
                <View style={[styles.categoryIcon, { backgroundColor: category.color || '#AED6F1' }]}>
                  <Ionicons
                    name={category.icon || 'ellipsis-horizontal-outline'}
                    size={20}
                    color="white"
                  />
                </View>
                <Text style={[styles.categoryName, { color: theme.colors.text }]}>
                  {category.name}
                </Text>
              </View>

              <View style={styles.categoryControls}>
                <TextInput
                  style={[
                    styles.categoryAmountInput,
                    {
                      backgroundColor: theme.colors.background,
                      borderColor: theme.colors.border,
                      color: theme.colors.text,
                    }
                  ]}
                  value={category.plannedAmount?.toString() || '0'}
                  onChangeText={(text) => handleCategoryAmountChange(index, text)}
                  placeholder="0"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="numeric"
                />
                <Text style={[styles.currencyText, { color: theme.colors.textSecondary }]}>₺</Text>

                <TouchableOpacity
                  style={[styles.removeCategoryButton, { backgroundColor: theme.colors.error }]}
                  onPress={() => handleRemoveCategory(index)}
                >
                  <Ionicons name="trash-outline" size={16} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Notlar */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Notlar
          </Text>
          <TextInput
            style={[
              styles.textArea,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border,
                color: theme.colors.text,
              }
            ]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Bütçe hakkında notlarınızı yazın..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            numberOfLines={4}
          />
        </View>
      </ScrollView>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={new Date(startDate)}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'start')}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={new Date(endDate)}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'end')}
        />
      )}

      {/* Category Selection Modal */}
      <Modal
        visible={showCategoryModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowCategoryModal(false)}
            >
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>

            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Kategori Seç
            </Text>

            <View style={{ width: 40 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            {availableCategories.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="checkmark-circle" size={64} color={theme.colors.success} />
                <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
                  Tüm Kategoriler Eklendi!
                </Text>
                <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
                  Bütçenize ekleyebileceğiniz başka kategori bulunmuyor.
                </Text>
              </View>
            ) : (
              availableCategories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[styles.categorySelectItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
                  onPress={() => handleSelectCategory(category.id)}
                >
                  <View style={[styles.categoryIcon, { backgroundColor: category.color || '#AED6F1' }]}>
                    <Ionicons
                      name={category.icon || 'ellipsis-horizontal-outline'}
                      size={20}
                      color="white"
                    />
                  </View>
                  <Text style={[styles.categorySelectName, { color: theme.colors.text }]}>
                    {category.name}
                  </Text>
                  <Ionicons name="add-circle-outline" size={24} color={theme.colors.primary} />
                </TouchableOpacity>
              ))
            )}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  dateRow: {
    flexDirection: 'row',
    gap: 12,
  },
  dateField: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  dateText: {
    fontSize: 16,
  },
  targetField: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  targetLabel: {
    fontSize: 14,
    width: 100,
  },
  numberInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginRight: 8,
  },
  currencyText: {
    fontSize: 16,
    fontWeight: '500',
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  // Category Styles
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  addCategoryText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  categoryControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  categoryAmountInput: {
    borderWidth: 1,
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    width: 80,
    textAlign: 'right',
  },
  removeCategoryButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 20,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  categorySelectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    gap: 12,
  },
  categorySelectName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});

export default BudgetEditScreen;
