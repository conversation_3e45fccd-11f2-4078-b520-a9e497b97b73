// Currency Selector Component - Para birimi seçici

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrency, Currency } from '../contexts/CurrencyContext';

interface CurrencySelectorProps {
  onCurrencySelect?: (currency: Currency) => void;
  selectedCurrency?: Currency;
  showLabel?: boolean;
  style?: any;
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  onCurrencySelect,
  selectedCurrency,
  showLabel = true,
  style,
}) => {
  const { theme } = useTheme();
  const { currencies, baseCurrency, setBaseCurrency, exchangeRates, updateExchangeRates } = useCurrency();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isUpdatingRates, setIsUpdatingRates] = useState(false);

  const currentCurrency = selectedCurrency || baseCurrency;

  const filteredCurrencies = currencies.filter(currency =>
    currency.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    currency.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCurrencySelect = async (currency: Currency) => {
    if (onCurrencySelect) {
      onCurrencySelect(currency);
    } else {
      await setBaseCurrency(currency);
    }
    setModalVisible(false);
    setSearchQuery('');
  };

  const handleUpdateRates = async () => {
    setIsUpdatingRates(true);
    try {
      await updateExchangeRates();
      Alert.alert('Başarılı', 'Döviz kurları güncellendi');
    } catch (error) {
      Alert.alert('Hata', 'Döviz kurları güncellenirken bir hata oluştu');
    } finally {
      setIsUpdatingRates(false);
    }
  };

  const renderCurrencyItem = ({ item }: { item: Currency }) => {
    const rate = exchangeRates[item.code];
    const isSelected = item.code === currentCurrency.code;

    return (
      <TouchableOpacity
        style={[
          styles.currencyItem,
          {
            backgroundColor: isSelected ? theme.colors.primary + '20' : theme.colors.surface,
            borderColor: isSelected ? theme.colors.primary : theme.colors.border,
          }
        ]}
        onPress={() => handleCurrencySelect(item)}
      >
        <View style={styles.currencyInfo}>
          <View style={styles.currencyHeader}>
            <Text style={styles.currencyFlag}>{item.flag}</Text>
            <View style={styles.currencyDetails}>
              <Text style={[styles.currencyCode, { color: theme.colors.text }]}>
                {item.code}
              </Text>
              <Text style={[styles.currencyName, { color: theme.colors.textSecondary }]}>
                {item.name}
              </Text>
            </View>
          </View>
          <View style={styles.currencyRate}>
            <Text style={[styles.currencySymbol, { color: theme.colors.text }]}>
              {item.symbol}
            </Text>
            {rate && (
              <Text style={[styles.rateText, { color: theme.colors.textSecondary }]}>
                1 USD = {rate.toFixed(4)}
              </Text>
            )}
          </View>
        </View>
        {isSelected && (
          <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {showLabel && (
        <Text style={[styles.label, { color: theme.colors.text }]}>Para Birimi</Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selector,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          }
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.selectorContent}>
          <Text style={styles.currencyFlag}>{currentCurrency.flag}</Text>
          <View style={styles.selectorText}>
            <Text style={[styles.selectorCode, { color: theme.colors.text }]}>
              {currentCurrency.code}
            </Text>
            <Text style={[styles.selectorName, { color: theme.colors.textSecondary }]}>
              {currentCurrency.name}
            </Text>
          </View>
        </View>
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: theme.colors.border }]}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Para Birimi Seç
            </Text>
            <TouchableOpacity 
              onPress={handleUpdateRates}
              disabled={isUpdatingRates}
            >
              <Ionicons 
                name={isUpdatingRates ? "sync" : "refresh"} 
                size={24} 
                color={theme.colors.primary}
                style={isUpdatingRates ? { transform: [{ rotate: '180deg' }] } : {}}
              />
            </TouchableOpacity>
          </View>

          {/* Search */}
          <View style={styles.searchContainer}>
            <View style={[styles.searchInput, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}>
              <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
              <TextInput
                style={[styles.searchText, { color: theme.colors.text }]}
                placeholder="Para birimi ara..."
                placeholderTextColor={theme.colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
          </View>

          {/* Currency List */}
          <FlatList
            data={filteredCurrencies}
            renderItem={renderCurrencyItem}
            keyExtractor={(item) => item.code}
            style={styles.currencyList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  selectorText: {
    flex: 1,
  },
  selectorCode: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectorName: {
    fontSize: 14,
    marginTop: 2,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInput: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
  currencyList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  currencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  currencyInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  currencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyDetails: {
    marginLeft: 12,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: '600',
  },
  currencyName: {
    fontSize: 14,
    marginTop: 2,
  },
  currencyRate: {
    alignItems: 'flex-end',
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
  },
  rateText: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default CurrencySelector;
