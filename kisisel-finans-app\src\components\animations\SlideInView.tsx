// Slide In Animation Component - Kayma animasyonu bileşeni

import React, { useEffect } from 'react';
import { ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
  Easing,
} from 'react-native-reanimated';

interface SlideInViewProps {
  children: React.ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  duration?: number;
  delay?: number;
  distance?: number;
  useSpring?: boolean;
  style?: ViewStyle;
}

const SlideInView: React.FC<SlideInViewProps> = ({
  children,
  direction = 'up',
  duration = 500,
  delay = 0,
  distance = 50,
  useSpring = false,
  style,
}) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? distance : direction === 'down' ? -distance : 0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    const animationConfig = useSpring
      ? withSpring(0, {
          damping: 15,
          stiffness: 150,
        })
      : withTiming(0, {
          duration,
          easing: Easing.out(Easing.quad),
        });

    translateX.value = withDelay(delay, animationConfig);
    translateY.value = withDelay(delay, animationConfig);
    opacity.value = withDelay(
      delay,
      withTiming(1, {
        duration: duration * 0.8,
        easing: Easing.out(Easing.quad),
      })
    );
  }, [translateX, translateY, opacity, duration, delay, useSpring]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View style={[style, animatedStyle]}>
      {children}
    </Animated.View>
  );
};

export default SlideInView;
