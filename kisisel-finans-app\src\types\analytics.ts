// Analytics Types - Analitik ve raporlama tipleri

import { 
  CurrencyCode, 
  TransactionType, 
  IncomeCategory, 
  ExpenseCategory,
  PaymentMethod 
} from './transaction';

// Zaman periyodu
export type TimePeriod = 
  | 'today' | 'yesterday' | 'this_week' | 'last_week'
  | 'this_month' | 'last_month' | 'this_quarter' | 'last_quarter'
  | 'this_year' | 'last_year' | 'last_7_days' | 'last_30_days'
  | 'last_90_days' | 'last_365_days' | 'all_time' | 'custom';

// Grafik tipi
export type ChartType = 
  | 'line' | 'bar' | 'pie' | 'doughnut' | 'area' 
  | 'scatter' | 'radar' | 'bubble' | 'heatmap';

// Analitik metrik tipi
export type MetricType = 
  | 'total_income' | 'total_expense' | 'net_income' | 'savings_rate'
  | 'expense_ratio' | 'category_breakdown' | 'trend_analysis'
  | 'budget_performance' | 'goal_progress' | 'cash_flow';

// Temel analitik veri noktası
export interface DataPoint {
  date: string;
  value: number;
  label?: string;
  category?: string;
  metadata?: Record<string, any>;
}

// Zaman serisi verisi
export interface TimeSeriesData {
  period: TimePeriod;
  data: DataPoint[];
  currency: CurrencyCode;
  aggregation: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  total: number;
  average: number;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    significance: 'low' | 'medium' | 'high';
  };
}

// Kategori dağılımı
export interface CategoryDistribution {
  category: IncomeCategory | ExpenseCategory | string;
  amount: number;
  percentage: number;
  count: number;
  color: string;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

// Finansal özet
export interface FinancialSummary {
  period: {
    start: string;
    end: string;
    label: string;
  };
  currency: CurrencyCode;
  income: {
    total: number;
    average: number;
    count: number;
    categories: CategoryDistribution[];
    topCategory: {
      category: IncomeCategory | string;
      amount: number;
      percentage: number;
    };
  };
  expense: {
    total: number;
    average: number;
    count: number;
    categories: CategoryDistribution[];
    topCategory: {
      category: ExpenseCategory | string;
      amount: number;
      percentage: number;
    };
  };
  net: {
    amount: number;
    percentage: number;
    trend: 'positive' | 'negative' | 'neutral';
  };
  savingsRate: number;
  expenseRatio: number;
  comparison: {
    previousPeriod: {
      income: number;
      expense: number;
      net: number;
      incomeChange: number;
      expenseChange: number;
      netChange: number;
    };
  };
}

// Bütçe performansı
export interface BudgetPerformance {
  categoryBudgets: Array<{
    category: ExpenseCategory | string;
    budgeted: number;
    spent: number;
    remaining: number;
    percentage: number;
    status: 'under' | 'on_track' | 'over' | 'exceeded';
    daysRemaining: number;
    projectedSpending: number;
  }>;
  totalBudget: number;
  totalSpent: number;
  totalRemaining: number;
  overallPerformance: 'excellent' | 'good' | 'warning' | 'critical';
  alerts: Array<{
    category: string;
    type: 'approaching_limit' | 'exceeded' | 'on_track';
    message: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

// Hedef ilerlemesi
export interface GoalProgress {
  goals: Array<{
    id: string;
    name: string;
    type: 'savings' | 'debt_reduction' | 'income_increase' | 'expense_reduction';
    target: number;
    current: number;
    progress: number;
    deadline: string;
    daysRemaining: number;
    onTrack: boolean;
    projectedCompletion: string;
    monthlyRequired: number;
  }>;
  overallProgress: number;
  achievedGoals: number;
  totalGoals: number;
}

// Nakit akışı analizi
export interface CashFlowAnalysis {
  period: {
    start: string;
    end: string;
  };
  currency: CurrencyCode;
  openingBalance: number;
  closingBalance: number;
  netCashFlow: number;
  inflows: Array<{
    category: IncomeCategory | string;
    amount: number;
    percentage: number;
  }>;
  outflows: Array<{
    category: ExpenseCategory | string;
    amount: number;
    percentage: number;
  }>;
  monthlyFlow: Array<{
    month: string;
    inflow: number;
    outflow: number;
    net: number;
    cumulativeNet: number;
  }>;
  projections: {
    nextMonth: {
      expectedInflow: number;
      expectedOutflow: number;
      projectedNet: number;
    };
    nextQuarter: {
      expectedInflow: number;
      expectedOutflow: number;
      projectedNet: number;
    };
  };
}

// Harcama deseni analizi
export interface SpendingPatternAnalysis {
  patterns: Array<{
    type: 'daily' | 'weekly' | 'monthly' | 'seasonal';
    description: string;
    confidence: number;
    impact: 'low' | 'medium' | 'high';
    categories: (ExpenseCategory | string)[];
    recommendations: string[];
  }>;
  peakSpendingDays: Array<{
    dayOfWeek: number;
    averageAmount: number;
    topCategories: (ExpenseCategory | string)[];
  }>;
  seasonalTrends: Array<{
    season: 'spring' | 'summer' | 'fall' | 'winter';
    averageSpending: number;
    topCategories: (ExpenseCategory | string)[];
    yearOverYearChange: number;
  }>;
  unusualTransactions: Array<{
    id: string;
    amount: number;
    category: string;
    date: string;
    reason: 'amount_outlier' | 'frequency_outlier' | 'category_outlier';
    severity: number;
  }>;
}

// Gelir analizi
export interface IncomeAnalysis {
  stability: {
    score: number;           // 0-100 arası istikrar skoru
    variance: number;
    predictability: 'high' | 'medium' | 'low';
    mainSources: Array<{
      category: IncomeCategory | string;
      percentage: number;
      stability: number;
    }>;
  };
  growth: {
    yearOverYear: number;
    quarterOverQuarter: number;
    monthOverMonth: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    projectedAnnual: number;
  };
  diversification: {
    score: number;           // 0-100 arası çeşitlilik skoru
    sources: number;
    riskLevel: 'low' | 'medium' | 'high';
    recommendations: string[];
  };
}

// Finansal sağlık skoru
export interface FinancialHealthScore {
  overallScore: number;        // 0-100 arası
  components: {
    savingsRate: {
      score: number;
      value: number;
      benchmark: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    };
    debtToIncome: {
      score: number;
      value: number;
      benchmark: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    };
    emergencyFund: {
      score: number;
      months: number;
      benchmark: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    };
    budgetAdherence: {
      score: number;
      value: number;
      benchmark: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    };
    incomeStability: {
      score: number;
      value: number;
      benchmark: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    };
  };
  recommendations: Array<{
    category: string;
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: number;
  }>;
  historicalScores: Array<{
    date: string;
    score: number;
  }>;
}

// Karşılaştırmalı analiz
export interface ComparativeAnalysis {
  userMetrics: FinancialSummary;
  benchmarks: {
    ageGroup: FinancialSummary;
    incomeLevel: FinancialSummary;
    location: FinancialSummary;
    industry: FinancialSummary;
  };
  rankings: {
    savingsRate: {
      percentile: number;
      rank: 'top_10' | 'top_25' | 'top_50' | 'bottom_50' | 'bottom_25';
    };
    expenseControl: {
      percentile: number;
      rank: 'excellent' | 'good' | 'average' | 'below_average' | 'poor';
    };
  };
  insights: Array<{
    type: 'strength' | 'weakness' | 'opportunity';
    title: string;
    description: string;
    metric: string;
    value: number;
    benchmark: number;
  }>;
}

// Rapor konfigürasyonu
export interface ReportConfig {
  id: string;
  name: string;
  type: 'summary' | 'detailed' | 'category' | 'budget' | 'goal' | 'custom';
  period: TimePeriod;
  customPeriod?: {
    start: string;
    end: string;
  };
  currency: CurrencyCode;
  includeCharts: boolean;
  chartTypes: ChartType[];
  metrics: MetricType[];
  categories: (IncomeCategory | ExpenseCategory | string)[];
  groupBy: 'category' | 'date' | 'payment_method' | 'merchant';
  sortBy: 'amount' | 'date' | 'category' | 'count';
  sortOrder: 'asc' | 'desc';
  filters: {
    minAmount?: number;
    maxAmount?: number;
    paymentMethods?: PaymentMethod[];
    includeTransfers?: boolean;
    excludeCategories?: (IncomeCategory | ExpenseCategory | string)[];
  };
  format: 'pdf' | 'excel' | 'csv' | 'json';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    dayOfWeek?: number;
    dayOfMonth?: number;
    time: string;
    recipients: string[];
  };
  createdAt: string;
  updatedAt: string;
}

// Rapor sonucu
export interface ReportResult {
  id: string;
  configId: string;
  generatedAt: string;
  period: {
    start: string;
    end: string;
  };
  summary: FinancialSummary;
  charts: Array<{
    type: ChartType;
    title: string;
    data: any;
    config: any;
  }>;
  tables: Array<{
    title: string;
    headers: string[];
    rows: any[][];
  }>;
  insights: Array<{
    type: 'insight' | 'warning' | 'recommendation';
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  fileUrl?: string;
  fileSize?: number;
  expiresAt?: string;
}

// Dashboard widget konfigürasyonu
export interface DashboardWidget {
  id: string;
  type: 'summary' | 'chart' | 'metric' | 'goal' | 'budget' | 'recent_transactions';
  title: string;
  size: 'small' | 'medium' | 'large';
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  config: {
    metric?: MetricType;
    chartType?: ChartType;
    period?: TimePeriod;
    currency?: CurrencyCode;
    categories?: (IncomeCategory | ExpenseCategory | string)[];
    refreshInterval?: number; // dakika
  };
  isVisible: boolean;
  createdAt: string;
  updatedAt: string;
}

// Analitik ayarları
export interface AnalyticsSettings {
  userId: string;
  defaultCurrency: CurrencyCode;
  defaultPeriod: TimePeriod;
  enablePredictions: boolean;
  enableBenchmarking: boolean;
  enableInsights: boolean;
  dataRetentionDays: number;
  privacyLevel: 'private' | 'anonymous' | 'public';
  shareAnalytics: boolean;
  autoGenerateReports: boolean;
  dashboardWidgets: DashboardWidget[];
  createdAt: string;
  updatedAt: string;
}

export default {
  // Utility functions can be added here
};
