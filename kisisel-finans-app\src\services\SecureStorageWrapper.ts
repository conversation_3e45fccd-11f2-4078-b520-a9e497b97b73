// Secure Storage Wrapper - Şifrelenmiş veri saklama wrapper'ı

import EncryptionService from './EncryptionService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

export interface StorageOptions {
  encrypt?: boolean;
  userPassword?: string;
  useSecureStore?: boolean;
  expiryTime?: number; // milliseconds
}

export interface StoredData {
  data: any;
  timestamp: number;
  expiryTime?: number;
  encrypted: boolean;
  version: string;
}

class SecureStorageWrapper {
  private static instance: SecureStorageWrapper;
  private readonly VERSION = '1.0.0';
  private readonly DEFAULT_OPTIONS: StorageOptions = {
    encrypt: true,
    useSecureStore: false,
    expiryTime: undefined,
  };

  static getInstance(): SecureStorageWrapper {
    if (!SecureStorageWrapper.instance) {
      SecureStorageWrapper.instance = new SecureStorageWrapper();
    }
    return SecureStorageWrapper.instance;
  }

  /**
   * <PERSON><PERSON><PERSON> gü<PERSON>li şekilde saklar
   */
  async setItem(key: string, value: any, options: StorageOptions = {}): Promise<void> {
    try {
      const opts = { ...this.DEFAULT_OPTIONS, ...options };
      
      const storedData: StoredData = {
        data: value,
        timestamp: Date.now(),
        expiryTime: opts.expiryTime ? Date.now() + opts.expiryTime : undefined,
        encrypted: opts.encrypt || false,
        version: this.VERSION,
      };

      let finalData: string;

      if (opts.encrypt) {
        // Şifrelenmiş saklama
        const encryptedData = await EncryptionService.encrypt(
          JSON.stringify(storedData),
          opts.userPassword
        );
        finalData = JSON.stringify(encryptedData);
      } else {
        // Şifrelenmemiş saklama
        finalData = JSON.stringify(storedData);
      }

      if (opts.useSecureStore) {
        await SecureStore.setItemAsync(key, finalData);
      } else {
        await AsyncStorage.setItem(key, finalData);
      }
    } catch (error) {
      console.error('Error setting secure item:', error);
      throw new Error(`Veri saklama başarısız: ${key}`);
    }
  }

  /**
   * Veriyi güvenli şekilde alır
   */
  async getItem(key: string, options: StorageOptions = {}): Promise<any> {
    try {
      const opts = { ...this.DEFAULT_OPTIONS, ...options };
      
      let rawData: string | null;

      if (opts.useSecureStore) {
        rawData = await SecureStore.getItemAsync(key);
      } else {
        rawData = await AsyncStorage.getItem(key);
      }

      if (!rawData) {
        return null;
      }

      let storedData: StoredData;

      if (opts.encrypt) {
        // Şifrelenmiş veri çözme
        const encryptedData = JSON.parse(rawData);
        const decryptedData = await EncryptionService.decrypt(
          encryptedData,
          opts.userPassword
        );
        storedData = JSON.parse(decryptedData);
      } else {
        // Şifrelenmemiş veri
        storedData = JSON.parse(rawData);
      }

      // Expiry kontrolü
      if (storedData.expiryTime && Date.now() > storedData.expiryTime) {
        await this.removeItem(key, options);
        return null;
      }

      return storedData.data;
    } catch (error) {
      console.error('Error getting secure item:', error);
      return null;
    }
  }

  /**
   * Veriyi siler
   */
  async removeItem(key: string, options: StorageOptions = {}): Promise<void> {
    try {
      const opts = { ...this.DEFAULT_OPTIONS, ...options };

      if (opts.useSecureStore) {
        await SecureStore.deleteItemAsync(key);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error removing secure item:', error);
      throw new Error(`Veri silme başarısız: ${key}`);
    }
  }

  /**
   * Anahtarın var olup olmadığını kontrol eder
   */
  async hasItem(key: string, options: StorageOptions = {}): Promise<boolean> {
    try {
      const value = await this.getItem(key, options);
      return value !== null;
    } catch (error) {
      console.error('Error checking item existence:', error);
      return false;
    }
  }

  /**
   * Finansal veriyi özel şifreleme ile saklar
   */
  async setFinancialData(key: string, data: any, userPassword?: string): Promise<void> {
    try {
      const options: StorageOptions = {
        encrypt: true,
        userPassword,
        useSecureStore: true, // Finansal veriler için SecureStore kullan
      };

      // Finansal veri için ek metadata
      const financialData = {
        ...data,
        _financial: true,
        _timestamp: Date.now(),
        _checksum: await EncryptionService.createHash(JSON.stringify(data)),
      };

      await this.setItem(`financial_${key}`, financialData, options);
    } catch (error) {
      console.error('Error setting financial data:', error);
      throw new Error(`Finansal veri saklama başarısız: ${key}`);
    }
  }

  /**
   * Finansal veriyi alır ve doğrular
   */
  async getFinancialData(key: string, userPassword?: string): Promise<any> {
    try {
      const options: StorageOptions = {
        encrypt: true,
        userPassword,
        useSecureStore: true,
      };

      const data = await this.getItem(`financial_${key}`, options);
      
      if (!data || !data._financial) {
        return null;
      }

      // Checksum doğrulama
      const { _checksum, _financial, _timestamp, ...actualData } = data;
      const expectedChecksum = await EncryptionService.createHash(JSON.stringify(actualData));
      
      if (_checksum !== expectedChecksum) {
        console.warn('Financial data integrity check failed');
        return null;
      }

      return actualData;
    } catch (error) {
      console.error('Error getting financial data:', error);
      return null;
    }
  }

  /**
   * Kullanıcı ayarlarını şifrelenmiş olarak saklar
   */
  async setUserSettings(settings: any, userPassword?: string): Promise<void> {
    try {
      const options: StorageOptions = {
        encrypt: true,
        userPassword,
        useSecureStore: false, // Ayarlar için AsyncStorage yeterli
      };

      await this.setItem('user_settings', settings, options);
    } catch (error) {
      console.error('Error setting user settings:', error);
      throw new Error('Kullanıcı ayarları saklama başarısız');
    }
  }

  /**
   * Kullanıcı ayarlarını alır
   */
  async getUserSettings(userPassword?: string): Promise<any> {
    try {
      const options: StorageOptions = {
        encrypt: true,
        userPassword,
        useSecureStore: false,
      };

      return await this.getItem('user_settings', options);
    } catch (error) {
      console.error('Error getting user settings:', error);
      return null;
    }
  }

  /**
   * Cache veriyi geçici olarak saklar (şifrelenmemiş)
   */
  async setCacheData(key: string, data: any, expiryMinutes: number = 60): Promise<void> {
    try {
      const options: StorageOptions = {
        encrypt: false,
        useSecureStore: false,
        expiryTime: expiryMinutes * 60 * 1000, // minutes to milliseconds
      };

      await this.setItem(`cache_${key}`, data, options);
    } catch (error) {
      console.error('Error setting cache data:', error);
      throw new Error(`Cache veri saklama başarısız: ${key}`);
    }
  }

  /**
   * Cache veriyi alır
   */
  async getCacheData(key: string): Promise<any> {
    try {
      const options: StorageOptions = {
        encrypt: false,
        useSecureStore: false,
      };

      return await this.getItem(`cache_${key}`, options);
    } catch (error) {
      console.error('Error getting cache data:', error);
      return null;
    }
  }

  /**
   * Tüm cache verilerini temizler
   */
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw new Error('Cache temizleme başarısız');
    }
  }

  /**
   * Süresi dolmuş verileri temizler
   */
  async cleanupExpiredData(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const expiredKeys: string[] = [];

      for (const key of keys) {
        try {
          const rawData = await AsyncStorage.getItem(key);
          if (rawData) {
            const storedData: StoredData = JSON.parse(rawData);
            if (storedData.expiryTime && Date.now() > storedData.expiryTime) {
              expiredKeys.push(key);
            }
          }
        } catch (error) {
          // Parsing hatası olan key'leri de temizle
          expiredKeys.push(key);
        }
      }

      if (expiredKeys.length > 0) {
        await AsyncStorage.multiRemove(expiredKeys);
        console.log(`Cleaned up ${expiredKeys.length} expired items`);
      }
    } catch (error) {
      console.error('Error cleaning up expired data:', error);
    }
  }

  /**
   * Storage istatistiklerini al
   */
  async getStorageStats(): Promise<{
    totalKeys: number;
    encryptedKeys: number;
    cacheKeys: number;
    financialKeys: number;
    expiredKeys: number;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      let encryptedKeys = 0;
      let expiredKeys = 0;
      
      const cacheKeys = keys.filter(key => key.startsWith('cache_')).length;
      const financialKeys = keys.filter(key => key.startsWith('financial_')).length;

      for (const key of keys) {
        try {
          const rawData = await AsyncStorage.getItem(key);
          if (rawData) {
            const storedData: StoredData = JSON.parse(rawData);
            if (storedData.encrypted) {
              encryptedKeys++;
            }
            if (storedData.expiryTime && Date.now() > storedData.expiryTime) {
              expiredKeys++;
            }
          }
        } catch (error) {
          // Ignore parsing errors
        }
      }

      return {
        totalKeys: keys.length,
        encryptedKeys,
        cacheKeys,
        financialKeys,
        expiredKeys,
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        totalKeys: 0,
        encryptedKeys: 0,
        cacheKeys: 0,
        financialKeys: 0,
        expiredKeys: 0,
      };
    }
  }
}

export default SecureStorageWrapper.getInstance();
