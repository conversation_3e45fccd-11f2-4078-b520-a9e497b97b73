// SSL Pinning Test Screen - SSL pinning test ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import SSLPinningService from '../../services/SSLPinningService';
import SecureHttpClient from '../../services/SecureHttpClient';
import ApiService from '../../services/ApiService';

const SSLPinningTestScreen: React.FC = () => {
  const { theme } = useTheme();
  
  const [testUrl, setTestUrl] = useState('https://api.exchangerate-api.com/v4/latest/USD');
  const [pinningStatus, setPinningStatus] = useState<any>(null);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [httpClientStats, setHttpClientStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // SSL Pinning durumunu kontrol et
  const checkPinningStatus = async () => {
    try {
      const status = SSLPinningService.getSSLPinningStatus();
      setPinningStatus(status);
    } catch (error) {
      Alert.alert('Hata', `SSL pinning durumu alınamadı: ${error}`);
    }
  };

  // SSL bağlantısını test et
  const testSSLConnection = async () => {
    try {
      setIsLoading(true);
      
      const hostname = new URL(testUrl).hostname;
      const result = await SSLPinningService.validateSSLConnection(hostname);
      setValidationResult(result);
      
      if (result.success) {
        Alert.alert('✅ Başarılı', `SSL validation başarılı!\nPin Matched: ${result.pinMatched}`);
      } else {
        Alert.alert('❌ Başarısız', `SSL validation başarısız!\nHata: ${result.error}`);
      }
    } catch (error) {
      Alert.alert('Hata', `SSL test başarısız: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Secure HTTP Client ile test
  const testSecureHttpClient = async () => {
    try {
      setIsLoading(true);
      
      const response = await SecureHttpClient.get(testUrl, {
        validateSSL: true,
        timeout: 10000,
        retryAttempts: 2,
      });
      
      Alert.alert(
        '✅ HTTP Client Test Başarılı',
        `Status: ${response.status}\nSSL Validated: ${response.sslValidation?.validated}\nPin Matched: ${response.sslValidation?.pinMatched}`
      );
    } catch (error) {
      Alert.alert('❌ HTTP Client Test Başarısız', `Hata: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // API Service ile test
  const testApiService = async () => {
    try {
      setIsLoading(true);
      
      // Mock API call
      const response = await ApiService.getServerStatus();
      
      Alert.alert(
        '✅ API Service Test',
        `Success: ${response.success}\nData: ${JSON.stringify(response.data)}`
      );
    } catch (error) {
      Alert.alert('❌ API Service Test Başarısız', `Hata: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Sertifika pin'i ekle
  const addTestPin = async () => {
    try {
      const hostname = new URL(testUrl).hostname;
      
      await SSLPinningService.addCertificatePin({
        hostname,
        publicKeyHash: 'TEST_PUBLIC_KEY_HASH_' + Date.now(),
        certificateHash: 'TEST_CERT_HASH_' + Date.now(),
        algorithm: 'SHA-256',
        expiryDate: '2025-12-31',
        isBackup: false,
      });
      
      Alert.alert('✅ Başarılı', 'Test pin eklendi!');
      await checkPinningStatus();
    } catch (error) {
      Alert.alert('Hata', `Pin eklenemedi: ${error}`);
    }
  };

  // HTTP Client istatistikleri
  const getHttpClientStats = async () => {
    try {
      const stats = SecureHttpClient.getCacheStats();
      const sslStatus = SecureHttpClient.getSSLPinningStatus();
      
      setHttpClientStats({
        cache: stats,
        ssl: sslStatus,
      });
    } catch (error) {
      Alert.alert('Hata', `İstatistikler alınamadı: ${error}`);
    }
  };

  // SSL Pinning'i etkinleştir/devre dışı bırak
  const toggleSSLPinning = async () => {
    try {
      const currentConfig = SSLPinningService.getConfiguration();
      
      await SSLPinningService.updateConfiguration({
        enabled: !currentConfig.enabled,
      });
      
      Alert.alert(
        '✅ Başarılı',
        `SSL Pinning ${!currentConfig.enabled ? 'etkinleştirildi' : 'devre dışı bırakıldı'}`
      );
      
      await checkPinningStatus();
    } catch (error) {
      Alert.alert('Hata', `SSL pinning ayarı değiştirilemedi: ${error}`);
    }
  };

  // Pin'leri temizle
  const clearAllPins = async () => {
    try {
      await SSLPinningService.clearAllPins();
      Alert.alert('✅ Başarılı', 'Tüm pin\'ler temizlendi!');
      await checkPinningStatus();
    } catch (error) {
      Alert.alert('Hata', `Pin\'ler temizlenemedi: ${error}`);
    }
  };

  // İstatistikleri sıfırla
  const resetStatistics = async () => {
    try {
      await SSLPinningService.resetStatistics();
      SecureHttpClient.clearCache();
      Alert.alert('✅ Başarılı', 'İstatistikler sıfırlandı!');
      await checkPinningStatus();
    } catch (error) {
      Alert.alert('Hata', `İstatistikler sıfırlanamadı: ${error}`);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      marginBottom: 10,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
    },
    buttonTextSecondary: {
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
    },
    statusBox: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginTop: 10,
    },
    statusText: {
      color: theme.colors.text,
      fontSize: 12,
      fontFamily: 'monospace',
    },
  });

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔒 SSL Pinning Test</Text>

      {/* Test URL Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test URL:</Text>
        <TextInput
          style={styles.input}
          value={testUrl}
          onChangeText={setTestUrl}
          placeholder="Test edilecek URL'yi girin..."
          placeholderTextColor={theme.colors.textSecondary}
        />
      </View>

      {/* SSL Pinning Tests */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>SSL Pinning Testleri:</Text>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={testSSLConnection}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Test Ediliyor...' : 'SSL Bağlantı Testi'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testSecureHttpClient}>
          <Text style={styles.buttonText}>Secure HTTP Client Testi</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testApiService}>
          <Text style={styles.buttonText}>API Service Testi</Text>
        </TouchableOpacity>
      </View>

      {/* Pin Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pin Yönetimi:</Text>
        
        <TouchableOpacity style={styles.buttonSecondary} onPress={addTestPin}>
          <Text style={styles.buttonTextSecondary}>Test Pin Ekle</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={clearAllPins}>
          <Text style={styles.buttonText}>Tüm Pin'leri Temizle</Text>
        </TouchableOpacity>
      </View>

      {/* Status & Configuration */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Durum & Ayarlar:</Text>
        
        <TouchableOpacity style={styles.buttonSecondary} onPress={checkPinningStatus}>
          <Text style={styles.buttonTextSecondary}>SSL Pinning Durumu</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={getHttpClientStats}>
          <Text style={styles.buttonTextSecondary}>HTTP Client İstatistikleri</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={toggleSSLPinning}>
          <Text style={styles.buttonTextSecondary}>SSL Pinning Aç/Kapat</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={resetStatistics}>
          <Text style={styles.buttonTextSecondary}>İstatistikleri Sıfırla</Text>
        </TouchableOpacity>
      </View>

      {/* Results */}
      {pinningStatus && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>SSL Pinning Durumu:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(pinningStatus, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {validationResult && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Validation Sonucu:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(validationResult, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {httpClientStats && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>HTTP Client İstatistikleri:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(httpClientStats, null, 2)}
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default SSLPinningTestScreen;
