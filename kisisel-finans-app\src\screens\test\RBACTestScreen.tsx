// RBAC Test Screen - <PERSON>ol tabanlı erişim kontrolü test ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { useAccessControl } from '../../hooks/useAccessControl';
import { RoleType, Permission } from '../../types/rbac';
import RBACService from '../../services/RBACService';

const RBACTestScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  // Enterprise RBAC features geçici olarak kapatıldı
  // const { assignRole, removeRole, getUserRoles, getRBACStatus } = useAuth();
  const {
    hasRole,
    hasPermission,
    userRoles,
    userPermissions,
    isLoading,
    refreshPermissions,
    canAccess,
    getRequiredRole,
  } = useAccessControl();

  const [rbacStatus, setRbacStatus] = useState<any>(null);
  const [permissionResults, setPermissionResults] = useState<any>({});
  const [roleResults, setRoleResults] = useState<any>({});

  useEffect(() => {
    loadRBACStatus();
  }, []);

  const loadRBACStatus = async () => {
    try {
      // Mock RBAC status
      const status = { message: 'RBAC geçici olarak kapatıldı' };
      setRbacStatus(status);
    } catch (error) {
      console.error('Error loading RBAC status:', error);
    }
  };

  // Test permissions
  const testPermissions = async () => {
    if (!user) return;

    const testPerms: Permission[] = [
      'transaction:create',
      'transaction:read',
      'budget:create',
      'investment:trade',
      'admin:users',
      'premium:advanced_analytics',
    ];

    const results: any = {};

    for (const perm of testPerms) {
      try {
        const hasAccess = await hasPermission(perm);
        const requiredRole = getRequiredRole(perm);
        results[perm] = {
          allowed: hasAccess,
          requiredRole,
        };
      } catch (error) {
        results[perm] = {
          allowed: false,
          error: error.toString(),
        };
      }
    }

    setPermissionResults(results);
    Alert.alert('✅ Permission Test Tamamlandı', 'Sonuçları aşağıda görebilirsiniz');
  };

  // Test roles
  const testRoles = () => {
    const testRoleTypes: RoleType[] = [
      'guest',
      'basic',
      'premium',
      'family_admin',
      'admin',
      'super_admin',
    ];

    const results: any = {};

    testRoleTypes.forEach(role => {
      results[role] = hasRole(role);
    });

    setRoleResults(results);
    Alert.alert('✅ Role Test Tamamlandı', 'Sonuçları aşağıda görebilirsiniz');
  };

  // Assign premium role
  const assignPremiumRole = async () => {
    if (!user) return;

    try {
      // Mock role assignment
      Alert.alert('ℹ️ Bilgi', 'RBAC geçici olarak kapatıldı. Enterprise features admin panel\'de kullanılacak.');
    } catch (error) {
      Alert.alert('❌ Hata', `Premium rol atama hatası: ${error}`);
    }
  };

  // Remove premium role
  const removePremiumRole = async () => {
    Alert.alert('ℹ️ Bilgi', 'RBAC geçici olarak kapatıldı. Enterprise features admin panel\'de kullanılacak.');
  };

  // Assign admin role
  const assignAdminRole = async () => {
    Alert.alert('ℹ️ Bilgi', 'RBAC geçici olarak kapatıldı. Enterprise features admin panel\'de kullanılacak.');
  };

  // Remove admin role
  const removeAdminRole = async () => {
    Alert.alert('ℹ️ Bilgi', 'RBAC geçici olarak kapatıldı. Enterprise features admin panel\'de kullanılacak.');
  };

  // Test resource access
  const testResourceAccess = async () => {
    if (!user) return;

    const resources = [
      { resource: 'transaction', action: 'create' },
      { resource: 'budget', action: 'delete' },
      { resource: 'investment', action: 'trade' },
      { resource: 'admin', action: 'users' },
    ];

    const results: any = {};

    for (const { resource, action } of resources) {
      try {
        const hasAccess = await canAccess(resource, action);
        results[`${resource}:${action}`] = hasAccess;
      } catch (error) {
        results[`${resource}:${action}`] = false;
      }
    }

    Alert.alert(
      '✅ Resource Access Test',
      Object.entries(results)
        .map(([key, value]) => `${key}: ${value ? '✅' : '❌'}`)
        .join('\n')
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonSuccess: {
      backgroundColor: '#4CAF50',
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
    },
    buttonTextSecondary: {
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
    },
    statusBox: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginTop: 10,
    },
    statusText: {
      color: theme.colors.text,
      fontSize: 12,
      fontFamily: 'monospace',
    },
    userInfo: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginBottom: 10,
    },
    userInfoText: {
      color: theme.colors.text,
      fontSize: 14,
    },
    resultItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    resultKey: {
      color: theme.colors.text,
      fontSize: 12,
      flex: 1,
    },
    resultValue: {
      fontSize: 12,
      fontWeight: 'bold',
    },
    allowed: {
      color: '#4CAF50',
    },
    denied: {
      color: '#F44336',
    },
  });

  if (isLoading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={styles.title}>RBAC Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>👥 RBAC Test</Text>

      {/* User Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Kullanıcı Bilgileri:</Text>
        <View style={styles.userInfo}>
          <Text style={styles.userInfoText}>ID: {user?.id}</Text>
          <Text style={styles.userInfoText}>Email: {user?.email}</Text>
          <Text style={styles.userInfoText}>Roller: {userRoles.join(', ') || 'Yok'}</Text>
          <Text style={styles.userInfoText}>İzin Sayısı: {userPermissions.length}</Text>
        </View>
      </View>

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>RBAC Testleri:</Text>

        <TouchableOpacity style={styles.button} onPress={testPermissions}>
          <Text style={styles.buttonText}>Permission Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testRoles}>
          <Text style={styles.buttonText}>Role Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testResourceAccess}>
          <Text style={styles.buttonText}>Resource Access Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={refreshPermissions}>
          <Text style={styles.buttonTextSecondary}>İzinleri Yenile</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={() => {
          RBACService.clearCache();
          Alert.alert('✅ Başarılı', 'RBAC Cache temizlendi!');
        }}>
          <Text style={styles.buttonTextSecondary}>🗑️ Cache Temizle</Text>
        </TouchableOpacity>
      </View>

      {/* Role Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Rol Yönetimi:</Text>

        <TouchableOpacity style={styles.buttonSuccess} onPress={assignPremiumRole}>
          <Text style={styles.buttonText}>Premium Rol Ata</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={removePremiumRole}>
          <Text style={styles.buttonText}>Premium Rol Kaldır</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.buttonSuccess, { backgroundColor: '#FF5722' }]} onPress={assignAdminRole}>
          <Text style={styles.buttonText}>🛡️ Admin Rol Ata</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={removeAdminRole}>
          <Text style={styles.buttonText}>Admin Rol Kaldır</Text>
        </TouchableOpacity>
      </View>

      {/* Permission Results */}
      {Object.keys(permissionResults).length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Permission Test Sonuçları:</Text>
          <View style={styles.statusBox}>
            {Object.entries(permissionResults).map(([perm, result]: [string, any]) => (
              <View key={perm} style={styles.resultItem}>
                <Text style={styles.resultKey}>{perm}</Text>
                <Text style={[styles.resultValue, result.allowed ? styles.allowed : styles.denied]}>
                  {result.allowed ? '✅' : '❌'} {result.requiredRole && `(${result.requiredRole})`}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Role Results */}
      {Object.keys(roleResults).length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Role Test Sonuçları:</Text>
          <View style={styles.statusBox}>
            {Object.entries(roleResults).map(([role, hasRole]: [string, any]) => (
              <View key={role} style={styles.resultItem}>
                <Text style={styles.resultKey}>{role}</Text>
                <Text style={[styles.resultValue, hasRole ? styles.allowed : styles.denied]}>
                  {hasRole ? '✅' : '❌'}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* RBAC Status */}
      {rbacStatus && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>RBAC Durumu:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(rbacStatus, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {/* Current Permissions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Mevcut İzinler ({userPermissions.length}):</Text>
        <View style={styles.statusBox}>
          <Text style={styles.statusText}>
            {userPermissions.join('\n') || 'İzin yok'}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

export default RBACTestScreen;
