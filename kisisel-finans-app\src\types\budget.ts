// RFC-003 Bütçe Planlama - Type Definitions

export type BudgetPeriod = 'weekly' | 'monthly' | 'quarterly' | 'annually';

export type BudgetStatus = 'active' | 'completed' | 'paused' | 'draft';

export type ThresholdType = 'warning' | 'critical' | 'limit';

// RFC-003 Ana Bütçe Yapısı
export interface Budget {
  id: string;
  userId: string;
  name: string;
  period: BudgetPeriod;
  startDate: string;
  endDate: string;
  totalIncomeTarget: number;
  totalExpenseLimit: number;
  savingsTarget: number;
  currency: string;
  notes?: string;
  templateName?: string;
  copiedFromBudgetId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// RFC-003 Kategori Bazlı Bütçe Yapısı
export interface BudgetCategory {
  id: string;
  budgetId: string;
  categoryId: string;
  plannedAmount: number;
  spentAmount: number;
  remainingAmount: number; // Computed field
  warningThreshold: number; // 0-100 percentage
  criticalThreshold: number; // 0-100 percentage
  limitThreshold: number; // 0-200 percentage (can exceed 100%)
  warningEnabled: boolean;
  criticalEnabled: boolean;
  limitEnabled: boolean;
  dailyDigestEnabled: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Bütçe oluşturma için input type
export interface CreateBudgetInput {
  name: string;
  period: BudgetPeriod;
  startDate: string;
  endDate: string;
  totalIncomeTarget?: number;
  totalExpenseLimit?: number;
  savingsTarget?: number;
  currency?: string;
  notes?: string;
  templateName?: string;
  copiedFromBudgetId?: string;
  categories: CreateBudgetCategoryInput[];
}

// Kategori bütçesi oluşturma için input type
export interface CreateBudgetCategoryInput {
  categoryId: string;
  plannedAmount: number;
  warningThreshold?: number;
  criticalThreshold?: number;
  limitThreshold?: number;
  warningEnabled?: boolean;
  criticalEnabled?: boolean;
  limitEnabled?: boolean;
  dailyDigestEnabled?: boolean;
}

// Bütçe güncelleme için input type
export interface UpdateBudgetInput {
  name?: string;
  totalIncomeTarget?: number;
  totalExpenseLimit?: number;
  savingsTarget?: number;
  notes?: string;
  isActive?: boolean;
}

// Kategori bütçesi güncelleme için input type
export interface UpdateBudgetCategoryInput {
  plannedAmount?: number;
  warningThreshold?: number;
  criticalThreshold?: number;
  limitThreshold?: number;
  warningEnabled?: boolean;
  criticalEnabled?: boolean;
  limitEnabled?: boolean;
  dailyDigestEnabled?: boolean;
  isActive?: boolean;
}

// Bütçe özeti
export interface BudgetSummary {
  budget: Budget;
  categories: BudgetCategoryWithDetails[];
  totalPlanned: number;
  totalSpent: number;
  totalRemaining: number;
  overallProgress: number; // 0-100 percentage
  status: BudgetStatus;
  daysRemaining: number;
  averageDailySpending: number;
  projectedEndAmount: number;
}

// Detaylı kategori bütçesi (kategori bilgileri ile birlikte)
export interface BudgetCategoryWithDetails extends BudgetCategory {
  categoryName: string;
  categoryIcon: string;
  categoryColor: string;
  progress: number; // 0-100+ percentage
  status: 'safe' | 'warning' | 'critical' | 'exceeded';
  dailyAverage: number;
  weeklyAverage: number;
  monthlyAverage: number;
}

// Bütçe uyarısı
export interface BudgetAlert {
  id: string;
  budgetId: string;
  budgetCategoryId?: string;
  type: ThresholdType;
  title: string;
  message: string;
  percentage: number;
  amount: number;
  isRead: boolean;
  createdAt: string;
}

// Bütçe şablonu
export interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  period: BudgetPeriod;
  categories: BudgetTemplateCategoryInput[];
  isDefault: boolean;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
}

// Şablon kategori yapısı
export interface BudgetTemplateCategoryInput {
  categoryId: string;
  plannedAmount: number;
  percentage?: number; // Total budget'in yüzdesi
  warningThreshold?: number;
  criticalThreshold?: number;
  limitThreshold?: number;
}

// Bütçe analizi
export interface BudgetAnalysis {
  budgetId: string;
  period: string;
  totalIncome: number;
  totalExpenses: number;
  totalSavings: number;
  savingsRate: number; // Percentage
  categoryBreakdown: CategoryAnalysis[];
  trends: BudgetTrend[];
  recommendations: BudgetRecommendation[];
}

// Kategori analizi
export interface CategoryAnalysis {
  categoryId: string;
  categoryName: string;
  planned: number;
  actual: number;
  variance: number; // actual - planned
  variancePercentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

// Bütçe trendi
export interface BudgetTrend {
  period: string;
  totalSpent: number;
  totalPlanned: number;
  savingsRate: number;
  topCategories: string[];
}

// Bütçe önerisi
export interface BudgetRecommendation {
  id: string;
  type: 'savings' | 'reduction' | 'reallocation' | 'increase';
  title: string;
  description: string;
  categoryId?: string;
  currentAmount: number;
  suggestedAmount: number;
  potentialSavings: number;
  confidence: number; // 0-100
  priority: 'low' | 'medium' | 'high';
}

// Bütçe karşılaştırması
export interface BudgetComparison {
  currentBudget: BudgetSummary;
  previousBudget?: BudgetSummary;
  improvements: string[];
  concerns: string[];
  overallScore: number; // 0-100
}

// Bütçe filtreleme seçenekleri
export interface BudgetFilters {
  period?: BudgetPeriod;
  status?: BudgetStatus;
  startDate?: string;
  endDate?: string;
  categoryIds?: string[];
  minAmount?: number;
  maxAmount?: number;
}

// Bütçe sıralama seçenekleri
export interface BudgetSortOptions {
  field: 'name' | 'startDate' | 'totalPlanned' | 'totalSpent' | 'progress';
  direction: 'asc' | 'desc';
}

// API Response types
export interface BudgetListResponse {
  budgets: BudgetSummary[];
  total: number;
  page: number;
  limit: number;
}

export interface BudgetStatsResponse {
  totalBudgets: number;
  activeBudgets: number;
  totalPlanned: number;
  totalSpent: number;
  averageSavingsRate: number;
  topCategories: CategoryAnalysis[];
}
