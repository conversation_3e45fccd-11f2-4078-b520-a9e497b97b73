// Category Manager Screen - <PERSON><PERSON><PERSON> y<PERSON> e<PERSON>nı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { INCOME_CATEGORIES, EXPENSE_CATEGORIES } from '../../constants/categories';
import { Ionicons } from '@expo/vector-icons';

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  subcategories?: string[];
}

const CategoryManagerScreen: React.FC = () => {
  const { theme } = useTheme();
  const [selectedTab, setSelectedTab] = useState<'income' | 'expense'>('expense');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    icon: 'pricetag',
    color: theme.colors.primary,
    subcategories: [] as string[],
  });
  const [newSubcategory, setNewSubcategory] = useState('');

  const categories = selectedTab === 'income' ? INCOME_CATEGORIES : EXPENSE_CATEGORIES;
  const availableIcons = [
    'home', 'car', 'restaurant', 'medical', 'school', 'fitness',
    'game-controller', 'shirt', 'gift', 'airplane', 'business',
    'card', 'cash', 'trending-up', 'trending-down', 'pricetag'
  ];
  const availableColors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'];

  const handleAddCategory = () => {
    if (!newCategory.name.trim()) {
      Alert.alert('Hata', 'Kategori adı gerekli.');
      return;
    }

    // TODO: Kategori ekleme API çağrısı
    Alert.alert('Başarılı', 'Kategori eklendi.');
    setShowAddModal(false);
    resetForm();
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setNewCategory({
      name: category.name,
      icon: category.icon,
      color: category.color,
      subcategories: category.subcategories || [],
    });
    setShowAddModal(true);
  };

  const handleDeleteCategory = (category: Category) => {
    Alert.alert(
      'Kategoriyi Sil',
      `"${category.name}" kategorisini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => {
            // TODO: Kategori silme API çağrısı
            Alert.alert('Başarılı', 'Kategori silindi.');
          },
        },
      ]
    );
  };

  const addSubcategory = () => {
    if (!newSubcategory.trim()) return;

    setNewCategory({
      ...newCategory,
      subcategories: [...newCategory.subcategories, newSubcategory.trim()],
    });
    setNewSubcategory('');
  };

  const removeSubcategory = (index: number) => {
    const updatedSubcategories = newCategory.subcategories.filter((_, i) => i !== index);
    setNewCategory({ ...newCategory, subcategories: updatedSubcategories });
  };

  const resetForm = () => {
    setNewCategory({
      name: '',
      icon: 'pricetag',
      color: theme.colors.primary,
      subcategories: [],
    });
    setNewSubcategory('');
    setEditingCategory(null);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 60,
      paddingBottom: 20,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    addButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tabContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      marginHorizontal: 4,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    tabActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    tabText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    tabTextActive: {
      color: theme.colors.surface,
    },
    categoriesList: {
      flex: 1,
      paddingHorizontal: 20,
    },
    categoryItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 8,
    },
    categoryLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    categoryIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    categoryInfo: {
      flex: 1,
    },
    categoryName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
    },
    subcategoryCount: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 2,
    },
    categoryActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      padding: 8,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 60,
      paddingBottom: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    cancelButton: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    saveButton: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    modalContent: {
      flex: 1,
      padding: 20,
    },
    inputContainer: {
      marginBottom: 24,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    iconScroll: {
      flexDirection: 'row',
    },
    iconButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8,
    },
    iconButtonSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.surfaceSecondary,
    },
    colorScroll: {
      flexDirection: 'row',
    },
    colorButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      marginRight: 8,
      borderWidth: 2,
      borderColor: 'transparent',
    },
    colorButtonSelected: {
      borderColor: theme.colors.text,
    },
    subcategoryInput: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    subcategoryTextInput: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: 14,
      color: theme.colors.text,
      marginRight: 8,
    },
    addSubcategoryButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    subcategoryItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 8,
      marginBottom: 4,
    },
    subcategoryText: {
      fontSize: 14,
      color: theme.colors.text,
    },
    previewContainer: {
      marginBottom: 24,
    },
    previewCategory: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
    },
    previewIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    previewText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
    },
  });

  const CategoryModal = () => (
    <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => { setShowAddModal(false); resetForm(); }}>
            <Text style={styles.cancelButton}>İptal</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>
            {editingCategory ? 'Kategoriyi Düzenle' : 'Yeni Kategori'}
          </Text>
          <TouchableOpacity onPress={handleAddCategory}>
            <Text style={styles.saveButton}>Kaydet</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* Category Name */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Kategori Adı</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Kategori adını girin"
              value={newCategory.name}
              onChangeText={(text) => setNewCategory({ ...newCategory, name: text })}
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>İkon</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.iconScroll}>
              {availableIcons.map((icon) => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconButton,
                    newCategory.icon === icon && styles.iconButtonSelected,
                  ]}
                  onPress={() => setNewCategory({ ...newCategory, icon })}
                >
                  <Ionicons name={icon as any} size={24} color={theme.colors.text} />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Color Selection */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Renk</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.colorScroll}>
              {availableColors.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorButton,
                    { backgroundColor: color },
                    newCategory.color === color && styles.colorButtonSelected,
                  ]}
                  onPress={() => setNewCategory({ ...newCategory, color })}
                />
              ))}
            </ScrollView>
          </View>

          {/* Subcategories */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Alt Kategoriler</Text>
            <View style={styles.subcategoryInput}>
              <TextInput
                style={styles.subcategoryTextInput}
                placeholder="Alt kategori ekle"
                value={newSubcategory}
                onChangeText={setNewSubcategory}
                onSubmitEditing={addSubcategory}
              />
              <TouchableOpacity style={styles.addSubcategoryButton} onPress={addSubcategory}>
                <Ionicons name="add" size={20} color={theme.colors.surface} />
              </TouchableOpacity>
            </View>

            {newCategory.subcategories.map((subcategory, index) => (
              <View key={index} style={styles.subcategoryItem}>
                <Text style={styles.subcategoryText}>{subcategory}</Text>
                <TouchableOpacity onPress={() => removeSubcategory(index)}>
                  <Ionicons name="close" size={16} color={theme.colors.error} />
                </TouchableOpacity>
              </View>
            ))}
          </View>

          {/* Preview */}
          <View style={styles.previewContainer}>
            <Text style={styles.inputLabel}>Önizleme</Text>
            <View style={styles.previewCategory}>
              <View style={[styles.previewIcon, { backgroundColor: newCategory.color }]}>
                <Ionicons name={newCategory.icon as any} size={20} color={theme.colors.surface} />
              </View>
              <Text style={styles.previewText}>{newCategory.name || 'Kategori Adı'}</Text>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Kategori Yönetimi</Text>
        <TouchableOpacity style={styles.addButton} onPress={() => setShowAddModal(true)}>
          <Ionicons name="add" size={24} color={theme.colors.surface} />
        </TouchableOpacity>
      </View>

      {/* Tab Selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'expense' && styles.tabActive]}
          onPress={() => setSelectedTab('expense')}
        >
          <Text style={[styles.tabText, selectedTab === 'expense' && styles.tabTextActive]}>
            Gider Kategorileri
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'income' && styles.tabActive]}
          onPress={() => setSelectedTab('income')}
        >
          <Text style={[styles.tabText, selectedTab === 'income' && styles.tabTextActive]}>
            Gelir Kategorileri
          </Text>
        </TouchableOpacity>
      </View>

      {/* Categories List */}
      <ScrollView style={styles.categoriesList}>
        {categories.map((category) => (
          <View key={category.id} style={styles.categoryItem}>
            <View style={styles.categoryLeft}>
              <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                <Ionicons name={category.icon as any} size={20} color={theme.colors.surface} />
              </View>
              <View style={styles.categoryInfo}>
                <Text style={styles.categoryName}>{category.name}</Text>
                {category.subcategories && (
                  <Text style={styles.subcategoryCount}>
                    {category.subcategories.length} alt kategori
                  </Text>
                )}
              </View>
            </View>
            <View style={styles.categoryActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleEditCategory(category)}
              >
                <Ionicons name="create-outline" size={20} color={theme.colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDeleteCategory(category)}
              >
                <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>

      <CategoryModal />
    </View>
  );
};

export default CategoryManagerScreen;
