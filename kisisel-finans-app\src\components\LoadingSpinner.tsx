// Loading Spinner Component - Yükleme göstergesi bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

interface LoadingSpinnerProps {
  visible?: boolean;
  text?: string;
  size?: 'small' | 'large';
  color?: string;
  overlay?: boolean;
  style?: any;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  visible = true,
  text = 'Yükleniyor...',
  size = 'large',
  color,
  overlay = false,
  style,
}) => {
  const { theme } = useTheme();
  const spinnerColor = color || theme.colors.primary;
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    overlayContainer: {
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    spinner: {
      alignItems: 'center',
    },
    overlaySpinner: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 24,
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    text: {
      marginTop: 12,
      fontSize: 16,
      fontWeight: '500',
    },
  });

  const content = (
    <View style={[styles.container, overlay && styles.overlayContainer, style]}>
      <View style={[styles.spinner, overlay && styles.overlaySpinner]}>
        <ActivityIndicator size={size} color={spinnerColor} />
        {text && <Text style={[styles.text, { color: spinnerColor }]}>{text}</Text>}
      </View>
    </View>
  );

  if (overlay) {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="fade"
      >
        {content}
      </Modal>
    );
  }

  return visible ? content : null;
};

// Inline Loading Component - Satır içi yükleme bileşeni
export const InlineLoading: React.FC<{
  text?: string;
  size?: 'small' | 'large';
  color?: string;
  style?: any;
}> = ({
  text = 'Yükleniyor...',
  size = 'small',
  color,
  style,
}) => {
  const { theme } = useTheme();
  const spinnerColor = color || theme.colors.primary;

  const inlineStyles = StyleSheet.create({
    inlineContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
    },
    inlineText: {
      marginLeft: 12,
      fontSize: 14,
      fontWeight: '500',
    },
  });

  return (
    <View style={[inlineStyles.inlineContainer, style]}>
      <ActivityIndicator size={size} color={spinnerColor} />
      {text && <Text style={[inlineStyles.inlineText, { color: spinnerColor }]}>{text}</Text>}
    </View>
  );
};

// Button Loading Component - Buton yükleme bileşeni
export const ButtonLoading: React.FC<{
  loading: boolean;
  text: string;
  loadingText?: string;
  color?: string;
  size?: 'small' | 'large';
}> = ({
  loading,
  text,
  loadingText = 'Yükleniyor...',
  color,
  size = 'small',
}) => {
  const { theme } = useTheme();
  const textColor = color || theme.colors.surface;

  const buttonStyles = StyleSheet.create({
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonSpinner: {
      marginRight: 8,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <View style={buttonStyles.buttonContainer}>
      {loading && (
        <ActivityIndicator
          size={size}
          color={textColor}
          style={buttonStyles.buttonSpinner}
        />
      )}
      <Text style={[buttonStyles.buttonText, { color: textColor }]}>
        {loading ? loadingText : text}
      </Text>
    </View>
  );
};

// Skeleton Loading Component - İskelet yükleme bileşeni
export const SkeletonLoading: React.FC<{
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <View
      style={[
        {
          backgroundColor: theme.colors.border,
          opacity: 0.6,
          width,
          height,
          borderRadius,
        },
        style,
      ]}
    />
  );
};

// Card Skeleton Component - Kart iskelet bileşeni
export const CardSkeleton: React.FC<{
  showAvatar?: boolean;
  lines?: number;
  style?: any;
}> = ({
  showAvatar = false,
  lines = 3,
  style,
}) => {
  const { theme } = useTheme();

  const cardStyles = StyleSheet.create({
    cardSkeleton: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
    },
    skeletonRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    skeletonContent: {
      flex: 1,
      marginLeft: 12,
    },
  });

  return (
    <View style={[cardStyles.cardSkeleton, style]}>
      {showAvatar && (
        <View style={cardStyles.skeletonRow}>
          <SkeletonLoading width={40} height={40} borderRadius={20} />
          <View style={cardStyles.skeletonContent}>
            <SkeletonLoading width="60%" height={16} />
            <SkeletonLoading width="40%" height={12} style={{ marginTop: 4 }} />
          </View>
        </View>
      )}

      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonLoading
          key={index}
          width={index === lines - 1 ? '70%' : '100%'}
          height={16}
          style={{ marginTop: index > 0 || showAvatar ? 8 : 0 }}
        />
      ))}
    </View>
  );
};

export default LoadingSpinner;
