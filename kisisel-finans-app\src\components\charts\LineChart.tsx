// Line Chart Component - Ç<PERSON>gi grafik bileşeni

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { LineChart as RNLineChart } from 'react-native-chart-kit';
import { useTheme } from '../../contexts/ThemeContext';

const { width } = Dimensions.get('window');

export interface LineChartDataPoint {
  value: number;
  label?: string;
}

export interface LineChartDataset {
  data: number[];
  color?: (opacity: number) => string;
  strokeWidth?: number;
}

interface LineChartProps {
  data: {
    labels: string[];
    datasets: LineChartDataset[];
  };
  title?: string;
  yAxisSuffix?: string;
  yAxisPrefix?: string;
  showGrid?: boolean;
  showDots?: boolean;
  size?: number;
  style?: any;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  yAxisSuffix = '',
  yAxisPrefix = '',
  showGrid = true,
  showDots = true,
  size = width - 40,
  style,
}) => {
  const { theme } = useTheme();

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.colors.primary,
    labelColor: (opacity = 1) => theme.colors.text,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: showDots ? '4' : '0',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
    propsForBackgroundLines: {
      strokeDasharray: showGrid ? '' : '0',
      stroke: theme.colors.border,
      strokeWidth: 1,
    },
    propsForLabels: {
      fontSize: 12,
      fontWeight: '500',
    },
  };

  const formatValue = (yValue: string) => {
    const value = parseFloat(yValue);
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 16,
      marginBottom: 16,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    chartContainer: {
      alignItems: 'center',
      overflow: 'hidden',
      borderRadius: 12,
    },
    chart: {
      marginVertical: 8,
      borderRadius: 16,
    },
    emptyContainer: {
      height: 200,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
  });

  if (!data || !data.datasets || data.datasets.length === 0) {
    return (
      <View style={[styles.container, style]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Gösterilecek veri yok</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {title && <Text style={styles.title}>{title}</Text>}

      <View style={styles.chartContainer}>
        <RNLineChart
          data={data}
          width={size}
          height={220}
          chartConfig={chartConfig}
          bezier={false}
          style={styles.chart}
          yAxisSuffix={yAxisSuffix}
          formatYLabel={formatValue}
          withInnerLines={showGrid}
          withOuterLines={showGrid}
          withVerticalLines={showGrid}
          withHorizontalLines={showGrid}
          withDots={showDots}
          withShadow={false}
          withVerticalLabels={true}
          withHorizontalLabels={true}
        />
      </View>
    </View>
  );
};

export default LineChart;
