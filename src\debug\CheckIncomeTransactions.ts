// Check Income Transactions Debug

import { DatabaseManager } from '../database/DatabaseManager';

export class CheckIncomeTransactions {
  static async run(): Promise<void> {
    try {
      console.log('🔍 Checking income transactions...');
      
      const db = DatabaseManager.getDatabase();
      
      // 1. Tüm gelir işlemlerini kontrol et
      const incomeTransactions = await db.getAllAsync(
        `SELECT id, amount, category, category_id, budget_id, date, description
         FROM transactions 
         WHERE type = 'income' AND is_deleted = 0
         ORDER BY date DESC`
      );
      
      console.log(`💰 Found ${incomeTransactions.length} income transactions:`);
      incomeTransactions.forEach((tx, index) => {
        console.log(`  ${index + 1}. ${tx.amount}₺ - ${tx.category} - Budget: ${tx.budget_id || 'NULL'} - Date: ${tx.date}`);
      });
      
      // 2. Budget income categories kontrol et
      const budgetIncomeCategories = await db.getAllAsync(
        `SELECT bic.*, b.name as budget_name, c.name as category_name
         FROM budget_income_categories bic
         JOIN budgets b ON bic.budget_id = b.id
         JOIN categories c ON bic.category_id = c.id
         WHERE bic.is_active = 1
         ORDER BY b.name, c.name`
      );
      
      console.log(`📊 Found ${budgetIncomeCategories.length} budget income categories:`);
      budgetIncomeCategories.forEach((bic, index) => {
        console.log(`  ${index + 1}. ${bic.budget_name} - ${bic.category_name} - Target: ${bic.target_amount}₺ - Actual: ${bic.actual_amount}₺`);
      });
      
      // 3. Bütçesiz gelir işlemlerini bul
      const unlinkedIncomeTransactions = incomeTransactions.filter(tx => !tx.budget_id);
      console.log(`🚨 Found ${unlinkedIncomeTransactions.length} unlinked income transactions:`);
      unlinkedIncomeTransactions.forEach((tx, index) => {
        console.log(`  ${index + 1}. ${tx.amount}₺ - ${tx.category} - ${tx.description} - Date: ${tx.date}`);
      });
      
      // 4. Aktif bütçeleri kontrol et
      const activeBudgets = await db.getAllAsync(
        `SELECT id, name, total_income_target, start_date, end_date
         FROM budgets 
         WHERE is_active = 1
         ORDER BY created_at DESC`
      );
      
      console.log(`📋 Found ${activeBudgets.length} active budgets:`);
      activeBudgets.forEach((budget, index) => {
        console.log(`  ${index + 1}. ${budget.name} - Target: ${budget.total_income_target}₺ - Period: ${budget.start_date} to ${budget.end_date}`);
      });
      
      console.log('✅ Income transactions check completed!');
      
    } catch (error) {
      console.error('❌ Error checking income transactions:', error);
      throw error;
    }
  }
}
