// Transaction Types - Gelir/Gider veri modelleri

// Para birimi tipleri
export type CurrencyCode =
  | 'TRY' | 'USD' | 'EUR' | 'GBP' | 'JPY'
  | 'CAD' | 'AUD' | 'CHF' | 'CNY' | 'INR';

// Gelir kategorileri
export type IncomeCategory =
  | 'salary'           // Maaş
  | 'investment'       // Yatırım geliri
  | 'side_income'      // Yan gelir
  | 'freelance'        // Freelance
  | 'business'         // İş geliri
  | 'rental'           // Kira geliri
  | 'dividend'         // Temettü
  | 'interest'         // Faiz geliri
  | 'bonus'            // Bonus
  | 'gift'             // Hediye
  | 'refund'           // İade
  | 'other';           // Diğer

// Gider kategorileri - Database code'ları ile senkronize
export type ExpenseCategory =
  | 'food'             // Yiyecek & İçecek
  | 'transportation'   // Ulaşım
  | 'bills'            // Faturalar
  | 'shopping'         // Alışveriş
  | 'entertainment'    // Eğlence
  | 'health'           // Sağlık
  | 'education'        // Eğitim
  | 'rent'             // Kira
  | 'insurance'        // Sigorta
  | 'personal_care'    // Kişisel bakım
  | 'gifts'            // Hediyeler
  | 'travel'           // Seyahat
  | 'donation'         // Bağış
  | 'tax'              // Vergiler
  | 'housing'          // Konut
  | 'other';           // Diğer

// Alt kategoriler
export type ExpenseSubcategory = {
  food: 'groceries' | 'restaurant' | 'fast_food' | 'coffee' | 'alcohol' | 'other';
  transportation: 'fuel' | 'public_transport' | 'taxi' | 'parking' | 'maintenance' | 'other';
  bills: 'electricity' | 'water' | 'gas' | 'internet' | 'phone' | 'rent' | 'other';
  shopping: 'clothing' | 'electronics' | 'home' | 'books' | 'sports' | 'other';
  entertainment: 'movies' | 'games' | 'music' | 'sports' | 'hobbies' | 'other';
  health: 'doctor' | 'pharmacy' | 'dental' | 'fitness' | 'supplements' | 'other';
  education: 'tuition' | 'books' | 'courses' | 'training' | 'other';
  housing: 'rent' | 'mortgage' | 'maintenance' | 'furniture' | 'utilities' | 'other';
  insurance: 'health' | 'auto' | 'home' | 'life' | 'other';
  investment: 'stocks' | 'bonds' | 'crypto' | 'real_estate' | 'other';
  debt: 'credit_card' | 'loan' | 'mortgage' | 'other';
  travel: 'flights' | 'hotels' | 'food' | 'transport' | 'activities' | 'other';
  personal_care: 'haircut' | 'cosmetics' | 'clothing' | 'other';
  gifts: 'birthday' | 'wedding' | 'holiday' | 'other';
  charity: 'donation' | 'sponsorship' | 'other';
  taxes: 'income' | 'property' | 'sales' | 'other';
  other: 'miscellaneous';
};

// Ödeme yöntemleri
export type PaymentMethod =
  | 'cash'             // Nakit
  | 'debit_card'       // Banka kartı
  | 'credit_card'      // Kredi kartı
  | 'bank_transfer'    // Banka havalesi
  | 'eft'              // EFT
  | 'wire_transfer'    // Havale/IBAN
  | 'digital_wallet'   // Dijital cüzdan (PayPal, Apple Pay, Google Pay)
  | 'mobile_payment'   // Mobil ödeme (BiP, Papara, İninal)
  | 'check'            // Çek
  | 'cryptocurrency'   // Kripto para
  | 'bitcoin'          // Bitcoin
  | 'ethereum'         // Ethereum
  | 'other_crypto'     // Diğer kripto paralar
  | 'installment'      // Taksit
  | 'other';           // Diğer

// Tekrarlama sıklığı
export type RecurrenceFrequency =
  | 'none'             // Tek seferlik
  | 'daily'            // Günlük
  | 'weekly'           // Haftalık
  | 'bi_weekly'        // İki haftada bir
  | 'monthly'          // Aylık
  | 'quarterly'        // Üç aylık
  | 'semi_annually'    // Altı aylık
  | 'annually';        // Yıllık

// İşlem durumu
export type TransactionStatus =
  | 'completed'        // Tamamlandı
  | 'pending'          // Beklemede
  | 'cancelled'        // İptal edildi
  | 'failed';          // Başarısız

// İşlem tipi
export type TransactionType = 'income' | 'expense';

// Tekrarlama kuralı
export interface RecurrenceRule {
  frequency: RecurrenceFrequency;
  interval: number;           // Her kaç günde/haftada/ayda bir
  endDate?: string;          // Bitiş tarihi (opsiyonel)
  occurrences?: number;      // Kaç kez tekrarlanacak (opsiyonel)
  dayOfWeek?: number;        // Haftanın günü (0=Pazar, 6=Cumartesi)
  dayOfMonth?: number;       // Ayın günü (1-31)
  monthOfYear?: number;      // Yılın ayı (1-12)
}

// Konum bilgisi
export interface TransactionLocation {
  latitude: number;
  longitude: number;
  address?: string;
  placeName?: string;
  city?: string;
  country?: string;
}

// Makbuz/Fatura bilgisi
export interface Receipt {
  id: string;
  imageUri: string;
  thumbnailUri?: string;
  ocrData?: {
    merchantName?: string;
    totalAmount?: number;
    date?: string;
    items?: Array<{
      name: string;
      quantity?: number;
      price?: number;
    }>;
    confidence?: number;     // OCR güven skoru (0-1)
  };
  uploadedAt: string;
  fileSize?: number;
}

// Etiket
export interface Tag {
  id: string;
  name: string;
  color?: string;
  icon?: string;
}

// Temel işlem interface'i
export interface BaseTransaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  currency: CurrencyCode;
  date: string;                    // ISO string
  description?: string;
  budgetId?: string;               // Bağlı olduğu bütçe ID (opsiyonel)
  tags: Tag[];
  location?: TransactionLocation;
  receipt?: Receipt;
  paymentMethod?: PaymentMethod;
  recurrence?: RecurrenceRule;
  parentTransactionId?: string;    // Tekrarlayan işlemler için ana işlem ID
  status: TransactionStatus;
  createdAt: string;
  updatedAt: string;
  syncedAt?: string;              // Son senkronizasyon zamanı
  isDeleted: boolean;
  deletedAt?: string;
}

// Gelir işlemi
export interface IncomeTransaction extends BaseTransaction {
  type: 'income';
  category: IncomeCategory;
  categoryId?: string;            // Kategori ID (hibrit sistem için)
  source?: string;                // Gelir kaynağı (şirket adı, müşteri adı vb.)
  taxable?: boolean;              // Vergiye tabi mi
  taxAmount?: number;             // Vergi miktarı
}

// Gider işlemi
export interface ExpenseTransaction extends BaseTransaction {
  type: 'expense';
  category: ExpenseCategory;
  categoryId?: string;            // Kategori ID (hibrit sistem için)
  subcategory?: ExpenseSubcategory[ExpenseCategory];
  merchant?: string;              // İşyeri adı
  isBusinessExpense?: boolean;    // İş gideri mi
  isDeductible?: boolean;         // Vergiden düşülebilir mi
  warranty?: {
    expiryDate: string;
    description?: string;
  };
}

// Birleşik işlem tipi
export type Transaction = IncomeTransaction | ExpenseTransaction;

// İşlem özeti
export interface TransactionSummary {
  totalIncome: number;
  totalExpense: number;
  netAmount: number;
  transactionCount: number;
  currency: CurrencyCode;
  period: {
    start: string;
    end: string;
  };
  categoryBreakdown: {
    income: Record<IncomeCategory, number>;
    expense: Record<ExpenseCategory, number>;
  };
}

// İşlem filtresi
export interface TransactionFilter {
  type?: TransactionType;
  categories?: (IncomeCategory | ExpenseCategory)[];
  paymentMethods?: PaymentMethod[];
  dateRange?: {
    start: string;
    end: string;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  currencies?: CurrencyCode[];
  tags?: string[];
  status?: TransactionStatus[];
  hasReceipt?: boolean;
  hasLocation?: boolean;
  searchQuery?: string;
}

// İşlem sıralama
export interface TransactionSort {
  field: 'date' | 'amount' | 'category' | 'createdAt';
  direction: 'asc' | 'desc';
}

// Sayfalama
export interface Pagination {
  page: number;
  limit: number;
  total?: number;
  hasMore?: boolean;
}

// İşlem listesi response
export interface TransactionListResponse {
  transactions: Transaction[];
  summary: TransactionSummary;
  pagination: Pagination;
}

// Kategori istatistikleri
export interface CategoryStats {
  category: IncomeCategory | ExpenseCategory;
  totalAmount: number;
  transactionCount: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

// Aylık trend
export interface MonthlyTrend {
  month: string;              // YYYY-MM format
  income: number;
  expense: number;
  net: number;
  transactionCount: number;
}

// Günlük özet
export interface DailySummary {
  date: string;               // YYYY-MM-DD format
  income: number;
  expense: number;
  net: number;
  transactionCount: number;
  topCategories: {
    income: IncomeCategory[];
    expense: ExpenseCategory[];
  };
}

// Tekrarlayan işlem şablonu
export interface RecurringTemplate {
  id: string;
  userId: string;
  name: string;
  type: TransactionType;
  amount: number;
  currency: CurrencyCode;
  category: IncomeCategory | ExpenseCategory;
  subcategory?: ExpenseSubcategory[ExpenseCategory];
  description?: string;
  paymentMethod?: PaymentMethod;
  recurrence: RecurrenceRule;
  isActive: boolean;
  nextExecutionDate: string;
  lastExecutionDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Bütçe kategorisi
export interface BudgetCategory {
  id: string;
  userId: string;
  category: ExpenseCategory;
  budgetAmount: number;
  spentAmount: number;
  currency: CurrencyCode;
  period: 'weekly' | 'monthly' | 'quarterly' | 'annually';
  startDate: string;
  endDate: string;
  alertThreshold: number;     // Uyarı eşiği (yüzde)
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// İşlem validasyon kuralları
export interface TransactionValidation {
  amount: {
    min: number;
    max: number;
  };
  description: {
    maxLength: number;
    required: boolean;
  };
  tags: {
    maxCount: number;
    maxLength: number;
  };
  receipt: {
    maxFileSize: number;        // bytes
    allowedFormats: string[];   // ['jpg', 'png', 'pdf']
  };
}

// Varsayılan değerler
export const DEFAULT_CURRENCY: CurrencyCode = 'TRY';
export const DEFAULT_TRANSACTION_STATUS: TransactionStatus = 'completed';
export const DEFAULT_PAYMENT_METHOD: PaymentMethod = 'cash';

// Kategori etiketleri (UI için)
export const INCOME_CATEGORY_LABELS: Record<IncomeCategory, string> = {
  salary: 'Maaş',
  investment: 'Yatırım Geliri',
  side_income: 'Yan Gelir',
  freelance: 'Freelance',
  business: 'İş Geliri',
  rental: 'Kira Geliri',
  dividend: 'Temettü',
  interest: 'Faiz Geliri',
  bonus: 'Bonus',
  gift: 'Hediye',
  refund: 'İade',
  other: 'Diğer',
};

export const EXPENSE_CATEGORY_LABELS: Record<ExpenseCategory, string> = {
  food: 'Yiyecek & İçecek',
  transportation: 'Ulaşım',
  bills: 'Faturalar',
  shopping: 'Alışveriş',
  entertainment: 'Eğlence',
  health: 'Sağlık',
  education: 'Eğitim',
  rent: 'Kira',
  insurance: 'Sigorta',
  personal_care: 'Kişisel Bakım',
  gifts: 'Hediyeler',
  travel: 'Seyahat',
  donation: 'Bağış',
  tax: 'Vergiler',
  housing: 'Konut',
  other: 'Diğer',
};

export const PAYMENT_METHOD_LABELS: Record<PaymentMethod, string> = {
  cash: 'Nakit',
  debit_card: 'Banka Kartı',
  credit_card: 'Kredi Kartı',
  bank_transfer: 'Banka Havalesi',
  eft: 'EFT',
  wire_transfer: 'Havale/IBAN',
  digital_wallet: 'Dijital Cüzdan',
  mobile_payment: 'Mobil Ödeme',
  check: 'Çek',
  cryptocurrency: 'Kripto Para',
  bitcoin: 'Bitcoin',
  ethereum: 'Ethereum',
  other_crypto: 'Diğer Kripto',
  installment: 'Taksit',
  other: 'Diğer',
};

export const RECURRENCE_LABELS: Record<RecurrenceFrequency, string> = {
  none: 'Tek Seferlik',
  daily: 'Günlük',
  weekly: 'Haftalık',
  bi_weekly: 'İki Haftada Bir',
  monthly: 'Aylık',
  quarterly: 'Üç Aylık',
  semi_annually: 'Altı Aylık',
  annually: 'Yıllık',
};

// Kategori ikonları
export const INCOME_CATEGORY_ICONS: Record<IncomeCategory, string> = {
  salary: 'briefcase-outline',
  investment: 'trending-up-outline',
  side_income: 'cash-outline',
  freelance: 'laptop-outline',
  business: 'business-outline',
  rental: 'home-outline',
  dividend: 'pie-chart-outline',
  interest: 'card-outline',
  bonus: 'gift-outline',
  gift: 'heart-outline',
  refund: 'return-up-back-outline',
  other: 'ellipsis-horizontal-outline',
};

export const EXPENSE_CATEGORY_ICONS: Record<ExpenseCategory, string> = {
  food: 'restaurant-outline',
  transportation: 'car-outline',
  bills: 'receipt-outline',
  shopping: 'bag-outline',
  entertainment: 'game-controller-outline',
  health: 'medical-outline',
  education: 'school-outline',
  rent: 'home-outline',
  insurance: 'shield-outline',
  personal_care: 'person-outline',
  gifts: 'gift-outline',
  travel: 'airplane-outline',
  donation: 'heart-outline',
  tax: 'document-text-outline',
  housing: 'business-outline',
  other: 'ellipsis-horizontal-outline',
};
