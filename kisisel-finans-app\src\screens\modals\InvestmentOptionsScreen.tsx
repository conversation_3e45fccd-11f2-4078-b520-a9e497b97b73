// Investment Options Screen - Yatırı<PERSON> seçenekleri ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const InvestmentOptionsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // Mock investment options data - replace with real API call
  const investmentOptions = [
    {
      id: '1',
      name: 'Apple Inc.',
      symbol: 'AAPL',
      type: 'stock',
      description: 'Apple Inc. teknoloji şirketi, iPhone, iPad, Mac ve diğer tüketici elektroniği ürünleri tasarlar ve üretir.',
      riskLevel: 'medium',
      expectedReturn: 12.5,
      minimumInvestment: 1000,
      provider: 'Nasdaq',
      performance: {
        oneYear: 22.3,
        sixMonths: 15.7,
      },
      tags: ['Teknoloji', 'Büyük Şirket', 'Te<PERSON><PERSON>'],
    },
    {
      id: '2',
      name: 'Microsoft Corporation',
      symbol: 'MSFT',
      type: 'stock',
      description: 'Microsoft Corporation yazılım, hizmetler, cihazlar ve çözümler geliştiren teknoloji şirketidir.',
      riskLevel: 'medium',
      expectedReturn: 11.8,
      minimumInvestment: 1000,
      provider: 'Nasdaq',
      performance: {
        oneYear: 18.5,
        sixMonths: 12.3,
      },
      tags: ['Teknoloji', 'Yazılım', 'Bulut'],
    },
    {
      id: '3',
      name: 'Bitcoin',
      symbol: 'BTC',
      type: 'crypto',
      description: 'Bitcoin, merkezi olmayan dijital para birimi ve ödeme sistemidir.',
      riskLevel: 'high',
      expectedReturn: 25.0,
      minimumInvestment: 100,
      provider: 'Binance',
      performance: {
        oneYear: -8.2,
        sixMonths: 45.1,
      },
      tags: ['Kripto', 'Volatil', 'Dijital'],
    },
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return theme.colors.success;
      case 'medium': return theme.colors.warning;
      case 'high': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getRiskText = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'Düşük Risk';
      case 'medium': return 'Orta Risk';
      case 'high': return 'Yüksek Risk';
      default: return 'Bilinmiyor';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'stock': return 'Hisse Senedi';
      case 'bond': return 'Tahvil';
      case 'fund': return 'Fon';
      case 'etf': return 'ETF';
      case 'crypto': return 'Kripto Para';
      case 'commodity': return 'Emtia';
      case 'real_estate': return 'Gayrimenkul';
      default: return type;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    optionCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    optionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    optionInfo: {
      flex: 1,
    },
    optionName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    optionSymbol: {
      fontSize: 12,
      color: theme.colors.primary,
      fontWeight: '500',
      marginBottom: 2,
    },
    optionType: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    optionPerformance: {
      alignItems: 'flex-end',
    },
    optionReturn: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    optionPeriod: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    optionDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: 16,
    },
    optionDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    detailItem: {
      alignItems: 'center',
    },
    detailLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    detailValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    optionFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    providerInfo: {
      flex: 1,
    },
    providerText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    performanceInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    performanceLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    performanceValue: {
      fontSize: 12,
      fontWeight: '600',
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    tag: {
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    tagText: {
      fontSize: 10,
      color: theme.colors.textSecondary,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 60,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateSubtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {investmentOptions.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={styles.optionCard}
            onPress={() => {
              // Navigate to investment detail
              navigation.navigate('InvestmentDetail' as never, { investmentId: option.id });
            }}
          >
            <View style={styles.optionHeader}>
              <View style={styles.optionInfo}>
                <Text style={styles.optionName}>{option.name}</Text>
                <Text style={styles.optionSymbol}>{option.symbol}</Text>
                <Text style={styles.optionType}>{getTypeText(option.type)}</Text>
              </View>
              <View style={styles.optionPerformance}>
                <Text style={[
                  styles.optionReturn,
                  { color: option.performance.oneYear >= 0 ? theme.colors.success : theme.colors.error }
                ]}>
                  {option.performance.oneYear >= 0 ? '+' : ''}{option.performance.oneYear.toFixed(1)}%
                </Text>
                <Text style={styles.optionPeriod}>1 Yıl</Text>
              </View>
            </View>

            <Text style={styles.optionDescription} numberOfLines={2}>
              {option.description}
            </Text>

            <View style={styles.optionDetails}>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Risk Seviyesi</Text>
                <Text style={[styles.detailValue, { color: getRiskColor(option.riskLevel) }]}>
                  {getRiskText(option.riskLevel)}
                </Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Beklenen Getiri</Text>
                <Text style={styles.detailValue}>%{option.expectedReturn.toFixed(1)}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Min. Yatırım</Text>
                <Text style={styles.detailValue}>₺{option.minimumInvestment}</Text>
              </View>
            </View>

            <View style={styles.optionFooter}>
              <View style={styles.providerInfo}>
                <Text style={styles.providerText}>{option.provider}</Text>
              </View>
              <View style={styles.performanceInfo}>
                <Text style={styles.performanceLabel}>6 Ay: </Text>
                <Text style={[
                  styles.performanceValue,
                  { color: option.performance.sixMonths >= 0 ? theme.colors.success : theme.colors.error }
                ]}>
                  {option.performance.sixMonths >= 0 ? '+' : ''}{option.performance.sixMonths.toFixed(1)}%
                </Text>
              </View>
            </View>

            <View style={styles.tagsContainer}>
              {option.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </TouchableOpacity>
        ))}

        {investmentOptions.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="trending-up-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>Yatırım seçeneği bulunamadı</Text>
            <Text style={styles.emptyStateSubtitle}>
              Şu anda mevcut yatırım seçeneği bulunmuyor
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default InvestmentOptionsScreen;
