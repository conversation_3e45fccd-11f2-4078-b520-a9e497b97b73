// Custom Drawer Content - Özel hamburger menu içeriği

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {
  DrawerContentScrollView,
  Drawer<PERSON>temList,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useSimpleAuth } from '../contexts/SimpleAuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import CurrencyExchangeModal from './CurrencyExchangeModal';

const CustomDrawerContent: React.FC<DrawerContentComponentProps> = (props) => {
  const { theme } = useTheme();
  const { user, logout } = useSimpleAuth();
  const { baseCurrency } = useCurrency();
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Günaydın';
    if (hour < 18) return 'İyi günler';
    return 'İyi akşamlar';
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header Section */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.profileSection}>
          <View style={[styles.profileImage, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="person" size={32} color={theme.colors.primary} />
          </View>
          <View style={styles.profileInfo}>
            <Text style={[styles.greeting, { color: theme.colors.surface }]}>
              {getGreeting()}
            </Text>
            <Text style={[styles.userName, { color: theme.colors.surface }]}>
              {user?.name || 'Kullanıcı'}
            </Text>
          </View>
        </View>
      </View>

      {/* Menu Items */}
      <DrawerContentScrollView {...props} style={styles.menuContainer}>
        <DrawerItemList {...props} />

        {/* Additional Menu Items */}
        <View style={styles.additionalItems}>
          <TouchableOpacity
            style={[styles.menuItem, { borderBottomColor: theme.colors.border }]}
            onPress={() => setShowCurrencyModal(true)}
          >
            <Ionicons name="cash-outline" size={24} color={theme.colors.textSecondary} />
            <Text style={[styles.menuItemText, { color: theme.colors.text }]}>
              Döviz Kurları
            </Text>
            <Text style={[styles.currencyBadge, { color: theme.colors.primary }]}>
              {baseCurrency.symbol}
            </Text>
          </TouchableOpacity>
        </View>
      </DrawerContentScrollView>

      {/* Footer */}
      <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={24} color={theme.colors.error} />
          <Text style={[styles.logoutText, { color: theme.colors.error }]}>
            Çıkış Yap
          </Text>
        </TouchableOpacity>
      </View>

      {/* Currency Exchange Modal */}
      <CurrencyExchangeModal
        visible={showCurrencyModal}
        onClose={() => setShowCurrencyModal(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    opacity: 0.9,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 4,
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  additionalItems: {
    marginTop: 20,
    paddingHorizontal: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 32,
    flex: 1,
  },
  currencyBadge: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    borderTopWidth: 1,
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
});

export default CustomDrawerContent;
