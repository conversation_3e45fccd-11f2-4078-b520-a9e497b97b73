// useResponsive Hook - Responsive tasarım hook'u

import { useState, useEffect } from 'react';
import { Dimensions, ScaledSize } from 'react-native';

interface ResponsiveInfo {
  width: number;
  height: number;
  isTablet: boolean;
  isLandscape: boolean;
  scale: number;
  fontScale: number;
}

export const useResponsive = (): ResponsiveInfo => {
  const [dimensions, setDimensions] = useState(() => {
    const { width, height, scale, fontScale } = Dimensions.get('window');
    return { width, height, scale, fontScale };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({
        width: window.width,
        height: window.height,
        scale: window.scale,
        fontScale: window.fontScale,
      });
    });

    return () => subscription?.remove();
  }, []);

  const isTablet = dimensions.width >= 768;
  const isLandscape = dimensions.width > dimensions.height;

  return {
    width: dimensions.width,
    height: dimensions.height,
    isTablet,
    isLandscape,
    scale: dimensions.scale,
    fontScale: dimensions.fontScale,
  };
};

// Responsive değerler için yardımcı fonksiyonlar
export const getResponsiveValue = (
  small: number,
  medium: number,
  large: number,
  width: number
): number => {
  if (width < 480) return small;
  if (width < 768) return medium;
  return large;
};

export const getResponsivePadding = (width: number): number => {
  return getResponsiveValue(16, 20, 24, width);
};

export const getResponsiveFontSize = (
  baseSize: number,
  fontScale: number,
  maxScale: number = 1.3
): number => {
  const scale = Math.min(fontScale, maxScale);
  return baseSize * scale;
};

export const getResponsiveColumns = (width: number): number => {
  if (width < 480) return 1;
  if (width < 768) return 2;
  if (width < 1024) return 3;
  return 4;
};
