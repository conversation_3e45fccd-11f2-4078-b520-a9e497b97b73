# RFC 005: Finansal Hedefler

## Özet
Bu RFC, Kişisel Finans Yönetimi uygulamasının finansal hedef belirleme ve takip özelliklerinin teknik tasarımını tanımlamaktadır.

## Motivasyon
Kullanıcıların finansal başarıya ulaşmaları için belirli, ölçülebilir ve zaman sınırlı hedefler belirlemeleri gerekmektedir. Finansal hedefler özelliği, kullanı<PERSON><PERSON><PERSON><PERSON>n ev alma, araba alma, tatil yapma, borç ödeme veya emeklilik gibi kısa, orta ve uzun vadeli finansal hedeflerini belirlemelerine ve izlemelerine olanak tanıyacaktır.

## Tasarım Detayları

### Hedef Veri Modeli
- **Hedef Ana Yapısı**
  - Hedef ID (unique identifier)
  - Kullanıcı ID (foreign key)
  - Hede<PERSON> adı
  - He<PERSON><PERSON> tipi (enum: ta<PERSON><PERSON><PERSON>, b<PERSON><PERSON>, ya<PERSON><PERSON><PERSON><PERSON><PERSON>, vb.)
  - <PERSON><PERSON><PERSON> (enum: ev, arab<PERSON>, tatil, em<PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON>, vb.)
  - Hede<PERSON> tutar
  - <PERSON>irikmiş/ödenmiş tutar
  - Başlangıç tarihi
  - Hedef bitiş tarihi
  - Öncelik seviyesi (enum: düşük, orta, yüksek)
  - Para birimi
  - Durum (enum: aktif, tamamlandı, ertelendi, iptal edildi)
  - Görsel (opsiyonel)
  - Notlar

- **Hedef İlerleme Takibi**
  - İlerleme ID (unique identifier)
  - Hedef ID (foreign key)
  - İşlem tipi (enum: katkı, çekim)
  - Tutar
  - Tarih
  - Açıklama

### Hedef Oluşturma ve Yönetim
- **Hedef Oluşturma İşlevi**
  - Hedef tipi ve kategorisi seçimi
  - Tutar ve süre belirleme
  - Görsel ve not ekleme
  - Öncelik belirleme
  - Düzenli katkı miktarı önerisi
  
- **Hedef Düzenleme ve Yönetim**
  - Hedef detaylarını düzenleme
  - Hedef ilerleme durumu güncelleme
  - Hedefi duraklatma/devam ettirme
  - Hedefi iptal etme/tamamlama
  - Birden fazla hedefe önceliklendirme

### Hedef İlerleme Takibi ve Bildirimler
- **İlerleme Takibi**
  - İlerleme yüzdesi hesaplama
  - Tahmini tamamlanma tarihi
  - Günlük/haftalık/aylık katkı önerisi
  - Hedefler arası fonların yeniden tahsisi
  
- **Bildirimler ve Hatırlatıcılar**
  - Hedef katkı hatırlatıcıları
  - İlerleme dönüm noktası bildirimleri (%25, %50, %75, %100)
  - Hedef gecikme uyarıları
  - Hedef tamamlama kutlamaları

### Hedef Önerileri ve Optimizasyon
- **Hedef Önerileri**
  - Kullanıcı yaşı ve durumuna göre hedef önerileri
  - Hedefler için optimum zaman çerçevesi önerisi
  - Benzer kullanıcı hedefleri bazında öneriler
  
- **Hedef Optimizasyonu**
  - Bütçe analizi ile uyumlu hedef ayarları
  - Paralel hedeflerin önceliklendirilmesi
  - Gerçekçi olmayan hedeflerin tespiti ve düzeltme önerileri

## Kullanıcı Arayüzü Bileşenleri
- Hedef oluşturma sihirbazı
- Hedefler listesi ve grid görünümü
- Hedef detay sayfası ve ilerleme göstergeleri
- İlerleme grafikleri ve zaman çizelgeleri
- Hedefler arası karşılaştırma ekranı

## Uygulama
- **Veri İşleme**
  - Hedef ilerleme hesaplamaları
  - Otomatik hedef katkı sistemi
  - Gerçekleşen vs. planlanan analizi
  
- **Entegrasyon Noktaları**
  - Bütçe modülü entegrasyonu
  - Tasarruf hesabı entegrasyonu
  - Bildirim sistemi entegrasyonu
  - Takvim entegrasyonu (maaş gününde otomatik katkı vb.)

## Alternatifler
- Sosyal hedefler (arkadaşlarla birlikte tasarruf)
- Yapay zeka destekli dinamik hedef ayarlamaları
- Hedeflere dayalı ödül/teşvik sistemi

## Açık Sorular
- Çoklu para birimi hedefleri nasıl yönetilecek?
- Ortak hedefler (aile veya eşler arası) nasıl yönetilecek?
- Uzun vadeli hedefler için enflasyon ayarlamaları nasıl yapılacak?

## Referanslar
- SMART hedef belirleme metodolojisi
- Davranışsal ekonomi prensipleri
- Finansal hedef belirleme psikolojisi araştırmaları
