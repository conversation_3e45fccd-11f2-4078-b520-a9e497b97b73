// Database Debug Screen - Veritabanı durumunu kontrol et

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import DatabaseManager from '../../database/DatabaseManager';
import BudgetSyncUtil from '../../utils/BudgetSyncUtil';
import BudgetDebugUtil from '../../utils/BudgetDebugUtil';
import IncomeHybridService from '../../services/IncomeHybridService';
import BudgetService from '../../services/BudgetService';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';

const DatabaseDebugScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [syncReport, setSyncReport] = useState<any>(null);
  const [forceUpdate, setForceUpdate] = useState(0);
  const [realBudgetCount, setRealBudgetCount] = useState(0);

  const checkDatabase = async () => {
    console.log('🔄 Starting checkDatabase...');
    setLoading(true);
    try {
      const db = DatabaseManager.getDatabase();
      console.log('✅ Database instance obtained');
      
      // Tabloları kontrol et
      const tables = await db.getAllAsync(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `);

      // Budgets tablosunu kontrol et
      const budgets = await db.getAllAsync('SELECT * FROM budgets ORDER BY created_at DESC');
      const activeBudgets = await db.getAllAsync('SELECT * FROM budgets WHERE is_active = 1 ORDER BY created_at DESC');
      
      // Budget categories tablosunu kontrol et
      const budgetCategories = await db.getAllAsync('SELECT * FROM budget_categories ORDER BY created_at DESC');
      
      // Categories tablosunu kontrol et
      const categories = await db.getAllAsync('SELECT * FROM categories ORDER BY created_at DESC');

      // Detailed budget info
      const budgetDetails = await db.getAllAsync(`
        SELECT
          b.*,
          COUNT(bc.id) as category_count,
          SUM(bc.planned_amount) as total_planned
        FROM budgets b
        LEFT JOIN budget_categories bc ON b.id = bc.budget_id AND bc.is_active = 1
        WHERE b.user_id = 'test_user_1' AND b.is_active = 1
        GROUP BY b.id
        ORDER BY b.created_at DESC
      `);

      // Raw budget_categories data
      const rawBudgetCategories = await db.getAllAsync(`
        SELECT
          bc.*,
          c.name as category_name,
          typeof(bc.planned_amount) as planned_amount_type
        FROM budget_categories bc
        LEFT JOIN categories c ON bc.category_id = c.id
        WHERE bc.is_active = 1
        ORDER BY bc.created_at DESC
        LIMIT 5
      `);

      // Test specific budget
      const testBudgetId = budgets.length > 0 ? budgets[0].id : null;
      let testBudgetCategories = [];
      if (testBudgetId) {
        testBudgetCategories = await db.getAllAsync(`
          SELECT
            bc.*,
            c.name as category_name,
            c.icon as category_icon,
            c.color as category_color,
            typeof(bc.planned_amount) as planned_amount_type,
            CAST(bc.planned_amount AS REAL) as planned_amount_real
          FROM budget_categories bc
          LEFT JOIN categories c ON bc.category_id = c.id
          WHERE bc.budget_id = ? AND bc.is_active = 1
        `, [testBudgetId]);
      }

      // Sync report al
      console.log('📊 Getting sync report...');
      const report = await BudgetSyncUtil.getBudgetSyncReport();
      console.log('✅ Sync report obtained:', report);
      setSyncReport(report);

      const newDebugInfo = {
        tables: tables.map(t => t.name),
        budgets,
        budgetCategories,
        categories,
        budgetDetails,
        rawBudgetCategories,
        testBudgetCategories,
        testBudgetId,
        tableCount: tables.length,
        budgetCount: budgets.length,
        activeBudgetCount: activeBudgets.length,
        categoryCount: categories.length,
        budgetCategoryCount: budgetCategories.length,
      };

      console.log('🔄 Setting new debug info:', { budgetCount: newDebugInfo.budgetCount });
      setDebugInfo(newDebugInfo);
      setRealBudgetCount(budgets.length);
      setForceUpdate(prev => prev + 1);

      console.log('🔍 Database Debug Info:', {
        tables: tables.map(t => t.name),
        budgetCount: budgets.length,
        activeBudgetCount: activeBudgets.length,
        categoryCount: categories.length,
        budgetCategoryCount: budgetCategories.length,
      });

      console.log('📊 Budget Details:', {
        totalBudgets: budgets.length,
        activeBudgets: activeBudgets.length,
        budgetData: budgets.map(b => ({ id: b.id, name: b.name, is_active: b.is_active, user_id: b.user_id }))
      });

      // User ID'leri kontrol et
      const userIds = await db.getAllAsync('SELECT DISTINCT user_id FROM transactions LIMIT 10');
      console.log('👥 Transaction user IDs:', userIds);

      // Transactions tablo yapısını kontrol et
      const transactionSchema = await db.getAllAsync(`PRAGMA table_info(transactions)`);
      console.log('📋 Transactions table schema:', transactionSchema);

      // İlk birkaç transaction'ı kontrol et
      const sampleTransactions = await db.getAllAsync(`
        SELECT * FROM transactions
        WHERE user_id = '1748368158014'
        LIMIT 3
      `);
      console.log('💳 Sample transactions:', sampleTransactions);

      // Bütçe kategori ID'lerini kontrol et
      const budgetCategoryIds = await db.getAllAsync(`
        SELECT bc.category_id, c.name
        FROM budget_categories bc
        LEFT JOIN categories c ON bc.category_id = c.id
        WHERE bc.is_active = 1
      `);
      console.log('📂 Budget category IDs:', budgetCategoryIds);

      console.log('✅ checkDatabase completed successfully!');

    } catch (error) {
      console.error('❌ Database debug error:', error);
      Alert.alert('Hata', `Veritabanı kontrol edilirken hata oluştu: ${error.message}`);
    } finally {
      console.log('🏁 checkDatabase finished, setting loading to false');
      setLoading(false);
    }
  };

  const clearAllData = async () => {
    Alert.alert(
      'Tüm Verileri Sil',
      'Tüm bütçe verilerini silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              const db = DatabaseManager.getDatabase();
              await db.runAsync('DELETE FROM budget_categories');
              await db.runAsync('DELETE FROM budgets');
              await db.runAsync('DELETE FROM categories');
              Alert.alert('Başarılı', 'Tüm veriler silindi.');
              checkDatabase();
            } catch (error) {
              Alert.alert('Hata', 'Veriler silinirken hata oluştu.');
            }
          }
        }
      ]
    );
  };

  const syncBudgets = async () => {
    try {
      setLoading(true);
      await BudgetSyncUtil.syncAllBudgetCategories();
      Alert.alert('Başarılı', 'Bütçe kategorileri senkronize edildi!');
      checkDatabase(); // Refresh data
    } catch (error) {
      Alert.alert('Hata', 'Senkronizasyon sırasında hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleTestPress = async () => {
    console.log('🟠 TEST BUTTON PRESSED - WORKING!');
    console.log('🟠 Starting sync directly...');
    await syncTestUser();
    console.log('🟠 Sync completed, showing success alert...');
    Alert.alert('Başarılı', 'Test sync tamamlandı!');
  };

  const syncTestUser = async () => {
    console.log('🟠 Test button clicked!');
    try {
      setLoading(true);
      console.log('🧪 Starting test user sync...');
      await BudgetSyncUtil.syncTestUserBudgets();
      console.log('✅ Test sync completed, showing alert...');
      Alert.alert('Başarılı', 'Test kullanıcı bütçeleri senkronize edildi!');
      console.log('🔄 Refreshing database...');
      checkDatabase(); // Refresh data
    } catch (error) {
      console.error('❌ Test sync error:', error);
      Alert.alert('Hata', `Test kullanıcı senkronizasyonu sırasında hata oluştu: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkDatabase();
  }, []);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 60,
      paddingBottom: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    buttonRow: {
      flexDirection: 'row',
      gap: 8,
    },
    refreshButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    refreshText: {
      color: theme.colors.surface,
      fontWeight: '600',
    },
    syncButton: {
      backgroundColor: '#4CAF50',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    syncText: {
      color: '#fff',
      fontWeight: '600',
    },
    testSyncButton: {
      backgroundColor: '#FF9800',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      minWidth: 80,
    },
    testSyncText: {
      color: '#fff',
      fontWeight: '600',
    },
    section: {
      margin: 20,
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 12,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    infoLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    infoValue: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
    },
    dataContainer: {
      backgroundColor: theme.colors.background,
      padding: 12,
      borderRadius: 8,
      marginTop: 8,
    },
    dataText: {
      fontSize: 12,
      fontFamily: 'monospace',
      color: theme.colors.text,
    },
    clearButton: {
      backgroundColor: theme.colors.error,
      margin: 20,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    clearButtonText: {
      color: theme.colors.surface,
      fontSize: 16,
      fontWeight: 'bold',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Debug {loading ? '(Loading...)' : ''}</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={checkDatabase}
            disabled={loading}
          >
            <Text style={styles.refreshText}>
              {loading ? 'Yükleniyor...' : 'Yenile'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.syncButton}
            onPress={syncBudgets}
            disabled={loading}
          >
            <Text style={styles.syncText}>Sync</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.testSyncButton}
            onPress={() => {
              console.log('🟠 TEST BUTTON CLICKED!');
              Alert.alert('Test', 'Test butonu çalışıyor!', [
                { text: 'Sync Yap', onPress: async () => {
                  console.log('🟠 STARTING SYNC FROM ALERT!');
                  try {
                    await BudgetSyncUtil.syncAllBudgetCategories();
                    Alert.alert('Başarılı', 'Sync tamamlandı!');
                    checkDatabase();
                  } catch (error) {
                    Alert.alert('Hata', error.message);
                  }
                }},
                { text: 'İptal', style: 'cancel' }
              ]);
            }}
          >
            <Text style={styles.testSyncText}>🟠 TEST</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView>
        {/* Sync Report */}
        {syncReport && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔄 Senkronizasyon Raporu</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Toplam Bütçe:</Text>
              <Text style={styles.infoValue}>{syncReport.totalBudgets}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Toplam Kategori:</Text>
              <Text style={styles.infoValue}>{syncReport.totalCategories}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Toplam İşlem:</Text>
              <Text style={styles.infoValue}>{syncReport.totalTransactions}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Senkronize Kategoriler:</Text>
              <Text style={[styles.infoValue, { color: '#4CAF50' }]}>{syncReport.syncedCategories}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Senkronize Olmayan:</Text>
              <Text style={[styles.infoValue, { color: '#F44336' }]}>{syncReport.unsyncedCategories}</Text>
            </View>
          </View>
        )}

        {/* Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 Özet</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Toplam Tablo:</Text>
            <Text style={styles.infoValue}>{debugInfo.tableCount || 0}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Toplam Bütçe:</Text>
            <Text style={styles.infoValue}>
              State: {debugInfo.budgetCount || 0} | Real: {realBudgetCount} | Update: {forceUpdate}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Aktif Bütçe:</Text>
            <Text style={styles.infoValue}>{debugInfo.activeBudgetCount || 0}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Kategori Sayısı:</Text>
            <Text style={styles.infoValue}>{debugInfo.categoryCount || 0}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Bütçe Kategorisi:</Text>
            <Text style={styles.infoValue}>{debugInfo.budgetCategoryCount || 0}</Text>
          </View>
        </View>

        {/* Tables */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 Tablolar</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {debugInfo.tables?.join(', ') || 'Yükleniyor...'}
            </Text>
          </View>
        </View>

        {/* Budgets */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💰 Bütçeler ({debugInfo.budgetCount || 0})</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {JSON.stringify(debugInfo.budgets, null, 2)}
            </Text>
          </View>
        </View>

        {/* Budget Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📂 Bütçe Kategorileri ({debugInfo.budgetCategoryCount || 0})</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {JSON.stringify(debugInfo.budgetCategories, null, 2)}
            </Text>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏷️ Kategoriler ({debugInfo.categoryCount || 0})</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {JSON.stringify(debugInfo.categories, null, 2)}
            </Text>
          </View>
        </View>

        {/* Budget Details Query */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔍 Bütçe Detayları (JOIN Query)</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {JSON.stringify(debugInfo.budgetDetails, null, 2)}
            </Text>
          </View>
        </View>

        {/* Raw Budget Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔬 Raw Budget Categories (İlk 5)</Text>
          <View style={styles.dataContainer}>
            <Text style={styles.dataText}>
              {JSON.stringify(debugInfo.rawBudgetCategories, null, 2)}
            </Text>
          </View>
        </View>

        {/* Test Budget Categories */}
        {debugInfo.testBudgetId && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🧪 Test Budget Categories (ID: {debugInfo.testBudgetId})</Text>
            <View style={styles.dataContainer}>
              <Text style={styles.dataText}>
                {JSON.stringify(debugInfo.testBudgetCategories, null, 2)}
              </Text>
            </View>
          </View>
        )}

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#2196F3', marginBottom: 10 }]}
          onPress={async () => {
            console.log('💳 MANUEL İŞLEM OLUŞTURMA!');
            try {
              setLoading(true);
              const db = DatabaseManager.getDatabase();

              // Gerçek user ID'yi al (bütçe ile aynı user)
              const budget = await db.getFirstAsync('SELECT user_id FROM budgets WHERE is_active = 1 LIMIT 1');
              const userId = budget?.user_id || Date.now().toString();

              console.log('👤 Creating transaction for user:', userId);

              // Manuel işlem oluştur
              const transactionId = `txn_${Date.now()}_test`;
              await db.runAsync(`
                INSERT INTO transactions (
                  id, user_id, type, amount, currency, category, subcategory,
                  description, merchant, payment_method, date, status,
                  is_business_expense, is_deductible, tax_amount,
                  created_at, updated_at, is_deleted
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                transactionId, userId, 'expense', 250, 'TRY', 'food', 'restaurant',
                'Test yemek gideri', 'Test Restaurant', 'credit_card',
                new Date().toISOString(), 'completed', 0, 0, 0,
                new Date().toISOString(), new Date().toISOString(), 0
              ]);

              console.log('✅ Manuel işlem oluşturuldu:', transactionId);
              Alert.alert('Başarılı', 'Manuel işlem oluşturuldu! (250 TL yemek)');
              checkDatabase();
            } catch (error) {
              console.error('❌ Manuel işlem hatası:', error);
              Alert.alert('Hata', `İşlem hatası: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }}
        >
          <Text style={styles.clearButtonText}>💳 MANUEL İŞLEM</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#9C27B0', marginBottom: 10 }]}
          onPress={async () => {
            console.log('🏷️ BÜTÇE KATEGORİSİ EKLEME!');
            try {
              setLoading(true);
              const db = DatabaseManager.getDatabase();

              // Aktif bütçeyi bul
              const budget = await db.getFirstAsync('SELECT * FROM budgets WHERE is_active = 1 LIMIT 1');
              if (!budget) {
                Alert.alert('Hata', 'Aktif bütçe bulunamadı!');
                return;
              }

              console.log('📊 Found budget:', budget.id);

              // Food kategorisini bul
              const category = await db.getFirstAsync('SELECT * FROM categories WHERE name = ? LIMIT 1', ['food']);
              if (!category) {
                Alert.alert('Hata', 'Food kategorisi bulunamadı!');
                return;
              }

              console.log('🍽️ Found category:', category.id);

              // Bütçe kategorisi oluştur
              const categoryBudgetId = `bc_${Date.now()}_test`;
              await db.runAsync(`
                INSERT INTO budget_categories (
                  id, budget_id, category_id, planned_amount, spent_amount,
                  warning_threshold, is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                categoryBudgetId, budget.id, category.id, 1000, 0, 80, 1,
                new Date().toISOString(), new Date().toISOString()
              ]);

              console.log('✅ Bütçe kategorisi oluşturuldu:', categoryBudgetId);
              Alert.alert('Başarılı', 'Food kategorisi bütçeye eklendi! (1000 TL)');
              checkDatabase();
            } catch (error) {
              console.error('❌ Kategori ekleme hatası:', error);
              Alert.alert('Hata', `Kategori hatası: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🏷️ KATEGORİ EKLE</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF9800', marginBottom: 10 }]}
          onPress={async () => {
            console.log('🔍 SYNC DEBUG BAŞLADI!');
            try {
              setLoading(true);
              const db = DatabaseManager.getDatabase();

              // 1. Bütçe kategorilerini kontrol et
              const budgetCategories = await db.getAllAsync(`
                SELECT bc.*, b.user_id, b.start_date, b.end_date, c.name as category_name
                FROM budget_categories bc
                JOIN budgets b ON bc.budget_id = b.id
                JOIN categories c ON bc.category_id = c.id
                WHERE bc.is_active = 1 AND b.is_active = 1
              `);
              console.log('📊 Budget categories:', budgetCategories);

              // 2. İşlemleri kontrol et
              const transactions = await db.getAllAsync(`
                SELECT * FROM transactions
                WHERE type = 'expense' AND is_deleted = 0
                ORDER BY created_at DESC LIMIT 5
              `);
              console.log('💳 Recent transactions:', transactions);

              // 3. Kategori eşleşmesini kontrol et
              for (const bc of budgetCategories) {
                const matchingTxns = await db.getAllAsync(`
                  SELECT * FROM transactions
                  WHERE user_id = ? AND category = ? AND type = 'expense' AND is_deleted = 0
                `, [bc.user_id, bc.category_name]);
                console.log(`🔍 Category ${bc.category_name} matches:`, matchingTxns.length, 'transactions');
              }

              // 4. Sync çalıştır
              await BudgetSyncUtil.syncAllBudgetCategories();
              Alert.alert('Debug', 'Sync debug tamamlandı! Console\'u kontrol edin.');
              checkDatabase();
            } catch (error) {
              console.error('❌ Sync debug error:', error);
              Alert.alert('Hata', `Debug hatası: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔍 SYNC DEBUG</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF9800', marginBottom: 20 }]}
          onPress={async () => {
            console.log('🟠 MANUAL TEST BUTTON CLICKED!');
            console.log('🟠 STARTING SYNC FROM MANUAL BUTTON!');
            try {
              setLoading(true);
              await BudgetSyncUtil.syncAllBudgetCategories();
              Alert.alert('Başarılı', 'Manuel sync tamamlandı!');
              checkDatabase();
            } catch (error) {
              console.error('❌ Manuel sync error:', error);
              Alert.alert('Hata', `Sync hatası: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🟠 MANUEL SYNC</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF4444', marginBottom: 10 }]}
          onPress={async () => {
            Alert.alert(
              'KAPSAMLI TEMİZLİK',
              'TÜM VERİTABANI SIFIRLANACAK!\n\n• Tüm kullanıcılar\n• Tüm bütçeler\n• Tüm işlemler\n• Tüm kategoriler\n\nEmin misiniz?',
              [
                { text: 'İptal', style: 'cancel' },
                {
                  text: 'SIFIRLA',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      setLoading(true);
                      const db = DatabaseManager.getDatabase();

                      console.log('🧹 KAPSAMLI VERİTABANI TEMİZLİĞİ BAŞLIYOR...');

                      // Tüm tabloları temizle
                      await db.runAsync('DELETE FROM transactions');
                      await db.runAsync('DELETE FROM budget_categories');
                      await db.runAsync('DELETE FROM budgets');
                      await db.runAsync('DELETE FROM categories');
                      await db.runAsync('DELETE FROM user_settings');
                      await db.runAsync('DELETE FROM goals');
                      await db.runAsync('DELETE FROM receipts');
                      await db.runAsync('DELETE FROM sync_records');

                      console.log('🧹 Tüm tablolar temizlendi');

                      Alert.alert('Başarılı', 'Veritabanı tamamen temizlendi!');
                      checkDatabase();
                    } catch (error) {
                      console.error('❌ Temizlik hatası:', error);
                      Alert.alert('Hata', `Temizlik hatası: ${error.message}`);
                    } finally {
                      setLoading(false);
                    }
                  }
                }
              ]
            );
          }}
        >
          <Text style={styles.clearButtonText}>🧹 KAPSAMLI TEMİZLİK</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#8B0000', marginBottom: 10 }]}
          onPress={async () => {
            Alert.alert(
              '💥 KOMPLE RESET',
              'VERİTABANI TAMAMEN SIFIRLANIP YENİDEN OLUŞTURULACAK!\n\n⚠️ Bu işlem geri alınamaz!\n\n• Tüm tablolar silinecek\n• Schema yeniden oluşturulacak\n• Default veriler eklenecek',
              [
                { text: 'İptal', style: 'cancel' },
                {
                  text: '💥 RESET',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      setLoading(true);
                      const db = DatabaseManager.getDatabase();

                      console.log('💥 KOMPLE VERİTABANI RESET BAŞLIYOR...');

                      // Tüm tabloları sil
                      const tables = [
                        'transaction_tags', 'receipts', 'transactions', 'budget_categories',
                        'budgets', 'goals', 'recurring_templates', 'category_rules',
                        'categories', 'tags', 'exchange_rates', 'sync_records', 'user_settings'
                      ];

                      for (const table of tables) {
                        await db.execAsync(`DROP TABLE IF EXISTS ${table}`);
                        console.log(`🗑️ Dropped table: ${table}`);
                      }

                      console.log('🔄 Recreating tables manually...');

                      // Tabloları manuel oluştur

                      // Users tablosu
                      await db.execAsync(`
                        CREATE TABLE IF NOT EXISTS users (
                          id TEXT PRIMARY KEY,
                          email TEXT UNIQUE NOT NULL,
                          name TEXT NOT NULL,
                          password_hash TEXT,
                          avatar TEXT,
                          phone TEXT,
                          date_of_birth TEXT,
                          gender TEXT,
                          occupation TEXT,
                          monthly_income REAL DEFAULT 0,
                          currency TEXT DEFAULT 'TRY',
                          language TEXT DEFAULT 'tr',
                          timezone TEXT DEFAULT 'Europe/Istanbul',
                          is_active INTEGER DEFAULT 1,
                          is_verified INTEGER DEFAULT 0,
                          last_login_at TEXT,
                          created_at TEXT NOT NULL,
                          updated_at TEXT NOT NULL
                        );
                      `);

                      await db.execAsync(`
                        CREATE TABLE IF NOT EXISTS budgets (
                          id TEXT PRIMARY KEY,
                          user_id TEXT NOT NULL,
                          name TEXT NOT NULL,
                          period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'quarterly', 'annually')),
                          start_date TEXT NOT NULL,
                          end_date TEXT NOT NULL,
                          total_income_target REAL DEFAULT 0 CHECK (total_income_target >= 0),
                          total_expense_limit REAL DEFAULT 0 CHECK (total_expense_limit >= 0),
                          savings_target REAL DEFAULT 0 CHECK (savings_target >= 0),
                          currency TEXT NOT NULL DEFAULT 'TRY',
                          notes TEXT,
                          template_name TEXT,
                          copied_from_budget_id TEXT,
                          is_active INTEGER DEFAULT 1,
                          created_at TEXT NOT NULL,
                          updated_at TEXT NOT NULL
                        );
                      `);

                      await db.execAsync(`
                        CREATE TABLE IF NOT EXISTS budget_categories (
                          id TEXT PRIMARY KEY,
                          budget_id TEXT NOT NULL,
                          category_id TEXT NOT NULL,
                          planned_amount REAL NOT NULL CHECK (planned_amount > 0),
                          spent_amount REAL DEFAULT 0,
                          remaining_amount REAL GENERATED ALWAYS AS (planned_amount - spent_amount) STORED,
                          warning_threshold REAL DEFAULT 50 CHECK (warning_threshold BETWEEN 0 AND 100),
                          critical_threshold REAL DEFAULT 80 CHECK (critical_threshold BETWEEN 0 AND 100),
                          limit_threshold REAL DEFAULT 100 CHECK (limit_threshold BETWEEN 0 AND 200),
                          warning_enabled INTEGER DEFAULT 1,
                          critical_enabled INTEGER DEFAULT 1,
                          limit_enabled INTEGER DEFAULT 1,
                          is_active INTEGER DEFAULT 1,
                          created_at TEXT NOT NULL,
                          updated_at TEXT NOT NULL
                        );
                      `);

                      await db.execAsync(`
                        CREATE TABLE IF NOT EXISTS categories (
                          id TEXT PRIMARY KEY,
                          user_id TEXT NOT NULL,
                          name TEXT NOT NULL,
                          type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
                          icon TEXT,
                          color TEXT,
                          parent_category_id TEXT,
                          sort_order INTEGER DEFAULT 0,
                          is_active INTEGER DEFAULT 1,
                          created_at TEXT NOT NULL,
                          updated_at TEXT NOT NULL,
                          UNIQUE(user_id, name, type)
                        );
                      `);

                      await db.execAsync(`
                        CREATE TABLE IF NOT EXISTS transactions (
                          id TEXT PRIMARY KEY,
                          user_id TEXT NOT NULL,
                          type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
                          amount REAL NOT NULL CHECK (amount > 0),
                          currency TEXT NOT NULL DEFAULT 'TRY',
                          category TEXT NOT NULL,
                          subcategory TEXT,
                          description TEXT,
                          merchant TEXT,
                          payment_method TEXT,
                          date TEXT NOT NULL,
                          budget_id TEXT,
                          status TEXT DEFAULT 'completed',
                          is_business_expense INTEGER DEFAULT 0,
                          is_deductible INTEGER DEFAULT 0,
                          tax_amount REAL DEFAULT 0,
                          created_at TEXT NOT NULL,
                          updated_at TEXT NOT NULL,
                          is_deleted INTEGER DEFAULT 0
                        );
                      `);

                      // Default kategorileri ekle
                      const defaultCategories = [
                        { name: 'food', type: 'expense', icon: '🍽️', color: '#FF6B6B' },
                        { name: 'transport', type: 'expense', icon: '🚗', color: '#4ECDC4' },
                        { name: 'shopping', type: 'expense', icon: '🛍️', color: '#45B7D1' },
                        { name: 'bills', type: 'expense', icon: '📄', color: '#96CEB4' },
                        { name: 'salary', type: 'income', icon: '💰', color: '#FFEAA7' }
                      ];

                      for (const cat of defaultCategories) {
                        const catId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        await db.runAsync(`
                          INSERT OR IGNORE INTO categories (id, user_id, name, type, icon, color, created_at, updated_at)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        `, [catId, 'system', cat.name, cat.type, cat.icon, cat.color, new Date().toISOString(), new Date().toISOString()]);
                      }

                      // Test kullanıcısı ekle
                      const testUserId = 'test_user_' + Date.now();
                      await db.runAsync(`
                        INSERT INTO users (id, email, name, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                      `, [testUserId, '<EMAIL>', 'Test User', new Date().toISOString(), new Date().toISOString()]);

                      console.log('👤 Test user created:', testUserId);
                      console.log('✅ Tables recreated manually!');

                      console.log('✅ Database completely reset and reinitialized!');
                      Alert.alert('Başarılı', 'Veritabanı tamamen sıfırlandı ve yeniden oluşturuldu!');
                      checkDatabase();
                    } catch (error) {
                      console.error('❌ Reset error:', error);
                      Alert.alert('Hata', `Reset hatası: ${error.message}`);
                    } finally {
                      setLoading(false);
                    }
                  }
                }
              ]
            );
          }}
        >
          <Text style={styles.clearButtonText}>💥 KOMPLE RESET</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#4CAF50', marginBottom: 10 }]}
          onPress={async () => {
            console.log('🟢 ADIM 1: MANUEL BÜTÇE OLUŞTURMA!');
            try {
              setLoading(true);
              const db = DatabaseManager.getDatabase();

              // İlk kullanıcıyı al
              const user = await db.getFirstAsync('SELECT * FROM users ORDER BY created_at DESC LIMIT 1');
              if (!user) {
                Alert.alert('Hata', 'Kullanıcı bulunamadı!');
                return;
              }

              console.log('👤 Using user:', user.id, user.name);

              // Bütçe oluştur
              const budgetId = `budget_${Date.now()}_manual`;
              const now = new Date().toISOString();
              const startDate = '2025-06-01';
              const endDate = '2025-06-30';

              await db.runAsync(`
                INSERT INTO budgets (
                  id, user_id, name, period, start_date, end_date,
                  total_income_target, total_expense_limit, savings_target,
                  currency, notes, is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                budgetId, user.id, 'Haziran 2025 Bütçesi', 'monthly',
                startDate, endDate, 10000, 8000, 2000,
                'TRY', 'Manuel test bütçesi', 1, now, now
              ]);

              console.log('✅ Bütçe oluşturuldu:', budgetId);
              Alert.alert('ADIM 1 ✅', `Bütçe oluşturuldu!\nID: ${budgetId}\nUser: ${user.name}`);
              checkDatabase();
            } catch (error) {
              console.error('❌ Bütçe oluşturma hatası:', error);
              Alert.alert('Hata', `Bütçe hatası: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🟢 ADIM 1: BÜTÇE OLUŞTUR</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF9800', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const AsyncStorage = require('@react-native-async-storage/async-storage').default;

              // Test user bilgilerini SimpleAuthContext'e set et
              const testUser = {
                id: 'test_user_1748893907946',
                email: '<EMAIL>',
                name: 'Test User',
                createdAt: new Date().toISOString(),
                preferences: {
                  currency: 'TRY',
                  language: 'tr',
                  notifications: true,
                  biometric: false,
                }
              };

              await AsyncStorage.setItem('simple_user', JSON.stringify(testUser));
              console.log('👤 Test user set in SimpleAuthContext:', testUser.id);

              Alert.alert(
                'Test User Login ✅',
                `User ID: ${testUser.id}\nEmail: ${testUser.email}\n\nŞimdi uygulamayı yeniden başlatın!`,
                [
                  { text: 'Tamam', onPress: () => {
                    // Uygulamayı yeniden başlatma talimatı
                    console.log('🔄 Please restart the app to see the budgets!');
                  }}
                ]
              );
            } catch (error) {
              console.error('❌ Test user login error:', error);
              Alert.alert('Hata', `Test user login hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>👤 TEST USER LOGIN</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#9C27B0', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // budget_categories tablosunun şemasını kontrol et
              const schema = await db.getAllAsync('PRAGMA table_info(budget_categories)');
              console.log('📋 budget_categories table schema:', schema);

              // daily_digest_enabled kolonu var mı?
              const hasColumn = schema.some(col => col.name === 'daily_digest_enabled');
              console.log('🔍 daily_digest_enabled column exists:', hasColumn);

              Alert.alert(
                'Tablo Şeması',
                `budget_categories tablosunda ${schema.length} kolon var.\ndaily_digest_enabled: ${hasColumn ? 'VAR ✅' : 'YOK ❌'}\n\nConsole'da detayları görün.`
              );
            } catch (error) {
              console.error('❌ Schema check error:', error);
              Alert.alert('Hata', `Şema kontrolü hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>📋 TABLO ŞEMASI</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#2196F3', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();
              const users = await db.getAllAsync('SELECT * FROM users ORDER BY created_at DESC');
              console.log('👥 All users:', users);
              Alert.alert('Kullanıcılar', `${users.length} kullanıcı bulundu. Console'u kontrol edin.`);
            } catch (error) {
              console.error('❌ Users query error:', error);
              Alert.alert('Hata', `Kullanıcılar alınamadı: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>👥 KULLANICILARI GÖSTER</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF5722', marginBottom: 10 }]}
          onPress={async () => {
            try {
              Alert.alert(
                'VERİTABANI SIFIRLAMA',
                'Bu işlem tüm verileri silecek ve veritabanını yeniden oluşturacak. Emin misiniz?',
                [
                  { text: 'İptal', style: 'cancel' },
                  {
                    text: 'SIFIRLA',
                    style: 'destructive',
                    onPress: async () => {
                      try {
                        const DatabaseManager = require('../../database/DatabaseManager').default;

                        // Veritabanını tamamen sıfırla
                        await DatabaseManager.resetDatabase();
                        console.log('🔄 Database reset completed');

                        Alert.alert('Başarılı ✅', 'Veritabanı sıfırlandı ve yeniden oluşturuldu!');

                        // Debug bilgilerini yenile
                        checkDatabase();
                      } catch (error) {
                        console.error('❌ Database reset error:', error);
                        Alert.alert('Hata', `Veritabanı sıfırlama hatası: ${error.message}`);
                      }
                    }
                  }
                ]
              );
            } catch (error) {
              console.error('❌ Reset preparation error:', error);
              Alert.alert('Hata', `Hazırlık hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔄 VERİTABANI SIFIRLA</Text>
        </TouchableOpacity>

        {/* Budget Debug Buttons */}
        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#2196F3', marginBottom: 10 }]}
          onPress={async () => {
            try {
              // Test bütçesi ID'sini al (en son oluşturulan)
              const db = DatabaseManager.getDatabase();
              const budget = await db.getFirstAsync(
                'SELECT id FROM budgets WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
                ['test_user_123']
              );

              if (budget) {
                await BudgetDebugUtil.debugBudgetCalculation(budget.id);
                Alert.alert('Debug Tamamlandı', 'Console\'da detayları görün.');
              } else {
                Alert.alert('Hata', 'Debug edilecek bütçe bulunamadı.');
              }
            } catch (error) {
              console.error('❌ Budget debug error:', error);
              Alert.alert('Hata', `Debug hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔍 Bütçe Debug</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#4CAF50', marginBottom: 10 }]}
          onPress={async () => {
            try {
              // Test bütçesi ID'sini al
              const db = DatabaseManager.getDatabase();
              const budget = await db.getFirstAsync(
                'SELECT id FROM budgets WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
                ['test_user_123']
              );

              if (budget) {
                await BudgetDebugUtil.recalculateBudgetSpending(budget.id);
                Alert.alert('Başarılı ✅', 'Bütçe harcamaları yeniden hesaplandı!');
              } else {
                Alert.alert('Hata', 'Yeniden hesaplanacak bütçe bulunamadı.');
              }
            } catch (error) {
              console.error('❌ Budget recalculation error:', error);
              Alert.alert('Hata', `Yeniden hesaplama hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔄 Bütçe Yeniden Hesapla</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF9800', marginBottom: 10 }]}
          onPress={async () => {
            try {
              await BudgetDebugUtil.recreateTriggers();
              Alert.alert('Başarılı ✅', 'Trigger\'lar yeniden oluşturuldu!');
            } catch (error) {
              console.error('❌ Trigger recreation error:', error);
              Alert.alert('Hata', `Trigger hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>⚡ Trigger\'ları Yenile</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#9C27B0', marginBottom: 10 }]}
          onPress={async () => {
            try {
              await BudgetDebugUtil.recalculateAllBudgets('test_user_123');
              Alert.alert('Başarılı ✅', 'Tüm bütçeler yeniden hesaplandı!');
            } catch (error) {
              console.error('❌ All budgets recalculation error:', error);
              Alert.alert('Hata', `Toplu hesaplama hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🎯 Tüm Bütçeleri Hesapla</Text>
        </TouchableOpacity>

        {/* Income Hybrid Debug Buttons */}
        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#00BCD4', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // Migration V17 kontrol et
              const tables = await db.getAllAsync(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='budget_income_categories'"
              );

              if (tables.length === 0) {
                Alert.alert('❌ Migration Sorunu', 'budget_income_categories tablosu bulunamadı!\n\nMigration V17 çalışmamış.');
                return;
              }

              // Gelir kategorilerini kontrol et
              const incomeCategories = await db.getAllAsync(
                "SELECT * FROM budget_income_categories LIMIT 5"
              );

              // Gelir işlemlerini kontrol et
              const incomeTransactions = await db.getAllAsync(
                "SELECT * FROM transactions WHERE type = 'income' ORDER BY created_at DESC LIMIT 5"
              );

              console.log('💰 INCOME HYBRID DEBUG:');
              console.log('📊 Income categories table exists:', tables.length > 0);
              console.log('📋 Income categories count:', incomeCategories.length);
              console.log('💳 Income transactions count:', incomeTransactions.length);
              console.log('💰 Income categories:', incomeCategories);
              console.log('💳 Income transactions:', incomeTransactions);

              Alert.alert(
                '💰 Gelir Hibrit Debug',
                `Tablo: ${tables.length > 0 ? '✅' : '❌'}\n` +
                `Gelir kategorileri: ${incomeCategories.length}\n` +
                `Gelir işlemleri: ${incomeTransactions.length}\n\n` +
                'Detaylar console\'da!'
              );
            } catch (error) {
              console.error('❌ Income hybrid debug error:', error);
              Alert.alert('Hata', `Debug hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>💰 Gelir Hibrit Debug</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#4CAF50', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // Test bütçesi oluştur
              const budgetId = `budget_${Date.now()}_test`;
              const now = new Date().toISOString();
              const startDate = new Date().toISOString().split('T')[0];
              const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

              await db.runAsync(
                `INSERT INTO budgets (
                  id, user_id, name, period, start_date, end_date,
                  total_income_target, total_expense_limit, savings_target,
                  currency, is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                  budgetId,
                  'test_user_123',
                  'Test Gelir Bütçesi',
                  'monthly',
                  startDate,
                  endDate,
                  25000, // Toplam gelir hedefi
                  20000, // Gider limiti
                  5000,  // Tasarruf hedefi
                  'TRY',
                  1,
                  now,
                  now
                ]
              );

              // Test gelir kategorileri ekle
              const incomeCategories = await db.getAllAsync(
                "SELECT id FROM categories WHERE type = 'income' LIMIT 3"
              );

              if (incomeCategories.length === 0) {
                Alert.alert('Hata', 'Gelir kategorileri bulunamadı. Önce kategorileri kontrol edin.');
                return;
              }

              const testIncomeCategories = [
                { categoryId: incomeCategories[0]?.id, targetAmount: 15000 }, // İlk gelir kategorisi
                { categoryId: incomeCategories[1]?.id, targetAmount: 5000 },  // İkinci gelir kategorisi
                { categoryId: incomeCategories[2]?.id, targetAmount: 5000 },  // Üçüncü gelir kategorisi
              ].filter(cat => cat.categoryId); // Sadece geçerli kategori ID'leri

              await IncomeHybridService.addIncomeCategoriesToBudget(budgetId, testIncomeCategories);

              Alert.alert(
                '✅ Test Bütçesi Oluşturuldu!',
                `Bütçe ID: ${budgetId}\n` +
                `Gelir kategorileri: ${testIncomeCategories.length}\n` +
                `Toplam gelir hedefi: 25.000 ₺\n\n` +
                'Artık gelir işlemleri otomatik bağlanacak!'
              );
            } catch (error) {
              console.error('❌ Create test budget error:', error);
              Alert.alert('Hata', `Test bütçesi oluşturulamadı: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🏗️ Test Bütçesi + Gelir Kategorileri</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#8BC34A', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // En son bütçeyi al (user_id'ye bakmadan)
              const budget = await db.getFirstAsync(
                'SELECT id, name, user_id FROM budgets ORDER BY created_at DESC LIMIT 1'
              );

              if (!budget) {
                Alert.alert('Hata', 'Test edilecek bütçe bulunamadı.\n\nÖnce "🏗️ Test Bütçesi + Gelir Kategorileri" butonuna tıklayın.');
                return;
              }

              // Mevcut gelir kategorilerini kontrol et
              const existingIncomeCategories = await db.getAllAsync(
                'SELECT * FROM budget_income_categories WHERE budget_id = ?',
                [budget.id]
              );

              if (existingIncomeCategories.length > 0) {
                Alert.alert('ℹ️ Zaten Var', `Bu bütçede ${existingIncomeCategories.length} gelir kategorisi zaten mevcut.`);
                return;
              }

              // Gelir kategorilerini al
              const incomeCategories = await db.getAllAsync(
                "SELECT id FROM categories WHERE type = 'income' LIMIT 3"
              );

              if (incomeCategories.length === 0) {
                Alert.alert('Hata', 'Gelir kategorileri bulunamadı.');
                return;
              }

              // Test gelir kategorileri ekle
              const testIncomeCategories = [
                { categoryId: incomeCategories[0]?.id, targetAmount: 15000 },
                { categoryId: incomeCategories[1]?.id, targetAmount: 5000 },
                { categoryId: incomeCategories[2]?.id, targetAmount: 5000 },
              ].filter(cat => cat.categoryId);

              await IncomeHybridService.addIncomeCategoriesToBudget(budget.id, testIncomeCategories);

              Alert.alert('✅ Test Tamamlandı', `"${budget.name}" bütçesine ${testIncomeCategories.length} gelir kategorisi eklendi!`);
            } catch (error) {
              console.error('❌ Add test income categories error:', error);
              Alert.alert('Hata', `Test kategorileri eklenemedi: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🧪 Mevcut Bütçeye Gelir Kategorileri Ekle</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF5722', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // En son gelir işlemini al
              const incomeTransaction = await db.getFirstAsync(
                'SELECT * FROM transactions WHERE type = ? ORDER BY created_at DESC LIMIT 1',
                ['income']
              );

              if (!incomeTransaction) {
                Alert.alert('Hata', 'Gelir işlemi bulunamadı.');
                return;
              }

              // Manuel bağlama dene (gerçek user_id ile)
              const budgetId = await IncomeHybridService.linkIncomeTransactionToBudget(
                incomeTransaction.id,
                incomeTransaction.user_id, // Gerçek user_id kullan
                incomeTransaction.category_id,
                incomeTransaction.amount,
                incomeTransaction.date
              );

              if (budgetId) {
                Alert.alert('✅ Başarılı', `Gelir işlemi bütçeye bağlandı!\nBütçe ID: ${budgetId}`);
              } else {
                Alert.alert('⚠️ Bağlanamadı', 'Uygun bütçe bulunamadı veya gelir kategorisi eksik.');
              }
            } catch (error) {
              console.error('❌ Manual income linking error:', error);
              Alert.alert('Hata', `Manuel bağlama hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔗 Manuel Gelir Bağlama</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF9800', marginBottom: 10 }]}
          onPress={async () => {
            try {
              const db = DatabaseManager.getDatabase();

              // Bütçeleri listele
              const budgets = await db.getAllAsync('SELECT id, name, user_id FROM budgets ORDER BY created_at DESC');

              // Her bütçe için gelir kategorilerini kontrol et
              let debugInfo = '📊 BÜTÇE VE GELİR KATEGORİLERİ:\n\n';

              for (const budget of budgets) {
                const incomeCategories = await db.getAllAsync(
                  'SELECT * FROM budget_income_categories WHERE budget_id = ?',
                  [budget.id]
                );

                debugInfo += `🏦 ${budget.name} (${budget.id})\n`;
                debugInfo += `   User ID: ${budget.user_id}\n`;
                debugInfo += `   Gelir kategorileri: ${incomeCategories.length}\n\n`;
              }

              // Son gelir işlemini kontrol et
              const lastIncomeTransaction = await db.getFirstAsync(
                'SELECT id, amount, category_id, user_id, date FROM transactions WHERE type = ? ORDER BY created_at DESC LIMIT 1',
                ['income']
              );

              if (lastIncomeTransaction) {
                debugInfo += `💰 SON GELİR İŞLEMİ:\n`;
                debugInfo += `   ID: ${lastIncomeTransaction.id}\n`;
                debugInfo += `   Tutar: ${lastIncomeTransaction.amount} ₺\n`;
                debugInfo += `   Kategori ID: ${lastIncomeTransaction.category_id}\n`;
                debugInfo += `   User ID: ${lastIncomeTransaction.user_id}\n`;
                debugInfo += `   Tarih: ${lastIncomeTransaction.date}\n`;
              }

              console.log(debugInfo);
              Alert.alert('🔍 Debug Bilgileri', debugInfo);
            } catch (error) {
              console.error('❌ Budget income debug error:', error);
              Alert.alert('Hata', `Debug hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔍 Bütçe-Gelir Debug</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#E91E63', marginBottom: 10 }]}
          onPress={async () => {
            try {
              console.log('🔧 Manuel Migration V17 başlatılıyor...');

              // Migration'ı manuel çalıştır
              const { IncomeHybridMigration } = await import('../../database/migrations/IncomeHybridMigration');
              const db = DatabaseManager.getDatabase();

              await IncomeHybridMigration.migrate(db);

              Alert.alert('✅ Migration Tamamlandı', 'Migration V17 manuel olarak çalıştırıldı!\n\nArtık gelir hibrit sistemi aktif.');
              console.log('✅ Manuel Migration V17 tamamlandı');
            } catch (error) {
              console.error('❌ Manuel migration error:', error);
              Alert.alert('Hata', `Migration hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔧 Manuel Migration V17</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#4CAF50', marginBottom: 10 }]}
          onPress={async () => {
            try {
              console.log('💰 Fixing income categories and linking transactions...');

              if (!user) {
                Alert.alert('Hata', 'Kullanıcı bulunamadı');
                return;
              }

              const db = DatabaseManager.getDatabase();

              // 1. Mevcut bütçelere gelir kategorilerini ekle
              await BudgetService.addIncomeCategoriestoExistingBudgets(user.id);
              console.log('✅ Income categories added to existing budgets');

              // 2. Bütçesiz gelir işlemlerini bul
              const unlinkedIncomeTransactions = await db.getAllAsync(
                `SELECT id, amount, category, category_id, date, description
                 FROM transactions
                 WHERE type = 'income' AND is_deleted = 0 AND budget_id IS NULL AND user_id = ?
                 ORDER BY date DESC`,
                [user.id]
              );

              console.log(`🔗 Found ${unlinkedIncomeTransactions.length} unlinked income transactions`);

              // 3. Her gelir işlemini uygun bütçeye bağla
              for (const transaction of unlinkedIncomeTransactions) {
                console.log(`🔗 Linking transaction: ${transaction.amount}₺ - ${transaction.category} - ${transaction.date}`);

                // Uygun bütçeyi bul (tarih aralığına göre)
                const budget = await db.getFirstAsync(
                  `SELECT id, name, start_date, end_date
                   FROM budgets
                   WHERE user_id = ?
                     AND is_active = 1
                     AND date(?) BETWEEN date(start_date) AND date(end_date)
                   ORDER BY created_at DESC
                   LIMIT 1`,
                  [user.id, transaction.date]
                );

                if (budget) {
                  // Transaction'ı bütçeye bağla
                  await db.runAsync(
                    `UPDATE transactions
                     SET budget_id = ?, updated_at = ?
                     WHERE id = ?`,
                    [budget.id, new Date().toISOString(), transaction.id]
                  );
                  console.log(`✅ Linked ${transaction.amount}₺ to budget: ${budget.name}`);
                } else {
                  console.log(`⚠️ No suitable budget found for transaction: ${transaction.date}`);
                }
              }

              // 4. Gelir işlemlerini yeniden hesapla
              const budgets = await BudgetService.getUserBudgets(user.id);
              console.log(`📊 Found ${budgets.length} budgets`);

              for (const budget of budgets) {
                console.log(`🔄 Recalculating income for budget: ${budget.name}`);

                const { default: IncomeHybridService } = await import('../../services/IncomeHybridService');
                await IncomeHybridService.recalculateIncomeCategories(budget.id);

                console.log(`✅ Recalculated income for budget: ${budget.name}`);
              }

              Alert.alert('🎉 Başarılı!', `${unlinkedIncomeTransactions.length} gelir işlemi bütçelere bağlandı!\n\n${budgets.length} bütçe güncellendi!`);
            } catch (error) {
              console.error('❌ Fix income categories error:', error);
              Alert.alert('Hata', `Gelir kategorileri düzeltilemedi: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>💰 Gelir Kategorilerini Düzelt</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#2196F3', marginBottom: 10 }]}
          onPress={async () => {
            try {
              console.log('🔍 STARTING INCOME CHECK...');

              const db = DatabaseManager.getDatabase();

              // 1. Tüm gelir işlemlerini kontrol et
              const incomeTransactions = await db.getAllAsync(
                `SELECT id, amount, category, category_id, budget_id, date, description
                 FROM transactions
                 WHERE type = 'income' AND is_deleted = 0
                 ORDER BY date DESC`
              );

              console.log(`💰 INCOME TRANSACTIONS FOUND: ${incomeTransactions.length}`);
              incomeTransactions.forEach((tx, index) => {
                console.log(`  ${index + 1}. ${tx.amount}₺ - ${tx.category} - Budget: ${tx.budget_id || 'NULL'} - Date: ${tx.date}`);
              });

              // 2. Aktif bütçeleri kontrol et
              const activeBudgets = await db.getAllAsync(
                `SELECT id, name, total_income_target, start_date, end_date
                 FROM budgets
                 WHERE is_active = 1
                 ORDER BY created_at DESC`
              );

              console.log(`📋 ACTIVE BUDGETS FOUND: ${activeBudgets.length}`);
              activeBudgets.forEach((budget, index) => {
                console.log(`  ${index + 1}. ${budget.name} - Target: ${budget.total_income_target}₺ - Period: ${budget.start_date} to ${budget.end_date}`);
              });

              // 4. Tarih eşleştirme testi
              console.log(`🔍 TESTING DATE MATCHING:`);
              for (const tx of incomeTransactions) {
                console.log(`📅 Transaction: ${tx.amount}₺ - Date: ${tx.date}`);
                for (const budget of activeBudgets) {
                  const txDate = tx.date.split('T')[0]; // 2025-06-07
                  const isInRange = txDate >= budget.start_date && txDate <= budget.end_date;
                  console.log(`  📊 Budget "${budget.name}": ${budget.start_date} to ${budget.end_date} - Match: ${isInRange}`);
                }
              }

              // 3. Budget income categories kontrol et
              const budgetIncomeCategories = await db.getAllAsync(
                `SELECT bic.*, b.name as budget_name, c.name as category_name
                 FROM budget_income_categories bic
                 JOIN budgets b ON bic.budget_id = b.id
                 JOIN categories c ON bic.category_id = c.id
                 WHERE bic.is_active = 1
                 ORDER BY b.name, c.name`
              );

              console.log(`📊 BUDGET INCOME CATEGORIES FOUND: ${budgetIncomeCategories.length}`);
              budgetIncomeCategories.forEach((bic, index) => {
                console.log(`  ${index + 1}. ${bic.budget_name} - ${bic.category_name} - Target: ${bic.target_amount}₺ - Actual: ${bic.actual_amount}₺`);
              });

              const unlinkedCount = incomeTransactions.filter(tx => !tx.budget_id).length;

              Alert.alert('✅ Kontrol Tamamlandı',
                `Gelir işlemleri: ${incomeTransactions.length}\n` +
                `Bütçesiz gelir: ${unlinkedCount}\n` +
                `Aktif bütçeler: ${activeBudgets.length}\n` +
                `Gelir kategorileri: ${budgetIncomeCategories.length}\n\n` +
                `Detaylar console'da!`
              );
            } catch (error) {
              console.error('❌ Check income transactions error:', error);
              Alert.alert('Hata', `Gelir işlemleri kontrol edilemedi: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔍 Gelir İşlemlerini Kontrol Et</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#FF5722', marginBottom: 10 }]}
          onPress={async () => {
            try {
              console.log('🔍 DETAILED TRIGGER ANALYSIS...');
              const db = DatabaseManager.getDatabase();

              // Tüm trigger'ları listele
              const allTriggers = await db.getAllAsync(
                `SELECT name, sql FROM sqlite_master WHERE type = 'trigger' ORDER BY name`
              );

              console.log('🔍 ALL TRIGGERS:');
              allTriggers.forEach((trigger, index) => {
                console.log(`  ${index + 1}. ${trigger.name}`);
                if (trigger.name.includes('budget')) {
                  console.log(`     ⚡ BUDGET TRIGGER: ${trigger.sql.substring(0, 200)}...`);
                }
              });

              // Problematik trigger'ları ara
              const problematicTriggers = allTriggers.filter(t =>
                t.name.includes('budget') &&
                t.sql.includes('WHERE budget_id = NEW.budget_id;') &&
                !t.sql.includes('AND category_id = NEW.category_id')
              );

              console.log('🚨 PROBLEMATIC TRIGGERS (updating all categories):');
              problematicTriggers.forEach((trigger, index) => {
                console.log(`  ${index + 1}. ${trigger.name} - UPDATES ALL CATEGORIES!`);
              });

              // Doğru trigger'ları ara
              const correctTriggers = allTriggers.filter(t =>
                t.name.includes('budget') &&
                t.sql.includes('AND category_id = NEW.category_id')
              );

              console.log('✅ CORRECT TRIGGERS (updating specific category):');
              correctTriggers.forEach((trigger, index) => {
                console.log(`  ${index + 1}. ${trigger.name} - UPDATES SPECIFIC CATEGORY!`);
              });

              // Test transaction oluştur
              console.log('🧪 TESTING TRIGGER BEHAVIOR...');

              // Mevcut kategori durumunu kaydet
              const beforeCategories = await db.getAllAsync(
                `SELECT bc.id, bc.budget_id, bc.category_id, bc.spent_amount,
                        b.name as budget_name, c.name as category_name
                 FROM budget_categories bc
                 JOIN budgets b ON bc.budget_id = b.id
                 JOIN categories c ON bc.category_id = c.id
                 WHERE b.user_id = ? AND bc.is_active = 1`,
                [user?.id || 'test_user_1748893907946']
              );

              console.log('📊 BEFORE TEST - BUDGET CATEGORIES:');
              beforeCategories.forEach((bc, index) => {
                console.log(`  ${index + 1}. ${bc.budget_name} > ${bc.category_name}: ${bc.spent_amount}₺`);
              });

              Alert.alert(
                '🔍 Detaylı Trigger Analizi',
                `Toplam trigger: ${allTriggers.length}\n` +
                `Problematik trigger: ${problematicTriggers.length}\n` +
                `Doğru trigger: ${correctTriggers.length}\n` +
                `Budget kategorileri: ${beforeCategories.length}\n\n` +
                'Console\'da detayları görün!'
              );
            } catch (error) {
              console.error('❌ Detailed trigger analysis error:', error);
              Alert.alert('Hata', `Analiz hatası: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔍 Detaylı Trigger Analizi</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: '#9C27B0', marginBottom: 10 }]}
          onPress={async () => {
            try {
              console.log('🔧 Fixing budget category cross-contamination...');

              if (!user) {
                Alert.alert('Hata', 'Kullanıcı bulunamadı');
                return;
              }

              const db = DatabaseManager.getDatabase();

              // 1. Tüm budget kategorilerinin spent_amount'unu sıfırla
              await db.runAsync(
                'UPDATE budget_categories SET spent_amount = 0 WHERE budget_id IN (SELECT id FROM budgets WHERE user_id = ?)',
                [user.id]
              );
              console.log('✅ Reset all budget category spent amounts');

              // 2. Her bütçe kategorisi için doğru spent_amount'u hesapla
              const budgetCategories = await db.getAllAsync(
                `SELECT bc.id, bc.budget_id, bc.category_id, b.name as budget_name, c.name as category_name
                 FROM budget_categories bc
                 JOIN budgets b ON bc.budget_id = b.id
                 JOIN categories c ON bc.category_id = c.id
                 WHERE b.user_id = ? AND bc.is_active = 1`,
                [user.id]
              );

              console.log(`🔧 Found ${budgetCategories.length} budget categories to fix`);

              for (const category of budgetCategories) {
                // Bu spesifik bütçe+kategori için harcamayı hesapla
                const spentResult = await db.getFirstAsync(
                  `SELECT COALESCE(SUM(amount), 0) as total_spent
                   FROM transactions
                   WHERE budget_id = ? AND category_id = ? AND type = 'expense' AND is_deleted = 0`,
                  [category.budget_id, category.category_id]
                );

                const spentAmount = spentResult?.total_spent || 0;

                // spent_amount'u güncelle
                await db.runAsync(
                  'UPDATE budget_categories SET spent_amount = ? WHERE id = ?',
                  [spentAmount, category.id]
                );

                console.log(`✅ Fixed ${category.budget_name} > ${category.category_name}: ${spentAmount}₺`);
              }

              Alert.alert('🎉 Başarılı!', `${budgetCategories.length} bütçe kategorisi düzeltildi!\n\nArtık her bütçenin kategorileri sadece kendi harcamalarını gösterecek.`);
            } catch (error) {
              console.error('❌ Fix budget categories error:', error);
              Alert.alert('Hata', `Bütçe kategorileri düzeltilemedi: ${error.message}`);
            }
          }}
        >
          <Text style={styles.clearButtonText}>🔧 Bütçe Kategori Çakışmasını Düzelt</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.clearButton} onPress={clearAllData}>
          <Text style={styles.clearButtonText}>🗑️ Tüm Verileri Sil</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default DatabaseDebugScreen;
