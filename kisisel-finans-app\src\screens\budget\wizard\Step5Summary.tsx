// RFC-003 Bütçe Sihirbazı - Adım 5: Özet & Onay

import React, { useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';

const Step5Summary: React.FC = () => {
  const { theme } = useTheme();
  const { state } = useBudgetWizard();

  // Debug: Step 5'te state'i kontrol et
  console.log('🔍 Step5 - Current state:', {
    currentStep: state.currentStep,
    totalSteps: state.totalSteps,
    errors: state.errors,
    isValid: state.isValid,
    selectedCategories: state.selectedCategories.length,
    name: state.name,
  });

  const formatCurrency = useCallback((amount: number): string => {
    const symbol = state.currency === 'TRY' ? '₺' : state.currency === 'USD' ? '$' : '€';
    return `${amount.toLocaleString('tr-TR')} ${symbol}`;
  }, [state.currency]);

  const formatDate = useCallback((dateString: string): string => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  }, []);

  const getPeriodLabel = useCallback((period: string): string => {
    switch (period) {
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'quarterly': return '3 Aylık';
      case 'annually': return 'Yıllık';
      default: return period;
    }
  }, []);

  const getTotalPlannedAmount = useCallback(() => {
    return state.selectedCategories.reduce((total, cat) => total + cat.plannedAmount, 0);
  }, [state.selectedCategories]);

  const getRemainingAmount = useCallback(() => {
    return state.totalIncomeTarget - state.totalExpenseLimit - state.savingsTarget;
  }, [state.totalIncomeTarget, state.totalExpenseLimit, state.savingsTarget]);

  const getBudgetHealthScore = useCallback(() => {
    const savingsRate = state.totalIncomeTarget > 0 ? (state.savingsTarget / state.totalIncomeTarget) * 100 : 0;
    const expenseRate = state.totalIncomeTarget > 0 ? (state.totalExpenseLimit / state.totalIncomeTarget) * 100 : 0;

    let score = 100;

    // Tasarruf oranı kontrolü
    if (savingsRate < 10) score -= 30;
    else if (savingsRate < 20) score -= 15;

    // Gider oranı kontrolü
    if (expenseRate > 80) score -= 25;
    else if (expenseRate > 70) score -= 10;

    // Kategori çeşitliliği
    if (state.selectedCategories.length < 3) score -= 15;

    return Math.max(0, Math.min(100, score));
  }, [state.totalIncomeTarget, state.totalExpenseLimit, state.savingsTarget, state.selectedCategories.length]);

  const getHealthScoreColor = useCallback((score: number) => {
    if (score >= 80) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  }, [theme.colors]);

  const getHealthScoreLabel = useCallback((score: number) => {
    if (score >= 80) return 'Mükemmel';
    if (score >= 60) return 'İyi';
    if (score >= 40) return 'Orta';
    return 'Geliştirilmeli';
  }, []);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Bütçe Özeti
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Oluşturduğunuz bütçenin detaylarını gözden geçirin
        </Text>
      </View>

      {/* Bütçe Sağlık Skoru */}
      <View style={[styles.healthCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.healthHeader}>
          <Ionicons name="fitness-outline" size={24} color={getHealthScoreColor(getBudgetHealthScore())} />
          <Text style={[styles.healthTitle, { color: theme.colors.text }]}>
            Bütçe Sağlık Skoru
          </Text>
        </View>
        <View style={styles.healthScore}>
          <Text style={[
            styles.healthScoreValue,
            { color: getHealthScoreColor(getBudgetHealthScore()) }
          ]}>
            {getBudgetHealthScore()}/100
          </Text>
          <Text style={[
            styles.healthScoreLabel,
            { color: getHealthScoreColor(getBudgetHealthScore()) }
          ]}>
            {getHealthScoreLabel(getBudgetHealthScore())}
          </Text>
        </View>
      </View>

      {/* Temel Bilgiler */}
      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Temel Bilgiler
        </Text>

        <View style={styles.infoRow}>
          <Ionicons name="document-text-outline" size={20} color={theme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
              Bütçe Adı
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>
              {state.name}
            </Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
              Dönem
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>
              {getPeriodLabel(state.period)}
            </Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="time-outline" size={20} color={theme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
              Tarih Aralığı
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>
              {formatDate(state.startDate)} - {formatDate(state.endDate)}
            </Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="card-outline" size={20} color={theme.colors.primary} />
          <View style={styles.infoContent}>
            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>
              Para Birimi
            </Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>
              {state.currency === 'TRY' ? 'Türk Lirası (₺)' :
               state.currency === 'USD' ? 'Amerikan Doları ($)' : 'Euro (€)'}
            </Text>
          </View>
        </View>
      </View>

      {/* Finansal Hedefler */}
      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Finansal Hedefler
        </Text>

        <View style={styles.financialRow}>
          <View style={styles.financialItem}>
            <Ionicons name="trending-up" size={20} color={theme.colors.success} />
            <Text style={[styles.financialLabel, { color: theme.colors.textSecondary }]}>
              Gelir Hedefi
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.success }]}>
              {formatCurrency(state.totalIncomeTarget)}
            </Text>
          </View>

          <View style={styles.financialItem}>
            <Ionicons name="trending-down" size={20} color={theme.colors.error} />
            <Text style={[styles.financialLabel, { color: theme.colors.textSecondary }]}>
              Gider Limiti
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.error }]}>
              {formatCurrency(state.totalExpenseLimit)}
            </Text>
          </View>

          <View style={styles.financialItem}>
            <Ionicons name="wallet" size={20} color={theme.colors.primary} />
            <Text style={[styles.financialLabel, { color: theme.colors.textSecondary }]}>
              Tasarruf Hedefi
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.primary }]}>
              {formatCurrency(state.savingsTarget)}
            </Text>
          </View>
        </View>

        <View style={[styles.remainingAmount, { borderTopColor: theme.colors.border }]}>
          <Text style={[styles.remainingLabel, { color: theme.colors.textSecondary }]}>
            Kalan Tutar:
          </Text>
          <Text style={[
            styles.remainingValue,
            { color: getRemainingAmount() >= 0 ? theme.colors.success : theme.colors.error }
          ]}>
            {formatCurrency(getRemainingAmount())}
          </Text>
        </View>
      </View>

      {/* Seçili Kategoriler */}
      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Seçili Kategoriler ({state.selectedCategories.length})
        </Text>

        <View style={styles.categoryList}>
          {state.selectedCategories.map((category, index) => {
            // Find category details from predefined list
            const categoryDetails = [
              { id: 'food', name: 'Yiyecek & İçecek', icon: 'restaurant-outline', color: '#FF6B6B' },
              { id: 'transport', name: 'Ulaşım', icon: 'car-outline', color: '#4ECDC4' },
              { id: 'shopping', name: 'Alışveriş', icon: 'bag-outline', color: '#45B7D1' },
              { id: 'entertainment', name: 'Eğlence', icon: 'game-controller-outline', color: '#96CEB4' },
              { id: 'health', name: 'Sağlık', icon: 'medical-outline', color: '#FFEAA7' },
              { id: 'education', name: 'Eğitim', icon: 'school-outline', color: '#DDA0DD' },
              { id: 'bills', name: 'Faturalar', icon: 'receipt-outline', color: '#FFB347' },
              { id: 'rent', name: 'Kira', icon: 'home-outline', color: '#F7DC6F' },
              { id: 'insurance', name: 'Sigorta', icon: 'shield-outline', color: '#BB8FCE' },
              { id: 'personal', name: 'Kişisel Bakım', icon: 'person-outline', color: '#85C1E9' },
              { id: 'gifts', name: 'Hediyeler', icon: 'gift-outline', color: '#F8C471' },
              { id: 'other', name: 'Diğer', icon: 'ellipsis-horizontal-outline', color: '#AED6F1' },
            ].find(cat => cat.id === category.categoryId);

            return (
              <View key={index} style={styles.categoryItem}>
                <View style={styles.categoryInfo}>
                  <View style={[
                    styles.categoryIcon,
                    { backgroundColor: (categoryDetails?.color || theme.colors.primary) + '20' }
                  ]}>
                    <Ionicons
                      name={(categoryDetails?.icon || 'ellipsis-horizontal-outline') as any}
                      size={16}
                      color={categoryDetails?.color || theme.colors.primary}
                    />
                  </View>
                  <Text style={[styles.categoryName, { color: theme.colors.text }]}>
                    {categoryDetails?.name || category.categoryId}
                  </Text>
                </View>
                <Text style={[styles.categoryAmount, { color: theme.colors.primary }]}>
                  {formatCurrency(category.plannedAmount)}
                </Text>
              </View>
            );
          })}
        </View>

        <View style={[styles.categoryTotal, { borderTopColor: theme.colors.border }]}>
          <Text style={[styles.categoryTotalLabel, { color: theme.colors.text }]}>
            Toplam Planlanan:
          </Text>
          <Text style={[styles.categoryTotalValue, { color: theme.colors.primary }]}>
            {formatCurrency(getTotalPlannedAmount())}
          </Text>
        </View>
      </View>

      {/* Uyarı Eşikleri */}
      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Uyarı Eşikleri
        </Text>

        <View style={styles.thresholdList}>
          <View style={styles.thresholdItem}>
            <Ionicons name="warning-outline" size={16} color={theme.colors.warning} />
            <Text style={[styles.thresholdLabel, { color: theme.colors.textSecondary }]}>
              Uyarı Eşiği:
            </Text>
            <Text style={[styles.thresholdValue, { color: theme.colors.warning }]}>
              %{state.defaultWarningThreshold}
            </Text>
          </View>

          <View style={styles.thresholdItem}>
            <Ionicons name="alert-circle-outline" size={16} color={theme.colors.error} />
            <Text style={[styles.thresholdLabel, { color: theme.colors.textSecondary }]}>
              Kritik Eşik:
            </Text>
            <Text style={[styles.thresholdValue, { color: theme.colors.error }]}>
              %{state.defaultCriticalThreshold}
            </Text>
          </View>

          <View style={styles.thresholdItem}>
            <Ionicons name="stop-circle-outline" size={16} color="#8B0000" />
            <Text style={[styles.thresholdLabel, { color: theme.colors.textSecondary }]}>
              Limit Eşiği:
            </Text>
            <Text style={[styles.thresholdValue, { color: '#8B0000' }]}>
              %{state.defaultLimitThreshold}
            </Text>
          </View>
        </View>
      </View>

      {/* Notlar */}
      {state.notes && (
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
            Notlar
          </Text>
          <Text style={[styles.notesText, { color: theme.colors.textSecondary }]}>
            {state.notes}
          </Text>
        </View>
      )}

      {/* Onay Mesajı */}
      <View style={[styles.confirmationCard, { backgroundColor: theme.colors.primary + '10' }]}>
        <Ionicons name="checkmark-circle" size={32} color={theme.colors.primary} />
        <Text style={[styles.confirmationTitle, { color: theme.colors.text }]}>
          Bütçeniz Oluşturulmaya Hazır!
        </Text>
        <Text style={[styles.confirmationDescription, { color: theme.colors.textSecondary }]}>
          Tüm bilgileri kontrol ettiniz mi? "Bütçeyi Oluştur" butonuna basarak bütçenizi kaydedin.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  healthCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
  },
  healthHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  healthTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  healthScore: {
    alignItems: 'center',
  },
  healthScoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  healthScoreLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 4,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  financialRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  financialItem: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  financialLabel: {
    fontSize: 12,
    marginTop: 4,
    marginBottom: 2,
    textAlign: 'center',
  },
  financialValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  remainingAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
  },
  remainingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  remainingValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  categoryList: {
    marginBottom: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 14,
    flex: 1,
  },
  categoryAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  categoryTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
  },
  categoryTotalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  categoryTotalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  thresholdList: {
    gap: 8,
  },
  thresholdItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  thresholdLabel: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  thresholdValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  confirmationCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  confirmationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  confirmationDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default Step5Summary;
