// RFC-003 Bütçe Sihirbazı - Adım 1: Temel Bilgiler

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../../contexts/ThemeContext';
import { useBudgetWizard } from '../../../contexts/BudgetWizardContext';
import { BudgetPeriod } from '../../../types/budget';
import BudgetService from '../../../services/BudgetService';

const Step1BasicInfo: React.FC = () => {
  const { theme } = useTheme();
  const { state, dispatch } = useBudgetWizard();
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Period options
  const periodOptions: { value: BudgetPeriod; label: string; icon: string }[] = [
    { value: 'weekly', label: 'Haftalık', icon: 'calendar-outline' },
    { value: 'monthly', label: 'Aylık', icon: 'calendar' },
    { value: 'quarterly', label: '3 Aylık', icon: 'calendar-sharp' },
    { value: 'annually', label: 'Yıllık', icon: 'calendar-number-outline' },
  ];

  // Currency options
  const currencyOptions = [
    { value: 'TRY', label: 'Türk Lirası (₺)', symbol: '₺' },
    { value: 'USD', label: 'Amerikan Doları ($)', symbol: '$' },
    { value: 'EUR', label: 'Euro (€)', symbol: '€' },
  ];

  const handleBasicInfoChange = (field: string, value: any) => {
    dispatch({
      type: 'SET_BASIC_INFO',
      payload: { [field]: value }
    });
  };

  const handlePeriodChange = (period: BudgetPeriod) => {
    handleBasicInfoChange('period', period);
    
    // Otomatik tarih hesaplama
    const startDate = new Date(state.startDate);
    let endDate = new Date(startDate);
    
    switch (period) {
      case 'weekly':
        endDate.setDate(startDate.getDate() + 7);
        break;
      case 'monthly':
        endDate.setMonth(startDate.getMonth() + 1);
        break;
      case 'quarterly':
        endDate.setMonth(startDate.getMonth() + 3);
        break;
      case 'annually':
        endDate.setFullYear(startDate.getFullYear() + 1);
        break;
    }
    
    handleBasicInfoChange('endDate', endDate.toISOString().split('T')[0]);
  };

  const handleDateChange = (event: any, selectedDate: Date | undefined, type: 'start' | 'end') => {
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];
      if (type === 'start') {
        handleBasicInfoChange('startDate', dateString);
        setShowStartDatePicker(false);
      } else {
        handleBasicInfoChange('endDate', dateString);
        setShowEndDatePicker(false);
      }
    } else {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }
  };

  const loadAutoSuggestion = async () => {
    try {
      Alert.alert(
        'Otomatik Öneri',
        'Geçmiş harcama verilerinize dayalı bütçe önerisi yüklensin mi?',
        [
          { text: 'Hayır', style: 'cancel' },
          {
            text: 'Evet',
            onPress: async () => {
              try {
                const suggestion = await BudgetService.generateBudgetSuggestion(
                  'current_user',
                  state.period,
                  6 // 6 aylık geçmiş
                );
                
                dispatch({ type: 'LOAD_AUTO_SUGGESTION', payload: suggestion });
                dispatch({ type: 'SET_AUTO_SUGGESTION', payload: true });
                
                Alert.alert('Başarılı', 'Otomatik bütçe önerisi yüklendi!');
              } catch (error) {
                Alert.alert('Hata', 'Otomatik öneri yüklenirken bir hata oluştu.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error loading auto suggestion:', error);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Başlık */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Temel Bilgiler
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Bütçenizin temel bilgilerini girin
        </Text>
      </View>

      {/* Bütçe Adı */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Bütçe Adı *
        </Text>
        <TextInput
          style={[
            styles.textInput,
            { 
              backgroundColor: theme.colors.surface,
              borderColor: state.errors.name ? theme.colors.error : theme.colors.border,
              color: theme.colors.text
            }
          ]}
          value={state.name}
          onChangeText={(text) => handleBasicInfoChange('name', text)}
          placeholder="Örn: Ocak 2024 Bütçesi"
          placeholderTextColor={theme.colors.textSecondary}
        />
        {state.errors.name && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.name}
          </Text>
        )}
      </View>

      {/* Dönem Seçimi */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Bütçe Dönemi *
        </Text>
        <View style={styles.periodGrid}>
          {periodOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.periodOption,
                {
                  backgroundColor: state.period === option.value 
                    ? theme.colors.primary 
                    : theme.colors.surface,
                  borderColor: state.period === option.value 
                    ? theme.colors.primary 
                    : theme.colors.border,
                }
              ]}
              onPress={() => handlePeriodChange(option.value)}
            >
              <Ionicons 
                name={option.icon as any} 
                size={24} 
                color={state.period === option.value ? theme.colors.surface : theme.colors.primary} 
              />
              <Text style={[
                styles.periodOptionText,
                { 
                  color: state.period === option.value 
                    ? theme.colors.surface 
                    : theme.colors.text 
                }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Tarih Aralığı */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Tarih Aralığı *
        </Text>
        
        <View style={styles.dateRow}>
          <View style={styles.dateField}>
            <Text style={[styles.dateLabel, { color: theme.colors.textSecondary }]}>
              Başlangıç
            </Text>
            <TouchableOpacity
              style={[
                styles.dateButton,
                { 
                  backgroundColor: theme.colors.surface,
                  borderColor: state.errors.startDate ? theme.colors.error : theme.colors.border
                }
              ]}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                {new Date(state.startDate).toLocaleDateString('tr-TR')}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.dateField}>
            <Text style={[styles.dateLabel, { color: theme.colors.textSecondary }]}>
              Bitiş
            </Text>
            <TouchableOpacity
              style={[
                styles.dateButton,
                { 
                  backgroundColor: theme.colors.surface,
                  borderColor: state.errors.endDate ? theme.colors.error : theme.colors.border
                }
              ]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                {new Date(state.endDate).toLocaleDateString('tr-TR')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {(state.errors.startDate || state.errors.endDate) && (
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {state.errors.startDate || state.errors.endDate}
          </Text>
        )}
      </View>

      {/* Para Birimi */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Para Birimi
        </Text>
        <View style={styles.currencyGrid}>
          {currencyOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.currencyOption,
                {
                  backgroundColor: state.currency === option.value 
                    ? theme.colors.primary 
                    : theme.colors.surface,
                  borderColor: state.currency === option.value 
                    ? theme.colors.primary 
                    : theme.colors.border,
                }
              ]}
              onPress={() => handleBasicInfoChange('currency', option.value)}
            >
              <Text style={[
                styles.currencySymbol,
                { 
                  color: state.currency === option.value 
                    ? theme.colors.surface 
                    : theme.colors.primary 
                }
              ]}>
                {option.symbol}
              </Text>
              <Text style={[
                styles.currencyLabel,
                { 
                  color: state.currency === option.value 
                    ? theme.colors.surface 
                    : theme.colors.text 
                }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Notlar */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Notlar (İsteğe Bağlı)
        </Text>
        <TextInput
          style={[
            styles.textArea,
            { 
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
              color: theme.colors.text
            }
          ]}
          value={state.notes}
          onChangeText={(text) => handleBasicInfoChange('notes', text)}
          placeholder="Bütçeniz hakkında notlar..."
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Hızlı Seçenekler */}
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.colors.text }]}>
          Hızlı Başlangıç
        </Text>
        <TouchableOpacity
          style={[
            styles.quickOption,
            { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }
          ]}
          onPress={loadAutoSuggestion}
        >
          <Ionicons name="bulb-outline" size={24} color={theme.colors.warning} />
          <View style={styles.quickOptionContent}>
            <Text style={[styles.quickOptionTitle, { color: theme.colors.text }]}>
              Otomatik Öneri
            </Text>
            <Text style={[styles.quickOptionDescription, { color: theme.colors.textSecondary }]}>
              Geçmiş harcamalarınıza dayalı bütçe önerisi
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={new Date(state.startDate)}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'start')}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={new Date(state.endDate)}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'end')}
        />
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 16,
    lineHeight: 22,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    fontSize: 14,
    marginTop: 4,
  },
  periodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  periodOption: {
    flex: 1,
    minWidth: '45%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  periodOptionText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  dateRow: {
    flexDirection: 'row',
    gap: 12,
  },
  dateField: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  dateText: {
    fontSize: 16,
    marginLeft: 8,
  },
  currencyGrid: {
    gap: 8,
  },
  currencyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 12,
  },
  currencyLabel: {
    fontSize: 16,
  },
  quickOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  quickOptionContent: {
    flex: 1,
    marginLeft: 12,
  },
  quickOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  quickOptionDescription: {
    fontSize: 14,
  },
});

export default Step1BasicInfo;
