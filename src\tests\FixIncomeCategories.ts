// Fix Income Categories Test - Mevcut bütçelere gelir kategorilerini ekle

import BudgetService from '../services/BudgetService';
import { AuthService } from '../services/AuthService';

export class FixIncomeCategoriesTest {
  static async run(): Promise<void> {
    try {
      console.log('🚀 Starting Fix Income Categories Test...');

      // Kullanıcı bilgisini al
      const user = await AuthService.getCurrentUser();
      if (!user) {
        console.error('❌ No user found');
        return;
      }

      console.log(`👤 User: ${user.name} (${user.id})`);

      // Mevcut bütçelere gelir kategorilerini ekle
      await BudgetService.addIncomeCategoriestoExistingBudgets(user.id);

      // Gelir işlemlerini yeniden hesapla
      const budgets = await BudgetService.getUserBudgets(user.id);
      console.log(`📊 Found ${budgets.length} budgets`);

      for (const budget of budgets) {
        console.log(`🔄 Recalculating income for budget: ${budget.name}`);
        
        // IncomeHybridService'i import et
        const { default: IncomeHybridService } = await import('../services/IncomeHybridService');
        await IncomeHybridService.recalculateIncomeCategories(budget.id);
        
        console.log(`✅ Recalculated income for budget: ${budget.name}`);
      }

      console.log('🎉 Fix Income Categories Test completed successfully!');
    } catch (error) {
      console.error('❌ Fix Income Categories Test failed:', error);
      throw error;
    }
  }
}

// Test'i çalıştır
if (require.main === module) {
  FixIncomeCategoriesTest.run()
    .then(() => {
      console.log('✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}
