# Kişisel Finans Yönetimi Uygulaması

## Proje Tanımı
Bu proje, kullanıcıların kişisel finans yönetimini kolaylaştıran, gelir-gider takibi, b<PERSON><PERSON><PERSON><PERSON> planlaması, yatırım önerileri ve finansal analiz araçları sunan kapsamlı bir mobil uygulama geliştirmeyi amaçlamaktadır. Uygulama, kullanıcı dostu arayüzü ve güvenli altyapısı ile kişisel finans yönetimi süreçlerini optimize etmeyi hedeflemektedir.

## Ana Özellikler

### 💰 Gelir ve Gider Takibi
- Farklı gelir kaynaklarını kategorilere göre kaydetme
- Harcamaları kategorilere ayırarak takip etme
- Makbuz/fatura görüntülerini saklama
- Banka ve kredi kartı entegrasyonu ile otomatik işlem takibi
- Tekrarlayan gelir ve gider yönetimi

### 📊 Bütçe Planlaması
- Aylık, 3 aylık ve yıllık bütçe oluşturma
- Kategori bazlı harcama limitleri belirleme
- Limit aşımı bildirimleri
- Bütçe performansı takibi ve öneriler
- Esnek bütçe düzenleme seçenekleri

### 🎯 Finansal Hedefler
- Kısa, orta ve uzun vadeli finansal hedefler belirleme
- Hedef ilerleme takibi ve görselleştirme
- Tasarruf planları ve stratejileri
- Hedef bazlı tasarruf hesapları
- Başarı ödülleri ve kutlamaları

### 💹 Yatırım Önerileri
- Risk profili belirleme
- Kişiselleştirilmiş yatırım tavsiyeleri
- Portföy takibi ve analizi
- Piyasa verileri ve trendler
- Otomatik yatırım seçenekleri

### 💳 Borç Yönetimi
- Tüm borçları tek bir yerden takip etme
- Borç ödeme stratejileri (çığ, çorap yöntemi)
- Ödeme hatırlatıcıları
- Borç kapanma projeksiyonları
- Kredi skoru takibi ve iyileştirme önerileri

### 📈 Finansal Analiz ve Raporlar
- Detaylı gelir-gider analizleri
- Kategori bazlı harcama dağılımı
- Trend analizleri ve projeksiyonlar
- Özelleştirilebilir raporlar
- Veri dışa aktarma seçenekleri

### 🔒 Güvenlik ve Gizlilik
- Uçtan uca veri şifreleme
- Biometrik kimlik doğrulama
- İki faktörlü kimlik doğrulama
- Güvenli veri saklama
- Veri yedekleme ve kurtarma

### 📱 Kullanıcı Deneyimi
- Sezgisel ve kullanımı kolay arayüz
- Özelleştirilebilir dashboard
- Koyu/açık tema desteği
- Çoklu dil desteği
- Widget'lar ve kısayollar

## Teknik Altyapı Özeti
Kişisel Finans Yönetimi uygulaması, modern mobil uygulama geliştirme teknolojileri ve güvenli bir bulut altyapısı üzerine inşa edilmiştir:

- **Mobil Uygulama**: Flutter (cross-platform) veya Native (iOS için Swift, Android için Kotlin)
- **Backend**: Microservices mimarisi, RESTful API, WebSocket
- **Veritabanı**: Mobil için SQLite/Realm, Bulut için MongoDB/PostgreSQL
- **Güvenlik**: Uçtan uca şifreleme, OAuth 2.0, JWT
- **Entegrasyonlar**: Açık bankacılık API'leri, piyasa verileri, ödeme sistemleri

## Proje Dokümanları

### Ürün Gereksinimleri Belgesi (PRD)
`prd.md` dosyası, uygulamanın tüm gereksinimlerini, özelliklerini, kullanıcı hikayeleri ve kullanım durumlarını ayrıntılı olarak açıklar.

### RFC Dokümanları
Projemiz, her bir özelliğin teknik tasarımını ve uygulamasını detaylandıran RFC (Request for Comments) dokümanları içerir:

1. **RFC-001**: Kullanıcı Kimlik Doğrulama ve Güvenlik
2. **RFC-002**: Gelir ve Gider Takibi
3. **RFC-003**: Bütçe Planlaması
4. **RFC-004**: Yatırım Önerileri
5. **RFC-005**: Finansal Hedefler
6. **RFC-006**: Borç Yönetimi
7. **RFC-007**: Finansal Analiz ve Raporlar
8. **RFC-008**: Kullanıcı Deneyimi (UX)
9. **RFC-009**: Teknik Altyapı
10. **RFC-010**: Mobil Uygulama Mimarisi

## Başlangıç ve Kurulum

### Gereksinimler
- Flutter SDK 2.10.0 veya üzeri
- Dart 2.16.0 veya üzeri
- Android Studio / Xcode
- Firebase hesabı
- API anahtarları (finansal veri sağlayıcılar için)

### Geliştirme Ortamının Kurulumu
1. Repository'yi klonlayın: `git clone https://github.com/kullanici/kisisel-finans-yonetimi.git`
2. Bağımlılıkları yükleyin: `flutter pub get`
3. Firebase yapılandırma dosyalarını ekleyin
4. Geliştirme sunucusunu başlatın: `flutter run`

## Katkıda Bulunma
Projemize katkıda bulunmak istiyorsanız:

1. Bir issue açın veya mevcut bir issue seçin
2. Repository'yi forklayın
3. Branch oluşturun: `git checkout -b feature/amazing-feature`
4. Değişikliklerinizi commit edin: `git commit -m 'Add amazing feature'`
5. Branch'inizi push edin: `git push origin feature/amazing-feature`
6. Bir Pull Request açın

## İletişim
Proje ile ilgili sorularınız için:
- Email: <EMAIL>
- GitHub Issues: [https://github.com/kullanici/kisisel-finans-yonetimi/issues](https://github.com/kullanici/kisisel-finans-yonetimi/issues)

## Lisans
Bu proje [MIT Lisansı](LICENSE) altında lisanslanmıştır.

---

© 2025 Kişisel Finans Yönetimi Projesi
