// Database Manager - SQLite veritabanı yönetimi

import * as SQLite from 'expo-sqlite';
import {
  DATABASE_NAME,
  DATABASE_VERSION,
  CREATE_TABLES,
  CREATE_INDEXES,
  CREATE_TRIGGERS,
  DEFAULT_DATA,
  MIGRATIONS,
} from './schema';

class DatabaseManager {
  private static instance: DatabaseManager;
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;
  private isDebugMode = __DEV__; // Production'da false

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Veritabanını başlat
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized && this.db) {
        if (this.isDebugMode) console.log('🗄️ Database already initialized, skipping...');
        return;
      }

      if (this.isDebugMode) console.log('🗄️ Initializing SQLite database...');

      // Database'i aç
      this.db = await SQLite.openDatabaseAsync(DATABASE_NAME);

      // WAL mode'u etkinleştir (better performance)
      await this.db.execAsync('PRAGMA journal_mode = WAL;');
      await this.db.execAsync('PRAGMA foreign_keys = ON;');

      // Tabloları oluştur (migration'dan önce!)
      await this.createTables();

      // Version kontrolü ve migration
      await this.handleMigrations();

      // Index'leri ve trigger'ları oluştur
      await this.createIndexes();
      await this.createTriggers();

      // Default verileri ekle
      await this.insertDefaultData();

      this.isInitialized = true;
      console.log('✅ SQLite database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization error:', error);
      throw error;
    }
  }

  /**
   * Database instance'ını al
   */
  getDatabase(): SQLite.SQLiteDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  /**
   * Migration'ları yönet
   */
  private async handleMigrations(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      // User version'ı al (SQLite'ın built-in version sistemi)
      const result = await this.db.getFirstAsync<{ user_version: number }>('PRAGMA user_version;');
      const currentVersion = result?.user_version || 0;

      console.log(`📊 Current database version: ${currentVersion}, Target version: ${DATABASE_VERSION}`);

      if (currentVersion < DATABASE_VERSION) {
        console.log('🔄 Running database migrations...');

        // Her version için migration'ları çalıştır
        for (let version = currentVersion + 1; version <= DATABASE_VERSION; version++) {
          const migration = MIGRATIONS[version as keyof typeof MIGRATIONS];
          if (migration && migration.up) {
            console.log(`⬆️ Migrating to version ${version}...`);

            // Async function mu yoksa string array mi kontrol et
            if (typeof migration.up === 'function') {
              // Async migration function
              await migration.up(this.db);
            } else {
              // String array migration (eski sistem)
              for (const sql of migration.up) {
                await this.db.execAsync(sql);
              }
            }
          }
        }

        // Version'ı güncelle
        await this.db.execAsync(`PRAGMA user_version = ${DATABASE_VERSION};`);
        console.log('✅ Database migrations completed');
      }
    } catch (error) {
      console.error('❌ Migration error:', error);
      throw error;
    }
  }

  /**
   * Tabloları oluştur
   */
  private async createTables(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('📋 Creating database tables...');

      for (const [tableName, sql] of Object.entries(CREATE_TABLES)) {
        await this.db.execAsync(sql);
        console.log(`✅ Table created: ${tableName}`);
      }
    } catch (error) {
      console.error('❌ Table creation error:', error);
      throw error;
    }
  }

  /**
   * Index'leri oluştur
   */
  private async createIndexes(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('🔍 Creating database indexes...');

      for (const [tableName, indexes] of Object.entries(CREATE_INDEXES)) {
        for (const indexSql of indexes) {
          await this.db.execAsync(indexSql);
        }
        console.log(`✅ Indexes created for: ${tableName}`);
      }
    } catch (error) {
      console.error('❌ Index creation error:', error);
      throw error;
    }
  }

  /**
   * Trigger'ları oluştur
   */
  private async createTriggers(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('⚡ Creating database triggers...');

      for (const triggerSql of CREATE_TRIGGERS) {
        await this.db.execAsync(triggerSql);
      }

      console.log('✅ Database triggers created');
    } catch (error) {
      console.error('❌ Trigger creation error:', error);
      throw error;
    }
  }

  /**
   * Default verileri ekle
   */
  private async insertDefaultData(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('📦 Inserting default data...');

      // Default kategorileri ekle (sadece yoksa)
      for (const category of DEFAULT_DATA.categories) {
        const existing = await this.db.getFirstAsync(
          'SELECT id FROM categories WHERE name = ? AND type = ? AND user_id = ?',
          [category.name, category.type, 'system']
        );

        if (!existing) {
          await this.db.runAsync(
            `INSERT INTO categories (id, user_id, name, type, icon, color, sort_order, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              'system',
              category.name,
              category.type,
              category.icon,
              category.color,
              0,
              new Date().toISOString(),
              new Date().toISOString(),
            ]
          );
        }
      }

      // Default ayarları ekle (sadece yoksa)
      for (const setting of DEFAULT_DATA.settings) {
        const existing = await this.db.getFirstAsync(
          'SELECT id FROM user_settings WHERE setting_key = ? AND user_id = ?',
          [setting.key, 'system']
        );

        if (!existing) {
          await this.db.runAsync(
            `INSERT INTO user_settings (id, user_id, setting_key, setting_value, data_type, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              `set_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              'system',
              setting.key,
              setting.value,
              setting.type,
              new Date().toISOString(),
              new Date().toISOString(),
            ]
          );
        }
      }

      console.log('✅ Default data inserted');
    } catch (error) {
      console.error('❌ Default data insertion error:', error);
      throw error;
    }
  }

  /**
   * Transaction ile işlem yap
   */
  async transaction<T>(callback: (db: SQLite.SQLiteDatabase) => Promise<T>): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    return await this.db.withTransactionAsync(callback);
  }

  /**
   * Veritabanı istatistiklerini al
   */
  async getStats(): Promise<{
    tables: Record<string, number>;
    totalSize: number;
    version: number;
  }> {
    try {
      if (!this.db) throw new Error('Database not available');

      const stats: Record<string, number> = {};

      // Her tablo için kayıt sayısını al
      const tableNames = Object.keys(CREATE_TABLES);
      for (const tableName of tableNames) {
        const result = await this.db.getFirstAsync<{ count: number }>(
          `SELECT COUNT(*) as count FROM ${tableName}`
        );
        stats[tableName] = result?.count || 0;
      }

      // Database boyutunu al (yaklaşık)
      const sizeResult = await this.db.getFirstAsync<{ page_count: number; page_size: number }>(
        'PRAGMA page_count; PRAGMA page_size;'
      );
      const totalSize = (sizeResult?.page_count || 0) * (sizeResult?.page_size || 0);

      // Version'ı al
      const versionResult = await this.db.getFirstAsync<{ user_version: number }>('PRAGMA user_version;');
      const version = versionResult?.user_version || 0;

      return {
        tables: stats,
        totalSize,
        version,
      };
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      throw error;
    }
  }

  /**
   * Veritabanını temizle (development için)
   */
  async clearDatabase(): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('🗑️ Clearing database...');

      // Tüm tabloları temizle (foreign key constraints nedeniyle sıralı)
      const clearOrder = [
        'transaction_tags',
        'receipts',
        'transactions',
        'budget_categories',
        'budgets',
        'goals',
        'recurring_templates',
        'category_rules',
        'categories',
        'tags',
        'exchange_rates',
        'sync_records',
        'user_settings',
        'users',
      ];

      for (const tableName of clearOrder) {
        await this.db.execAsync(`DELETE FROM ${tableName}`);
      }

      // Default verileri tekrar ekle
      await this.insertDefaultData();

      console.log('✅ Database cleared and reset');
    } catch (error) {
      console.error('❌ Error clearing database:', error);
      throw error;
    }
  }

  /**
   * Veritabanını tamamen sıfırla ve yeniden oluştur
   */
  async resetDatabase(): Promise<void> {
    try {
      console.log('🔄 Resetting database completely...');

      // Veritabanını kapat
      if (this.db) {
        await this.db.closeAsync();
        this.db = null;
        this.isInitialized = false;
      }

      // Veritabanı dosyasını sil (SQLite.deleteDatabaseAsync kullan)
      try {
        await SQLite.deleteDatabaseAsync(DATABASE_NAME);
        console.log('🗑️ Database file deleted');
      } catch (deleteError) {
        console.warn('⚠️ Could not delete database file:', deleteError);
      }

      // Yeniden başlat
      await this.initialize();

      console.log('✅ Database reset and reinitialized');
    } catch (error) {
      console.error('❌ Error resetting database:', error);
      throw error;
    }
  }

  /**
   * Veritabanını kapat
   */
  async close(): Promise<void> {
    try {
      if (this.db) {
        await this.db.closeAsync();
        this.db = null;
        this.isInitialized = false;
        console.log('✅ Database closed');
      }
    } catch (error) {
      console.error('❌ Error closing database:', error);
      throw error;
    }
  }

  /**
   * Backup oluştur (JSON export)
   */
  async createBackup(userId: string): Promise<string> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('💾 Creating database backup...');

      const backup: any = {
        version: DATABASE_VERSION,
        createdAt: new Date().toISOString(),
        userId,
        data: {},
      };

      // Her tablo için verileri export et
      const tableNames = Object.keys(CREATE_TABLES);

      // user_id kolonu olan tablolar
      const userTables = [
        'transactions', 'categories', 'budgets', 'category_rules',
        'tags', 'goals', 'recurring_templates', 'user_settings'
      ];

      // user_id kolonu olmayan tablolar
      const systemTables = [
        'receipts', 'transaction_tags', 'exchange_rates', 'sync_records'
      ];

      for (const tableName of tableNames) {
        if (userTables.includes(tableName)) {
          // user_id ile filtrele
          const rows = await this.db.getAllAsync(
            `SELECT * FROM ${tableName} WHERE user_id = ? OR user_id = 'system'`,
            [userId]
          );
          backup.data[tableName] = rows;
        } else if (systemTables.includes(tableName)) {
          // Tüm kayıtları al (system tabloları)
          const rows = await this.db.getAllAsync(`SELECT * FROM ${tableName}`);
          backup.data[tableName] = rows;
        } else {
          // Bilinmeyen tablo, boş array
          backup.data[tableName] = [];
        }
      }

      const backupJson = JSON.stringify(backup, null, 2);
      console.log('✅ Database backup created');

      return backupJson;
    } catch (error) {
      console.error('❌ Error creating backup:', error);
      throw error;
    }
  }

  /**
   * Backup'tan restore et
   */
  async restoreFromBackup(backupJson: string, userId: string): Promise<void> {
    try {
      if (!this.db) throw new Error('Database not available');

      console.log('📥 Restoring from backup...');

      const backup = JSON.parse(backupJson);

      // Version uyumluluğunu kontrol et
      if (backup.version > DATABASE_VERSION) {
        throw new Error('Backup version is newer than current database version');
      }

      // Transaction içinde restore et
      await this.transaction(async (db) => {
        // user_id kolonu olan tablolar
        const userTables = [
          'transactions', 'categories', 'budgets', 'category_rules',
          'tags', 'goals', 'recurring_templates', 'user_settings'
        ];

        // Kullanıcının mevcut verilerini sil
        for (const tableName of userTables) {
          await db.execAsync(`DELETE FROM ${tableName} WHERE user_id = ?`, [userId]);
        }

        // Backup verilerini ekle
        for (const [tableName, rows] of Object.entries(backup.data)) {
          if (Array.isArray(rows)) {
            for (const row of rows as any[]) {
              // user_id olan tablolar için filtrele
              if (userTables.includes(tableName)) {
                if (row.user_id === userId || row.user_id === 'system') {
                  const columns = Object.keys(row);
                  const placeholders = columns.map(() => '?').join(', ');
                  const values = columns.map(col => row[col]);

                  await db.runAsync(
                    `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`,
                    values
                  );
                }
              } else {
                // System tabloları için direkt ekle
                const columns = Object.keys(row);
                const placeholders = columns.map(() => '?').join(', ');
                const values = columns.map(col => row[col]);

                await db.runAsync(
                  `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`,
                  values
                );
              }
            }
          }
        }
      });

      console.log('✅ Database restored from backup');
    } catch (error) {
      console.error('❌ Error restoring from backup:', error);
      throw error;
    }
  }

  /**
   * Database sağlık kontrolü
   */
  async healthCheck(): Promise<{
    isHealthy: boolean;
    issues: string[];
    stats: any;
  }> {
    try {
      const issues: string[] = [];

      if (!this.db) {
        issues.push('Database not initialized');
        return { isHealthy: false, issues, stats: null };
      }

      // Integrity check
      const integrityResult = await this.db.getFirstAsync<{ integrity_check: string }>('PRAGMA integrity_check;');
      if (integrityResult?.integrity_check !== 'ok') {
        issues.push('Database integrity check failed');
      }

      // Foreign key check
      const foreignKeyResult = await this.db.getAllAsync('PRAGMA foreign_key_check;');
      if (foreignKeyResult.length > 0) {
        issues.push('Foreign key constraints violated');
      }

      // Stats al
      const stats = await this.getStats();

      return {
        isHealthy: issues.length === 0,
        issues,
        stats,
      };
    } catch (error) {
      console.error('❌ Health check error:', error);
      return {
        isHealthy: false,
        issues: ['Health check failed: ' + error],
        stats: null,
      };
    }
  }
}

// Singleton instance'ı export et
const databaseManager = DatabaseManager.getInstance();

// Helper function for getting database instance
export const getDatabaseInstance = () => {
  return databaseManager.getDatabase();
};

export default databaseManager;
