// Admin Dashboard Screen - Yönetici genel bakış ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { useAccessControl } from '../../hooks/useAccessControl';
import RBACService from '../../services/RBACService';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    newToday: number;
    byRole: Record<string, number>;
  };
  security: {
    totalLogins: number;
    failedAttempts: number;
    activeSessions: number;
    securityAlerts: number;
  };
  system: {
    rbacStatus: any;
    cacheSize: number;
    lastUpdate: string;
    uptime: string;
  };
  permissions: {
    totalChecks: number;
    allowedChecks: number;
    deniedChecks: number;
    successRate: number;
  };
}

const AdminDashboardScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useSimpleAuth();
  const { hasPermission, userRoles } = useAccessControl();

  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [hasAdminAccess, setHasAdminAccess] = useState(false);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  // userRoles değiştiğinde yeniden kontrol et
  useEffect(() => {
    checkAdminAccess();
  }, [userRoles, hasPermission]);

  useEffect(() => {
    if (hasAdminAccess) {
      loadDashboardStats();
    }
  }, [hasAdminAccess]);

  const checkAdminAccess = async () => {
    try {
      console.log('🔍 Admin access check - Current roles:', userRoles);
      const canAccessAdmin = await hasPermission('admin:system');
      console.log('🔍 Admin access result:', canAccessAdmin);
      setHasAdminAccess(canAccessAdmin);

      if (!canAccessAdmin) {
        console.log('❌ Admin access denied for roles:', userRoles);
        // Alert kaldırıldı - sadece erişim reddedildi ekranı gösterilecek
      } else {
        console.log('✅ Admin access granted for roles:', userRoles);
      }
    } catch (error) {
      console.error('Admin access check error:', error);
      setHasAdminAccess(false);
    }
  };

  const loadDashboardStats = async () => {
    try {
      setIsLoading(true);

      // RBAC durumunu al
      const rbacStatus = RBACService.getRBACStatus();

      // Access loglarını al
      const accessLogs = await RBACService.getAccessLogs(undefined, 1000);

      // Mock user statistics (gerçek uygulamada API'den gelecek)
      const mockStats: DashboardStats = {
        users: {
          total: 1247,
          active: 892,
          newToday: 23,
          byRole: {
            basic: 1050,
            premium: 150,
            family_admin: 35,
            family_member: 8,
            moderator: 3,
            admin: 1,
          },
        },
        security: {
          totalLogins: 3456,
          failedAttempts: 45,
          activeSessions: 234,
          securityAlerts: 3,
        },
        system: {
          rbacStatus,
          cacheSize: rbacStatus.cacheSize.roles + rbacStatus.cacheSize.userRoles + rbacStatus.cacheSize.permissions,
          lastUpdate: new Date().toISOString(),
          uptime: '7 gün 14 saat',
        },
        permissions: {
          totalChecks: accessLogs.length,
          allowedChecks: accessLogs.filter(log => log.allowed).length,
          deniedChecks: accessLogs.filter(log => !log.allowed).length,
          successRate: accessLogs.length > 0 ?
            (accessLogs.filter(log => log.allowed).length / accessLogs.length) * 100 : 0,
        },
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      Alert.alert('Hata', 'Dashboard verileri yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardStats();
    setRefreshing(false);
  };

  const navigateToUserManagement = () => {
    // Navigation will be implemented
    Alert.alert('🚧 Geliştirme Aşamasında', 'Kullanıcı yönetimi ekranı yakında eklenecek');
  };

  const navigateToRoleManagement = () => {
    // Navigation will be implemented
    Alert.alert('🚧 Geliştirme Aşamasında', 'Rol yönetimi ekranı yakında eklenecek');
  };

  const navigateToSecurityLogs = () => {
    // Navigation will be implemented
    Alert.alert('🚧 Geliştirme Aşamasında', 'Güvenlik logları ekranı yakında eklenecek');
  };

  const navigateToSystemSettings = () => {
    // Navigation will be implemented
    Alert.alert('🚧 Geliştirme Aşamasında', 'Sistem ayarları ekranı yakında eklenecek');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    accessDenied: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    accessDeniedText: {
      fontSize: 18,
      color: theme.colors.error,
      textAlign: 'center',
      marginTop: 10,
    },
    header: {
      padding: 20,
      backgroundColor: theme.colors.primary,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.background,
      marginBottom: 5,
    },
    headerSubtitle: {
      fontSize: 14,
      color: theme.colors.background,
      opacity: 0.8,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    statCard: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    statCardLarge: {
      width: '100%',
    },
    statIcon: {
      marginBottom: 8,
    },
    statValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    statSubValue: {
      fontSize: 11,
      color: theme.colors.textSecondary,
    },
    quickActions: {
      marginTop: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    actionGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    actionCard: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      alignItems: 'center',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    actionIcon: {
      marginBottom: 8,
    },
    actionLabel: {
      fontSize: 12,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '500',
    },
    rolesList: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 8,
    },
    roleChip: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginRight: 6,
      marginBottom: 4,
    },
    roleChipText: {
      fontSize: 10,
      color: theme.colors.background,
      fontWeight: '500',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.text,
      marginTop: 10,
    },
  });

  // Access denied screen
  if (!hasAdminAccess) {
    return (
      <View style={styles.accessDenied}>
        <Ionicons name="shield-outline" size={64} color={theme.colors.error} />
        <Text style={styles.accessDeniedText}>
          Bu sayfaya erişim için admin yetkisi gereklidir.
        </Text>
        <Text style={[styles.accessDeniedText, { fontSize: 14, marginTop: 10 }]}>
          Mevcut rolleriniz: {userRoles.join(', ')}
        </Text>
      </View>
    );
  }

  // Loading screen
  if (isLoading || !stats) {
    return (
      <View style={styles.loadingContainer}>
        <Ionicons name="refresh-outline" size={32} color={theme.colors.primary} />
        <Text style={styles.loadingText}>Dashboard yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <Text style={styles.headerSubtitle}>
          Hoş geldiniz, {user?.name} • Son güncelleme: {new Date().toLocaleTimeString('tr-TR')}
        </Text>
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Statistics Grid */}
        <View style={styles.statsGrid}>
          {/* Users Stats */}
          <View style={styles.statCard}>
            <Ionicons name="people-outline" size={24} color={theme.colors.primary} style={styles.statIcon} />
            <Text style={styles.statValue}>{stats.users.total.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Toplam Kullanıcı</Text>
            <Text style={styles.statSubValue}>
              {stats.users.active} aktif • {stats.users.newToday} yeni
            </Text>
          </View>

          {/* Security Stats */}
          <View style={styles.statCard}>
            <Ionicons name="shield-checkmark-outline" size={24} color="#4CAF50" style={styles.statIcon} />
            <Text style={styles.statValue}>{stats.security.activeSessions}</Text>
            <Text style={styles.statLabel}>Aktif Oturum</Text>
            <Text style={styles.statSubValue}>
              {stats.security.securityAlerts} güvenlik uyarısı
            </Text>
          </View>

          {/* Permission Stats */}
          <View style={styles.statCard}>
            <Ionicons name="key-outline" size={24} color="#FF9800" style={styles.statIcon} />
            <Text style={styles.statValue}>{stats.permissions.successRate.toFixed(1)}%</Text>
            <Text style={styles.statLabel}>İzin Başarı Oranı</Text>
            <Text style={styles.statSubValue}>
              {stats.permissions.totalChecks} toplam kontrol
            </Text>
          </View>

          {/* System Stats */}
          <View style={styles.statCard}>
            <Ionicons name="server-outline" size={24} color="#9C27B0" style={styles.statIcon} />
            <Text style={styles.statValue}>{stats.system.cacheSize}</Text>
            <Text style={styles.statLabel}>Cache Boyutu</Text>
            <Text style={styles.statSubValue}>
              Uptime: {stats.system.uptime}
            </Text>
          </View>

          {/* Role Distribution */}
          <View style={[styles.statCard, styles.statCardLarge]}>
            <Ionicons name="layers-outline" size={24} color={theme.colors.primary} style={styles.statIcon} />
            <Text style={styles.statValue}>Rol Dağılımı</Text>
            <Text style={styles.statLabel}>Kullanıcı rolleri</Text>
            <View style={styles.rolesList}>
              {Object.entries(stats.users.byRole).map(([role, count]) => (
                <View key={role} style={styles.roleChip}>
                  <Text style={styles.roleChipText}>{role}: {count}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Hızlı İşlemler</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity style={styles.actionCard} onPress={navigateToUserManagement}>
              <Ionicons name="people-outline" size={32} color={theme.colors.primary} style={styles.actionIcon} />
              <Text style={styles.actionLabel}>Kullanıcı Yönetimi</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard} onPress={navigateToRoleManagement}>
              <Ionicons name="layers-outline" size={32} color={theme.colors.primary} style={styles.actionIcon} />
              <Text style={styles.actionLabel}>Rol Yönetimi</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard} onPress={navigateToSecurityLogs}>
              <Ionicons name="document-text-outline" size={32} color={theme.colors.primary} style={styles.actionIcon} />
              <Text style={styles.actionLabel}>Güvenlik Logları</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionCard} onPress={navigateToSystemSettings}>
              <Ionicons name="settings-outline" size={32} color={theme.colors.primary} style={styles.actionIcon} />
              <Text style={styles.actionLabel}>Sistem Ayarları</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default AdminDashboardScreen;
