// Advanced Filter Modal - Gelişmiş filtreleme modal'ı
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrency } from '../contexts/CurrencyContext';
import {
  IncomeCategory,
  ExpenseCategory,
  PaymentMethod,
  CurrencyCode,
  INCOME_CATEGORY_LABELS,
  EXPENSE_CATEGORY_LABELS,
  PAYMENT_METHOD_LABELS,
} from '../types/transaction';

export interface AdvancedFilters {
  // Amount filters
  minAmount?: number;
  maxAmount?: number;
  currency?: CurrencyCode;

  // Category filters
  includeCategories: string[];
  excludeCategories: string[];

  // Payment method filters
  paymentMethods: PaymentMethod[];

  // Date filters
  startDate?: Date;
  endDate?: Date;

  // Tag filters
  includeTags: string[];
  excludeTags: string[];

  // Business/Tax filters
  isBusinessExpense?: boolean;
  isDeductible?: boolean;
  isTaxable?: boolean;

  // Location filters
  hasLocation?: boolean;
  hasReceipt?: boolean;
}

interface AdvancedFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: AdvancedFilters) => void;
  initialFilters: AdvancedFilters;
  transactionType: 'all' | 'income' | 'expense';
}

const AdvancedFilterModal: React.FC<AdvancedFilterModalProps> = ({
  visible,
  onClose,
  onApply,
  initialFilters,
  transactionType,
}) => {
  const { theme } = useTheme();
  const { currencies } = useCurrency();

  const [filters, setFilters] = useState<AdvancedFilters>(initialFilters);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Reset filters when modal opens
  useEffect(() => {
    if (visible) {
      setFilters(initialFilters);
    }
  }, [visible, initialFilters]);

  // Get available categories based on transaction type
  const getAvailableCategories = () => {
    if (transactionType === 'income') {
      return Object.entries(INCOME_CATEGORY_LABELS).map(([key, label]) => ({ key: `income_${key}`, label, originalKey: key }));
    } else if (transactionType === 'expense') {
      return Object.entries(EXPENSE_CATEGORY_LABELS).map(([key, label]) => ({ key: `expense_${key}`, label, originalKey: key }));
    } else {
      return [
        ...Object.entries(INCOME_CATEGORY_LABELS).map(([key, label]) => ({ key: `income_${key}`, label, originalKey: key })),
        ...Object.entries(EXPENSE_CATEGORY_LABELS).map(([key, label]) => ({ key: `expense_${key}`, label, originalKey: key })),
      ];
    }
  };

  // Handle category selection
  const toggleCategory = (category: string, type: 'include' | 'exclude') => {
    setFilters(prev => {
      const listKey = type === 'include' ? 'includeCategories' : 'excludeCategories';
      const currentList = prev[listKey];

      if (currentList.includes(category)) {
        return {
          ...prev,
          [listKey]: currentList.filter(c => c !== category),
        };
      } else {
        return {
          ...prev,
          [listKey]: [...currentList, category],
        };
      }
    });
  };

  // Handle payment method selection
  const togglePaymentMethod = (method: PaymentMethod) => {
    setFilters(prev => {
      if (prev.paymentMethods.includes(method)) {
        return {
          ...prev,
          paymentMethods: prev.paymentMethods.filter(m => m !== method),
        };
      } else {
        return {
          ...prev,
          paymentMethods: [...prev.paymentMethods, method],
        };
      }
    });
  };

  // Handle apply filters
  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  // Handle reset filters
  const handleReset = () => {
    const resetFilters: AdvancedFilters = {
      includeCategories: [],
      excludeCategories: [],
      paymentMethods: [],
      includeTags: [],
      excludeTags: [],
    };
    setFilters(resetFilters);
  };

  // Render category section
  const renderCategorySection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Kategoriler
      </Text>

      <Text style={[styles.subsectionTitle, { color: theme.colors.textSecondary }]}>
        Dahil Et
      </Text>
      <View style={styles.chipContainer}>
        {getAvailableCategories().map((category) => (
          <TouchableOpacity
            key={`include-${category.key}`}
            style={[
              styles.chip,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.includeCategories.includes(category.originalKey) && {
                backgroundColor: theme.colors.primary,
                borderColor: theme.colors.primary,
              },
            ]}
            onPress={() => toggleCategory(category.originalKey, 'include')}
          >
            <Text style={[
              styles.chipText,
              { color: theme.colors.text },
              filters.includeCategories.includes(category.originalKey) && { color: theme.colors.surface },
            ]}>
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.subsectionTitle, { color: theme.colors.textSecondary }]}>
        Hariç Tut
      </Text>
      <View style={styles.chipContainer}>
        {getAvailableCategories().map((category) => (
          <TouchableOpacity
            key={`exclude-${category.key}`}
            style={[
              styles.chip,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.excludeCategories.includes(category.originalKey) && {
                backgroundColor: theme.colors.error,
                borderColor: theme.colors.error,
              },
            ]}
            onPress={() => toggleCategory(category.originalKey, 'exclude')}
          >
            <Text style={[
              styles.chipText,
              { color: theme.colors.text },
              filters.excludeCategories.includes(category.originalKey) && { color: theme.colors.surface },
            ]}>
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Render payment method section
  const renderPaymentMethodSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Ödeme Yöntemleri
      </Text>
      <View style={styles.chipContainer}>
        {Object.entries(PAYMENT_METHOD_LABELS).map(([key, label]) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.chip,
              { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
              filters.paymentMethods.includes(key as PaymentMethod) && {
                backgroundColor: theme.colors.primary,
                borderColor: theme.colors.primary,
              },
            ]}
            onPress={() => togglePaymentMethod(key as PaymentMethod)}
          >
            <Text style={[
              styles.chipText,
              { color: theme.colors.text },
              filters.paymentMethods.includes(key as PaymentMethod) && { color: theme.colors.surface },
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Render amount section
  const renderAmountSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Tutar Aralığı
      </Text>
      <View style={styles.amountContainer}>
        <View style={styles.amountInput}>
          <Text style={[styles.inputLabel, { color: theme.colors.textSecondary }]}>
            Min Tutar
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
              color: theme.colors.text,
            }]}
            placeholder="0"
            placeholderTextColor={theme.colors.textSecondary}
            value={filters.minAmount?.toString() || ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              minAmount: text ? parseFloat(text) : undefined,
            }))}
            keyboardType="numeric"
          />
        </View>
        <View style={styles.amountInput}>
          <Text style={[styles.inputLabel, { color: theme.colors.textSecondary }]}>
            Max Tutar
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
              color: theme.colors.text,
            }]}
            placeholder="∞"
            placeholderTextColor={theme.colors.textSecondary}
            value={filters.maxAmount?.toString() || ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              maxAmount: text ? parseFloat(text) : undefined,
            }))}
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  // Render date section
  const renderDateSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Tarih Aralığı
      </Text>
      <View style={styles.dateContainer}>
        <TouchableOpacity
          style={[styles.dateButton, {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          }]}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
          <Text style={[styles.dateText, { color: theme.colors.text }]}>
            {filters.startDate ? filters.startDate.toLocaleDateString('tr-TR') : 'Başlangıç'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.dateButton, {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          }]}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
          <Text style={[styles.dateText, { color: theme.colors.text }]}>
            {filters.endDate ? filters.endDate.toLocaleDateString('tr-TR') : 'Bitiş'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Gelişmiş Filtreler
          </Text>
          <TouchableOpacity onPress={handleReset}>
            <Text style={[styles.resetText, { color: theme.colors.primary }]}>
              Sıfırla
            </Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderAmountSection()}
          {renderCategorySection()}
          {renderPaymentMethodSection()}
          {renderDateSection()}
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { borderTopColor: theme.colors.border }]}>
          <TouchableOpacity
            style={[styles.applyButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleApply}
          >
            <Text style={[styles.applyButtonText, { color: theme.colors.surface }]}>
              Filtreleri Uygula
            </Text>
          </TouchableOpacity>
        </View>

        {/* Date Pickers */}
        {showStartDatePicker && (
          <DateTimePicker
            value={filters.startDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowStartDatePicker(false);
              if (selectedDate) {
                setFilters(prev => ({ ...prev, startDate: selectedDate }));
              }
            }}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={filters.endDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowEndDatePicker(false);
              if (selectedDate) {
                setFilters(prev => ({ ...prev, endDate: selectedDate }));
              }
            }}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  resetText: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  subsectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  chipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  amountContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  amountInput: {
    flex: 1,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  dateContainer: {
    gap: 12,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  dateText: {
    fontSize: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  applyButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AdvancedFilterModal;
