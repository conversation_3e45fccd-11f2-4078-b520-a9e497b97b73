// OAuth Test Screen - OAuth 2.0 test ekranı

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import SocialAuthService from '../../services/SocialAuthService';
import { getStoredTokens, revokeTokens } from '../../services/OAuthHelpers';

const OAuthTestScreen: React.FC = () => {
  const { theme } = useTheme();
  const socialAuthService = SocialAuthService.getInstance();
  
  const [oauthStatus, setOauthStatus] = useState<any>(null);
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadOAuthStatus();
  }, []);

  const loadOAuthStatus = async () => {
    try {
      const status = await socialAuthService.getOAuthStatus();
      setOauthStatus(status);
      
      // Google token bilgilerini al
      const googleTokens = await getStoredTokens('google');
      setTokenInfo(googleTokens);
      
      // Mevcut kullanıcı bilgilerini al
      if (status.google) {
        const user = await socialAuthService.getCurrentUser('google');
        setUserInfo(user);
      }
    } catch (error) {
      console.error('Error loading OAuth status:', error);
    }
  };

  const testGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      
      const result = await socialAuthService.signInWithGoogle();
      
      if (result.success) {
        Alert.alert(
          '✅ Google Sign-In Başarılı',
          `Hoş geldiniz ${result.user?.name}!\n\nEmail: ${result.user?.email}\nProvider: ${result.user?.provider}`
        );
        await loadOAuthStatus();
      } else {
        Alert.alert('❌ Google Sign-In Başarısız', result.error || 'Bilinmeyen hata');
      }
    } catch (error) {
      Alert.alert('❌ Hata', `Google Sign-In hatası: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testTokenRefresh = async () => {
    try {
      setIsLoading(true);
      
      const newTokens = await socialAuthService.refreshToken('google');
      
      if (newTokens) {
        Alert.alert('✅ Token Refresh Başarılı', 'Access token yenilendi!');
        await loadOAuthStatus();
      } else {
        Alert.alert('❌ Token Refresh Başarısız', 'Token yenilenemedi');
      }
    } catch (error) {
      Alert.alert('❌ Hata', `Token refresh hatası: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignOut = async () => {
    try {
      setIsLoading(true);
      
      await socialAuthService.signOut('google');
      
      Alert.alert('✅ Sign Out Başarılı', 'Google oturumu kapatıldı');
      await loadOAuthStatus();
      setTokenInfo(null);
      setUserInfo(null);
    } catch (error) {
      Alert.alert('❌ Hata', `Sign out hatası: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testRevokeTokens = async () => {
    try {
      setIsLoading(true);
      
      const success = await revokeTokens('google');
      
      if (success) {
        Alert.alert('✅ Token Revoke Başarılı', 'Token\'lar iptal edildi');
        await loadOAuthStatus();
        setTokenInfo(null);
        setUserInfo(null);
      } else {
        Alert.alert('❌ Token Revoke Başarısız', 'Token\'lar iptal edilemedi');
      }
    } catch (error) {
      Alert.alert('❌ Hata', `Token revoke hatası: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testGetCurrentUser = async () => {
    try {
      setIsLoading(true);
      
      const user = await socialAuthService.getCurrentUser('google');
      
      if (user) {
        Alert.alert(
          '✅ Current User',
          `Name: ${user.name}\nEmail: ${user.email}\nProvider: ${user.provider}\nEmail Verified: ${user.emailVerified}`
        );
        setUserInfo(user);
      } else {
        Alert.alert('❌ Current User', 'Kullanıcı bilgisi bulunamadı');
      }
    } catch (error) {
      Alert.alert('❌ Hata', `Get current user hatası: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 20,
      textAlign: 'center',
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 10,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonSecondary: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
      padding: 12,
      borderRadius: 8,
      marginBottom: 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonText: {
      color: theme.colors.background,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    buttonTextSecondary: {
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
      marginLeft: 8,
    },
    statusBox: {
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
      marginTop: 10,
    },
    statusText: {
      color: theme.colors.text,
      fontSize: 12,
      fontFamily: 'monospace',
    },
    statusItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    statusKey: {
      color: theme.colors.text,
      fontSize: 14,
      fontWeight: '500',
    },
    statusValue: {
      fontSize: 14,
      fontWeight: 'bold',
    },
    connected: {
      color: '#4CAF50',
    },
    disconnected: {
      color: '#F44336',
    },
  });

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔐 OAuth 2.0 Test</Text>

      {/* OAuth Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>OAuth Durumu:</Text>
        {oauthStatus && (
          <View style={styles.statusBox}>
            <View style={styles.statusItem}>
              <Text style={styles.statusKey}>Google:</Text>
              <Text style={[styles.statusValue, oauthStatus.google ? styles.connected : styles.disconnected]}>
                {oauthStatus.google ? '✅ Bağlı' : '❌ Bağlı Değil'}
              </Text>
            </View>
            <View style={styles.statusItem}>
              <Text style={styles.statusKey}>Apple:</Text>
              <Text style={[styles.statusValue, oauthStatus.apple ? styles.connected : styles.disconnected]}>
                {oauthStatus.apple ? '✅ Bağlı' : '❌ Bağlı Değil'}
              </Text>
            </View>
            <View style={styles.statusItem}>
              <Text style={styles.statusKey}>Facebook:</Text>
              <Text style={[styles.statusValue, oauthStatus.facebook ? styles.connected : styles.disconnected]}>
                {oauthStatus.facebook ? '✅ Bağlı' : '❌ Bağlı Değil'}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* OAuth Tests */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>OAuth Testleri:</Text>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={testGoogleSignIn}
          disabled={isLoading}
        >
          <Ionicons name="logo-google" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>
            {isLoading ? 'Test Ediliyor...' : 'Google Sign-In Test'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={testTokenRefresh}>
          <Ionicons name="refresh-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Token Refresh Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonSecondary} onPress={testGetCurrentUser}>
          <Ionicons name="person-outline" size={20} color={theme.colors.text} />
          <Text style={styles.buttonTextSecondary}>Get Current User</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={testSignOut}>
          <Ionicons name="log-out-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Sign Out Test</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.buttonDanger} onPress={testRevokeTokens}>
          <Ionicons name="ban-outline" size={20} color={theme.colors.background} />
          <Text style={styles.buttonText}>Revoke Tokens</Text>
        </TouchableOpacity>
      </View>

      {/* Token Info */}
      {tokenInfo && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Token Bilgileri:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify({
                tokenType: tokenInfo.tokenType,
                expiresIn: tokenInfo.expiresIn,
                scope: tokenInfo.scope,
                hasRefreshToken: !!tokenInfo.refreshToken,
                storedAt: new Date(tokenInfo.storedAt).toLocaleString('tr-TR'),
              }, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {/* User Info */}
      {userInfo && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Kullanıcı Bilgileri:</Text>
          <View style={styles.statusBox}>
            <Text style={styles.statusText}>
              {JSON.stringify(userInfo, null, 2)}
            </Text>
          </View>
        </View>
      )}

      {/* Refresh Button */}
      <TouchableOpacity style={styles.buttonSecondary} onPress={loadOAuthStatus}>
        <Ionicons name="refresh-outline" size={20} color={theme.colors.text} />
        <Text style={styles.buttonTextSecondary}>Durumu Yenile</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default OAuthTestScreen;
