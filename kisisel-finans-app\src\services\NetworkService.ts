// Network Service - Ağ durumu ve offline/online yönetimi

import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction } from '../types';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
}

export interface PendingAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: 'transaction' | 'category' | 'budget' | 'goal';
  data: any;
  timestamp: string;
}

class NetworkService {
  private static instance: NetworkService;
  private networkState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown'
  };
  private listeners: Array<(state: NetworkState) => void> = [];
  private pendingActions: PendingAction[] = [];

  static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  constructor() {
    this.initialize();
  }

  private async initialize() {
    // Load pending actions from storage
    await this.loadPendingActions();

    // Subscribe to network state changes
    NetInfo.addEventListener(state => {
      const newNetworkState: NetworkState = {
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type
      };

      const wasOffline = !this.networkState.isConnected;
      const isNowOnline = newNetworkState.isConnected;

      this.networkState = newNetworkState;

      // Notify listeners
      this.listeners.forEach(listener => listener(newNetworkState));

      // If we just came back online, sync pending actions
      if (wasOffline && isNowOnline) {
        this.syncPendingActions();
      }
    });

    // Get initial network state
    const state = await NetInfo.fetch();
    this.networkState = {
      isConnected: state.isConnected ?? false,
      isInternetReachable: state.isInternetReachable ?? false,
      type: state.type
    };
  }

  // Get current network state
  getNetworkState(): NetworkState {
    return this.networkState;
  }

  // Check if device is online
  isOnline(): boolean {
    return this.networkState.isConnected && this.networkState.isInternetReachable;
  }

  // Add network state listener
  addListener(listener: (state: NetworkState) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Add action to pending queue (for offline mode)
  async addPendingAction(action: Omit<PendingAction, 'id' | 'timestamp'>) {
    const pendingAction: PendingAction = {
      ...action,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    };

    this.pendingActions.push(pendingAction);
    await this.savePendingActions();
  }

  // Get pending actions
  getPendingActions(): PendingAction[] {
    return this.pendingActions;
  }

  // Clear pending actions
  async clearPendingActions() {
    this.pendingActions = [];
    await this.savePendingActions();
  }

  // Remove specific pending action
  async removePendingAction(actionId: string) {
    this.pendingActions = this.pendingActions.filter(action => action.id !== actionId);
    await this.savePendingActions();
  }

  // Save pending actions to storage
  private async savePendingActions() {
    try {
      await AsyncStorage.setItem('pendingActions', JSON.stringify(this.pendingActions));
    } catch (error) {
      console.error('Error saving pending actions:', error);
    }
  }

  // Load pending actions from storage
  private async loadPendingActions() {
    try {
      const stored = await AsyncStorage.getItem('pendingActions');
      if (stored) {
        this.pendingActions = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading pending actions:', error);
      this.pendingActions = [];
    }
  }

  // Sync pending actions when back online
  private async syncPendingActions() {
    if (!this.isOnline() || this.pendingActions.length === 0) {
      return;
    }

    console.log(`Syncing ${this.pendingActions.length} pending actions...`);

    // Process actions in order
    for (const action of this.pendingActions) {
      try {
        await this.processPendingAction(action);
        await this.removePendingAction(action.id);
      } catch (error) {
        console.error('Error processing pending action:', error);
        // Keep the action in queue for next sync attempt
        break;
      }
    }
  }

  // Process individual pending action
  private async processPendingAction(action: PendingAction) {
    // This would integrate with your API service
    // For now, just log the action
    console.log('Processing pending action:', action);
    
    // Example implementation:
    /*
    switch (action.entity) {
      case 'transaction':
        if (action.type === 'CREATE') {
          await ApiService.createTransaction(action.data);
        } else if (action.type === 'UPDATE') {
          await ApiService.updateTransaction(action.data.id, action.data);
        } else if (action.type === 'DELETE') {
          await ApiService.deleteTransaction(action.data.id);
        }
        break;
      // Handle other entities...
    }
    */
  }

  // Check server connectivity
  async checkServerConnectivity(): Promise<boolean> {
    try {
      // Try to reach your API server
      const response = await fetch(`${process.env.API_BASE_URL}/health`, {
        method: 'GET',
        timeout: 5000,
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Get network quality indicator
  getNetworkQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
    if (!this.networkState.isConnected) {
      return 'offline';
    }

    // This is a simplified implementation
    // In a real app, you might want to measure actual network speed
    switch (this.networkState.type) {
      case 'wifi':
        return 'excellent';
      case 'cellular':
        return 'good';
      case 'bluetooth':
      case 'ethernet':
        return 'good';
      default:
        return 'poor';
    }
  }

  // Force sync (manual sync trigger)
  async forceSync(): Promise<boolean> {
    if (!this.isOnline()) {
      throw new Error('Device is offline');
    }

    try {
      await this.syncPendingActions();
      return true;
    } catch (error) {
      console.error('Force sync failed:', error);
      return false;
    }
  }

  // Get sync status
  getSyncStatus(): {
    pendingCount: number;
    lastSyncAttempt: string | null;
    isOnline: boolean;
  } {
    return {
      pendingCount: this.pendingActions.length,
      lastSyncAttempt: this.pendingActions.length > 0 
        ? this.pendingActions[this.pendingActions.length - 1].timestamp 
        : null,
      isOnline: this.isOnline()
    };
  }
}

export default NetworkService;
