// Security Monitoring Service - Güvenlik izleme servisi

import AsyncStorage from '@react-native-async-storage/async-storage';
import SecureStorageWrapper from './SecureStorageWrapper';
import {
  SecurityEvent,
  SecurityAlert,
  SecurityAction,
  ThreatDetectionRule,
  SecurityMetrics,
  AuditTrail,
  DeviceFingerprint,
  SecurityConfiguration,
  LoginAttempt,
  SessionSecurity,
  SecurityEventType,
  SecurityLevel,
  ThreatCategory,
  DEFAULT_THREAT_DETECTION_RULES,
} from '../types/security';

class SecurityMonitoringService {
  private static instance: SecurityMonitoringService;

  // Storage keys
  private readonly EVENTS_STORAGE_KEY = 'security_events';
  private readonly ALERTS_STORAGE_KEY = 'security_alerts';
  private readonly AUDIT_TRAIL_KEY = 'audit_trail';
  private readonly DEVICE_FINGERPRINTS_KEY = 'device_fingerprints';
  private readonly LOGIN_ATTEMPTS_KEY = 'login_attempts';
  private readonly SESSIONS_KEY = 'security_sessions';
  private readonly CONFIG_KEY = 'security_config';
  private readonly RULES_KEY = 'threat_detection_rules';

  // Default configuration
  private config: SecurityConfiguration = {
    monitoring: {
      enabled: true,
      logLevel: 'medium',
      retentionDays: 90,
      realTimeAlerts: true,
    },
    threatDetection: {
      enabled: true,
      bruteForceThreshold: 5,
      suspiciousActivityThreshold: 3,
      anomalyDetectionEnabled: true,
    },
    deviceSecurity: {
      fingerprintingEnabled: true,
      jailbreakDetection: true,
      rootDetection: true,
      emulatorDetection: true,
    },
    notifications: {
      emailAlerts: true,
      pushNotifications: true,
      smsAlerts: false,
    },
    compliance: {
      gdprCompliant: true,
      auditTrailEnabled: true,
      dataRetentionDays: 365,
      anonymizeAfterDays: 1095, // 3 years
    },
  };

  // Threat detection rules
  private threatRules: ThreatDetectionRule[] = [];

  static getInstance(): SecurityMonitoringService {
    if (!SecurityMonitoringService.instance) {
      SecurityMonitoringService.instance = new SecurityMonitoringService();
    }
    return SecurityMonitoringService.instance;
  }

  constructor() {
    this.initializeService();
  }

  /**
   * Service'i başlat
   */
  private async initializeService(): Promise<void> {
    try {
      await this.loadConfiguration();
      await this.loadThreatDetectionRules();
      await this.cleanupOldData();
      if (__DEV__) console.log('🛡️ Security Monitoring Service initialized');
    } catch (error) {
      console.error('Security Monitoring Service initialization error:', error);
    }
  }

  /**
   * Konfigürasyonu yükle
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const configStr = await AsyncStorage.getItem(this.CONFIG_KEY);
      if (configStr) {
        this.config = { ...this.config, ...JSON.parse(configStr) };
      }
    } catch (error) {
      console.error('Error loading security config:', error);
    }
  }

  /**
   * Threat detection rules'ları yükle
   */
  private async loadThreatDetectionRules(): Promise<void> {
    try {
      const rulesStr = await AsyncStorage.getItem(this.RULES_KEY);
      if (rulesStr) {
        this.threatRules = JSON.parse(rulesStr);
      } else {
        // Default rules'ları yükle
        this.threatRules = DEFAULT_THREAT_DETECTION_RULES.map(rule => ({
          ...rule,
          id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));
        await this.saveThreatDetectionRules();
      }
    } catch (error) {
      console.error('Error loading threat detection rules:', error);
    }
  }

  /**
   * Eski verileri temizle
   */
  private async cleanupOldData(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.monitoring.retentionDays);

      // Events cleanup
      const events = await this.getSecurityEvents();
      const filteredEvents = events.filter(event =>
        new Date(event.timestamp) > cutoffDate
      );
      await this.saveSecurityEvents(filteredEvents);

      // Login attempts cleanup
      const loginAttempts = await this.getLoginAttempts();
      const filteredAttempts = loginAttempts.filter(attempt =>
        new Date(attempt.timestamp) > cutoffDate
      );
      await this.saveLoginAttempts(filteredAttempts);

      console.log('🧹 Security data cleanup completed');
    } catch (error) {
      console.error('Error cleaning up old security data:', error);
    }
  }

  /**
   * Güvenlik olayı kaydet
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<string> {
    try {
      if (!this.config.monitoring.enabled) {
        return '';
      }

      const securityEvent: SecurityEvent = {
        ...event,
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };

      // Event'i kaydet
      const events = await this.getSecurityEvents();
      events.push(securityEvent);
      await this.saveSecurityEvents(events);

      // Threat detection çalıştır
      await this.runThreatDetection(securityEvent);

      console.log(`🔒 Security event logged: ${event.type} (${event.level})`);
      return securityEvent.id;
    } catch (error) {
      console.error('Error logging security event:', error);
      return '';
    }
  }

  /**
   * Login attempt kaydet
   */
  async logLoginAttempt(attempt: Omit<LoginAttempt, 'id' | 'timestamp' | 'riskScore'>): Promise<void> {
    try {
      const riskScore = await this.calculateLoginRiskScore(attempt);

      const loginAttempt: LoginAttempt = {
        ...attempt,
        id: `login_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        riskScore,
      };

      const attempts = await this.getLoginAttempts();
      attempts.push(loginAttempt);
      await this.saveLoginAttempts(attempts);

      // Security event oluştur
      await this.logSecurityEvent({
        type: attempt.success ? 'login_success' : 'login_failed',
        level: riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low',
        category: 'authentication',
        userId: attempt.userId,
        ipAddress: attempt.ipAddress,
        userAgent: attempt.userAgent,
        location: attempt.location,
        details: {
          action: 'login_attempt',
          result: attempt.success ? 'success' : 'failure',
          reason: attempt.failureReason,
          metadata: {
            email: attempt.email,
            blocked: attempt.blocked,
            riskScore,
          },
        },
        context: {
          riskScore,
          anomalyDetected: riskScore > 70,
        },
      });
    } catch (error) {
      console.error('Error logging login attempt:', error);
    }
  }

  /**
   * Audit trail kaydet
   */
  async logAuditTrail(trail: Omit<AuditTrail, 'id' | 'timestamp'>): Promise<void> {
    try {
      if (!this.config.compliance.auditTrailEnabled) {
        return;
      }

      const auditTrail: AuditTrail = {
        ...trail,
        id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };

      const trails = await this.getAuditTrails();
      trails.push(auditTrail);
      await this.saveAuditTrails(trails);

      // Security event oluştur
      await this.logSecurityEvent({
        type: 'data_access',
        level: 'low',
        category: 'data_breach',
        userId: trail.userId,
        ipAddress: trail.ipAddress,
        userAgent: trail.userAgent,
        details: {
          action: trail.action,
          resource: trail.resource,
          result: 'success',
          metadata: {
            resourceId: trail.resourceId,
            changes: trail.changes,
          },
        },
      });
    } catch (error) {
      console.error('Error logging audit trail:', error);
    }
  }

  /**
   * Login risk score hesapla
   */
  private async calculateLoginRiskScore(attempt: Partial<LoginAttempt>): Promise<number> {
    let riskScore = 0;

    try {
      // Geçmiş başarısız denemeler
      const recentAttempts = await this.getRecentLoginAttempts(attempt.email!, 24 * 60 * 60 * 1000);
      const failedAttempts = recentAttempts.filter(a => !a.success).length;
      riskScore += Math.min(failedAttempts * 15, 60);

      // Yeni IP adresi
      const knownIPs = await this.getKnownIPAddresses(attempt.userId);
      if (attempt.ipAddress && !knownIPs.includes(attempt.ipAddress)) {
        riskScore += 20;
      }

      // Yeni cihaz
      if (attempt.deviceFingerprint) {
        const knownDevices = await this.getKnownDevices(attempt.userId);
        if (!knownDevices.includes(attempt.deviceFingerprint)) {
          riskScore += 25;
        }
      }

      // Zaman anomalisi (normal saatler dışında)
      const hour = new Date().getHours();
      if (hour < 6 || hour > 23) {
        riskScore += 10;
      }

      return Math.min(riskScore, 100);
    } catch (error) {
      console.error('Error calculating login risk score:', error);
      return 50; // Default medium risk
    }
  }

  /**
   * Threat detection çalıştır
   */
  private async runThreatDetection(event: SecurityEvent): Promise<void> {
    try {
      if (!this.config.threatDetection.enabled) {
        return;
      }

      for (const rule of this.threatRules) {
        if (!rule.enabled || !rule.conditions.eventTypes.includes(event.type)) {
          continue;
        }

        const isTriggered = await this.evaluateRule(rule, event);
        if (isTriggered) {
          await this.triggerSecurityAlert(rule, event);
        }
      }
    } catch (error) {
      console.error('Error running threat detection:', error);
    }
  }

  /**
   * Rule'ı değerlendir
   */
  private async evaluateRule(rule: ThreatDetectionRule, event: SecurityEvent): Promise<boolean> {
    try {
      const timeWindow = rule.conditions.timeWindow;
      const threshold = rule.conditions.threshold;

      const cutoffTime = new Date(Date.now() - timeWindow);
      const events = await this.getSecurityEvents();

      const relevantEvents = events.filter(e =>
        rule.conditions.eventTypes.includes(e.type) &&
        new Date(e.timestamp) > cutoffTime &&
        this.matchesFilters(e, rule.conditions.filters || {})
      );

      return relevantEvents.length >= threshold;
    } catch (error) {
      console.error('Error evaluating rule:', error);
      return false;
    }
  }

  /**
   * Filter'ları kontrol et
   */
  private matchesFilters(event: SecurityEvent, filters: Record<string, any>): boolean {
    try {
      if (filters.sameUser && event.userId) {
        // Same user filter implementation
        return true;
      }

      if (filters.unusualLocation && event.location) {
        // Unusual location filter implementation
        return true;
      }

      if (filters.adminRole) {
        // Admin role filter implementation
        return event.details.metadata?.role === 'admin';
      }

      return true;
    } catch (error) {
      console.error('Error matching filters:', error);
      return false;
    }
  }

  /**
   * Security alert tetikle
   */
  private async triggerSecurityAlert(rule: ThreatDetectionRule, event: SecurityEvent): Promise<void> {
    try {
      const alert: SecurityAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: `Security Alert: ${rule.name}`,
        description: rule.description,
        level: rule.severity,
        category: rule.category,
        eventIds: [event.id],
        createdAt: new Date().toISOString(),
        status: 'open',
        actions: [],
      };

      // Alert'i kaydet
      const alerts = await this.getSecurityAlerts();
      alerts.push(alert);
      await this.saveSecurityAlerts(alerts);

      // Actions'ları çalıştır
      for (const actionType of rule.actions) {
        await this.executeSecurityAction(actionType, alert, event);
      }

      console.log(`🚨 Security alert triggered: ${rule.name} (${rule.severity})`);
    } catch (error) {
      console.error('Error triggering security alert:', error);
    }
  }

  /**
   * Security action çalıştır
   */
  private async executeSecurityAction(
    actionType: SecurityAction['type'],
    alert: SecurityAlert,
    event: SecurityEvent
  ): Promise<void> {
    try {
      const action: SecurityAction = {
        id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: actionType,
        timestamp: new Date().toISOString(),
        performedBy: 'system',
        details: {
          alertId: alert.id,
          eventId: event.id,
          userId: event.userId,
        },
        result: 'success',
      };

      switch (actionType) {
        case 'block_user':
          // User blocking implementation
          console.log(`🚫 User blocked: ${event.userId}`);
          break;

        case 'lock_account':
          // Account locking implementation
          console.log(`🔒 Account locked: ${event.userId}`);
          break;

        case 'require_2fa':
          // 2FA requirement implementation
          console.log(`🔐 2FA required: ${event.userId}`);
          break;

        case 'notify_admin':
          // Admin notification implementation
          console.log(`📧 Admin notified: ${alert.title}`);
          break;

        case 'log_event':
          // Additional logging
          console.log(`📝 Event logged: ${event.type}`);
          break;

        case 'quarantine_device':
          // Device quarantine implementation
          console.log(`📱 Device quarantined: ${event.deviceId}`);
          break;
      }

      alert.actions.push(action);
    } catch (error) {
      console.error('Error executing security action:', error);
    }
  }

  // Storage helper methods
  private async getSecurityEvents(): Promise<SecurityEvent[]> {
    try {
      const eventsStr = await SecureStorageWrapper.getItem(this.EVENTS_STORAGE_KEY, { encrypt: true });
      return eventsStr || [];
    } catch (error) {
      console.error('Error getting security events:', error);
      return [];
    }
  }

  private async saveSecurityEvents(events: SecurityEvent[]): Promise<void> {
    try {
      await SecureStorageWrapper.setItem(this.EVENTS_STORAGE_KEY, events, { encrypt: true });
    } catch (error) {
      console.error('Error saving security events:', error);
    }
  }

  private async getSecurityAlerts(): Promise<SecurityAlert[]> {
    try {
      const alertsStr = await SecureStorageWrapper.getItem(this.ALERTS_STORAGE_KEY, { encrypt: true });
      return alertsStr || [];
    } catch (error) {
      console.error('Error getting security alerts:', error);
      return [];
    }
  }

  private async saveSecurityAlerts(alerts: SecurityAlert[]): Promise<void> {
    try {
      await SecureStorageWrapper.setItem(this.ALERTS_STORAGE_KEY, alerts, { encrypt: true });
    } catch (error) {
      console.error('Error saving security alerts:', error);
    }
  }

  private async getLoginAttempts(): Promise<LoginAttempt[]> {
    try {
      const attemptsStr = await AsyncStorage.getItem(this.LOGIN_ATTEMPTS_KEY);
      return attemptsStr ? JSON.parse(attemptsStr) : [];
    } catch (error) {
      console.error('Error getting login attempts:', error);
      return [];
    }
  }

  private async saveLoginAttempts(attempts: LoginAttempt[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.LOGIN_ATTEMPTS_KEY, JSON.stringify(attempts));
    } catch (error) {
      console.error('Error saving login attempts:', error);
    }
  }

  private async getAuditTrails(): Promise<AuditTrail[]> {
    try {
      const trailsStr = await SecureStorageWrapper.getItem(this.AUDIT_TRAIL_KEY, { encrypt: true });
      return trailsStr || [];
    } catch (error) {
      console.error('Error getting audit trails:', error);
      return [];
    }
  }

  private async saveAuditTrails(trails: AuditTrail[]): Promise<void> {
    try {
      await SecureStorageWrapper.setItem(this.AUDIT_TRAIL_KEY, trails, { encrypt: true });
    } catch (error) {
      console.error('Error saving audit trails:', error);
    }
  }

  private async saveThreatDetectionRules(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.RULES_KEY, JSON.stringify(this.threatRules));
    } catch (error) {
      console.error('Error saving threat detection rules:', error);
    }
  }

  // Helper methods for risk calculation
  private async getRecentLoginAttempts(email: string, timeWindow: number): Promise<LoginAttempt[]> {
    const attempts = await this.getLoginAttempts();
    const cutoffTime = new Date(Date.now() - timeWindow);
    return attempts.filter(attempt =>
      attempt.email === email && new Date(attempt.timestamp) > cutoffTime
    );
  }

  private async getKnownIPAddresses(userId?: string): Promise<string[]> {
    if (!userId) return [];
    const attempts = await this.getLoginAttempts();
    return [...new Set(attempts
      .filter(attempt => attempt.userId === userId && attempt.success)
      .map(attempt => attempt.ipAddress)
    )];
  }

  private async getKnownDevices(userId?: string): Promise<string[]> {
    if (!userId) return [];
    const attempts = await this.getLoginAttempts();
    return [...new Set(attempts
      .filter(attempt => attempt.userId === userId && attempt.success && attempt.deviceFingerprint)
      .map(attempt => attempt.deviceFingerprint!)
    )];
  }

  /**
   * Public API methods
   */

  async getSecurityMetrics(timeRange?: { start: string; end: string }): Promise<SecurityMetrics> {
    try {
      const events = await this.getSecurityEvents();
      const alerts = await this.getSecurityAlerts();

      const filteredEvents = timeRange ? events.filter(event => {
        const eventTime = new Date(event.timestamp);
        return eventTime >= new Date(timeRange.start) && eventTime <= new Date(timeRange.end);
      }) : events;

      const eventsByType = filteredEvents.reduce((acc, event) => {
        acc[event.type] = (acc[event.type] || 0) + 1;
        return acc;
      }, {} as Record<SecurityEventType, number>);

      const eventsByLevel = filteredEvents.reduce((acc, event) => {
        acc[event.level] = (acc[event.level] || 0) + 1;
        return acc;
      }, {} as Record<SecurityLevel, number>);

      const activeAlerts = alerts.filter(alert => alert.status === 'open').length;
      const resolvedAlerts = alerts.filter(alert => alert.status === 'resolved').length;

      // Calculate threat score based on recent events and alerts
      const threatScore = Math.min(
        (eventsByLevel.critical || 0) * 25 +
        (eventsByLevel.high || 0) * 15 +
        (eventsByLevel.medium || 0) * 10 +
        (eventsByLevel.low || 0) * 5 +
        activeAlerts * 20,
        100
      );

      return {
        totalEvents: filteredEvents.length,
        eventsByType,
        eventsByLevel,
        activeAlerts,
        resolvedAlerts,
        threatScore,
        lastUpdate: new Date().toISOString(),
        timeRange: timeRange || {
          start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error('Error getting security metrics:', error);
      return {
        totalEvents: 0,
        eventsByType: {} as Record<SecurityEventType, number>,
        eventsByLevel: {} as Record<SecurityLevel, number>,
        activeAlerts: 0,
        resolvedAlerts: 0,
        threatScore: 0,
        lastUpdate: new Date().toISOString(),
        timeRange: {
          start: new Date().toISOString(),
          end: new Date().toISOString(),
        },
      };
    }
  }

  async getRecentSecurityEvents(limit: number = 50): Promise<SecurityEvent[]> {
    const events = await this.getSecurityEvents();
    return events
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  async getActiveSecurityAlerts(): Promise<SecurityAlert[]> {
    const alerts = await this.getSecurityAlerts();
    return alerts.filter(alert => alert.status === 'open');
  }

  async resolveSecurityAlert(alertId: string, resolvedBy: string): Promise<boolean> {
    try {
      const alerts = await this.getSecurityAlerts();
      const alertIndex = alerts.findIndex(alert => alert.id === alertId);

      if (alertIndex === -1) {
        return false;
      }

      alerts[alertIndex].status = 'resolved';
      alerts[alertIndex].resolvedAt = new Date().toISOString();
      alerts[alertIndex].resolvedBy = resolvedBy;

      await this.saveSecurityAlerts(alerts);
      return true;
    } catch (error) {
      console.error('Error resolving security alert:', error);
      return false;
    }
  }

  getConfiguration(): SecurityConfiguration {
    return { ...this.config };
  }

  async updateConfiguration(newConfig: Partial<SecurityConfiguration>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      await AsyncStorage.setItem(this.CONFIG_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('Error updating security configuration:', error);
    }
  }
}

export default SecurityMonitoringService.getInstance();
