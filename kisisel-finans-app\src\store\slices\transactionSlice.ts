// Transaction Slice - İşlem state yönetimi

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Transaction } from '../../types';
import ApiService from '../../services/ApiService';

// Transaction state interface
interface TransactionState {
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
  filters: {
    dateRange: {
      start: string;
      end: string;
    };
    categories: string[];
    type: 'all' | 'income' | 'expense';
    searchQuery: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// Initial state
const initialState: TransactionState = {
  transactions: [],
  isLoading: false,
  error: null,
  filters: {
    dateRange: {
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
      end: new Date().toISOString(),
    },
    categories: [],
    type: 'all',
    searchQuery: '',
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: true,
  },
};

// Async thunks
export const fetchTransactions = createAsyncThunk(
  'transactions/fetchTransactions',
  async (params: {
    page?: number;
    limit?: number;
    filters?: Partial<TransactionState['filters']>;
  } = {}, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { transactions: TransactionState };
      const { page = 1, limit = 20, filters = {} } = params;

      // Gerçek API çağrısı
      const apiParams = {
        page,
        limit,
        startDate: filters.dateRange?.start,
        endDate: filters.dateRange?.end,
        category: filters.categories?.length ? filters.categories[0] : undefined,
        type: filters.type !== 'all' ? filters.type : undefined,
      };

      const response = await ApiService.getTransactions(apiParams);

      if (response.success && response.data) {
        return response.data;
      } else {
        // Fallback to mock data if API fails
        const mockTransactions: Transaction[] = [
        {
          id: '1',
          userId: '1',
          type: 'expense',
          amount: 150.50,
          currency: 'TRY',
          category: 'food',
          subcategory: 'Restaurant',
          description: 'Akşam yemeği',
          date: new Date().toISOString(),
          paymentMethod: 'card',
          location: 'İstanbul',
          tags: ['yemek', 'restoran'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          userId: '1',
          type: 'income',
          amount: 5000,
          currency: 'TRY',
          category: 'salary',
          description: 'Aylık maaş',
          date: new Date().toISOString(),
          paymentMethod: 'bank_transfer',
          tags: ['maaş'],
          isRecurring: true,
          recurringType: 'monthly',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '3',
          userId: '1',
          type: 'expense',
          amount: 85.00,
          currency: 'TRY',
          category: 'transportation',
          description: 'Benzin',
          date: new Date(Date.now() - ********).toISOString(), // 1 gün önce
          paymentMethod: 'card',
          tags: ['benzin', 'ulaşım'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '4',
          userId: '1',
          type: 'expense',
          amount: 250.00,
          currency: 'TRY',
          category: 'shopping',
          description: 'Kıyafet alışverişi',
          date: new Date(Date.now() - *********).toISOString(), // 2 gün önce
          paymentMethod: 'card',
          tags: ['kıyafet', 'alışveriş'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '5',
          userId: '1',
          type: 'expense',
          amount: 45.00,
          currency: 'TRY',
          category: 'entertainment',
          description: 'Sinema bileti',
          date: new Date(Date.now() - 259200000).toISOString(), // 3 gün önce
          paymentMethod: 'card',
          tags: ['sinema', 'eğlence'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '6',
          userId: '1',
          type: 'expense',
          amount: 120.00,
          currency: 'TRY',
          category: 'health',
          description: 'Eczane',
          date: new Date(Date.now() - 345600000).toISOString(), // 4 gün önce
          paymentMethod: 'cash',
          tags: ['sağlık', 'ilaç'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '7',
          userId: '1',
          type: 'expense',
          amount: 75.00,
          currency: 'TRY',
          category: 'food',
          description: 'Market alışverişi',
          date: new Date(Date.now() - 432000000).toISOString(), // 5 gün önce
          paymentMethod: 'card',
          tags: ['market', 'yemek'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

        return {
          transactions: mockTransactions,
          total: mockTransactions.length,
          page,
          hasMore: false,
        };
      }
    } catch (error: any) {
      // Fallback to mock data on error
      const mockTransactions: Transaction[] = [
        {
          id: '1',
          userId: '1',
          type: 'expense',
          amount: 150.50,
          currency: 'TRY',
          category: 'food',
          subcategory: 'Restaurant',
          description: 'Akşam yemeği',
          date: new Date().toISOString(),
          paymentMethod: 'card',
          location: 'İstanbul',
          tags: ['yemek', 'restoran'],
          isRecurring: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return {
        transactions: mockTransactions,
        total: mockTransactions.length,
        page: 1,
        hasMore: false,
      };
    }
  }
);

export const addTransaction = createAsyncThunk(
  'transactions/addTransaction',
  async (transactionData: Omit<Transaction, 'id' | 'userId' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      // Gerçek API çağrısı
      const response = await ApiService.createTransaction(transactionData);

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'İşlem ekleme başarısız');
      }
    } catch (error: any) {
      // Fallback to local creation
      const newTransaction: Transaction = {
        ...transactionData,
        id: Date.now().toString(),
        userId: '1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return newTransaction;
    }
  }
);

export const updateTransaction = createAsyncThunk(
  'transactions/updateTransaction',
  async (params: { id: string; data: Partial<Transaction> }, { rejectWithValue }) => {
    try {
      // TODO: API çağrısı yapılacak
      const updatedTransaction: Transaction = {
        ...params.data,
        id: params.id,
        updatedAt: new Date().toISOString(),
      } as Transaction;

      return updatedTransaction;
    } catch (error: any) {
      return rejectWithValue(error.message || 'İşlem güncellenirken hata oluştu');
    }
  }
);

export const deleteTransaction = createAsyncThunk(
  'transactions/deleteTransaction',
  async (transactionId: string, { rejectWithValue }) => {
    try {
      // TODO: API çağrısı yapılacak
      return transactionId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'İşlem silinirken hata oluştu');
    }
  }
);

export const getTransactionsByCategory = createAsyncThunk(
  'transactions/getTransactionsByCategory',
  async (categoryId: string, { rejectWithValue }) => {
    try {
      // TODO: API çağrısı yapılacak
      return [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Kategori işlemleri yüklenirken hata oluştu');
    }
  }
);

export const getTransactionStats = createAsyncThunk(
  'transactions/getTransactionStats',
  async (params: { startDate: string; endDate: string }, { rejectWithValue }) => {
    try {
      // TODO: API çağrısı yapılacak
      return {
        totalIncome: 5000,
        totalExpense: 3500,
        netAmount: 1500,
        transactionCount: 25,
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'İstatistikler yüklenirken hata oluştu');
    }
  }
);

// Transaction slice
const transactionSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<Partial<TransactionState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.filters.dateRange = action.payload;
    },
    setCategoryFilter: (state, action: PayloadAction<string[]>) => {
      state.filters.categories = action.payload;
    },
    setTypeFilter: (state, action: PayloadAction<'all' | 'income' | 'expense'>) => {
      state.filters.type = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.filters.searchQuery = action.payload;
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    resetPagination: (state) => {
      state.pagination = initialState.pagination;
    },
    clearTransactions: (state) => {
      state.transactions = [];
      state.pagination = initialState.pagination;
    },
  },
  extraReducers: (builder) => {
    // Fetch transactions
    builder
      .addCase(fetchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        const { transactions, total, page, hasMore } = action.payload;

        if (page === 1) {
          state.transactions = transactions;
        } else {
          state.transactions = [...state.transactions, ...transactions];
        }

        state.pagination = {
          ...state.pagination,
          page,
          total,
          hasMore,
        };
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Add transaction
    builder
      .addCase(addTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions.unshift(action.payload);
        state.pagination.total += 1;
      })
      .addCase(addTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update transaction
    builder
      .addCase(updateTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.transactions.findIndex(t => t.id === action.payload.id);
        if (index !== -1) {
          state.transactions[index] = action.payload;
        }
      })
      .addCase(updateTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete transaction
    builder
      .addCase(deleteTransaction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = state.transactions.filter(t => t.id !== action.payload);
        state.pagination.total -= 1;
      })
      .addCase(deleteTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setFilters,
  setDateRange,
  setCategoryFilter,
  setTypeFilter,
  setSearchQuery,
  resetFilters,
  resetPagination,
  clearTransactions,
} = transactionSlice.actions;

export default transactionSlice.reducer;
