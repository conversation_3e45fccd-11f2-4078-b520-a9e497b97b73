# RFC 003: Bütçe Planlaması

## Özet
Bu RFC, Kişisel Finans Yönetimi uygulamasının bütçe planlama özelliklerinin teknik tasarımını tanımlamaktadır.

## Motivasyon
Kullanıcıların finansal hedeflerine ulaşabilmeleri için gelir ve giderlerini planlamaları gerekmektedir. Bütçe planlama özelliği, kullanıcılar<PERSON>n harcamalarını kontrol altında tutmalarına, tasarruf etmelerine ve finansal disiplin oluşturmalarına yardımcı olacaktır.

## Tasarım Detayları

### Bütçe Veri Modeli
- **Bütçe Ana Yapısı**
  - Bütçe ID (unique identifier)
  - Kullanıcı ID (foreign key)
  - Bütçe adı
  - Bütçe dönemi (enum: haftalık, aylık, 3 aylık, yıll<PERSON>k)
  - Başlangıç tarihi
  - Bitiş tarihi
  - Toplam gelir hedefi
  - Toplam gider limiti
  - <PERSON>sar<PERSON>f hedefi
  - Para birimi
  - Notlar
  
- **Kategori Bazlı Bütçe Yapısı**
  - Kategori bütçe ID
  - Bütçe ID (foreign key)
  - Kategori ID (foreign key)
  - Planlanan miktar
  - Harcanan miktar
  - Kalan miktar
  - Uyarı eşiği (yüzde)

### Bütçe Oluşturma ve Yönetme
- **Bütçe Oluşturma İşlevi**
  - Sıfırdan bütçe oluşturma
  - Önceki dönemden kopyalama
  - Otomatik bütçe önerisi (geçmiş harcama verilerine dayalı)
  - Şablon bütçe kullanımı
  
- **Bütçe Düzenleme İşlevi**
  - Kategori limitlerini değiştirme
  - Dönem içinde bütçe revizyonu
  - Kategori ekleme/çıkarma
  - Bütçe notları ekleme

### Bütçe İzleme ve Raporlama
- **Bütçe Durum Takibi**
  - Gerçek zamanlı bütçe kullanım durumu
  - Kategori bazlı ilerleme göstergeleri
  - Kalan gün/bütçe oranları
  - Trend analizleri
  
- **Bütçe Raporları**
  - Bütçe-gerçekleşen karşılaştırma raporları
  - Dönemler arası karşılaştırma
  - Kategori bazlı analiz raporları
  - Tasarruf performansı raporları

### Harcama Limitleri ve Uyarılar
- **Limit Tanımlama**
  - Kategori bazlı limitler
  - Alt kategori bazlı limitler
  - Toplam harcama limitleri
  - Ödeme yöntemi bazlı limitler
  
- **Limit Uyarı Sistemi**
  - Eşik belirleme (%50, %75, %90, %100)
  - Bildirim tercihleri
  - E-posta/push bildirimleri
  - Günlük/haftalık özet bildirimleri

### Bütçe Önerileri ve Optimizasyon
- **Tasarruf Önerileri**
  - Kategori bazlı tasarruf önerileri
  - Geçmiş veriler üzerinden analiz
  - "Şunu yaparsanız X kadar tasarruf edersiniz" önerileri
  
- **Bütçe Optimizasyonu**
  - Düşük öncelikli harcama kategorileri belirleme
  - Aşırı/düşük tahmin edilen kategorileri tespit etme
  - Mevsimsel harcama ayarlamaları önerme

## Kullanıcı Arayüzü Bileşenleri
- Bütçe oluşturma sihirbazı
- Bütçe genel bakış ekranı
- Kategori detay ekranları
- İlerleme göstergeleri (dairesel, çubuk grafik)
- Uyarı ve bildirim yönetim paneli

## Uygulama
- **Veri İşleme**
  - Günlük bütçe durumu hesaplama
  - Otomatik bildirim tetikleme
  - Dönem sonu bütçe raporlama
  
- **Entegrasyon Noktaları**
  - Gelir-gider takip modülü entegrasyonu
  - Bildirim sistemi entegrasyonu
  - Raporlama modülü entegrasyonu

## Alternatifler
- Yapay zeka destekli dinamik bütçe ayarlamaları
- Sıfır tabanlı bütçeleme yaklaşımı
- Hedef bazlı bütçeleme yaklaşımı

## Açık Sorular
- Birden fazla para birimi desteği nasıl sağlanacak?
- Paylaşımlı bütçeler (örn. aile üyeleri arasında) nasıl yönetilecek?
- Yıl ortası bütçe değişiklikleri nasıl ele alınacak?

## Referanslar
- Envelope bütçeleme metodolojisi
- 50/30/20 bütçe kuralı
- Finansal davranış psikolojisi araştırmaları
