// Settings Screen - Ayarlar ekranı

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../contexts/ThemeContext';
import { useCurrency } from '../../contexts/CurrencyContext';
import { useSimpleAuth } from '../../contexts/SimpleAuthContext';
import { Ionicons } from '@expo/vector-icons';
import CurrencySelector from '../../components/CurrencySelector';
import NetworkService from '../../services/NetworkService';
import NotificationService from '../../services/NotificationService';
import BackupService from '../../services/BackupService';
import SecurityService from '../../services/SecurityService';
import TwoFactorService from '../../services/TwoFactorService';

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { theme, toggleTheme, isDark } = useTheme();
  const { baseCurrency } = useCurrency();
  const { user, logout } = useSimpleAuth();

  const [networkState, setNetworkState] = useState(NetworkService.getInstance().getNetworkState());
  const [syncStatus, setSyncStatus] = useState(NetworkService.getInstance().getSyncStatus());
  const [lastBackupDates, setLastBackupDates] = useState<{
    local: string | null;
    cloud: string | null;
    restore: string | null;
  }>({ local: null, cloud: null, restore: null });

  const [securitySettings, setSecuritySettings] = useState(SecurityService.getInstance().getSecuritySettings());
  const [twoFactorStatus, setTwoFactorStatus] = useState<any>(null);

  // Network state listener
  React.useEffect(() => {
    const networkService = NetworkService.getInstance();

    const unsubscribe = networkService.addListener((state) => {
      setNetworkState(state);
      setSyncStatus(networkService.getSyncStatus());
    });

    return unsubscribe;
  }, []);

  // Load backup dates
  React.useEffect(() => {
    const loadBackupDates = async () => {
      const dates = await BackupService.getInstance().getLastBackupDates();
      setLastBackupDates(dates);
    };

    loadBackupDates();
  }, []);

  // Load 2FA status
  React.useEffect(() => {
    const load2FAStatus = async () => {
      const status = await TwoFactorService.getInstance().getTwoFactorStatus();
      setTwoFactorStatus(status);
    };

    load2FAStatus();
  }, []);

  // Mock settings state - replace with real settings context
  const [settings, setSettings] = useState({
    theme: 'system',
    notifications: {
      budgetAlerts: true,
      goalReminders: true,
      weeklyReports: false,
    },
    security: {
      biometricEnabled: false,
      autoLockEnabled: true,
    },
  });

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            await logout();
          },
        },
      ]
    );
  };

  const handleForceSync = async () => {
    try {
      const networkService = NetworkService.getInstance();
      const success = await networkService.forceSync();

      if (success) {
        Alert.alert('Başarılı', 'Veriler başarıyla senkronize edildi');
        setSyncStatus(networkService.getSyncStatus());
      } else {
        Alert.alert('Hata', 'Senkronizasyon başarısız oldu');
      }
    } catch (error) {
      Alert.alert('Hata', 'Cihaz çevrimdışı');
    }
  };

  const handleNotificationPermission = async () => {
    try {
      const hasPermission = await NotificationService.requestPermissions();
      if (hasPermission) {
        Alert.alert('Başarılı', 'Bildirim izni verildi');
        // Test bildirimi gönder
        await NotificationService.sendImmediateNotification({
          title: '🎉 Bildirimler Aktif',
          body: 'Artık önemli finansal güncellemeler hakkında bilgilendirileceksiniz!',
          data: { type: 'test' }
        });
      } else {
        Alert.alert('Hata', 'Bildirim izni reddedildi');
      }
    } catch (error) {
      Alert.alert('Hata', 'Bildirim ayarları yapılandırılamadı');
    }
  };

  const handleCreateBackup = async () => {
    try {
      Alert.alert(
        'Yedek Oluştur',
        'Hangi türde yedek oluşturmak istiyorsunuz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Yerel Yedek',
            onPress: async () => {
              try {
                await BackupService.getInstance().createBackup();
                const dates = await BackupService.getInstance().getLastBackupDates();
                setLastBackupDates(dates);
                Alert.alert('Başarılı', 'Yerel yedek oluşturuldu');
              } catch (error) {
                Alert.alert('Hata', 'Yedek oluşturulamadı');
              }
            }
          },
          {
            text: 'Cloud Yedek',
            onPress: async () => {
              try {
                const backup = await BackupService.getInstance().createBackup();
                const success = await BackupService.getInstance().uploadToCloud(backup);

                if (success) {
                  const dates = await BackupService.getInstance().getLastBackupDates();
                  setLastBackupDates(dates);
                  Alert.alert('Başarılı', 'Cloud yedek oluşturuldu');
                } else {
                  Alert.alert('Hata', 'Cloud yedek yüklenemedi');
                }
              } catch (error) {
                Alert.alert('Hata', 'Cloud yedek oluşturulamadı');
              }
            }
          },
        ]
      );
    } catch (error) {
      Alert.alert('Hata', 'Yedekleme işlemi başarısız oldu');
    }
  };

  const handleRestoreBackup = async () => {
    try {
      const localBackup = await BackupService.getInstance().getLocalBackup();

      if (!localBackup) {
        Alert.alert('Hata', 'Yerel yedek bulunamadı');
        return;
      }

      Alert.alert(
        'Geri Yükle',
        'Mevcut veriler silinecek ve yedekten geri yüklenecek. Emin misiniz?',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Geri Yükle',
            style: 'destructive',
            onPress: async () => {
              try {
                await BackupService.getInstance().restoreFromBackup(localBackup);
                const dates = await BackupService.getInstance().getLastBackupDates();
                setLastBackupDates(dates);
                Alert.alert('Başarılı', 'Veriler geri yüklendi. Uygulama yeniden başlatılacak.');
              } catch (error) {
                Alert.alert('Hata', 'Geri yükleme başarısız oldu');
              }
            }
          },
        ]
      );
    } catch (error) {
      Alert.alert('Hata', 'Geri yükleme işlemi başarısız oldu');
    }
  };

  // Güvenlik fonksiyonları
  const handleScreenCaptureToggle = async (enabled: boolean) => {
    try {
      await SecurityService.getInstance().updateSecuritySettings({
        screenCaptureBlocked: enabled
      });

      setSecuritySettings(SecurityService.getInstance().getSecuritySettings());

      Alert.alert(
        'Başarılı',
        enabled
          ? 'Ekran görüntüsü koruması etkinleştirildi'
          : 'Ekran görüntüsü koruması devre dışı bırakıldı'
      );
    } catch (error) {
      Alert.alert('Hata', 'Güvenlik ayarı güncellenemedi');
    }
  };

  const handleBiometricToggle = async (enabled: boolean) => {
    try {
      if (enabled) {
        // Biyometrik doğrulama test et
        const success = await SecurityService.getInstance().authenticateWithBiometrics();
        if (!success) {
          Alert.alert('Hata', 'Biyometrik doğrulama başarısız');
          return;
        }
      }

      await SecurityService.getInstance().updateSecuritySettings({
        biometricEnabled: enabled
      });

      setSecuritySettings(SecurityService.getInstance().getSecuritySettings());

      Alert.alert(
        'Başarılı',
        enabled
          ? 'Biyometrik doğrulama etkinleştirildi'
          : 'Biyometrik doğrulama devre dışı bırakıldı'
      );
    } catch (error) {
      Alert.alert('Hata', 'Biyometrik ayar güncellenemedi');
    }
  };

  const handleSecurityCheck = async () => {
    try {
      const report = await SecurityService.getInstance().getSecurityReport();

      // Risk seviyesine göre renk
      const getRiskColor = (risk: string) => {
        switch (risk) {
          case 'LOW': return '🟢';
          case 'MEDIUM': return '🟡';
          case 'HIGH': return '🟠';
          case 'CRITICAL': return '🔴';
          default: return '⚪';
        }
      };

      let message = `${getRiskColor(report.riskLevel)} Güvenlik Skoru: ${report.securityScore}/100\n`;
      message += `Risk Seviyesi: ${report.riskLevel}\n\n`;

      message += `📱 Cihaz Bilgileri:\n`;
      message += `• Marka: ${report.deviceFingerprint.brand || 'Bilinmiyor'}\n`;
      message += `• Model: ${report.deviceFingerprint.modelName || 'Bilinmiyor'}\n`;
      message += `• İşletim Sistemi: ${report.deviceFingerprint.osName} ${report.deviceFingerprint.osVersion}\n`;
      message += `• Gerçek Cihaz: ${report.deviceFingerprint.isDevice ? 'Evet' : 'Hayır'}\n\n`;

      message += `🔒 Güvenlik Durumu:\n`;
      message += `• Jailbreak/Root: ${report.status.isJailbroken ? '⚠️ Tespit edildi' : '✅ Güvenli'}\n`;
      message += `• Emulator: ${report.status.isEmulator ? '⚠️ Tespit edildi' : '✅ Gerçek cihaz'}\n`;
      message += `• Biyometrik: ${report.status.biometricAvailable ? '✅ Destekleniyor' : '❌ Desteklenmiyor'}\n`;
      message += `• Ekran koruması: ${report.settings.screenCaptureBlocked ? '✅ Aktif' : '❌ Pasif'}\n`;
      message += `• Otomatik kilit: ${report.settings.autoLockEnabled ? '✅ Aktif' : '❌ Pasif'}\n\n`;

      if (report.recommendations.length > 0) {
        message += `💡 Öneriler:\n${report.recommendations.map(r => `${r}`).join('\n')}\n\n`;
      }

      message += `🛡️ Güvenlik Özellikleri:\n`;
      message += `• Ekran görüntüsü koruması: ${report.settings.screenCaptureBlocked ? 'Aktif' : 'Pasif'}\n`;
      message += `• Biyometrik giriş: ${report.settings.biometricEnabled ? 'Aktif' : 'Pasif'}\n`;
      message += `• Jailbreak algılama: ${report.settings.jailbreakDetectionEnabled ? 'Aktif' : 'Pasif'}`;

      Alert.alert('🔒 Detaylı Güvenlik Raporu', message, [
        { text: 'Tamam', style: 'default' },
        {
          text: 'Güvenlik Ayarları',
          onPress: () => {
            // Güvenlik bölümüne scroll yapılabilir
          }
        }
      ]);
    } catch (error) {
      Alert.alert('Hata', 'Güvenlik kontrolü yapılamadı');
    }
  };

  const SettingItem: React.FC<{
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
    showArrow?: boolean;
  }> = ({ icon, title, subtitle, onPress, rightElement, showArrow = true }) => (
    <TouchableOpacity style={[styles.settingItem, { borderBottomColor: theme.colors.border }]} onPress={onPress}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={theme.colors.primary} />
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: theme.colors.text }]}>{title}</Text>
          {subtitle && <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>{subtitle}</Text>}
        </View>
      </View>
      <View style={styles.settingRight}>
        {rightElement}
        {showArrow && !rightElement && (
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        )}
      </View>
    </TouchableOpacity>
  );

  const SectionHeader: React.FC<{ title: string }> = ({ title }) => (
    <Text style={[styles.sectionHeader, { color: theme.colors.textSecondary }]}>{title}</Text>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Profile Section */}
      <View style={[styles.profileSection, { backgroundColor: theme.colors.surface }]}>
        <View style={[styles.profileIcon, { backgroundColor: theme.colors.primary }]}>
          <Ionicons name="person" size={32} color={theme.colors.surface} />
        </View>
        <Text style={[styles.profileName, { color: theme.colors.text }]}>{user?.name || 'Kullanıcı'}</Text>
        <Text style={[styles.profileEmail, { color: theme.colors.textSecondary }]}>{user?.email || ''}</Text>
      </View>

      {/* Account Settings */}
      <SectionHeader title="Hesap" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="person-outline"
          title="Profil Bilgileri"
          subtitle="Ad, e-posta ve diğer bilgiler"
          onPress={() => console.log('Navigate to profile')}
        />
        <SettingItem
          icon="shield-outline"
          title="Güvenlik"
          subtitle="Şifre ve güvenlik ayarları"
          onPress={() => {}}
        />
        <SettingItem
          icon="card-outline"
          title="Ödeme Yöntemleri"
          subtitle="Kayıtlı kartlar ve hesaplar"
          onPress={() => {}}
        />
      </View>

      {/* App Settings */}
      <SectionHeader title="Uygulama" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="color-palette-outline"
          title="Tema"
          subtitle={isDark ? 'Koyu' : 'Açık'}
          rightElement={
            <Switch
              value={isDark}
              onValueChange={toggleTheme}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
        <View style={styles.currencyContainer}>
          <CurrencySelector showLabel={false} />
        </View>
        <SettingItem
          icon="language-outline"
          title="Dil"
          subtitle="Türkçe"
          onPress={() => {}}
        />
      </View>

      {/* Notifications */}
      <SectionHeader title="Bildirimler" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="notifications-outline"
          title="Bildirim İzni"
          subtitle="Push bildirimler için izin ver"
          onPress={handleNotificationPermission}
        />
        <SettingItem
          icon="alert-circle-outline"
          title="Bütçe Uyarıları"
          subtitle="Bütçe limitine yaklaştığında bildir"
          rightElement={
            <Switch
              value={settings.notifications.budgetAlerts}
              onValueChange={(value) =>
                setSettings(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, budgetAlerts: value }
                }))
              }
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
        <SettingItem
          icon="flag-outline"
          title="Hedef Hatırlatmaları"
          subtitle="Hedef tarihlerine yaklaştığında bildir"
          rightElement={
            <Switch
              value={settings.notifications.goalReminders}
              onValueChange={(value) =>
                setSettings(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, goalReminders: value }
                }))
              }
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
        <SettingItem
          icon="bar-chart-outline"
          title="Haftalık Raporlar"
          subtitle="Haftalık finansal özet gönder"
          rightElement={
            <Switch
              value={settings.notifications.weeklyReports}
              onValueChange={(value) =>
                setSettings(prev => ({
                  ...prev,
                  notifications: { ...prev.notifications, weeklyReports: value }
                }))
              }
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
      </View>

      {/* Security */}
      <SectionHeader title="Güvenlik" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="shield-checkmark-outline"
          title="Güvenlik Kontrolü"
          subtitle="Cihaz güvenlik durumunu kontrol et"
          onPress={handleSecurityCheck}
        />
        <SettingItem
          icon="eye-off-outline"
          title="Ekran Görüntüsü Koruması"
          subtitle="Ekran görüntüsü alınmasını engelle"
          rightElement={
            <Switch
              value={securitySettings.screenCaptureBlocked}
              onValueChange={handleScreenCaptureToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
        <SettingItem
          icon="finger-print-outline"
          title="Biyometrik Giriş"
          subtitle="Parmak izi veya yüz tanıma ile giriş"
          rightElement={
            <Switch
              value={securitySettings.biometricEnabled}
              onValueChange={handleBiometricToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
        <SettingItem
          icon="lock-closed-outline"
          title="Otomatik Kilit"
          subtitle="Uygulama kullanılmadığında kilitle"
          rightElement={
            <Switch
              value={securitySettings.autoLockEnabled}
              onValueChange={(value) =>
                SecurityService.getInstance().updateSecuritySettings({
                  autoLockEnabled: value
                }).then(() => {
                  setSecuritySettings(SecurityService.getInstance().getSecuritySettings());
                })
              }
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.surface}
            />
          }
          showArrow={false}
        />
      </View>

      {/* Two Factor Authentication */}
      <SectionHeader title="İki Faktörlü Kimlik Doğrulama" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="shield-outline"
          title="2FA Ayarları"
          subtitle={twoFactorStatus?.isEnabled
            ? `Aktif (${twoFactorStatus.enabledMethods.length} yöntem)`
            : 'İki faktörlü kimlik doğrulama kapalı'
          }
          rightElement={
            twoFactorStatus?.isEnabled ? (
              <View style={[styles.statusIndicator, { backgroundColor: '#10B981' }]} />
            ) : (
              <View style={[styles.statusIndicator, { backgroundColor: '#EF4444' }]} />
            )
          }
          onPress={() => navigation.navigate('TwoFactorSetup' as never)}
        />

        {twoFactorStatus?.isEnabled && (
          <>
            <SettingItem
              icon="mail-outline"
              title="Email Doğrulama"
              subtitle={twoFactorStatus.enabledMethods.includes('email')
                ? '✅ Aktif'
                : 'Pasif'
              }
              showArrow={false}
            />
            <SettingItem
              icon="chatbubble-outline"
              title="SMS Doğrulama"
              subtitle={twoFactorStatus.enabledMethods.includes('sms')
                ? '✅ Aktif'
                : 'Pasif'
              }
              showArrow={false}
            />
            <SettingItem
              icon="key-outline"
              title="Authenticator App"
              subtitle={twoFactorStatus.enabledMethods.includes('authenticator')
                ? '✅ Aktif'
                : 'Pasif'
              }
              showArrow={false}
            />

            <SettingItem
              icon="shield-checkmark-outline"
              title="Güvenilir Cihazlar"
              subtitle="Güvenilir cihaz yönetimi"
              onPress={() => navigation.navigate('TrustedDevices' as never)}
            />
          </>
        )}
      </View>

      {/* Data & Sync */}
      <SectionHeader title="Veri ve Senkronizasyon" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        {/* Network Status */}
        <SettingItem
          icon={networkState.isConnected ? "wifi" : "wifi-off"}
          title="Ağ Durumu"
          subtitle={networkState.isConnected
            ? `Bağlı (${networkState.type})`
            : 'Çevrimdışı'
          }
          rightElement={
            <View style={[
              styles.statusIndicator,
              { backgroundColor: networkState.isConnected ? '#10B981' : '#EF4444' }
            ]} />
          }
          showArrow={false}
        />

        {/* Sync Status */}
        <SettingItem
          icon="sync-outline"
          title="Senkronizasyon"
          subtitle={syncStatus.pendingCount > 0
            ? `${syncStatus.pendingCount} bekleyen işlem`
            : 'Tüm veriler senkronize'
          }
          rightElement={
            syncStatus.pendingCount > 0 ? (
              <View style={[styles.badge, { backgroundColor: '#F59E0B' }]}>
                <Text style={[styles.badgeText, { color: 'white' }]}>
                  {syncStatus.pendingCount}
                </Text>
              </View>
            ) : (
              <Ionicons name="checkmark-circle" size={20} color="#10B981" />
            )
          }
          onPress={handleForceSync}
        />

        <SettingItem
          icon="cloud-upload-outline"
          title="Manuel Senkronizasyon"
          subtitle="Verileri şimdi senkronize et"
          onPress={handleForceSync}
        />
      </View>

      {/* Cloud Backup */}
      <SectionHeader title="Yedekleme ve Geri Yükleme" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="cloud-upload-outline"
          title="Yedek Oluştur"
          subtitle={lastBackupDates.local
            ? `Son yerel yedek: ${new Date(lastBackupDates.local).toLocaleDateString('tr-TR')}`
            : 'Henüz yedek oluşturulmamış'
          }
          onPress={handleCreateBackup}
        />
        <SettingItem
          icon="cloud-download-outline"
          title="Yedekten Geri Yükle"
          subtitle={lastBackupDates.restore
            ? `Son geri yükleme: ${new Date(lastBackupDates.restore).toLocaleDateString('tr-TR')}`
            : 'Henüz geri yükleme yapılmamış'
          }
          onPress={handleRestoreBackup}
        />
        <SettingItem
          icon="cloud-outline"
          title="Cloud Yedek Durumu"
          subtitle={lastBackupDates.cloud
            ? `Son cloud yedek: ${new Date(lastBackupDates.cloud).toLocaleDateString('tr-TR')}`
            : 'Cloud yedek bulunamadı'
          }
          rightElement={
            <View style={[
              styles.statusIndicator,
              { backgroundColor: lastBackupDates.cloud ? '#10B981' : '#F59E0B' }
            ]} />
          }
          showArrow={false}
        />
      </View>

      {/* Data & Privacy */}
      <SectionHeader title="Veri ve Gizlilik" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="download-outline"
          title="Verileri Dışa Aktar"
          subtitle="Tüm verilerinizi indirin"
          onPress={() => {}}
        />
        <SettingItem
          icon="document-text-outline"
          title="Gizlilik Politikası"
          onPress={() => {}}
        />
        <SettingItem
          icon="document-outline"
          title="Kullanım Koşulları"
          onPress={() => {}}
        />
      </View>

      {/* Support */}
      <SectionHeader title="Destek" />
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="help-circle-outline"
          title="Yardım Merkezi"
          onPress={() => {}}
        />
        <SettingItem
          icon="mail-outline"
          title="İletişim"
          subtitle="Bizimle iletişime geçin"
          onPress={() => {}}
        />
        <SettingItem
          icon="star-outline"
          title="Uygulamayı Değerlendir"
          onPress={() => {}}
        />
        <SettingItem
          icon="information-circle-outline"
          title="Hakkında"
          subtitle="Sürüm 1.0.0"
          onPress={() => {}}
        />
      </View>

      {/* Logout */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <SettingItem
          icon="log-out-outline"
          title="Çıkış Yap"
          onPress={handleLogout}
          showArrow={false}
        />
      </View>

      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          Kişisel Finans v1.0.0
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  profileIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
    marginHorizontal: 20,
    textTransform: 'uppercase',
  },
  section: {
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },

  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  footerText: {
    fontSize: 12,
  },
  currencyContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default SettingsScreen;
