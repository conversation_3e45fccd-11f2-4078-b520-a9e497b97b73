// Offline Status Bar - Çevrimdışı durum göstergesi
// RFC-002: Offline Support - Status Component

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import OfflineService, { NetworkStatus } from '../services/OfflineService';

const { width } = Dimensions.get('window');

interface OfflineStatusBarProps {
  onSyncPress?: () => void;
}

const OfflineStatusBar: React.FC<OfflineStatusBarProps> = ({ onSyncPress }) => {
  const { theme } = useTheme();
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    type: 'unknown',
    isInternetReachable: true,
    connectionQuality: 'good',
  });
  const [syncStatus, setSyncStatus] = useState({
    isOnline: true,
    syncInProgress: false,
    pendingCount: 0,
    lastSync: null as string | null,
    networkQuality: 'good',
  });
  const [slideAnim] = useState(new Animated.Value(-60));

  useEffect(() => {
    // Network status listener
    const unsubscribeNetwork = OfflineService.addNetworkStatusListener((status) => {
      setNetworkStatus(status);
      updateSyncStatus();
    });

    // Initial load
    updateSyncStatus();

    return () => {
      unsubscribeNetwork();
    };
  }, []);

  useEffect(() => {
    // Animate status bar visibility
    if (!networkStatus.isConnected || syncStatus.pendingCount > 0) {
      // Show status bar
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Hide status bar
      Animated.timing(slideAnim, {
        toValue: -60,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [networkStatus.isConnected, syncStatus.pendingCount]);

  const updateSyncStatus = async () => {
    try {
      const status = await OfflineService.getSyncStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('Error updating sync status:', error);
    }
  };

  const handleSyncPress = async () => {
    if (onSyncPress) {
      onSyncPress();
    } else {
      try {
        await OfflineService.triggerManualSync();
        await updateSyncStatus();
      } catch (error) {
        console.error('Manual sync error:', error);
      }
    }
  };

  const getStatusColor = () => {
    if (!networkStatus.isConnected) return theme.colors.error;
    if (syncStatus.pendingCount > 0) return theme.colors.warning;
    if (syncStatus.syncInProgress) return theme.colors.primary;
    return theme.colors.success;
  };

  const getStatusIcon = () => {
    if (!networkStatus.isConnected) return 'cloud-offline-outline';
    if (syncStatus.syncInProgress) return 'sync-outline';
    if (syncStatus.pendingCount > 0) return 'cloud-upload-outline';
    return 'cloud-done-outline';
  };

  const getStatusText = () => {
    if (!networkStatus.isConnected) {
      return 'Çevrimdışı';
    }
    if (syncStatus.syncInProgress) {
      return 'Senkronize ediliyor...';
    }
    if (syncStatus.pendingCount > 0) {
      return `${syncStatus.pendingCount} işlem bekliyor`;
    }
    return 'Çevrimiçi';
  };

  const getNetworkQualityIcon = () => {
    switch (networkStatus.connectionQuality) {
      case 'excellent': return 'wifi';
      case 'good': return 'cellular';
      case 'poor': return 'cellular-outline';
      default: return 'help-outline';
    }
  };

  const formatLastSync = (lastSync: string | null) => {
    if (!lastSync) return 'Hiç';
    
    const syncDate = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - syncDate.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Az önce';
    if (diffMins < 60) return `${diffMins} dk önce`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} sa önce`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} gün önce`;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: getStatusColor(),
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <Ionicons
            name={getStatusIcon()}
            size={20}
            color={theme.colors.surface}
          />
          <Text style={[styles.statusText, { color: theme.colors.surface }]}>
            {getStatusText()}
          </Text>
        </View>

        <View style={styles.rightSection}>
          {networkStatus.isConnected && (
            <View style={styles.networkInfo}>
              <Ionicons
                name={getNetworkQualityIcon()}
                size={16}
                color={theme.colors.surface}
              />
              <Text style={[styles.networkText, { color: theme.colors.surface }]}>
                {networkStatus.type.toUpperCase()}
              </Text>
            </View>
          )}

          {(syncStatus.pendingCount > 0 || !networkStatus.isConnected) && (
            <TouchableOpacity
              style={styles.syncButton}
              onPress={handleSyncPress}
              disabled={syncStatus.syncInProgress || !networkStatus.isConnected}
            >
              <Ionicons
                name={syncStatus.syncInProgress ? 'hourglass-outline' : 'refresh-outline'}
                size={16}
                color={theme.colors.surface}
              />
              <Text style={[styles.syncButtonText, { color: theme.colors.surface }]}>
                {syncStatus.syncInProgress ? 'Bekle' : 'Sync'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Detailed info row */}
      {syncStatus.lastSync && (
        <View style={styles.detailRow}>
          <Text style={[styles.detailText, { color: theme.colors.surface }]}>
            Son sync: {formatLastSync(syncStatus.lastSync)}
          </Text>
          {syncStatus.pendingCount > 0 && (
            <Text style={[styles.detailText, { color: theme.colors.surface }]}>
              • {syncStatus.pendingCount} bekleyen işlem
            </Text>
          )}
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingTop: 40, // Status bar height
    paddingHorizontal: 16,
    paddingBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  networkInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  networkText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  syncButtonText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  detailText: {
    fontSize: 11,
    opacity: 0.9,
  },
});

export default OfflineStatusBar;
