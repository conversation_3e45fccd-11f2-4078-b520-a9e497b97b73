// Database Migration Utilities - RFC-003 Category Synchronization

import { SQLiteDatabase } from 'expo-sqlite/next';

/**
 * <PERSON><PERSON><PERSON> yoksa g<PERSON> ekler - Ultra-Detailed Debug Version
 */
export async function addColumnIfNotExists(
  db: SQLiteDatabase,
  table: string,
  column: string,
  type: string
): Promise<void> {
  console.log(`🔍 addColumnIfNotExists: Checking table '${table}' for column '${column}'.`);

  let pragmas;
  try {
    pragmas = await db.getAllAsync(`PRAGMA table_info(${table});`);
    console.log(`📊 addColumnIfNotExists: PRAGMA table_info(${table}) result:`, JSON.stringify(pragmas));
  } catch (pragmaError: any) {
    console.error(`❌ addColumnIfNotExists: Error executing PRAGMA table_info(${table}). Raw error:`, pragmaError);
    console.error(`❌ addColumnIfNotExists: PRAGMA error message:`, pragmaError.message);
    if (pragmaError.cause) console.error(`❌ addColumnIfNotExists: PRAGMA error cause:`, pragmaError.cause);
    throw pragmaError;
  }

  const columnExists = pragmas.some((col: any) => col.name === column);
  console.log(`🔍 addColumnIfNotExists: Column '${column}' in table '${table}' exists? ${columnExists}`);

  if (!columnExists) {
    console.log(`🔄 addColumnIfNotExists: Column '${column}' does NOT exist in '${table}'. Attempting to ADD.`);
    try {
      await db.execAsync(`ALTER TABLE ${table} ADD COLUMN ${column} ${type};`);
      console.log(`✅ addColumnIfNotExists: Successfully ADDED column '${column}' to table '${table}'.`);
    } catch (e: any) {
      // Hata nesnesinin tümünü ve önemli özelliklerini loglayalım
      console.error(`❌ addColumnIfNotExists: Error during ALTER TABLE ADD COLUMN for '${column}' in '${table}'.`);
      console.error(`❌ addColumnIfNotExists: Raw error object:`, JSON.stringify(e, Object.getOwnPropertyNames(e))); // Tüm özellikleri görmek için
      console.error(`❌ addColumnIfNotExists: Error message:`, e.message);
      if (e.cause) {
        console.error(`❌ addColumnIfNotExists: Error cause:`, e.cause);
      }

      // Hata mesajını ve nedenini (cause) daha dikkatli kontrol edelim
      const mainErrorMessage = (typeof e.message === 'string' ? e.message : '').toLowerCase();
      // e.cause bir string veya hata nesnesi olabilir. Stringe çevirip kontrol edelim.
      const causeMessage = (typeof e.cause === 'string' ? e.cause : (e.cause && typeof e.cause.message === 'string' ? e.cause.message : '')).toLowerCase();

      console.log(`🔍 addColumnIfNotExists: Lowercase mainErrorMessage: "${mainErrorMessage}"`);
      console.log(`🔍 addColumnIfNotExists: Lowercase causeMessage: "${causeMessage}"`);

      if (
        mainErrorMessage.includes('duplicate column name') ||
        causeMessage.includes('duplicate column name')
      ) {
        console.warn(
          `⚠️ addColumnIfNotExists: Caught 'duplicate column name' error for column '${column}' in table '${table}'. Assuming it was added in a previous (possibly failed) attempt. IGNORING error.`
        );
        // Bu durumda hata fırlatmıyoruz, çünkü sütun zaten var.
      } else {
        console.error(
          `❌ addColumnIfNotExists: Unhandled error adding column '${column}' to table '${table}'. Re-throwing.`
        );
        throw e; // Diğer hataları yeniden fırlat
      }
    }
  } else {
    console.log(
      `✅ addColumnIfNotExists: Column '${column}' ALREADY EXISTS in table '${table}'. Skipping ADD COLUMN.`
    );
  }
}

/**
 * Migration V13: Categories Code Sütunu - RFC-003 Ultra-Debug Implementation
 */
export async function migrationV13(db: SQLiteDatabase): Promise<void> {
  console.log('🚀🚀🚀 Starting migrationV13...');

  try {
    // 1. Sütunu kontrol ederek ekle
    console.log('🔄 migrationV13: Step 1: Calling addColumnIfNotExists for "code" column...');
    await addColumnIfNotExists(db, 'categories', 'code', 'TEXT DEFAULT NULL');
    console.log('✅ migrationV13: Step 1: addColumnIfNotExists for "code" column completed.');

    // 2. Daha önceki hatalı verileri temizle
    console.log('🔄 migrationV13: Step 2: Clearing existing "code" values...');
    await db.execAsync(`UPDATE categories SET code = NULL WHERE code IS NOT NULL;`);
    console.log('✅ migrationV13: Step 2: Clearing existing "code" values completed.');

    // 3. Mevcut kategorilere code ver
    console.log('🔄 migrationV13: Step 3: Starting category code updates...');
    const updates = [
      [`salary`, `Maaş`, `income`],
      [`investment_income`, `Yatırım Geliri`, `income`],
      [`side_income`, `Yan Gelir`, `income`],
      [`freelance`, `Freelance`, `income`],
      [`food`, `Yiyecek`, `expense`],
      [`food_drink`, `Yiyecek & İçecek`, `expense`], // Düzeltilmiş kod
      [`transportation`, `Ulaşım`, `expense`],
      [`bills`, `Faturalar`, `expense`],
      [`shopping`, `Alışveriş`, `expense`],
      [`entertainment`, `Eğlence`, `expense`],
      [`health`, `Sağlık`, `expense`],
    ];

    for (const [code, name, type] of updates) {
      await db.execAsync(
        `UPDATE categories SET code = ? WHERE name = ? AND type = ? AND code IS NULL`,
        [code, name, type]
      );
    }
    console.log('✅ migrationV13: Step 3: Category code updates completed.');

    // 4. Eksik kategorileri ekle (Income + Expense)
    console.log('🔄 migrationV13: Step 4: Starting to add missing categories...');
    const now = new Date().toISOString();

    // Income kategorileri ekle
    const incomeCategories = [
      ['cat_salary_sys', 'Maaş', 'income', 'salary', 'cash-outline', '#4CAF50'],
      ['cat_investment_income_sys', 'Yatırım Geliri', 'income', 'investment_income', 'trending-up-outline', '#2196F3'],
      ['cat_side_income_sys', 'Yan Gelir', 'income', 'side_income', 'briefcase-outline', '#FF9800'],
      ['cat_freelance_sys', 'Freelance', 'income', 'freelance', 'laptop-outline', '#9C27B0'],
    ];

    // Expense kategorileri ekle
    const expenseCategories = [
      ['cat_education_sys', 'Eğitim', 'expense', 'education', 'school-outline', '#3F51B5'],
      ['cat_rent_sys', 'Kira', 'expense', 'rent', 'home-outline', '#795548'],
      ['cat_insurance_sys', 'Sigorta', 'expense', 'insurance', 'shield-outline', '#607D8B'],
      ['cat_personal_care_sys', 'Kişisel Bakım', 'expense', 'personal_care', 'person-outline', '#E91E63'],
      ['cat_gifts_sys', 'Hediyeler', 'expense', 'gifts', 'gift-outline', '#FF9800'],
      ['cat_travel_sys', 'Seyahat', 'expense', 'travel', 'airplane-outline', '#00BCD4'],
      ['cat_donation_sys', 'Bağış', 'expense', 'donation', 'heart-outline', '#4CAF50'],
      ['cat_tax_sys', 'Vergiler', 'expense', 'tax', 'document-text-outline', '#9E9E9E'],
      ['cat_housing_sys', 'Konut', 'expense', 'housing', 'business-outline', '#795548'],
      ['cat_other_sys', 'Diğer', 'expense', 'other', 'ellipsis-horizontal-outline', '#9E9E9E'],
    ];

    const allCategories = [...incomeCategories, ...expenseCategories];

    for (const [id, name, type, code, icon, color] of allCategories) {
      await db.execAsync(
        `INSERT OR IGNORE INTO categories
        (id, user_id, name, type, code, icon, color, created_at, updated_at)
        VALUES (?, 'system', ?, ?, ?, ?, ?, ?, ?)`,
        [id, name, type, code, icon, color, now, now]
      );
    }
    console.log('✅ migrationV13: Step 4: Adding missing categories (income + expense) completed.');

    // 5. Index oluştur
    console.log('🔄 migrationV13: Step 5: Creating unique index...');
    try {
      await db.execAsync(
        `CREATE UNIQUE INDEX IF NOT EXISTS idx_categories_code ON categories(code) WHERE code IS NOT NULL;`
      );
      console.log('✅ migrationV13: Step 5: Unique index idx_categories_code created (if not exists).');
    } catch (e: any) {
      const errorMessage = (e.message || '').toString().toLowerCase();
      const errorCause = (e.cause || '').toString().toLowerCase();
      if (errorMessage.includes('index idx_categories_code already exists') || errorCause.includes('index idx_categories_code already exists')) {
          console.warn('⚠️ migrationV13: Index idx_categories_code already exists. Ignoring creation error.');
      } else if (errorMessage.includes('unique constraint failed') || errorCause.includes('unique constraint failed')) {
          console.error(
`❌ migrationV13: Error creating unique index: UNIQUE constraint failed. This means there are duplicate non-NULL values in the 'code' column.
Please check steps 3 and 4 to ensure all assigned 'code' values are unique for non-NULL codes.`
          );
          // Hangi kodların çakıştığını bulmak için bir sorgu çalıştırabilirsiniz:
          // const duplicates = await db.getAllAsync(`SELECT code, COUNT(*) c FROM categories WHERE code IS NOT NULL GROUP BY code HAVING c > 1;`);
          // console.log('Duplicate codes:', duplicates);
          throw e;
      } else {
          console.error('❌ migrationV13: Error creating unique index idx_categories_code:', e);
          throw e;
      }
    }

    console.log('🎉🎉🎉 MigrationV13 completed successfully.');

  } catch (migrationError: any) {
    console.error('🚨🚨🚨 FATAL ERROR in migrationV13 🚨🚨🚨');
    console.error(`❌ migrationV13 Error message:`, migrationError.message);
    if (migrationError.cause) console.error(`❌ migrationV13 Error cause:`, migrationError.cause);
    console.error(`❌ migrationV13 Raw error:`, JSON.stringify(migrationError, Object.getOwnPropertyNames(migrationError)));
    // Hatayı uygulamanın daha üst katmanlarına iletmek istiyorsanız:
    throw migrationError;
  }
}

// Migration V15: Complete Category Synchronization - SAFE VERSION
export const migrationV15 = async (db: SQLiteDatabase) => {
  console.log('🚀 Starting Migration V15: Complete Category Sync (SAFE)...');

  try {
    // 1. Add code column if not exists
    console.log('📝 Adding code column to categories table...');
    await addColumnIfNotExists(db, 'categories', 'code', 'TEXT DEFAULT NULL');

    // 2. Remove existing UNIQUE constraint on code (if exists)
    console.log('🔄 Removing existing code index to prevent conflicts...');
    await db.execAsync(`DROP INDEX IF EXISTS idx_categories_code;`);

    // 3. Clear all existing code values to prevent conflicts
    console.log('🧹 Clearing existing code values...');
    await db.execAsync(`UPDATE categories SET code = NULL;`);

    // 4. Check for duplicate codes before proceeding
    console.log('🔍 Checking for potential conflicts...');
    const duplicates = await db.getAllAsync(
      `SELECT code, COUNT(*) as count FROM categories
       WHERE code IS NOT NULL
       GROUP BY code
       HAVING COUNT(*) > 1`
    );
    if (duplicates.length > 0) {
      console.log('⚠️ Found duplicate codes:', duplicates);
    }

    // 5. Update existing categories with unique codes
    console.log('🔄 Updating existing categories with codes...');
    const codeUpdates = [
      // INCOME CATEGORIES
      { code: 'salary', name: 'Maaş', type: 'income' },
      { code: 'investment_income', name: 'Yatırım Geliri', type: 'income' },
      { code: 'side_income', name: 'Yan Gelir', type: 'income' },
      { code: 'freelance', name: 'Freelance', type: 'income' },

      // EXPENSE CATEGORIES
      { code: 'food', name: 'Yiyecek', type: 'expense' },
      { code: 'food_drink', name: 'Yiyecek & İçecek', type: 'expense' },
      { code: 'transportation', name: 'Ulaşım', type: 'expense' },
      { code: 'bills', name: 'Faturalar', type: 'expense' },
      { code: 'shopping', name: 'Alışveriş', type: 'expense' },
      { code: 'entertainment', name: 'Eğlence', type: 'expense' },
      { code: 'health', name: 'Sağlık', type: 'expense' },
    ];

    for (const update of codeUpdates) {
      await db.runAsync(
        `UPDATE categories SET code = ?, updated_at = datetime('now')
         WHERE name = ? AND type = ? AND code IS NULL`,
        [update.code, update.name, update.type]
      );
      console.log(`✅ Updated: ${update.name} → ${update.code}`);
    }

    // 6. Add missing categories
    console.log('➕ Adding missing categories...');
    const missingCategories = [
      // INCOME CATEGORIES
      { name: 'Kira Geliri', code: 'rental_income', type: 'income', icon: 'home-outline', color: '#795548' },
      { name: 'Bağış Geliri', code: 'donation_income', type: 'income', icon: 'heart-outline', color: '#4CAF50' },
      { name: 'Diğer Gelir', code: 'other_income', type: 'income', icon: 'cash-outline', color: '#607D8B' },

      // EXPENSE CATEGORIES
      { name: 'Eğitim', code: 'education', type: 'expense', icon: 'school-outline', color: '#3F51B5' },
      { name: 'Kira', code: 'rent', type: 'expense', icon: 'home-outline', color: '#795548' },
      { name: 'Konut', code: 'housing', type: 'expense', icon: 'business-outline', color: '#795548' },
      { name: 'Sigorta', code: 'insurance', type: 'expense', icon: 'shield-outline', color: '#607D8B' },
      { name: 'Yatırım', code: 'investment_expense', type: 'expense', icon: 'trending-up-outline', color: '#2196F3' },
      { name: 'Borç Ödemesi', code: 'debt_payment', type: 'expense', icon: 'card-outline', color: '#F44336' },
      { name: 'Seyahat', code: 'travel', type: 'expense', icon: 'airplane-outline', color: '#00BCD4' },
      { name: 'Kişisel Bakım', code: 'personal_care', type: 'expense', icon: 'person-outline', color: '#E91E63' },
      { name: 'Hediyeler', code: 'gifts', type: 'expense', icon: 'gift-outline', color: '#FF9800' },
      { name: 'Bağış', code: 'donation', type: 'expense', icon: 'heart-outline', color: '#4CAF50' },
      { name: 'Vergiler', code: 'taxes', type: 'expense', icon: 'document-text-outline', color: '#9E9E9E' },
      { name: 'Diğer', code: 'other', type: 'expense', icon: 'ellipsis-horizontal-outline', color: '#9E9E9E' }
    ];

    for (const category of missingCategories) {
      // Check if category already exists by name or code
      const existing = await db.getFirstAsync(
        `SELECT id FROM categories WHERE name = ? OR code = ?`,
        [category.name, category.code]
      );

      if (!existing) {
        const categoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await db.runAsync(
          `INSERT INTO categories (id, user_id, name, code, type, icon, color, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
          [categoryId, 'system', category.name, category.code, category.type, category.icon, category.color]
        );
        console.log(`✅ Added: ${category.name} (${category.code})`);
      } else {
        console.log(`⏭️ Skipped: ${category.name} (already exists)`);
      }
    }

    // 7. Create UNIQUE index for code column (only for non-NULL values)
    console.log('🔗 Creating unique index for code column...');
    await db.execAsync(`CREATE UNIQUE INDEX IF NOT EXISTS idx_categories_code ON categories(code) WHERE code IS NOT NULL;`);

    console.log('✅ Migration V15 completed successfully!');

    // 8. Verify results
    const totalCategories = await db.getFirstAsync(`SELECT COUNT(*) as count FROM categories`);
    const categoriesWithCode = await db.getFirstAsync(`SELECT COUNT(*) as count FROM categories WHERE code IS NOT NULL`);
    console.log(`📊 Total categories: ${totalCategories?.count}`);
    console.log(`📊 Categories with code: ${categoriesWithCode?.count}`);

  } catch (error) {
    console.error('❌ Migration V15 failed:', error);
    throw error;
  }
};

// Migration V16: Safe Category Addition - UNIQUE Constraint Bypass
export const migrationV16 = async (db: SQLiteDatabase) => {
  console.log('🛡️ Starting Migration V16: Safe Category Addition...');

  try {
    // 1. Add code column if not exists
    await addColumnIfNotExists(db, 'categories', 'code', 'TEXT DEFAULT NULL');

    // 2. Only add missing categories (don't touch existing ones)
    console.log('➕ Adding only missing categories...');
    const missingCategories = [
      // INCOME CATEGORIES
      { name: 'Kira Geliri', code: 'rental_income', type: 'income', icon: 'home-outline', color: '#795548' },
      { name: 'Bağış Geliri', code: 'donation_income', type: 'income', icon: 'heart-outline', color: '#4CAF50' },
      { name: 'Diğer Gelir', code: 'other_income', type: 'income', icon: 'cash-outline', color: '#607D8B' },

      // EXPENSE CATEGORIES
      { name: 'Eğitim', code: 'education', type: 'expense', icon: 'school-outline', color: '#3F51B5' },
      { name: 'Kira', code: 'rent', type: 'expense', icon: 'home-outline', color: '#795548' },
      { name: 'Konut', code: 'housing', type: 'expense', icon: 'business-outline', color: '#795548' },
      { name: 'Sigorta', code: 'insurance', type: 'expense', icon: 'shield-outline', color: '#607D8B' },
      { name: 'Yatırım', code: 'investment_expense', type: 'expense', icon: 'trending-up-outline', color: '#2196F3' },
      { name: 'Borç Ödemesi', code: 'debt_payment', type: 'expense', icon: 'card-outline', color: '#F44336' },
      { name: 'Seyahat', code: 'travel', type: 'expense', icon: 'airplane-outline', color: '#00BCD4' },
      { name: 'Kişisel Bakım', code: 'personal_care', type: 'expense', icon: 'person-outline', color: '#E91E63' },
      { name: 'Hediyeler', code: 'gifts', type: 'expense', icon: 'gift-outline', color: '#FF9800' },
      { name: 'Bağış', code: 'donation', type: 'expense', icon: 'heart-outline', color: '#4CAF50' },
      { name: 'Vergiler', code: 'taxes', type: 'expense', icon: 'document-text-outline', color: '#9E9E9E' },
      { name: 'Diğer', code: 'other', type: 'expense', icon: 'ellipsis-horizontal-outline', color: '#9E9E9E' }
    ];

    for (const category of missingCategories) {
      // Check if category already exists by name only (ignore code conflicts)
      const existing = await db.getFirstAsync(
        `SELECT id FROM categories WHERE name = ?`,
        [category.name]
      );

      if (!existing) {
        // Generate unique code if conflict exists
        let finalCode = category.code;
        let counter = 1;

        while (true) {
          const codeExists = await db.getFirstAsync(
            `SELECT id FROM categories WHERE code = ?`,
            [finalCode]
          );

          if (!codeExists) break;

          finalCode = `${category.code}_${counter}`;
          counter++;
        }

        const categoryId = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await db.runAsync(
          `INSERT INTO categories (id, user_id, name, code, type, icon, color, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
          [categoryId, 'system', category.name, finalCode, category.type, category.icon, category.color]
        );
        console.log(`✅ Added: ${category.name} (${finalCode})`);
      } else {
        console.log(`⏭️ Skipped: ${category.name} (already exists)`);
      }
    }

    console.log('✅ Migration V16 completed successfully!');

    // Verify results
    const totalCategories = await db.getFirstAsync(`SELECT COUNT(*) as count FROM categories`);
    console.log(`📊 Total categories after V16: ${totalCategories?.count}`);

  } catch (error) {
    console.error('❌ Migration V16 failed:', error);
    throw error;
  }
};
