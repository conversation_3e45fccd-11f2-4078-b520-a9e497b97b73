// Storage & Sync Types - Veri saklama ve senkronizasyon tipleri

import { Transaction } from './transaction';
import { CustomCategory, CategoryRule, CategoryBudget } from './category';
import { CurrencyCode } from './currency';

// Sync durumu
export type SyncStatus = 
  | 'idle'           // Boşta
  | 'syncing'        // Senkronize ediliyor
  | 'success'        // Başarılı
  | 'error'          // Hata
  | 'conflict'       // Çakışma
  | 'offline';       // Çevrimdışı

// Veri tipi
export type DataType = 
  | 'transaction' 
  | 'category' 
  | 'budget' 
  | 'goal' 
  | 'rule' 
  | 'setting' 
  | 'user_profile';

// Sync operasyon tipi
export type SyncOperation = 
  | 'create' 
  | 'update' 
  | 'delete' 
  | 'restore';

// Çakışma çözüm stratejisi
export type ConflictResolution = 
  | 'local_wins'     // Yerel veri kazanır
  | 'remote_wins'    // Uzak veri kazanır
  | 'merge'          // Birleştir
  | 'manual'         // <PERSON>ö<PERSON>ü<PERSON>
  | 'latest_wins';   // En son güncellenen kazanır

// Sync kayıt
export interface SyncRecord {
  id: string;
  dataType: DataType;
  dataId: string;
  operation: SyncOperation;
  localTimestamp: string;
  remoteTimestamp?: string;
  status: SyncStatus;
  retryCount: number;
  lastRetry?: string;
  error?: string;
  conflictData?: {
    local: any;
    remote: any;
    resolution?: ConflictResolution;
    resolvedAt?: string;
    resolvedBy?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Offline değişiklik
export interface OfflineChange {
  id: string;
  dataType: DataType;
  dataId: string;
  operation: SyncOperation;
  data: any;
  timestamp: string;
  userId: string;
  deviceId: string;
  isProcessed: boolean;
  processedAt?: string;
  error?: string;
}

// Sync konfigürasyonu
export interface SyncConfig {
  userId: string;
  enabled: boolean;
  autoSync: boolean;
  syncInterval: number;        // dakika
  wifiOnly: boolean;
  backgroundSync: boolean;
  conflictResolution: ConflictResolution;
  maxRetries: number;
  retryDelay: number;          // saniye
  batchSize: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  lastFullSync?: string;
  lastIncrementalSync?: string;
  createdAt: string;
  updatedAt: string;
}

// Backup konfigürasyonu
export interface BackupConfig {
  userId: string;
  enabled: boolean;
  autoBackup: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;                // HH:MM format
  cloudProvider: 'google_drive' | 'icloud' | 'dropbox' | 'onedrive' | 'local';
  encryptionEnabled: boolean;
  compressionEnabled: boolean;
  includeImages: boolean;
  maxBackups: number;
  lastBackup?: string;
  nextBackup?: string;
  backupSize?: number;         // bytes
  createdAt: string;
  updatedAt: string;
}

// Backup dosyası
export interface BackupFile {
  id: string;
  userId: string;
  filename: string;
  size: number;
  createdAt: string;
  dataTypes: DataType[];
  recordCount: number;
  version: string;
  checksum: string;
  isEncrypted: boolean;
  isCompressed: boolean;
  cloudUrl?: string;
  localPath?: string;
  expiresAt?: string;
  metadata: {
    appVersion: string;
    deviceInfo: string;
    totalTransactions: number;
    totalCategories: number;
    dateRange: {
      start: string;
      end: string;
    };
  };
}

// Veri export formatı
export interface DataExport {
  version: string;
  exportedAt: string;
  userId: string;
  currency: CurrencyCode;
  data: {
    transactions: Transaction[];
    categories: CustomCategory[];
    rules: CategoryRule[];
    budgets: CategoryBudget[];
    settings: Record<string, any>;
  };
  metadata: {
    totalRecords: number;
    dateRange: {
      start: string;
      end: string;
    };
    appVersion: string;
    exportFormat: 'json' | 'csv' | 'excel';
  };
}

// Veri import sonucu
export interface ImportResult {
  success: boolean;
  totalRecords: number;
  importedRecords: number;
  skippedRecords: number;
  errorRecords: number;
  duplicateRecords: number;
  summary: {
    transactions: {
      imported: number;
      skipped: number;
      errors: number;
    };
    categories: {
      imported: number;
      skipped: number;
      errors: number;
    };
    rules: {
      imported: number;
      skipped: number;
      errors: number;
    };
    budgets: {
      imported: number;
      skipped: number;
      errors: number;
    };
  };
  errors: Array<{
    record: any;
    error: string;
    line?: number;
  }>;
  warnings: Array<{
    message: string;
    record?: any;
  }>;
  importedAt: string;
  duration: number;            // milisaniye
}

// Storage istatistikleri
export interface StorageStats {
  totalSize: number;           // bytes
  usedSize: number;
  freeSize: number;
  recordCounts: Record<DataType, number>;
  oldestRecord: string;
  newestRecord: string;
  syncStats: {
    pendingChanges: number;
    lastSync: string;
    syncErrors: number;
  };
  backupStats: {
    totalBackups: number;
    lastBackup: string;
    totalBackupSize: number;
  };
  cacheStats: {
    cacheSize: number;
    hitRate: number;
    lastCleanup: string;
  };
}

// Veri temizleme kuralları
export interface DataCleanupRules {
  userId: string;
  enabled: boolean;
  rules: Array<{
    dataType: DataType;
    retentionDays: number;
    conditions?: {
      status?: string[];
      categories?: string[];
      amountThreshold?: number;
    };
    action: 'delete' | 'archive' | 'anonymize';
  }>;
  lastCleanup?: string;
  nextCleanup?: string;
  autoCleanup: boolean;
  cleanupTime: string;         // HH:MM format
  createdAt: string;
  updatedAt: string;
}

// Cache konfigürasyonu
export interface CacheConfig {
  enabled: boolean;
  maxSize: number;             // bytes
  ttl: number;                 // saniye
  strategy: 'lru' | 'lfu' | 'fifo';
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  preloadData: DataType[];
  invalidationRules: Array<{
    trigger: 'time' | 'size' | 'manual';
    condition: any;
  }>;
}

// Veri migrasyon
export interface DataMigration {
  id: string;
  fromVersion: string;
  toVersion: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  progress: number;            // 0-100
  steps: Array<{
    name: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress: number;
    error?: string;
  }>;
  backupCreated: boolean;
  backupId?: string;
  rollbackAvailable: boolean;
  error?: string;
}

// Sync çakışması
export interface SyncConflict {
  id: string;
  dataType: DataType;
  dataId: string;
  localData: any;
  remoteData: any;
  conflictFields: string[];
  detectedAt: string;
  resolution?: ConflictResolution;
  resolvedAt?: string;
  resolvedBy?: string;
  mergedData?: any;
  autoResolvable: boolean;
  severity: 'low' | 'medium' | 'high';
}

// Offline queue
export interface OfflineQueue {
  id: string;
  userId: string;
  changes: OfflineChange[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  processedAt?: string;
  totalChanges: number;
  processedChanges: number;
  failedChanges: number;
  retryCount: number;
  lastRetry?: string;
  error?: string;
}

// Veri doğrulama
export interface DataValidation {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    severity: 'error' | 'warning';
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  validatedAt: string;
  validationRules: string[];
}

// Storage provider interface
export interface StorageProvider {
  name: string;
  type: 'local' | 'cloud';
  isAvailable: boolean;
  maxSize?: number;
  usedSize?: number;
  features: {
    encryption: boolean;
    compression: boolean;
    versioning: boolean;
    sharing: boolean;
  };
  config: Record<string, any>;
}

// Sync event
export interface SyncEvent {
  id: string;
  type: 'sync_started' | 'sync_completed' | 'sync_failed' | 'conflict_detected' | 'backup_created';
  timestamp: string;
  userId: string;
  deviceId: string;
  data?: any;
  metadata?: Record<string, any>;
}

// Performance metrikleri
export interface PerformanceMetrics {
  syncDuration: number;        // milisaniye
  uploadSpeed: number;         // bytes/second
  downloadSpeed: number;       // bytes/second
  errorRate: number;           // 0-1 arası
  successRate: number;         // 0-1 arası
  averageRetries: number;
  peakMemoryUsage: number;     // bytes
  networkUsage: number;        // bytes
  batteryImpact: 'low' | 'medium' | 'high';
  measuredAt: string;
}

export default {
  // Utility functions can be added here
};
