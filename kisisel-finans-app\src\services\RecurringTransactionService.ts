// Recurring Transaction Service - Tekrarlayan işlem otomasyonu

import RecurringTemplateRepository from '../database/repositories/RecurringTemplateRepository';
import TransactionRepository from '../database/repositories/TransactionRepository';
import NotificationService from './NotificationService';
import { RecurringTemplate, Transaction, RecurrenceFrequency } from '../types/transaction';

class RecurringTransactionService {
  private notificationService: NotificationService;

  constructor() {
    this.notificationService = NotificationService;
  }

  /**
   * Tekrarlayan şablon oluştur
   */
  async createRecurringTemplate(template: Omit<RecurringTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log('🔄 Creating recurring template:', template.name);

      // Şablonu kaydet
      const templateId = await RecurringTemplateRepository.create(template);

      // İlk bildirim zamanla
      if (template.isActive) {
        await this.scheduleNextNotification(templateId, template);
      }

      console.log(`✅ Recurring template created with ID: ${templateId}`);
      return templateId;
    } catch (error) {
      console.error('❌ Error creating recurring template:', error);
      throw error;
    }
  }

  /**
   * Günlük kontrol - çalıştırılması gereken işlemleri kontrol et
   */
  async processDailyRecurringTransactions(): Promise<void> {
    try {
      console.log('🔄 Processing daily recurring transactions...');
      const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

      // Çalıştırılması gereken şablonları al
      const templates = await RecurringTemplateRepository.getTemplatesForExecution(currentDate);

      for (const template of templates) {
        await this.executeRecurringTemplate(template);
      }

      console.log(`✅ Processed ${templates.length} recurring transactions`);
    } catch (error) {
      console.error('❌ Error processing daily recurring transactions:', error);
      throw error;
    }
  }

  /**
   * Tekrarlayan şablonu çalıştır (işlem oluştur)
   */
  async executeRecurringTemplate(template: RecurringTemplate): Promise<string | null> {
    try {
      console.log(`🔄 Executing recurring template: ${template.name}`);

      // Yeni işlem oluştur
      const transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: template.userId,
        type: template.type,
        amount: template.amount,
        currency: template.currency,
        category: template.category,
        description: template.description || `${template.name} (Otomatik)`,
        date: new Date().toISOString(),
        paymentMethod: template.paymentMethod,
        status: 'completed',
        tags: [],
        isDeleted: false,
        parentTransactionId: template.id, // Şablon ID'sini parent olarak kaydet
        recurrence: template.recurrence,
      };

      // Expense specific fields
      if (template.type === 'expense') {
        (transaction as any).subcategory = template.subcategory;
        (transaction as any).merchant = template.description;
        (transaction as any).isBusinessExpense = false;
        (transaction as any).isDeductible = false;
      }

      // Income specific fields
      if (template.type === 'income') {
        (transaction as any).source = template.description;
        (transaction as any).taxable = false;
      }

      // İşlemi oluştur
      const transactionId = await TransactionRepository.create(transaction);

      // Şablonun son çalıştırma tarihini güncelle
      const now = new Date().toISOString();
      const nextExecutionDate = this.calculateNextExecutionDate(template.recurrence, now);

      await RecurringTemplateRepository.update(template.id, {
        lastExecutionDate: now,
        nextExecutionDate: nextExecutionDate,
      });

      // Bildirim gönder
      await this.notificationService.sendImmediateNotification({
        title: 'Otomatik İşlem Oluşturuldu',
        body: `${template.name} işlemi otomatik olarak eklendi (${template.amount} ${template.currency})`,
        data: {
          type: 'recurring_transaction',
          transactionId: transactionId,
          templateId: template.id,
        },
      });

      // Sonraki bildirim zamanla
      await this.scheduleNextNotification(template.id, {
        ...template,
        nextExecutionDate: nextExecutionDate,
      });

      console.log(`✅ Recurring transaction executed: ${transactionId}`);
      return transactionId;
    } catch (error) {
      console.error('❌ Error executing recurring template:', error);

      // Hata bildirimi gönder
      await this.notificationService.sendImmediateNotification({
        title: 'Otomatik İşlem Hatası',
        body: `${template.name} işlemi oluşturulurken hata oluştu`,
        data: {
          type: 'recurring_error',
          templateId: template.id,
        },
      });

      return null;
    }
  }

  /**
   * Sonraki çalıştırma tarihini hesapla
   */
  private calculateNextExecutionDate(recurrence: any, currentDate: string): string {
    const current = new Date(currentDate);
    const next = new Date(current);

    switch (recurrence.frequency) {
      case 'daily':
        next.setDate(current.getDate() + recurrence.interval);
        break;
      case 'weekly':
        next.setDate(current.getDate() + (7 * recurrence.interval));
        break;
      case 'bi_weekly':
        next.setDate(current.getDate() + (14 * recurrence.interval));
        break;
      case 'monthly':
        next.setMonth(current.getMonth() + recurrence.interval);
        break;
      case 'quarterly':
        next.setMonth(current.getMonth() + (3 * recurrence.interval));
        break;
      case 'semi_annually':
        next.setMonth(current.getMonth() + (6 * recurrence.interval));
        break;
      case 'annually':
        next.setFullYear(current.getFullYear() + recurrence.interval);
        break;
      default:
        // Varsayılan olarak 1 ay sonra
        next.setMonth(current.getMonth() + 1);
    }

    return next.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  /**
   * Sonraki bildirim zamanla
   */
  private async scheduleNextNotification(templateId: string, template: RecurringTemplate): Promise<void> {
    try {
      const nextDate = new Date(template.nextExecutionDate);

      // Bildirimi 1 saat önce zamanla
      const notificationDate = new Date(nextDate.getTime() - (60 * 60 * 1000));

      await this.notificationService.scheduleNotification(
        {
          title: 'Tekrarlayan İşlem Hatırlatması',
          body: `${template.name} işlemi yarın otomatik olarak eklenecek`,
          data: {
            type: 'recurring_reminder',
            templateId: templateId,
          },
        },
        notificationDate
      );

      console.log(`📅 Notification scheduled for template: ${templateId}`);
    } catch (error) {
      console.error('❌ Error scheduling notification:', error);
    }
  }

  /**
   * Kullanıcının tekrarlayan şablonlarını al
   */
  async getUserRecurringTemplates(userId: string): Promise<RecurringTemplate[]> {
    try {
      return await RecurringTemplateRepository.getByUserId(userId);
    } catch (error) {
      console.error('❌ Error getting user recurring templates:', error);
      throw error;
    }
  }

  /**
   * Tekrarlayan şablonu aktif/pasif yap
   */
  async toggleRecurringTemplate(templateId: string, isActive: boolean): Promise<void> {
    try {
      await RecurringTemplateRepository.toggleActive(templateId, isActive);

      if (isActive) {
        // Aktif yapıldığında bildirim zamanla
        const template = await RecurringTemplateRepository.getByUserId(''); // TODO: userId gerekli
        // await this.scheduleNextNotification(templateId, template);
      }

      console.log(`✅ Recurring template ${isActive ? 'activated' : 'deactivated'}: ${templateId}`);
    } catch (error) {
      console.error('❌ Error toggling recurring template:', error);
      throw error;
    }
  }

  /**
   * Tekrarlayan şablonu sil
   */
  async deleteRecurringTemplate(templateId: string): Promise<void> {
    try {
      await RecurringTemplateRepository.delete(templateId);

      // İlgili bildirimleri iptal et
      // TODO: Notification service'de cancel by data implementasyonu gerekli

      console.log(`✅ Recurring template deleted: ${templateId}`);
    } catch (error) {
      console.error('❌ Error deleting recurring template:', error);
      throw error;
    }
  }

  /**
   * Frequency label'ını al
   */
  getFrequencyLabel(frequency: RecurrenceFrequency): string {
    const labels: Record<RecurrenceFrequency, string> = {
      none: 'Tek seferlik',
      daily: 'Günlük',
      weekly: 'Haftalık',
      bi_weekly: 'İki haftada bir',
      monthly: 'Aylık',
      quarterly: 'Üç aylık',
      semi_annually: 'Altı aylık',
      annually: 'Yıllık',
    };

    return labels[frequency] || 'Bilinmiyor';
  }

  /**
   * Sonraki çalıştırma tarihini formatla
   */
  formatNextExecutionDate(date: string): string {
    const nextDate = new Date(date);
    const now = new Date();
    const diffTime = nextDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Bugün';
    } else if (diffDays === 1) {
      return 'Yarın';
    } else if (diffDays < 7) {
      return `${diffDays} gün sonra`;
    } else {
      return nextDate.toLocaleDateString('tr-TR');
    }
  }
}

export default new RecurringTransactionService();
